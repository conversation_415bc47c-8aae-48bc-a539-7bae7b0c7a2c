/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#ifndef VMONITORSELECT_H
#define VMONITORSELECT_H
#include "controls_global.h"

#include "basewidget.h"
#include "basedialogframe.h"

class VMonitorSelect;
class ProbeCodeDialog;
class IBeamFormer;
class ITestCommand;
class AutoSwitchProbeDialog;
class BurnInTestDialog;
class PatientWorkflow;
class ImageSkimManager;
class MotorControl;
class MotorOperationDialog;
class MotorControl;
class ProbeSelfTestDialog;
class ChisonUltrasoundContext;
class AutoTestDialog;
class IStateManager;
class IColorMapManager;

class CONTROLSSHARED_EXPORT VMonitorSelectDialog : public BaseDialogFrame
{
    Q_OBJECT
public:
    explicit VMonitorSelectDialog(IBeamFormer* beamFormer, QWidget* parent = 0);
    void setVMonitor(QWidget* w);
    void setProbeTemperature(QWidget* w);
    void setPatientWorkFlow(PatientWorkflow* value);
    void setTestCommand(ITestCommand* value);
    void setImageSkimManager(ImageSkimManager* value);
    void setMotorControl(MotorControl* value);
    void setProbeSelfTest(ChisonUltrasoundContext* value);
    void setAutoTest();
    void showVMonitor();
    void setStateManager(IStateManager* value);
    void setColorMapManager(IColorMapManager* value);

signals:
    void sendParameter();

private:
    VMonitorSelect* m_child;
};

namespace Ui
{
class VMonitorSelect;
}

class CONTROLSSHARED_EXPORT VMonitorSelect : public BaseWidget
{
    Q_OBJECT

public:
    explicit VMonitorSelect(QWidget* parent = 0);
    ~VMonitorSelect();
    void setVMonitor(QWidget* w);
    void setProbeTemperature(QWidget* w);
    void setBeamFormer(IBeamFormer* value);
    void setTestCommand(ITestCommand* value);
    void setPatientWorkFlow(PatientWorkflow* value);
    void setImageSkimManager(ImageSkimManager* value);
    void setMotorControl(MotorControl* motorCtrl);
    void setProbeSelfTest(ChisonUltrasoundContext* value);
    void setAutoTest();
    void showVMonitor();
    void setStateManager(IStateManager* value);
    void setColorMapManager(IColorMapManager* value);

private slots:
    void on_pushButtonSwitchProbe_clicked();

protected:
    void retranslateUi()
    {
    }
private slots:
    void on_pushButtonVMonitor_clicked();

    void on_pushButtonProbeCode_clicked();
    void on_pushButtonBurnInTest_clicked();

    void on_pushButtonProbeTemp_clicked();

    void on_pushButtonMotorTest_clicked();
    void on_pushButtonFpgaInfo_clicked();

    void on_ExportGrabScreen_clicked();

    void on_pushButtonProbeSelfTest_clicked();

    void on_pushButtonAutoTest_clicked();

signals:
    void quit();
    void sendParameter();

private:
    Ui::VMonitorSelect* ui;
    QWidget* m_vmonitor;
    ProbeCodeDialog* m_probeCodeDialog;
    IBeamFormer* m_beamFormer;
    ITestCommand* m_testCommand;
    AutoSwitchProbeDialog* m_switchProbeWidget;
    PatientWorkflow* m_patientWorkFlow;
    ImageSkimManager* m_imageSkimManager;
    BurnInTestDialog* m_burnInTestDialog;
    QWidget* m_ProbeTempDialog;
    MotorOperationDialog* m_MotorOperationDialog;
    ProbeSelfTestDialog* m_ProbeSelfTestDialog;
    AutoTestDialog* m_AutoTestDialog;
    bool m_isDone;
    IStateManager* m_StateManager;
    IColorMapManager* m_ColorMapManager;
};

#endif // VMONITORSELECT_H
