#include "databackupwidget.h"
#include "ui_databackupwidget.h"
#include "util.h"

DataBackupDialog::DataBackupDialog(IDiskDevice* diskDevice, QWidget* parent)
    : BaseInputAbleDialogFrame(parent)
{
    m_Child = new DataBackupWidget(diskDevice, this);
    this->setContent(this->m_Child);
    this->setIsShowOk(false);
    connect(m_Child, SIGNAL(clickOK(QString)), this, SIGNAL(clickOK(QString)));
    connect(m_<PERSON>, SIGNAL(exit()), this, SLOT(close()));
    this->adjustSize();
}

DataBackupWidget* DataBackupDialog::childDialog()
{
    return m_Child;
}

bool DataBackupDialog::isUDiskMounted() const
{
    return m_Child->isUDiskMounted();
}

DataBackupWidget::DataBackupWidget(IDiskDevice* diskDevice, QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::DataBackupWidget)
{
    ui->setupUi(this);
    ui->comboBox->setDiskDevice(diskDevice);
    ui->comboBox->setDiskType(DiskPathComboBox::RemoveableDisk);
}

DataBackupWidget::~DataBackupWidget()
{
    delete ui;
}

bool DataBackupWidget::isUDiskMounted() const
{
    return ui->comboBox->count() > 0;
}

// added by jyq to retranslateUi
void DataBackupWidget::retranslateUi()
{
    this->ui->retranslateUi(this);
}

void DataBackupWidget::on_pushButtonOK_clicked()
{
    emit exit();
    Util::processEvents();
    emit clickOK(ui->comboBox->currentDiskPath());
}

void DataBackupWidget::on_pushButtonCancel_clicked()
{
    emit exit();
}
