#ifndef ABSTRACTFOURDLIVEWIDGET_H
#define ABSTRACTFOURDLIVEWIDGET_H

#include "controls_global.h"
#include "ifourdlivewidget.h"
#include "imageeventargs.h"
#include "fourddatatypedef.h"
class ImageTile;
class ILineBufferManager;
class SonoParameters;
class FourDLiveModel;
class IFourDDataSender;
class ColorMapAssembly;
class FourDVolumeLineImageArgs;
class IFourDParameter;
class CONTROLSSHARED_EXPORT AbstractFourDLiveWidget : public IFourDLiveWidget
{
    Q_OBJECT
public:
    explicit AbstractFourDLiveWidget(IFourDDataSender* dataSender, QWidget* parent = 0);
    ~AbstractFourDLiveWidget();
    void prepareFourDParameters(bool resetParameters);
    void clearFourDData();
    void saveROIInfo();
    void onAviPlay(const QString& aviFileName);
    ICommand* currentMouseCommand() const;
    void resetToPresetDefaultValue();
    void setFreezeBarAllIndex();

protected:
    void retranslateUi();
    void updateCurrentIndex();
    void updateMouseCommand(FourDMouseStateEnum::FourDMouseActionMode mode);
public slots:
    void onFourDZoom(bool value);
    virtual void onCurvedLineCtrlPointMoved(const QString& direction) = 0;
    void sendVolumeData(const FourDVolumeLineImageArgs& arg);
    void onFourDTransformAxisChanged(QString value);
    virtual void onFourDDirectionChanged(bool increase);

protected:
    ImageTile* m_ImageTile;
    ILineBufferManager* m_LineBufferManager;
    SonoParameters* m_SonoParameters;
    FourDLiveModel* m_Model;
    ICommand* m_currentMouseCommand;
    IFourDParameter* m_fourDParameter;
};

#endif // ABSTRACTFOURDLIVEWIDGET_H
