#ifndef IFOURDLICENSEPROCESSOR_H
#define IFOURDLICENSEPROCESSOR_H

#include "controls_global.h"
#include <QObject>

class SonoParameters;
class CONTROLSSHARED_EXPORT IFourDLicenseProcessor : public QObject
{
    Q_OBJECT
public:
    enum FourDLicenseState
    {
        NotVerified,
        NotFound,
        Invalid,
        RemainDays,
        UltimateUser,
        ProductionTest
    };
    virtual ~IFourDLicenseProcessor()
    {
    }
    virtual bool isFourDOpened() const = 0;
    virtual int remainDays() const = 0;
    virtual void verifyLicenseState() = 0;
    virtual void processFourDLicenseState(bool showInfo = true) = 0;
    virtual void setSonoParameters(SonoParameters* sonoParameters) = 0;
public slots:
    virtual void onFourDStatusChanged(bool isOpen) = 0;
    virtual void onVirtualHDStatusChanged(bool isOpen) = 0;
    virtual void onSystemDateChanged() = 0;
signals:
    void activeFourD(bool verifyLicense);

protected:
    explicit IFourDLicenseProcessor(QObject* parent = 0);
};

#endif // IFOURDLICENSECHECKER_H
