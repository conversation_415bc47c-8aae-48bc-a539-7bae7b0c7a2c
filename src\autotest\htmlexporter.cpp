/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#include "htmlexporter.h"
#include <QString>
#include <QTextStream>
#include <QTextDocument>
#include <QFile>

HTMLExporter::HTMLExporter(int row, int col)
    : m_rowCount(row)
    , m_colCount(col)
{
}

QString HTMLExporter::create()
{
    QString strStream;
    QTextStream out(&strStream);

    const int rowCount = m_rowCount;
    const int columnCount = m_colCount;

    out << "<html>\n<center>\n"
           "<head>\n"
           "<meta Content=\"Text/html; charset=Windows-1251\">\n"
        << QString("<title>%1</title>\n").arg(m_title);

    if (!m_head.isEmpty())
    {
        out << "<span style=' font-size:22pt; font-weight:600;'>" << m_head << "</span>";
    }

    out << "</head>\n"
           "<body bgcolor=#ffffff link=#5000A0>\n";

    // front list

    foreach (QString val, m_frontList)
    {
        out << "<p>" + val + "</p>";
    }

    out << "<table style='text-align:center' border=1 width=80% cellspacing=0 cellpadding=10>\n";
    // headers
    out << "<thead><tr bgcolor=#f0f0f0>";
    for (int column = 0; column < columnCount; column++)
        out << QString("<th>%1</th>").arg(m_titleList.at(column));
    out << "</tr></thead>\n";

    // data table
    for (int row = 0; row < rowCount; row++)
    {
        out << "<tr>";
        for (int column = 0; column < columnCount; column++)
        {
            QString data = m_valueList.at(column).at(row);
            out << QString("<td bkcolor=0>%1</td>").arg((!data.isEmpty()) ? data : QString("&nbsp;"));
        }
        out << "</tr>\n";
    }
    out << "</table>\n";

    // end list

    foreach (QString val, m_endList)
    {
        out << "<p>" + val + "</p>";
    }

    out << "</body>\n"
        << "</center>\n"
        << "</html>\n";

    QTextDocument* document = new QTextDocument();
    document->setHtml(strStream);

    return strStream;

    //        document->set

    //        QFile file("test.html");
    //        file.open(QIODevice::WriteOnly);
    //        file.write(document->toHtml().toAscii());
    //        file.write(strStream.toAscii());
    //        file.close();
}

void HTMLExporter::setHTMLTitle(const QString& title)
{
    m_title = title;
}

void HTMLExporter::setTitleList(const QStringList& value)
{
    m_titleList = value;
}

void HTMLExporter::setTitle(const QString& val)
{
    m_title = val;
}

void HTMLExporter::addValueList(const QList<QString>& value)
{
    m_valueList.push_back(value);
}

void HTMLExporter::setRow(int value)
{
    m_rowCount = value;
}

void HTMLExporter::setCol(int value)
{
    m_colCount = value;
}

void HTMLExporter::setHead(const QString& value)
{
    m_head = value;
}

QString HTMLExporter::head() const
{
    return m_head;
}

void HTMLExporter::clearValue()
{
    m_valueList.clear();
}

void HTMLExporter::setFront(const QStringList& value)
{
    m_frontList = value;
}

void HTMLExporter::setEnd(const QStringList& value)
{
    m_endList = value;
}
