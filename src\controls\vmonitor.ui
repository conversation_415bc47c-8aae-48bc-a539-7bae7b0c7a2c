<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>VMonitor</class>
 <widget class="QWidget" name="VMonitor">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>925</width>
    <height>675</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Hardware Information</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_7">
   <item row="0" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_9">
     <item>
      <widget class="QTabWidget" name="tabWidget">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="currentIndex">
        <number>0</number>
       </property>
       <widget class="QWidget" name="tab">
        <attribute name="title">
         <string>PC</string>
        </attribute>
        <layout class="QGridLayout" name="gridLayout_5">
         <item row="0" column="0">
          <widget class="QLabel" name="label_37">
           <property name="text">
            <string>CPU Temp.</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QLineEdit" name="lineEdit_cputemp">
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QLabel" name="label_38">
           <property name="text">
            <string>°C</string>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="label_39">
           <property name="text">
            <string>CPU Usage</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QLineEdit" name="lineEdit_cpuusage">
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item row="1" column="2">
          <widget class="QLabel" name="label_41">
           <property name="text">
            <string>%</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="label_44">
           <property name="text">
            <string>Core Num</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLineEdit" name="lineEdit_cpucorenum">
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item row="2" column="3">
          <widget class="QLabel" name="label_40">
           <property name="text">
            <string>Thread Num</string>
           </property>
          </widget>
         </item>
         <item row="2" column="4">
          <widget class="QLineEdit" name="lineEdit_cputhread">
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QLabel" name="label_43">
           <property name="text">
            <string>Mem Free</string>
           </property>
          </widget>
         </item>
         <item row="3" column="1">
          <widget class="QLineEdit" name="lineEdit_memfree">
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item row="3" column="2">
          <widget class="QLabel" name="label_51">
           <property name="text">
            <string>MB</string>
           </property>
          </widget>
         </item>
         <item row="3" column="3">
          <widget class="QLabel" name="label_50">
           <property name="text">
            <string>Mem Total</string>
           </property>
          </widget>
         </item>
         <item row="3" column="4">
          <widget class="QLineEdit" name="lineEdit_memtotal">
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item row="3" column="5">
          <widget class="QLabel" name="label_52">
           <property name="text">
            <string>MB</string>
           </property>
          </widget>
         </item>
         <item row="4" column="0">
          <widget class="QLabel" name="label_46">
           <property name="text">
            <string>Volume</string>
           </property>
          </widget>
         </item>
         <item row="4" column="4">
          <widget class="QLineEdit" name="lineEdit">
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item row="4" column="5">
          <widget class="QLabel" name="label_48">
           <property name="text">
            <string>%</string>
           </property>
          </widget>
         </item>
         <item row="4" column="1" colspan="3">
          <widget class="ClickSlider" name="horizontalSlider">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>25</height>
            </size>
           </property>
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="tab_2">
        <attribute name="title">
         <string>Hardware</string>
        </attribute>
        <layout class="QGridLayout" name="gridLayout_4">
         <item row="0" column="0">
          <layout class="QVBoxLayout" name="verticalLayout_2">
           <item>
            <spacer name="verticalSpacer_6">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Fixed</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QLabel" name="label_36">
             <property name="text">
              <string>System State 0 :</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="sysState_0">
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="verticalSpacer_7">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QLabel" name="label_35">
             <property name="text">
              <string>System State 1 :</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="sysState_1">
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="verticalSpacer_8">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QLabel" name="label_34">
             <property name="text">
              <string>System State 2 :</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="sysState_2">
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="verticalSpacer_5">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>328</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
         <item row="0" column="1">
          <layout class="QGridLayout" name="gridLayout">
           <item row="8" column="1">
            <widget class="QLineEdit" name="power_temp_r">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_2">
             <property name="text">
              <string>A-55V:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="4" column="0">
            <widget class="QLabel" name="label_4">
             <property name="text">
              <string>D+12V:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="5" column="1">
            <widget class="QLineEdit" name="a_dec_5_r">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="3" column="1">
            <widget class="QLineEdit" name="emitVoltage_r">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="7" column="1">
            <widget class="QLineEdit" name="battery_r">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="5" column="0">
            <widget class="QLabel" name="label_5">
             <property name="text">
              <string>A-5V:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="10" column="0">
            <widget class="QLabel" name="label_13">
             <property name="text">
              <string>5805 Temp.:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="10" column="1">
            <widget class="QLineEdit" name="five805_temp_r">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="0" column="0" colspan="2">
            <widget class="QLabel" name="label_10">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>20</height>
              </size>
             </property>
             <property name="font">
              <font>
               <pointsize>15</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="text">
              <string>Real Time</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="7" column="0">
            <widget class="QLabel" name="label_7">
             <property name="text">
              <string>Battery:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="3" column="0">
            <widget class="QLabel" name="label_3">
             <property name="text">
              <string>HVTx:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="9" column="1">
            <widget class="QLineEdit" name="fpga_temp_r">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="6" column="1">
            <widget class="QLineEdit" name="a_plus_5_r">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="4" column="1">
            <widget class="QLineEdit" name="d_plus_12_r">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="a_plus_128_r">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="6" column="0">
            <widget class="QLabel" name="label_6">
             <property name="text">
              <string>A+5V:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>A+128V: </string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QLineEdit" name="a_dec_55_r">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="8" column="0">
            <widget class="QLabel" name="label_8">
             <property name="text">
              <string>PB Temp.:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="9" column="0">
            <widget class="QLabel" name="label_9">
             <property name="text">
              <string>FPGA Temp.:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="0" column="2">
          <layout class="QGridLayout" name="gridLayout_2">
           <item row="0" column="0" colspan="2">
            <widget class="QLabel" name="label_11">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>20</height>
              </size>
             </property>
             <property name="font">
              <font>
               <pointsize>15</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="text">
              <string>Max</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_31">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>A+128V: </string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="a_plus_128_max">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_27">
             <property name="text">
              <string>A-55V:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QLineEdit" name="a_dec_55_max">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="3" column="0">
            <widget class="QLabel" name="label_25">
             <property name="text">
              <string>HVTx:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="3" column="1">
            <widget class="QLineEdit" name="emitVoltage_max">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="4" column="0">
            <widget class="QLabel" name="label_28">
             <property name="text">
              <string>D+12V:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="4" column="1">
            <widget class="QLineEdit" name="d_plus_12_max">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="5" column="0">
            <widget class="QLabel" name="label_29">
             <property name="text">
              <string>A-5V:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="5" column="1">
            <widget class="QLineEdit" name="a_dec_5_max">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="6" column="0">
            <widget class="QLabel" name="label_32">
             <property name="text">
              <string>A+5V:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="6" column="1">
            <widget class="QLineEdit" name="a_plus_5_max">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="7" column="0">
            <widget class="QLabel" name="label_24">
             <property name="text">
              <string>Battery:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="7" column="1">
            <widget class="QLineEdit" name="battery_max">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="8" column="0">
            <widget class="QLabel" name="label_26">
             <property name="text">
              <string>PB Temp.:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="8" column="1">
            <widget class="QLineEdit" name="power_temp_max">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="9" column="0">
            <widget class="QLabel" name="label_30">
             <property name="text">
              <string>FPGA Temp.:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="9" column="1">
            <widget class="QLineEdit" name="fpga_temp_max">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="10" column="0">
            <widget class="QLabel" name="label_33">
             <property name="text">
              <string>5805 Temp.:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="10" column="1">
            <widget class="QLineEdit" name="five805_temp_max">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="0" column="3">
          <layout class="QGridLayout" name="gridLayout_3">
           <item row="0" column="0" colspan="2">
            <widget class="QLabel" name="label_12">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>20</height>
              </size>
             </property>
             <property name="font">
              <font>
               <pointsize>15</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="text">
              <string>Min</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_21">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>A+128V: </string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="a_plus_128_min">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_17">
             <property name="text">
              <string>A-55V:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QLineEdit" name="a_dec_55_min">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="3" column="0">
            <widget class="QLabel" name="label_15">
             <property name="text">
              <string>HVTx:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="3" column="1">
            <widget class="QLineEdit" name="emitVoltage_min">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="4" column="0">
            <widget class="QLabel" name="label_18">
             <property name="text">
              <string>D+12V:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="4" column="1">
            <widget class="QLineEdit" name="d_plus_12_min">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="5" column="0">
            <widget class="QLabel" name="label_19">
             <property name="text">
              <string>A-5V:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="5" column="1">
            <widget class="QLineEdit" name="a_dec_5_min">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="6" column="0">
            <widget class="QLabel" name="label_22">
             <property name="text">
              <string>A+5V:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="6" column="1">
            <widget class="QLineEdit" name="a_plus_5_min">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="7" column="0">
            <widget class="QLabel" name="label_14">
             <property name="text">
              <string>Battery:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="7" column="1">
            <widget class="QLineEdit" name="battery_min">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="8" column="0">
            <widget class="QLabel" name="label_16">
             <property name="text">
              <string>PB Temp.:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="8" column="1">
            <widget class="QLineEdit" name="power_temp_min">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="9" column="0">
            <widget class="QLabel" name="label_20">
             <property name="text">
              <string>FPGA Temp.:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="9" column="1">
            <widget class="QLineEdit" name="fpga_temp_min">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="10" column="0">
            <widget class="QLabel" name="label_23">
             <property name="text">
              <string>5805 Temp.:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="10" column="1">
            <widget class="QLineEdit" name="five805_temp_min">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="0" column="4">
          <layout class="QVBoxLayout" name="verticalLayout">
           <item>
            <spacer name="verticalSpacer_2">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Fixed</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>17</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>30</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>80</width>
               <height>30</height>
              </size>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="text">
              <string>Pause</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="verticalSpacer_3">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Fixed</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="accelButton">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>30</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>80</width>
               <height>30</height>
              </size>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="text">
              <string>+</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="verticalSpacer_4">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Fixed</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="decelButton">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>30</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>80</width>
               <height>30</height>
              </size>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="text">
              <string>--</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="verticalSpacer">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Fixed</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>275</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
         <item row="1" column="0" colspan="5">
          <widget class="QTextEdit" name="textEditRawData"/>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="tab_3">
        <attribute name="title">
         <string>Information</string>
        </attribute>
        <layout class="QGridLayout" name="gridLayout_6">
         <item row="0" column="0">
          <widget class="QLabel" name="label_45">
           <property name="text">
            <string>Probe EC version</string>
           </property>
          </widget>
         </item>
         <item row="1" column="2">
          <widget class="QLineEdit" name="lineEdit_USB_version">
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item row="2" column="2">
          <spacer name="verticalSpacer_9">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Fixed</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>400</height>
            </size>
           </property>
          </spacer>
         </item>
         <item row="1" column="3">
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item row="0" column="2">
          <widget class="QLineEdit" name="lineEdit_EC_version">
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item row="0" column="3">
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="label_42">
           <property name="text">
            <string>USB3.0 version</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="tab_4">
        <attribute name="title">
         <string>FanSpeed</string>
        </attribute>
        <layout class="QGridLayout" name="gridLayout_fanspeed_1">
         <item row="0" column="2">
          <layout class="QGridLayout" name="gridLayout_fanspeed_4">
           <item row="0" column="0" colspan="2">
            <widget class="QLabel" name="label">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>20</height>
              </size>
             </property>
             <property name="font">
              <font>
               <pointsize>15</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="text">
              <string>Bottom FAN</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_bottomfan_1">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Bot FAN1: </string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="value_bottomfan_1">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_bottomfan_2">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Bot FAN2: </string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QLineEdit" name="value_bottomfan_2">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="3" column="0">
            <widget class="QLabel" name="label_bottomfan_3">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Bot FAN3: </string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="3" column="1">
            <widget class="QLineEdit" name="value_bottomfan_3">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="4" column="0">
            <widget class="QLabel" name="label_bottomfan_4">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Bot FAN4: </string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="4" column="1">
            <widget class="QLineEdit" name="value_bottomfan_4">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="5" column="0">
            <widget class="QLabel" name="label_bottomfan_5">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Bot FAN5: </string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="5" column="1">
            <widget class="QLineEdit" name="value_bottomfan_5">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="6" column="0">
            <widget class="QLabel" name="label_bottomfan_6">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Bot FAN6: </string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="6" column="1">
            <widget class="QLineEdit" name="value_bottomfan_6">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="7" column="0">
            <widget class="QLabel" name="label_bottomfan_7">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Bot FAN7: </string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="7" column="1">
            <widget class="QLineEdit" name="value_bottomfan_7">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="8" column="0">
            <widget class="QLabel" name="label_bottomfan_8">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Bot FAN8: </string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="8" column="1">
            <widget class="QLineEdit" name="value_bottomfan_8">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="9" column="0">
            <widget class="QLabel" name="label_bottomfan_9">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Bot FAN9: </string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="9" column="1">
            <widget class="QLineEdit" name="value_bottomfan_9">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="0" column="1">
          <layout class="QGridLayout" name="gridLayout_fanspeed_3">
           <item row="0" column="0" colspan="2">
            <widget class="QLabel" name="label">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>20</height>
              </size>
             </property>
             <property name="font">
              <font>
               <pointsize>15</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="text">
              <string>ADP FAN</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_adpfan_1">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>ADP FAN1: </string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="value_adpfan_1">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_adpfan_2">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>ADP FAN2: </string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QLineEdit" name="value_adpfan_2">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="3" column="0">
            <widget class="QLabel" name="label_adpfan_3">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>ADP FAN3: </string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="3" column="1">
            <widget class="QLineEdit" name="value_adpfan_3">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item row="0" column="0">
          <layout class="QGridLayout" name="gridLayout_fanspeed_2">
           <item row="3" column="1">
            <widget class="QLineEdit" name="value_rearfan_3">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="4" column="1">
            <widget class="QLineEdit" name="value_rearfan_4">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="value_rearfan_1">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="0" column="0" colspan="2">
            <widget class="QLabel" name="label">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>20</height>
              </size>
             </property>
             <property name="font">
              <font>
               <pointsize>15</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="text">
              <string>Rear FAN</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_rearfan_2">
             <property name="text">
              <string>FAN2:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QLineEdit" name="value_rearfan_2">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>100</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_rearfan_1">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>FAN1: </string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="4" column="0">
            <widget class="QLabel" name="label_rearfan_4">
             <property name="text">
              <string>FAN4:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="3" column="0">
            <widget class="QLabel" name="label_rearfan_3">
             <property name="text">
              <string>FAN3:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="tab_5">
        <attribute name="title">
         <string>FanRelatedInfo</string>
        </attribute>
        <layout class="QGridLayout" name="gridLayout_11">
         <item row="8" column="7">
          <widget class="QLineEdit" name="lineEdit_N12V_FB"/>
         </item>
         <item row="6" column="5">
          <widget class="QLineEdit" name="lineEdit_AP5V6_FB"/>
         </item>
         <item row="5" column="5">
          <widget class="QLineEdit" name="lineEdit_AP3V6_IOUT"/>
         </item>
         <item row="3" column="1">
          <widget class="QLineEdit" name="lineEdit_BFH_VCCINT_0V95_IOUT"/>
         </item>
         <item row="7" column="0">
          <widget class="QLabel" name="label_MGTAVCC_1V_IOUT">
           <property name="text">
            <string>MGTAVCC_1V_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QLabel" name="label_DVDD_2V5">
           <property name="text">
            <string>DVDD_2V5</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QLineEdit" name="lineEdit_BFL_VCCINT_0V95_IOUT"/>
         </item>
         <item row="14" column="1">
          <widget class="QLineEdit" name="lineEdit_VCCAUX_1V8"/>
         </item>
         <item row="9" column="1">
          <widget class="QLineEdit" name="lineEdit_MGTAVTT_1V2_IOUT"/>
         </item>
         <item row="8" column="4">
          <widget class="QLabel" name="label_AN5V6_FB">
           <property name="text">
            <string>AN5V6_FB</string>
           </property>
          </widget>
         </item>
         <item row="4" column="0">
          <widget class="QLabel" name="label_DSP_VCCINT_0V95">
           <property name="text">
            <string>DSP_VCCINT_0V95</string>
           </property>
          </widget>
         </item>
         <item row="3" column="4">
          <widget class="QLabel" name="label_AP2V3_IOUT">
           <property name="text">
            <string>AP2V3_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="5" column="6">
          <widget class="QLabel" name="label_HVPB_IOUT">
           <property name="text">
            <string>HVPB_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="11" column="6">
          <widget class="QLabel" name="label_12VIN_ALL_IOUT">
           <property name="text">
            <string>12VIN_ALL_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="2" column="4">
          <widget class="QLabel" name="label_AP2V3_FB">
           <property name="text">
            <string>AP2V3_FB</string>
           </property>
          </widget>
         </item>
         <item row="5" column="0">
          <widget class="QLabel" name="label_DSP_VCCINT_0V95_IOUT">
           <property name="text">
            <string>DSP_VCCINT_0V95_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="0" column="4">
          <widget class="QLabel" name="label_AP1V6_FB">
           <property name="text">
            <string>AP1V6_FB</string>
           </property>
          </widget>
         </item>
         <item row="3" column="6">
          <widget class="QLabel" name="label_HVNA_IOUT">
           <property name="text">
            <string>HVNA_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="11" column="5">
          <widget class="QLineEdit" name="lineEdit_AP13V5_IOUT"/>
         </item>
         <item row="10" column="6">
          <widget class="QLabel" name="label_12V_IN_FB">
           <property name="text">
            <string>12V_IN_FB</string>
           </property>
          </widget>
         </item>
         <item row="12" column="6">
          <widget class="QLabel" name="label_12VIN_MAIN_IOUT">
           <property name="text">
            <string>12VIN_MAIN_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="8" column="5">
          <widget class="QLineEdit" name="lineEdit_AN5V6_FB"/>
         </item>
         <item row="9" column="0">
          <widget class="QLabel" name="label_MGTAVTT_1V2_IOUT">
           <property name="text">
            <string>MGTAVTT_1V2_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="14" column="7">
          <widget class="QLineEdit" name="lineEdit_CWLD0_IN_FB"/>
         </item>
         <item row="7" column="1">
          <widget class="QLineEdit" name="lineEdit_MGTAVCC_1V_IOUT"/>
         </item>
         <item row="10" column="1">
          <widget class="QLineEdit" name="lineEdit_VCC0_1V8"/>
         </item>
         <item row="6" column="0">
          <widget class="QLabel" name="label_MGTAVCC_1V">
           <property name="text">
            <string>MGTAVCC_1V</string>
           </property>
          </widget>
         </item>
         <item row="1" column="6">
          <widget class="QLabel" name="label_HVPA_IOUT">
           <property name="text">
            <string>HVPA_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="5" column="1">
          <widget class="QLineEdit" name="lineEdit_DSP_VCCINT_0V95_IOUT"/>
         </item>
         <item row="8" column="0">
          <widget class="QLabel" name="label_MGTAVTT_1V2">
           <property name="text">
            <string>MGTAVTT_1V2</string>
           </property>
          </widget>
         </item>
         <item row="13" column="6">
          <widget class="QLabel" name="label_MCW1_FB">
           <property name="text">
            <string>MCW1_FB</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLineEdit" name="lineEdit_BFH_VCCINT_0V95"/>
         </item>
         <item row="12" column="0">
          <widget class="QLabel" name="label_VCC0_1V2">
           <property name="text">
            <string>VCC0_1V2</string>
           </property>
          </widget>
         </item>
         <item row="6" column="4">
          <widget class="QLabel" name="label_AP5V6_FB">
           <property name="text">
            <string>AP5V6_FB</string>
           </property>
          </widget>
         </item>
         <item row="11" column="4">
          <widget class="QLabel" name="label_AP13V5_IOUT">
           <property name="text">
            <string>AP13V5_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="2" column="5">
          <widget class="QLineEdit" name="lineEdit_AP2V3_FB"/>
         </item>
         <item row="13" column="4">
          <widget class="QLabel" name="label_AN13V5_FB">
           <property name="text">
            <string>AN13V5_FB</string>
           </property>
          </widget>
         </item>
         <item row="1" column="5">
          <widget class="QLineEdit" name="lineEdit_AP1V6_IOUT"/>
         </item>
         <item row="10" column="4">
          <widget class="QLabel" name="label_AP13V5_FB">
           <property name="text">
            <string>AP13V5_FB</string>
           </property>
          </widget>
         </item>
         <item row="13" column="7">
          <widget class="QLineEdit" name="lineEdit_MCW1_FB"/>
         </item>
         <item row="4" column="1">
          <widget class="QLineEdit" name="lineEdit_DSP_VCCINT_0V95"/>
         </item>
         <item row="11" column="0">
          <widget class="QLabel" name="label_VCC0_1V8_IOUT">
           <property name="text">
            <string>VCC0_1V8_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="label_BFL_VCCINT_0V95_IOUT">
           <property name="text">
            <string>BFL_VCCINT_0V95_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="12" column="4">
          <widget class="QLabel" name="label_AP3V3_FB">
           <property name="text">
            <string>AP3V3_FB</string>
           </property>
          </widget>
         </item>
         <item row="13" column="5">
          <widget class="QLineEdit" name="lineEdit_AN13V5_FB"/>
         </item>
         <item row="7" column="5">
          <widget class="QLineEdit" name="lineEdit_AP5V6_IOUT"/>
         </item>
         <item row="6" column="6">
          <widget class="QLabel" name="label_HVNB_FB">
           <property name="text">
            <string>HVNB_FB</string>
           </property>
          </widget>
         </item>
         <item row="0" column="7">
          <widget class="QLineEdit" name="lineEdit_HVPA_FB"/>
         </item>
         <item row="9" column="7">
          <widget class="QLineEdit" name="lineEdit_N12V_IOUT"/>
         </item>
         <item row="6" column="1">
          <widget class="QLineEdit" name="lineEdit_MGTAVCC_1V"/>
         </item>
         <item row="12" column="1">
          <widget class="QLineEdit" name="lineEdit_VCC0_1V2"/>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="label_BFH_VCCINT_0V95">
           <property name="text">
            <string>BFH_VCCINT_0V95</string>
           </property>
          </widget>
         </item>
         <item row="0" column="3">
          <widget class="QLineEdit" name="lineEdit_DVDD_2V5"/>
         </item>
         <item row="2" column="2">
          <widget class="QLabel" name="label_MGTVCCAUX_1V8">
           <property name="text">
            <string>MGTVCCAUX_1V8</string>
           </property>
          </widget>
         </item>
         <item row="7" column="4">
          <widget class="QLabel" name="label_AP5V6_IOUT">
           <property name="text">
            <string>AP5V6_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="7" column="7">
          <widget class="QLineEdit" name="lineEdit_HVNB_IOUT"/>
         </item>
         <item row="1" column="3">
          <widget class="QLineEdit" name="lineEdit_DVDD_2V5_IOUT"/>
         </item>
         <item row="10" column="0">
          <widget class="QLabel" name="label_VCC0_1V8">
           <property name="text">
            <string>VCC0_1V8</string>
           </property>
          </widget>
         </item>
         <item row="12" column="5">
          <widget class="QLineEdit" name="lineEdit_AP3V3_FB"/>
         </item>
         <item row="9" column="6">
          <widget class="QLabel" name="label_N12V_IOUT">
           <property name="text">
            <string>N12V_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="1" column="2">
          <widget class="QLabel" name="label_DVDD_2V5_IOUT">
           <property name="text">
            <string>DVDD_2V5_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="10" column="5">
          <widget class="QLineEdit" name="lineEdit_AP13V5_FB"/>
         </item>
         <item row="14" column="6">
          <widget class="QLabel" name="label_CWLD0_IN_FB">
           <property name="text">
            <string>CWLD0_IN_FB</string>
           </property>
          </widget>
         </item>
         <item row="11" column="1">
          <widget class="QLineEdit" name="lineEdit_VCC0_1V8_IOUT"/>
         </item>
         <item row="2" column="3">
          <widget class="QLineEdit" name="lineEdit_MGTVCCAUX_1V8"/>
         </item>
         <item row="8" column="6">
          <widget class="QLabel" name="label_N12V_FB">
           <property name="text">
            <string>N12V_FB</string>
           </property>
          </widget>
         </item>
         <item row="10" column="7">
          <widget class="QLineEdit" name="lineEdit_12V_IN_FB"/>
         </item>
         <item row="9" column="4">
          <widget class="QLabel" name="label_AN5V6_IOUT">
           <property name="text">
            <string>AN5V6_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="14" column="0">
          <widget class="QLabel" name="label_VCCAUX_1V8">
           <property name="text">
            <string>VCCAUX_1V8</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QLineEdit" name="lineEdit_BFL_VCCINT_0V95"/>
         </item>
         <item row="1" column="7">
          <widget class="QLineEdit" name="lineEdit_HVPA_IOUT"/>
         </item>
         <item row="0" column="6">
          <widget class="QLabel" name="label_HVPA_FB">
           <property name="text">
            <string>HVPA_FB</string>
           </property>
          </widget>
         </item>
         <item row="3" column="2">
          <widget class="QLabel" name="label_MGTVCCAUX_1V8_IOUT">
           <property name="text">
            <string>MGTVCCAUX_1V8_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="5" column="4">
          <widget class="QLabel" name="label_AP3V6_IOUT">
           <property name="text">
            <string>AP3V6_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="0" column="5">
          <widget class="QLineEdit" name="lineEdit_AP1V6_FB"/>
         </item>
         <item row="4" column="4">
          <widget class="QLabel" name="label_AP3V6_FB">
           <property name="text">
            <string>AP3V6_FB</string>
           </property>
          </widget>
         </item>
         <item row="4" column="5">
          <widget class="QLineEdit" name="lineEdit_AP3V6_FB"/>
         </item>
         <item row="5" column="7">
          <widget class="QLineEdit" name="lineEdit_HVPB_IOUT"/>
         </item>
         <item row="4" column="6">
          <widget class="QLabel" name="label_HVPB_FB">
           <property name="text">
            <string>HVPB_FB</string>
           </property>
          </widget>
         </item>
         <item row="3" column="7">
          <widget class="QLineEdit" name="lineEdit_HVNA_IOUT"/>
         </item>
         <item row="2" column="7">
          <widget class="QLineEdit" name="lineEdit_HVNA_FB"/>
         </item>
         <item row="6" column="7">
          <widget class="QLineEdit" name="lineEdit_HVNB_FB"/>
         </item>
         <item row="1" column="4">
          <widget class="QLabel" name="label_AP1V6_IOUT">
           <property name="text">
            <string>AP1V6_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="4" column="7">
          <widget class="QLineEdit" name="lineEdit_HVPB_FB"/>
         </item>
         <item row="3" column="3">
          <widget class="QLineEdit" name="lineEdit_MGTVCCAUX_1V8_IOUT"/>
         </item>
         <item row="8" column="1">
          <widget class="QLineEdit" name="lineEdit_MGTAVTT_1V2"/>
         </item>
         <item row="12" column="7">
          <widget class="QLineEdit" name="lineEdit_12VIN_MAIN_IOUT"/>
         </item>
         <item row="2" column="6">
          <widget class="QLabel" name="label_HVNA_FB">
           <property name="text">
            <string>HVNA_FB</string>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QLabel" name="label_BFH_VCCINT_0V95_IOUT">
           <property name="text">
            <string>BFH_VCCINT_0V95_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="9" column="5">
          <widget class="QLineEdit" name="lineEdit_AN5V6_IOUT"/>
         </item>
         <item row="7" column="6">
          <widget class="QLabel" name="label_HVNB_IOUT">
           <property name="text">
            <string>HVNB_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="3" column="5">
          <widget class="QLineEdit" name="lineEdit_AP2V3_IOUT"/>
         </item>
         <item row="13" column="1">
          <widget class="QLineEdit" name="lineEdit_VCC0_1V2_IOUT"/>
         </item>
         <item row="11" column="7">
          <widget class="QLineEdit" name="lineEdit_12VIN_ALL_IOUT"/>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="label_BFL_VCCINT_0V95">
           <property name="text">
            <string>BFL_VCCINT_0V95</string>
           </property>
          </widget>
         </item>
         <item row="13" column="0">
          <widget class="QLabel" name="label_VCC0_1V2_IOUT">
           <property name="text">
            <string>VCC0_1V2_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="15" column="0">
          <widget class="QLabel" name="label_VCCAUX_1V8_IOUT">
           <property name="text">
            <string>VCCAUX_1V8_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="15" column="1">
          <widget class="QLineEdit" name="lineEdit_VCCAUX_1V8_IOUT"/>
         </item>
         <item row="4" column="2">
          <widget class="QLabel" name="label_DVDD_3V3">
           <property name="text">
            <string>DVDD_3V3</string>
           </property>
          </widget>
         </item>
         <item row="4" column="3">
          <widget class="QLineEdit" name="lineEdit_DVDD_3V3"/>
         </item>
         <item row="5" column="2">
          <widget class="QLabel" name="label_VDD_3V3_FB">
           <property name="text">
            <string>VDD_3V3_FB</string>
           </property>
          </widget>
         </item>
         <item row="5" column="3">
          <widget class="QLineEdit" name="lineEdit_VDD_3V3_FB"/>
         </item>
         <item row="6" column="2">
          <widget class="QLabel" name="label_VDD_3V3_IOUT">
           <property name="text">
            <string>VDD_3V3_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="6" column="3">
          <widget class="QLineEdit" name="lineEdit_VDD_3V3_IOUT"/>
         </item>
         <item row="7" column="2">
          <widget class="QLabel" name="label_VDD_5V_FB">
           <property name="text">
            <string>VDD_5V_FB</string>
           </property>
          </widget>
         </item>
         <item row="7" column="3">
          <widget class="QLineEdit" name="lineEdit_VDD_5V_FB"/>
         </item>
         <item row="8" column="2">
          <widget class="QLabel" name="label_VDD5V_IOUT">
           <property name="text">
            <string>VDD5V_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="8" column="3">
          <widget class="QLineEdit" name="lineEdit_VDD5V_IOUT"/>
         </item>
         <item row="9" column="2">
          <widget class="QLabel" name="label_VDD5V_PRB_FB">
           <property name="text">
            <string>VDD5V_PRB_FB</string>
           </property>
          </widget>
         </item>
         <item row="9" column="3">
          <widget class="QLineEdit" name="lineEdit_VDD5V_PRB_FB"/>
         </item>
         <item row="10" column="2">
          <widget class="QLabel" name="label_5V_PRB_IOUT">
           <property name="text">
            <string>5V_PRB_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="10" column="3">
          <widget class="QLineEdit" name="lineEdit_5V_PRB_IOUT"/>
         </item>
         <item row="11" column="2">
          <widget class="QLabel" name="label_VCC0_3V3_IOUT">
           <property name="text">
            <string>VCC0_3V3_IOUT</string>
           </property>
          </widget>
         </item>
         <item row="11" column="3">
          <widget class="QLineEdit" name="lineEdit_VCC0_3V3_IOUT"/>
         </item>
         <item row="12" column="2">
          <widget class="QLabel" name="label_RESV">
           <property name="text">
            <string>RESV</string>
           </property>
          </widget>
         </item>
         <item row="12" column="3">
          <widget class="QLineEdit" name="lineEdit_RESV"/>
         </item>
         <item row="13" column="2">
          <widget class="QLabel" name="label_RESV2">
           <property name="text">
            <string>RESV2</string>
           </property>
          </widget>
         </item>
         <item row="13" column="3">
          <widget class="QLineEdit" name="lineEdit_RESV2"/>
         </item>
         <item row="14" column="2">
          <widget class="QLabel" name="label_RESV3">
           <property name="text">
            <string>RESV3</string>
           </property>
          </widget>
         </item>
         <item row="14" column="3">
          <widget class="QLineEdit" name="lineEdit_RESV3"/>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="tab_6">
        <attribute name="title">
         <string>FanRelatedInfo2</string>
        </attribute>
        <layout class="QGridLayout" name="gridLayout_6">
         <item row="2" column="0">
          <widget class="QLabel" name="label_BFL_5589_TEMP">
           <property name="text">
            <string>BFL_5589_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="6" column="1" colspan="3">
          <widget class="QLineEdit" name="lineEdit_HV2_DCDC_TEMP"/>
         </item>
         <item row="5" column="4">
          <widget class="QLabel" name="label_VDD3V3_PWR_TEMP">
           <property name="text">
            <string>VDD3V3_PWR_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="6" column="6">
          <widget class="QLineEdit" name="lineEdit_VDD5V_PWR_TEMP"/>
         </item>
         <item row="4" column="0">
          <widget class="QLabel" name="label_BFL_FPGA_TEMP">
           <property name="text">
            <string>BFL_FPGA_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="6" column="0">
          <widget class="QLabel" name="label_HV2_DCDC_TEMP">
           <property name="text">
            <string>HV2_DCDC_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1" colspan="3">
          <widget class="QLineEdit" name="lineEdit_BFL_AFE_TEMP"/>
         </item>
         <item row="5" column="6">
          <widget class="QLineEdit" name="lineEdit_VDD3V3_PWR_TEMP"/>
         </item>
         <item row="4" column="1" colspan="3">
          <widget class="QLineEdit" name="lineEdit_BFL_FPGA_TEMP"/>
         </item>
         <item row="0" column="1" colspan="3">
          <widget class="QLineEdit" name="lineEdit_DSP_FPGA_TEMP"/>
         </item>
         <item row="6" column="4">
          <widget class="QLabel" name="label_VDD5V_PWR_TEMP">
           <property name="text">
            <string>VDD5V_PWR_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="5" column="0">
          <widget class="QLabel" name="label_HV1_DCDC_TEMP">
           <property name="text">
            <string>HV1_DCDC_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="5" column="1" colspan="3">
          <widget class="QLineEdit" name="lineEdit_HV1_DCDC_TEMP"/>
         </item>
         <item row="2" column="1" colspan="3">
          <widget class="QLineEdit" name="lineEdit_BFL_5589_TEMP"/>
         </item>
         <item row="3" column="1" colspan="3">
          <widget class="QLineEdit" name="lineEdit_BFL_VCCINT_TEMP"/>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="label_BFL_AFE_TEMP">
           <property name="text">
            <string>BFL_AFE_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="label_DSP_FPGA_TEMP">
           <property name="text">
            <string>DSP_FPGA_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QLabel" name="label_BFL_VCCINT_TEMP">
           <property name="text">
            <string>BFL_VCCINT_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="1" column="4">
          <widget class="QLabel" name="label_AP2V3_PWR_TEMP">
           <property name="text">
            <string>AP2V3_PWR_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="1" column="6">
          <widget class="QLineEdit" name="lineEdit_AP2V3_PWR_TEMP"/>
         </item>
         <item row="0" column="4">
          <widget class="QLabel" name="label_AP1V6_DCDC_TEMP">
           <property name="text">
            <string>AP1V6_DCDC_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="0" column="6">
          <widget class="QLineEdit" name="lineEdit_AP1V6_DCDC_TEMP"/>
         </item>
         <item row="2" column="4">
          <widget class="QLabel" name="label_AP3V6_PWR_TEMP">
           <property name="text">
            <string>AP3V6_PWR_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="2" column="6">
          <widget class="QLineEdit" name="lineEdit_AP3V6_PWR_TEMP"/>
         </item>
         <item row="4" column="4">
          <widget class="QLabel" name="label_AP13V5_PWR_TEMP">
           <property name="text">
            <string>AP13V5_PWR_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="4" column="6">
          <widget class="QLineEdit" name="lineEdit_AP13V5_PWR_TEMP"/>
         </item>
         <item row="3" column="4">
          <widget class="QLabel" name="label_AP5V6_PWR_TEMP">
           <property name="text">
            <string>AP5V6_PWR_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="3" column="6">
          <widget class="QLineEdit" name="lineEdit_AP5V6_PWR_TEMP"/>
         </item>
         <item row="7" column="4">
          <widget class="QLabel" name="label_CW_DCDC_TEMP">
           <property name="text">
            <string>CW_DCDC_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="7" column="6">
          <widget class="QLineEdit" name="lineEdit_CW_DCDC_TEMP"/>
         </item>
         <item row="8" column="4">
          <widget class="QLabel" name="label_CW_LD0_TEMP">
           <property name="text">
            <string>CW_LD0_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="8" column="6">
          <widget class="QLineEdit" name="lineEdit_CW_LD0_TEMP"/>
         </item>
         <item row="10" column="4">
          <widget class="QLabel" name="label_DN12V_PWR_TEMP">
           <property name="text">
            <string>DN12V_PWR_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="10" column="6">
          <widget class="QLineEdit" name="lineEdit_DN12V_PWR_TEMP"/>
         </item>
         <item row="7" column="0">
          <widget class="QLabel" name="label_PWR_MIDTOP_TEMP">
           <property name="text">
            <string>PWR_MIDTOP_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="7" column="2">
          <widget class="QLineEdit" name="lineEdit_PWR_MIDTOP_TEMP"/>
         </item>
         <item row="8" column="0">
          <widget class="QLabel" name="label_PWR_MID_TEMP">
           <property name="text">
            <string>PWR_MID_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="8" column="2">
          <widget class="QLineEdit" name="lineEdit_PWR_MID_TEMP"/>
         </item>
         <item row="10" column="0">
          <widget class="QLabel" name="label_PWR_BOT_TEMP">
           <property name="text">
            <string>PWR_BOT_TEMP</string>
           </property>
          </widget>
         </item>
         <item row="10" column="2">
          <widget class="QLineEdit" name="lineEdit_PWR_BOT_TEMP"/>
         </item>
        </layout>
       </widget>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ClickSlider</class>
   <extends>QSlider</extends>
   <header>clickslider.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
