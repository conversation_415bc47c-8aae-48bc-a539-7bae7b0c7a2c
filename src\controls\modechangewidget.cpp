#include "modechangewidget.h"
#include "ui_modechangewidget.h"
#include "util.h"
#include <QTimer>
#include "istatemanager.h"
#include "stateeventnames.h"

ModeChangeWidget::ModeChangeWidget(QWidget* parent)
    : Q<PERSON><PERSON>e(parent)
    , ui(new Ui::ModeChangeWidget)
    , m_model(NULL)
    , m_flashButton(NULL)
    , m_isUpArrowEnabled(false)
    , m_isDownArrowEnabled(false)
    , m_isLRArrowEnabled(false)
    , m_isFlashUpArrow(false)
    , m_isFlashDownArrow(false)
    , m_isFlashLRArrow(false)
    , m_StateManager(NULL)
{
    ui->setupUi(this);
}

ModeChangeWidget::~ModeChangeWidget()
{
    delete ui;
}

void ModeChangeWidget::setModel(ModeChangeModel* value)
{
    m_model = value;
    if (m_model != NULL)
    {
        connect(m_model, SIGNAL(modeShowChanged()), this, SLOT(onModeShowChanged()));
        connect(m_model, SIGNAL(flashArrow(int)), this, SLOT(onFlashArrow(int)));
    }
}

void ModeChangeWidget::setStateManager(IStateManager* value)
{
    m_StateManager = value;
}

void ModeChangeWidget::onSetLRArrowState()
{
    bool arrowState = m_model->arrowState();

    m_isLRArrowEnabled = arrowState;
    QIcon left_icon, right_icon;
    arrowStateIcon(left_icon, ModeChangeModel::Arrow_Left, m_isLRArrowEnabled);
    arrowStateIcon(right_icon, ModeChangeModel::Arrow_Right, m_isLRArrowEnabled);
    ;
    ui->toolBtn_Left->setIcon(left_icon);
    ui->toolBtn_Right->setIcon(right_icon);

    Util::processEvents(QEventLoop::ExcludeUserInputEvents);
}

void ModeChangeWidget::onModeShowChanged()
{
    if (m_model->modeShow().count() == 0)
    {
        ui->label_preMode->setText("");
        ui->label_nextMode->setText("");
        ui->label_curMode->setText("");
    }
    else if (m_model->modeShow().count() == 1)
    {
        ui->label_preMode->setText("");
        ui->label_nextMode->setText("");
        ui->label_curMode->setText(m_model->modeShow().first());
    }
    else
    {
        ui->label_preMode->setText(m_model->modeShow().first());
        ui->label_nextMode->setText(m_model->modeShow().last());
        ui->label_curMode->setText(m_model->modeShow().at(1));
    }
    Util::processEvents(QEventLoop::ExcludeUserInputEvents);
    if (m_isFlashLRArrow)
    {
        QTimer::singleShot(500, this, SLOT(onSetLRArrowState()));
        m_isFlashLRArrow = false;
    }
    else
    {
        onSetLRArrowState();
    }
}

void ModeChangeWidget::onFlashArrow(int dir)
{
    //直接使用m_pressIcon.addFile会有时出现使用的上次的icon
    QIcon icon;
    switch (dir)
    {
    case ModeChangeModel::Arrow_Up:
        if (m_isUpArrowEnabled)
        {
            m_isFlashUpArrow = true;
            icon.addFile(QString::fromUtf8(":/images/up_press.png"), QSize(), QIcon::Normal, QIcon::Off);
            m_pressIcon = icon;
            flashArrow(ui->toolBtn_Up);
        }
        break;
    case ModeChangeModel::Arrow_Down:
        if (m_isDownArrowEnabled)
        {
            m_isFlashDownArrow = true;
            icon.addFile(QString::fromUtf8(":/images/down_press.png"), QSize(), QIcon::Normal, QIcon::Off);
            m_pressIcon = icon;
            flashArrow(ui->toolBtn_Down);
        }
        break;
    case ModeChangeModel::Arrow_Left:
        m_isFlashLRArrow = true;
        icon.addFile(QString::fromUtf8(":/images/left_scroll_press.png"), QSize(), QIcon::Normal, QIcon::Off);
        m_pressIcon = icon;
        flashArrow(ui->toolBtn_Left);
        break;
    case ModeChangeModel::Arrow_Right:
        m_isFlashLRArrow = true;
        icon.addFile(QString::fromUtf8(":/images/right_scroll_press.png"), QSize(), QIcon::Normal, QIcon::Off);
        m_pressIcon = icon;
        flashArrow(ui->toolBtn_Right);
        break;
    default:
        break;
    }
}

void ModeChangeWidget::highLightToolButton()
{
    m_flashButton->setStyleSheet(
        "background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop:0 #134EA1, stop:1 #0B1E48);");
    Util::processEvents(QEventLoop::ExcludeUserInputEvents);
}

void ModeChangeWidget::lowLightToolButton()
{
    m_flashButton->setStyleSheet("background-color: rgba(255, 255, 255, 0);");
    Util::processEvents(QEventLoop::ExcludeUserInputEvents);
}

void ModeChangeWidget::flashArrow(QToolButton* button)
{
    if (button != NULL)
    {
        m_flashButton = button;
        // highLightToolButton();
        // QTimer::singleShot(100, this, SLOT(lowLightToolButton()));
        m_flashButton->setIcon(m_pressIcon);
        Util::processEvents(QEventLoop::ExcludeUserInputEvents);
    }
}

void ModeChangeWidget::arrowStateIcon(QIcon& icon, int dir, bool isEnabled)
{
    switch (dir)
    {
    case ModeChangeModel::Arrow_Up:
        if (isEnabled)
        {
            icon.addFile(QString::fromUtf8(":/images/up.png"), QSize(), QIcon::Normal, QIcon::Off);
        }
        else
        {
            icon.addFile(QString::fromUtf8(":/images/up_disable.png"), QSize(), QIcon::Normal, QIcon::Off);
        }
        break;
    case ModeChangeModel::Arrow_Down:
        if (isEnabled)
        {
            icon.addFile(QString::fromUtf8(":/images/down.png"), QSize(), QIcon::Normal, QIcon::Off);
        }
        else
        {
            icon.addFile(QString::fromUtf8(":/images/down_disable.png"), QSize(), QIcon::Normal, QIcon::Off);
        }
        break;
    case ModeChangeModel::Arrow_Left:
        if (isEnabled)
        {
            icon.addFile(QString::fromUtf8(":/images/left_scroll.png"), QSize(), QIcon::Normal, QIcon::Off);
        }
        else
        {
            icon.addFile(QString::fromUtf8(":/images/left_scroll_disable.png"), QSize(), QIcon::Normal, QIcon::Off);
        }
        break;
    case ModeChangeModel::Arrow_Right:
        if (isEnabled)
        {
            icon.addFile(QString::fromUtf8(":/images/right_scroll.png"), QSize(), QIcon::Normal, QIcon::Off);
        }
        else
        {
            icon.addFile(QString::fromUtf8(":/images/right_scroll_disable.png"), QSize(), QIcon::Normal, QIcon::Off);
        }
        break;
    default:
        break;
    }
}

void ModeChangeWidget::onSetUpArrowState()
{
    QIcon up_icon;
    arrowStateIcon(up_icon, ModeChangeModel::Arrow_Up, m_isUpArrowEnabled);
    ui->toolBtn_Up->setIcon(up_icon);

    Util::processEvents(QEventLoop::ExcludeUserInputEvents);
}

void ModeChangeWidget::onSetDownArrowState()
{
    QIcon down_icon;
    arrowStateIcon(down_icon, ModeChangeModel::Arrow_Down, m_isDownArrowEnabled);
    ;
    ui->toolBtn_Down->setIcon(down_icon);

    Util::processEvents(QEventLoop::ExcludeUserInputEvents);
}

void ModeChangeWidget::onSetUDArrowState()
{
    onSetUpArrowState();
    onSetDownArrowState();
}

void ModeChangeWidget::setUpAndDownArrowState(bool UpState, bool DownState)
{
    m_isUpArrowEnabled = UpState;
    m_isDownArrowEnabled = DownState;

    if (m_isFlashUpArrow)
    {
        onSetDownArrowState();
        QTimer::singleShot(500, this, SLOT(onSetUpArrowState()));
        m_isFlashUpArrow = false;
    }
    else if (m_isFlashDownArrow)
    {
        onSetUpArrowState();
        QTimer::singleShot(500, this, SLOT(onSetDownArrowState()));
        m_isFlashDownArrow = false;
    }
    else
    {
        onSetUDArrowState();
    }
}

void ModeChangeWidget::on_toolBtn_Up_clicked()
{
    m_StateManager->postEvent(StateEventNames::BottomMenuUp);
}

void ModeChangeWidget::on_toolBtn_Down_clicked()
{
    m_StateManager->postEvent(StateEventNames::BottomMenuDown);
}

void ModeChangeWidget::on_toolBtn_Left_clicked()
{
    m_StateManager->postEvent(StateEventNames::MenuChangePre);
}

void ModeChangeWidget::on_toolBtn_Right_clicked()
{
    m_StateManager->postEvent(StateEventNames::MenuChangeNext);
}
