#ifndef IFINGERPRINTOPERATOR_H
#define IFINGERPRINTOPERATOR_H

#include <QObject>
#include <QVariant>
#include "fingerprintinfo.h"
#include "adminmodel_global.h"

class ADMINMODELSHARED_EXPORT IFingerPrintOperator : public QObject
{
    Q_OBJECT

public:
    enum FPrintType
    {
        Sync,
        Async
    };

    enum EnrollState
    {
        NoneSpace,
        EnrollOK
    };

    enum VerifyState
    {
        VerifyFailed,
        VerifyOK
    };

    explicit IFingerPrintOperator(QObject* parent = nullptr);
    virtual ~IFingerPrintOperator();

    virtual FPrintType fprintType() const = 0;
    virtual int maxCount() = 0;
    virtual bool enroll(const QString& userId) = 0;
    virtual bool verify() = 0;
    virtual void stop() = 0;
    virtual bool remove(const FingerprintInfo& fpinfo) = 0;
    virtual const QList<FingerprintInfo> getFingerprintInfo(const QString& userId) const = 0;
    virtual bool addFingerprint(const QString& fingerName, int fingerId, int status, const QString& userId) = 0;
    virtual bool hasFingerprint() = 0;

signals:
    void finish(int value);
    void send();
    void pressFinger(int timer);
    void enrollState(uint state, int id);
};

#endif // IFINGERPRINTOPERATOR_H
