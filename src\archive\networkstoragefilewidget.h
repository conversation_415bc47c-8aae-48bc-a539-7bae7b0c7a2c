/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#ifndef NETWORKSTORAGEFILEWIDGET_H
#define NETWORKSTORAGEFILEWIDGET_H
#include "archive_global.h"

/**
 * 飞鸽传书的UI界面进行相关的信息配置
 **/

#include "basewidget.h"
#include "threadmodel.h"
#include "dicomtaskmanager.h"

class ImageProcessedCopy;
class DeleteFilesController;
class IStateManager;
class IColorMapManager;

namespace Ui
{
class NetworkStorageFileWidget;
}

class ARCHIVESHARED_EXPORT NetworkStorageFileWidget : public BaseWidget
{
    Q_OBJECT
public:
    explicit NetworkStorageFileWidget(IStateManager* value, IColorMapManager* colorMapManager, QWidget* parent = 0);
    ~NetworkStorageFileWidget();
    void setDefaultDirName(const QString& dirName);
    void setExportFilePaths(const QStringList& filePaths);
    // 在每次打开网络共享界面的时候进行init,并不需要每次系统开机进行init
    void initServiceNames();
    void setIsCurrentMultiChoice(bool value);
    void setDeleteAfterSendExam(bool value);
    void setDicomTaskManager(DicomTaskManager* dicomTaskManager);
    void setDeleteFilesController(DeleteFilesController* controller);
    /**
     * @brief 判断后台是否有任务在运行
     */
    bool isWorking();
    void showDcmSRCheckBox(bool value);
    void setContainRetrieveExam(bool value);

protected:
    void retranslateUi();

private:
    void setLineEditAndLableVisible(bool value);
    void checkIfNeedSingleFrameImgData(QStringList& filePaths);
    void initRadioButtons();
    void convertRpt2Pdf();
    void convertRpt2PdfSelf(QString& selfFile);
    void rptToPdfSelf();
    void setCopyOptions();
signals:
    void closed();
    void deleteCurrentSelections();
    void exportTypeChanged(QString type);
public slots:
    void onExportTypeChanged(QString type);
private slots:
    void onConfirmButtonClicked(int value);
    void onInfoShowed(bool isShowInfo);
    void on_pushButtonClose_clicked();
    void onThreadFinished(bool isSuccessful);
    void on_pushButtonExport_clicked();
    void on_radioButtonBMP_toggled(bool checked);
    void on_radioButtonJPG_toggled(bool checked);
    void on_radioButtonDCM_toggled(bool checked);

    void on_checkBoxGDPR_toggled(bool checked);

    void on_radioButtonPNG_toggled(bool checked);

private:
    void responseUIToFilePaths();
    void deleteRPT(QString destPath);

private:
    Ui::NetworkStorageFileWidget* ui;
    QStringList m_filePaths;
    ThreadModel m_threadModel;
    ProgressBarView* m_progressBar;
    ConfirmDialogFrame* m_confirmDialog;
    ImageProcessedCopy* m_copy;
    DicomTaskManager* m_DicomTaskManager;
    DeleteFilesController* m_DeleteFilesController;
    QString m_ImageType;
    bool m_isCurrentMulticChoice;
    bool m_isCopy;
    bool m_IsShowTransformInfo;
    bool m_IsCopyThreadStarted; //记录是否开启过拷贝线程
    QString m_DefaultDir;
    bool m_ContainRetrieveExam;
    static const char* const m_AnimalString[];
    enum ANIMALSTRING
    {
        Delete_animal_exam_after_sending
    };
    QStringList m_PDFPath;
    QString m_FinalDir;
};

#endif // NETWORKSTORAGEFILEWIDGET_H
