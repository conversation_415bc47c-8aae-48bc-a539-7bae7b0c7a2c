#include "basefilesystemview.h"
#include "ui_basefilesystemview.h"

BaseFileSystemView::BaseFileSystemView(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::BaseFileSystemView)
{
    ui->setupUi(this);
}

BaseFileSystemView::~BaseFileSystemView()
{
    delete ui;
}

void BaseFileSystemView::setModel(QAbstractItemModel* model)
{
    ui->treeView->setModel(model);
}

void BaseFileSystemView::setSelectionMode(QAbstractItemView::SelectionMode mode)
{
    ui->treeView->setSelectionMode(mode);
}

QAbstractItemView::SelectionMode BaseFileSystemView::selectionMode() const
{
    return ui->treeView->selectionMode();
}

bool BaseFileSystemView::isSortingEnabled() const
{
    return ui->treeView->isSortingEnabled();
}

void BaseFileSystemView::setSortingEnabled(bool enable)
{
    ui->treeView->setSortingEnabled(enable);
}

void BaseFileSystemView::selectAll()
{
    ui->treeView->selectAll();
}

void BaseFileSystemView::retranslateUi()
{
    ui->retranslateUi(this);
}

void BaseFileSystemView::on_treeView_clicked(const QModelIndex& index)
{
}
