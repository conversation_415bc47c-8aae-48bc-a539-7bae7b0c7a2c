#!/bin/sh

echo_usage()
{
    cat <<-END
usage: 
    ./configure_unix_x86_64bit.sh [-h] [-m <model>]

Options:
    -h                      help
    -m <model>              model, like Phoenix, Apple etc.

examples:


END
    exit 0
}

while getopts "hm:" arg
do
    case $arg in
        h)  echo_usage;;
        m)  model=$OPTARG;;
    esac
done

lowermodel=${model,,}
branchname="pangu"
respath="resource-xunit"
testdatapath="testdata"

# 拷贝资源到测试目录
chmod +w build -R
rsync -rl ~/code/$respath/res build/$model/test --exclude=modelfiles
rsync -rl ~/code/$respath/res/modelfiles/$lowermodel build/$model/test/res/modelfiles

# 拷贝测试数据到测试目录
#cp -fra ~/code/$testdatapath/common build/$model/test
cp -fra ~/code/$testdatapath/$lowermodel/* build/$model/test


# 执行测试程序
currentpath=`pwd`
echo ${currentpath}
export LD_LIBRARY_PATH=$currentpath/build/$model/bin/lib
cd $currentpath/build/$model/tests && ctest -j8
