#include "messageevent.h"
#include "messageboxframe.h"

QEvent::Type MessageEvent::eventType()
{
    static QEvent::Type _t = (QEvent::Type)QEvent::registerEventType(QEvent::User + 103);
    return _t;
}

MessageEvent::MessageEvent(const QString& message)
    : QInputEvent(eventType())
    , m_Message(message)
{
}

const QString& MessageEvent::message() const
{
    return m_Message;
}

void MessageEvent::showMessage()
{
    MessageBoxFrame::tipInformation(m_Message);
}
