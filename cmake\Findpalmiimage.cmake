#
# The module defines the following variables:
# PAML_IIMAGE_FOUND - True if PAML_IIMAGE found.
# PAML_IIMAGE_INCLUDE_DIRS - where to find chengine.h, etc.
# PAML_IIMAGE_LIBRARIES - List of libraries when using PAML_IIMAGE.
#

thirdparty_prefix_path(iimage)

find_path ( PAML_IIMAGE_INCLUDE_DIR
            NAMES
                chengine.h
            HINTS
                ${PAML_IIMAGE}/
            )
set(PAML_IIMAGE_INCLUDE_DIRS ${PAML_IIMAGE_INCLUDE_DIR})
message("Found PAML_IIMAGE headers: ${PAML_IIMAGE_INCLUDE_DIRS}")


find_library ( PAML_IIMAGE_LIBRARY
            NAMES 
                chengine
            HINTS
                ${PAML_IIMAGE_DIR}/lib
                ${PAML_IIMAGE}/
             )
set(PAML_IIMAGE_LIBRARIES ${PAML_IIMAGE_LIBRARY})
message("Found PAML_IIMAGE libs: ${PAML_IIMAGE_LIBRARIES}")