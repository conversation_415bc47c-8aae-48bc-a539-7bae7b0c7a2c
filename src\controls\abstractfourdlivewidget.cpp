#include "abstractfourdlivewidget.h"
#include "ifourdrenderwidget.h"
#include "screenshots.h"
#include "fourdlivemodel.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include "setting.h"
#include "toolnames.h"
#include "toolsfactory.h"
#include "icommand.h"
#include "fourdapihandler.h"
#include "ifourdparameter.h"
AbstractFourDLiveWidget::AbstractFourDLiveWidget(IFourDDataSender* dataSender, QWidget* parent)
    : IFourDLiveWidget(parent)
    , m_ImageTile(NULL)
    , m_LineBufferManager(NULL)
    , m_SonoParameters(NULL)
    , m_Model(new FourDLiveModel(dataSender, parent))
    , m_currentMouseCommand(NULL)
    , m_fourDParameter(NULL)
{
}

AbstractFourDLiveWidget::~AbstractFourDLiveWidget()
{
    delete m_Model;
    m_Model = NULL;
}
void AbstractFourDLiveWidget::prepareFourDParameters(bool resetParameters)
{
    if (m_Model != NULL)
    {
        m_Model->prepareFourDParameters(resetParameters);
    }
}

void AbstractFourDLiveWidget::clearFourDData()
{
    if (m_Model != NULL)
    {
        m_Model->clearFourDData();
    }
}

void AbstractFourDLiveWidget::saveROIInfo()
{
}

void AbstractFourDLiveWidget::onAviPlay(const QString& aviFileName)
{
    Q_UNUSED(aviFileName);
}

void AbstractFourDLiveWidget::retranslateUi()
{
}

void AbstractFourDLiveWidget::onFourDZoom(bool value)
{
    if (m_Model != NULL)
    {
        m_Model->zoom(value);
    }
}

void AbstractFourDLiveWidget::updateCurrentIndex()
{
    if (m_Model != NULL)
    {
        m_Model->updateCurrentIndex();
    }
}

void AbstractFourDLiveWidget::sendVolumeData(const FourDVolumeLineImageArgs& arg)
{
    if (m_Model != NULL)
    {
        m_Model->sendVolumeData(arg);
    }
}
void AbstractFourDLiveWidget::onFourDTransformAxisChanged(QString value)
{
    if (m_SonoParameters != NULL)
    {
        m_SonoParameters->setPV(BFPNames::TransformationAxisStr, value.toInt());
    }
}

void AbstractFourDLiveWidget::onFourDDirectionChanged(bool increase)
{
    if (m_SonoParameters != NULL)
    {
        if (m_fourDParameter != NULL)
        {
            m_fourDParameter->setRotateDirection(m_SonoParameters->pIV(BFPNames::FourDDirectionSetStr), increase);
        }
    }
}
ICommand* AbstractFourDLiveWidget::currentMouseCommand() const
{
    return m_currentMouseCommand;
}
void AbstractFourDLiveWidget::updateMouseCommand(FourDMouseStateEnum::FourDMouseActionMode mode)
{
    QString toolName = ToolNames::CinePlayMouseStr;
    switch (mode)
    {
    case FourDMouseStateEnum::CurvedLine:
    case FourDMouseStateEnum::Panning:
    case FourDMouseStateEnum::VirtualHDLight:
        toolName = ToolNames::FourDMouseMoveStr;
        break;
    default:
        break;
    }
    if (m_currentMouseCommand != NULL && QString::compare(m_currentMouseCommand->toolName(), toolName) == 0)
    {
        return;
    }
    else if (m_currentMouseCommand != NULL)
    {
        m_currentMouseCommand->stop();
    }
    m_currentMouseCommand = ToolsFactory::instance().command(toolName);
    if (m_SonoParameters->pBV(BFPNames::ShowFourDWidgetStr))
    {
        m_currentMouseCommand->run();
    }
}
void AbstractFourDLiveWidget::resetToPresetDefaultValue()
{
    FourDAPIHandler::instance().reset();
    QVariant value;
    if (m_SonoParameters->parameterDefaultValue(BFPNames::FourDDirectionSetStr, value))
    {
        m_SonoParameters->setPV(BFPNames::FourDDirectionSetStr, value);
    }
}
void AbstractFourDLiveWidget::setFreezeBarAllIndex()
{
}
