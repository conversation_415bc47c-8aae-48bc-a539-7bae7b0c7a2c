#ifndef ISYSTEMUSAGE_H
#define ISYSTEMUSAGE_H

#include "controls_global.h"

class CONTROLSSHARED_EXPORT ISystemUsage
{
public:
    virtual ~ISystemUsage();

    virtual int getCpuUsage() = 0;
    virtual int getCpuTemperature() = 0;
    virtual unsigned long getCpuThreadCount() = 0;
    virtual unsigned long getCpuCoresCount() = 0;
    virtual void getMemoryUsage(unsigned int& nMemTotal, unsigned int& nMemFree) = 0;
    virtual int getCurVolumeVal() = 0;
    virtual bool setCurVolumeVal(int nVolumeVal) = 0;
    virtual bool playVideo(const QString& fileName) = 0;
};

#endif // ISYSTEMUSAGE_H
