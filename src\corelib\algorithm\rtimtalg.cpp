#include "rtimtalg.h"

RtimtAlg::RtimtAlg()
    : VirtualAlg()
{
    m_CreateContext = nullptr;
    m_RemoveContext = nullptr;
    m_InitMa = nullptr;
    m_RoiCal = nullptr;
    m_CycleDetect = nullptr;
    m_GetMargin = nullptr;
    m_Lib.setFileName("rtimt");
}

void RtimtAlg::createViewontextRtimt(const char* modelpath, int width, int height)
{
    m_BeginInit = true;
    if (m_CreateContext == NULL)
    {
        m_CreateContext = (CreateViewontextRtimt)m_Lib.resolve("createViewontext_rtimt");
    }
    if (m_CreateContext == NULL)
    {
        m_IsError = true;
        return;
    }
    m_CreateContext(modelpath, width, height);
    m_Initialized = true;
    m_BeginInit = false;
}

void RtimtAlg::removeViewontextRtimt()
{
    if (m_RemoveContext == NULL)
    {
        m_RemoveContext = (RemoveViewontextRtimt)m_Lib.resolve("removeViewontext_rtimt");
    }
    if (m_RemoveContext == NULL)
    {
        m_IsError = true;
        return;
    }
    m_Initialized = false;
    m_RemoveContext();
}

void RtimtAlg::intimaMeasureRtimt(unsigned char* imgin, int width, int height, int left_bbox_x, int right_bbox_x,
                                  int lower_bbox_y, int upper_bbox_y, int crop_width, int crop_height, int width_bias,
                                  int height_bias, IMOutput* output)
{
    if (m_InitMa == NULL)
    {
        m_InitMa = (IntimaMeasureRtimt)m_Lib.resolve("intimameasure_rtimt");
    }
    if (m_InitMa == NULL || !m_Initialized)
    {
        m_IsError = true;
        return;
    }
    m_InitMa(imgin, width, height, left_bbox_x, right_bbox_x, lower_bbox_y, upper_bbox_y, crop_width, crop_height,
             width_bias, height_bias, output);
}

void RtimtAlg::roibboxCalculateRtimt(int* p_middle_ys_diff, float* angles_diff, int* x_centers, int* y_begins,
                                     int* y_ends, int duration_determine, int duration_roi_calculate, int use_still_roi,
                                     int change_still_roi, int num_frames_since_still, int x_center_still,
                                     int y_begin_still, int y_end_still, IMRoibox* roibox)
{
    if (m_RoiCal == NULL)
    {
        m_RoiCal = (RoibboxCalculateRtimt)m_Lib.resolve("roibboxcalculate_rtimt");
    }
    if (m_RoiCal == NULL || !m_Initialized)
    {
        m_IsError = true;
        return;
    }
    m_RoiCal(p_middle_ys_diff, angles_diff, x_centers, y_begins, y_ends, duration_determine, duration_roi_calculate,
             use_still_roi, change_still_roi, num_frames_since_still, x_center_still, y_begin_still, y_end_still,
             roibox);
}

void RtimtAlg::cardiacCycleDetection(double* lumen_diameters, int length, int start_idx, int* local_minmax,
                                     IMCycle* cycle)
{
    if (m_CycleDetect == NULL)
    {
        m_CycleDetect = (CardiacCycleDetection)m_Lib.resolve("cardiaccycledetection");
    }
    if (m_CycleDetect == NULL || !m_Initialized)
    {
        m_IsError = true;
        return;
    }
    m_CycleDetect(lumen_diameters, length, start_idx, local_minmax, cycle);
}

void RtimtAlg::getMarginValues(unsigned char* imgin, int width, int height, int& left_margin_x, int& right_margin_x,
                               int& lower_margin_y, int& upper_margin_y)
{
    if (m_GetMargin == NULL)
    {
        m_GetMargin = (GetMarginValues)m_Lib.resolve("get_margin_values");
    }
    if (m_GetMargin == NULL || !m_Initialized)
    {
        m_IsError = true;
        return;
    }
    m_GetMargin(imgin, width, height, left_margin_x, right_margin_x, lower_margin_y, upper_margin_y);
}
