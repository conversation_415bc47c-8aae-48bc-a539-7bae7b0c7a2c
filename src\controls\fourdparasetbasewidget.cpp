#include "fourdparasetbasewidget.h"
#include "sonoparameters.h"
#include "parameter.h"

FourDParaSetBaseWidget::FourDParaSetBaseWidget(QWidget* parent)
    : BaseWidget(parent)
    , m_sonoParameters(NULL)
{
}

FourDParaSetBaseWidget::~FourDParaSetBaseWidget()
{
}

void FourDParaSetBaseWidget::setSonoParameters(SonoParameters* sonoParameters)
{
    m_sonoParameters = sonoParameters;
    onSetSonoParameters();
}

void FourDParaSetBaseWidget::setParametersIndex(int index)
{
    if (NULL != m_sonoParameters)
    {
        if (index <= m_sonoParameters->parameter(m_RelateParasName)->max())
        {
            m_sonoParameters->setPV(m_RelateParasName, index);
        }
    }
}

void FourDParaSetBaseWidget::setParametersMax(int max)
{
    if (NULL != m_sonoParameters)
    {
        m_sonoParameters->parameter(m_RelateParasName)->setMax(max);
    }
}

void FourDParaSetBaseWidget::updateParameters()
{
    if (NULL != m_sonoParameters)
    {
        m_sonoParameters->parameter(m_RelateParasName)->update();
    }
}

void FourDParaSetBaseWidget::onSetSonoParameters()
{
}
