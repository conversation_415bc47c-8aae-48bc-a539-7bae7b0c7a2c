#include "screensaverdialog.h"
#include "ui_screensaverdialog.h"
#include "resource.h"
#include <QFile>
#include "setting.h"
#include "formula.h"
#include <QDesktopWidget>
#include "qscreen.h"

ScreenSaverDialog::ScreenSaverDialog(QWidget* parent)
    : QDialog(parent)
    , ui(new Ui::ScreenSaverDialog)
{
    ui->setupUi(this);
    setWindowFlags(windowFlags() | Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint);

    setFixedSize(QGuiApplication::primaryScreen()->geometry().width(),
                 QGuiApplication::primaryScreen()->geometry().height());

    m_LabelMoveTimer.setInterval(3000);
    connect(&m_LabelMoveTimer, SIGNAL(timeout()), this, SLOT(onLabelMoved()));
}

ScreenSaverDialog::~ScreenSaverDialog()
{
    delete ui;
}

void ScreenSaverDialog::doExec(int pictureIndex)
{
    if (!isVisible())
    {
        if (pictureIndex == 0)
        {
            m_Pixmap.load(Resource::defaultScreenSaverFileName());
        }
        else if (pictureIndex == 1)
        {
            foreach (const QString& name, Resource::screenSaverIconNames)
            {
                QString fullName = Resource::getFileFullName(name, Resource::iconDir);
                if (QFile::exists(fullName))
                {
                    m_Pixmap.load(fullName);
                    break;
                }
            }
        }

        if (!m_Pixmap.isNull())
        {
            ui->label->setScaledContents(true);
            ui->label->resize(m_Pixmap.width(), m_Pixmap.height());
            ui->label->setPixmap(m_Pixmap);
            ui->label->move(Formula::middleLeftTop(size(), ui->label->size()));
        }

        m_LabelMoveTimer.start();
        exec();
    }
}

void ScreenSaverDialog::doQuit()
{
    if (isVisible())
    {
        m_LabelMoveTimer.stop();
        reject();
    }
}

void ScreenSaverDialog::onLabelMoved()
{
    int w = width() - m_Pixmap.width();
    int h = height() - m_Pixmap.height();
    ui->label->move(rand() % w, rand() % h);
}
