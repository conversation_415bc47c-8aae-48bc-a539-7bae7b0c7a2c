#include "parametersetupwidget.h"
#include "ui_parametersetupwidget.h"
#include "util.h"

const QStringList ParameterSetUpWidget::Names = QStringList() << QT_TRANSLATE_NOOP("ParameterSetUpWidget", "Dark")
                                                              << QT_TRANSLATE_NOOP("ParameterSetUpWidget", "Light")
                                                              << QT_TRANSLATE_NOOP("ParameterSetUpWidget", "Sharpness")
                                                              << QT_TRANSLATE_NOOP("ParameterSetUpWidget", "Gamma");
ParameterSetUpWidget::ParameterSetUpWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::ParameterSetUpWidget)
    , m_Value(0)
{
    ui->setupUi(this);
}

ParameterSetUpWidget::~ParameterSetUpWidget()
{
    delete ui;
}

void ParameterSetUpWidget::initializeParameter(const QString& name, int min, int max, int value)
{
    m_Name = name;
    m_Value = value;
    ui->labelName->setText(Util::translate("ParameterSetUpWidget", name));
    ui->horizontalSlider->setMinimum(min);
    ui->horizontalSlider->setMaximum(max);
    ui->horizontalSlider->setValue(value);
    ui->labelValue->setText(QString::number(value));
}

int ParameterSetUpWidget::value() const
{
    return ui->horizontalSlider->value();
}

void ParameterSetUpWidget::setDefaultValue(int value)
{
    ui->horizontalSlider->setValue(value);
}

void ParameterSetUpWidget::retranslateUi()
{
    ui->retranslateUi(this);
    ui->labelName->setText(Util::translate("ParameterSetUpWidget", m_Name));
    ui->labelValue->setText(QString::number(m_Value));
}

void ParameterSetUpWidget::on_horizontalSlider_valueChanged(int value)
{
    m_Value = value;
    ui->labelValue->setText(QString::number(value));
}
