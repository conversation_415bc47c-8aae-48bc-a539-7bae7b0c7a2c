<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ArchiveButtonWidget</class>
 <widget class="QWidget" name="ArchiveButtonWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>171</width>
    <height>387</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="4" column="0">
    <widget class="QPushButton" name="pushButtonRestore">
     <property name="text">
      <string>Restore Exam</string>
     </property>
    </widget>
   </item>
   <item row="6" column="0">
    <widget class="QPushButton" name="pushButtonDelete">
     <property name="text">
      <string>Delete Exam</string>
     </property>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QPushButton" name="pushButtonInfo">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="text">
      <string>Patient Info</string>
     </property>
    </widget>
   </item>
   <item row="2" column="0">
    <widget class="QPushButton" name="pushButtonReport">
     <property name="text">
      <string>Review Report</string>
     </property>
    </widget>
   </item>
   <item row="5" column="0">
    <widget class="QPushButton" name="pushButtonSend">
     <property name="text">
      <string>Send Exam</string>
     </property>
    </widget>
   </item>
   <item row="3" column="0">
    <widget class="QPushButton" name="pushButtonBackup">
     <property name="text">
      <string>Backup Exam</string>
     </property>
     <property name="autoDefault">
      <bool>false</bool>
     </property>
     <property name="default">
      <bool>false</bool>
     </property>
    </widget>
   </item>
   <item row="0" column="0">
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
