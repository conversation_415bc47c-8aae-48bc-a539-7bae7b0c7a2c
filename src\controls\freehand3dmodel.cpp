#include "freehand3dmodel.h"
#include "ilinebuffermanager.h"
#include "imagetile.h"
#include "sonoparameters.h"
#include "parameter.h"
#include "bfpnames.h"
#include "freehand3dapi.h"
#include "imageeventargs.h"

FreeHand3DModel::FreeHand3DModel(ImageTile* imageTile, ILineBufferManager* bufferManager, QObject* parent)
    : QObject(parent)
    , m_ImageTile(imageTile)
    , m_BufferManager(bufferManager)
    , m_FrameDestData(NULL)
    , m_DataSizeInfo(m_ImageTile->dataSizeInfo())
    , m_FrameCount(0)
    , m_FrameSize(m_DataSizeInfo.size())
{
    m_FrameDestData = new uchar[m_FrameSize];
    memset(m_FrameDestData, 0, m_FrameSize);
}

FreeHand3DModel::~FreeHand3DModel()
{
    DELETE_ARR(m_FrameDestData)
}

void FreeHand3DModel::process3DData()
{
    emit updateProgressBarText(QObject::tr("Loading. Please wait..."));
    int startIndex = m_BufferManager->startIndex();
    int endIndex = m_BufferManager->endIndex();
    m_FrameCount = endIndex - startIndex + 1;
    FreeHand3DProperty property;
    getFreeHand3DProperty(property);
    FreeHand3DAPI::instance().setViewerProperty(property);
    int lostFrames = 0;
    ImageEventArgs** imageEventArgsPtr = NULL;

    int getFrameTimes = 0;

    for (int i = startIndex; i <= endIndex; ++i)
    {
        if (m_BufferManager->getOneFrame(i, imageEventArgsPtr))
        {
            getFrameTimes = 0;
            if (NULL != imageEventArgsPtr)
            {
                int frameSize = catchPicureSize(m_FrameDestData, (*imageEventArgsPtr)->imageData());
                if ((i == startIndex) || (i == endIndex))
                {
                    for (int iCurrentBit = 0; iCurrentBit < frameSize; ++iCurrentBit)
                    {
                        m_FrameDestData[iCurrentBit] = 30;
                    }
                }
                FreeHand3DAPI::instance().setFrameData(m_FrameDestData, i - startIndex - lostFrames);
                setUpdateProgressBar(i + 1 - startIndex);
                memset(m_FrameDestData, 0, m_FrameSize);
            }
            imageEventArgsPtr = NULL;
        }
        else
        {
            //这里存在获取失败的情况，如果获取失败，重新获取，最大重复次数为3次
            if (getFrameTimes++ < 3)
            {
                i--;
                continue;
            }
            else
            {
                getFrameTimes = 0;
                lostFrames++;
            }
        }
    }
    emit volumeDataSended();
}

void FreeHand3DModel::onVolumeSpacingChanged(const QVariant& value)
{
    Q_UNUSED(value)
    FreeHand3DProperty property;
    getFreeHand3DProperty(property);
    FreeHand3DAPI::instance().setViewerProperty(property);
}

void FreeHand3DModel::setUpdateProgressBar(int index)
{
    if (-1 != index)
    {
        QString msg = QString(QObject::tr("Analyzing: %1/%2")).arg(index).arg(m_FrameCount);
        emit updateProgressBarText(msg);
        qint64 value = index * 100 / m_FrameCount;
        emit updateProgressBarValue(value);
    }
}

int FreeHand3DModel::catchPicureSize(unsigned char* dest, const unsigned char* src)
{
    if ((NULL == dest) || (NULL == src))
    {
        return 0;
    }
    QRect roiRect = m_ImageTile->freeHand3DRoiGeometry();
    QSize srcSize = QSize(m_DataSizeInfo.Width, m_DataSizeInfo.Height);
    if (roiRect.width() > srcSize.width())
    {
        roiRect.setWidth(srcSize.width());
    }

    if (roiRect.height() > srcSize.height())
    {
        roiRect.setHeight(srcSize.height());
    }

    int startRow = srcSize.width() * roiRect.y();

    int pos = 0;
    //裁剪矩形ROI框内的像素，并设置矩形ROI框的边像素为固定值30
    for (int row = 0; row < roiRect.height(); ++row)
    {
        for (int column = 0; column < roiRect.width(); ++column)
        {
            if ((0 == row) || (0 == column) || (roiRect.width() - 1 == column) || (roiRect.height() - 1) == row)
            {
                dest[pos] = 30;
            }
            else
            {
                dest[pos] = src[startRow + column + roiRect.x()];
            }
            ++pos;
        }
        startRow += srcSize.width();
    }
    return pos;
}

void FreeHand3DModel::getFreeHand3DProperty(FreeHand3DProperty& property)
{
    SonoParameters* sonoParameters = m_ImageTile->sonoParameters();
    float pixelSizeMM = sonoParameters->pFV(BFPNames::PixelSizeMMStr);
    float volumeSpacing = sonoParameters->pFV(BFPNames::FreeHand3DVolmnSpacingStr) / 100;
    QRect roiRect = m_ImageTile->freeHand3DRoiGeometry();

    property.m_Width = roiRect.width();
    property.m_Height = roiRect.height();
    property.m_Depth = m_FrameCount;
    property.m_XSpacing = pixelSizeMM;
    property.m_YSpacing = pixelSizeMM;
    property.m_ZSpacing = volumeSpacing;
}
