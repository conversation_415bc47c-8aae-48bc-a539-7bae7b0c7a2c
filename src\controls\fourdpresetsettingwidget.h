#ifndef FOURDPRESETSETTINGWIDGET_H
#define FOURDPRESETSETTINGWIDGET_H

#include "controls_global.h"
#include "fourdparassettingbasewidget.h"
#include "fourdpresetwidget.h"

class CONTROLSSHARED_EXPORT FourDPresetSettingWidget : public FourDParasSettingBaseWidget
{
    Q_OBJECT
public:
    explicit FourDPresetSettingWidget(QWidget* parent = 0);
    void initalize();
    void setSonoParameters(SonoParameters* sonoParameters);

protected:
    virtual void currenIndexChanged(const QString& arg1);
    virtual bool contains(const QString& name);
    virtual void add(const QString& name);
    virtual void save(const QString& name);
    virtual void del(const QString& name);
    virtual bool rename(const QString& oldName, const QString& newName);
    virtual QStringList names();
signals:
    void fourDPresetParasChanged();
    void fourdPresetParasDeleted(int index);

private:
    FourDPresetWidget* m_Child;
};

#endif // FOURDPRESETSETTINGWIDGET_H
