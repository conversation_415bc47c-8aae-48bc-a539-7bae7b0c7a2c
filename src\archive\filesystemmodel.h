#ifndef FILESYSTEMMODEL_H
#define FILESYSTEMMODEL_H
#include "archive_global.h"

#include <QFileSystemModel>

class ARCHIVESHARED_EXPORT FileSystemModel : public QFileSystemModel
{
    Q_OBJECT
public:
    explicit FileSystemModel(QObject* parent = 0);

protected:
    QVariant data(const QModelIndex& index, int role) const;
signals:

public slots:
private:
    QIcon m_FolderIcon;
    QIcon m_fileIcon;
};

#endif // FILESYSTEMMODEL_H
