#include "chisonfourdcolorbarwidget.h"
//#include <GL/glu.h>
#include "glut.h"
#include "fourdapihandler.h"
#include <QDebug>

ChisonFourDColorBarWidget::ChisonFourDColorBarWidget(QWidget* parent)
    : QGLWidget(QGLFormat(QGL::SampleBuffers), parent)
    , m_BackgroundColor(Qt::black)
    , m_BorderColor(QColor(0, 0, 0))
    , m_Orientation(this->width() > this->height() ? Qt::Horizontal : Qt::Vertical)
    , m_ScalarMin(0)
    , m_ScalarMax(255)
    , m_MarginX(0.01)
    , m_MarginY(0)
{
    m_CTFNodeList.append(ChisonFourDCTFNode(0, 0, 0, 0));
    m_CTFNodeList.append(ChisonFourDCTFNode(1, 1, 1, 1));
    this->setAttribute(Qt::WA_TranslucentBackground);
    this->setAttribute(Qt::WA_NoSystemBackground);
}

ChisonFourDColorBarWidget::~ChisonFourDColorBarWidget()
{
}

void ChisonFourDColorBarWidget::setOrientation(Qt::Orientation value)
{
    if (m_Orientation != value)
    {
        m_Orientation = value;
        this->updateGL();
    }
}

void ChisonFourDColorBarWidget::setMarginX(double marginX)
{
    m_MarginX = marginX;
}

void ChisonFourDColorBarWidget::setBackgroundColor(const QColor& backgroundColor)
{
    if (m_BackgroundColor != backgroundColor)
    {
        m_BackgroundColor = backgroundColor;
        initializeGL();
    }
}

void ChisonFourDColorBarWidget::setBorderColor(const QColor& borderColor)
{
    if (m_BorderColor != borderColor)
    {
        m_BorderColor = borderColor;
        this->updateGL();
    }
}

void ChisonFourDColorBarWidget::setScalarRange(double minVal, double maxVal)
{
    m_ScalarMin = minVal;
    m_ScalarMax = maxVal;
}

void ChisonFourDColorBarWidget::refreshColorBar(const QList<ChisonFourDCTFNode>& nodeList)
{
    m_CTFNodeList.clear();
    m_CTFNodeList = nodeList;
    this->updateGL();
}

void ChisonFourDColorBarWidget::initializeGL()
{
    glClearColor(1.0f, 1.0f, 1.0f, 1.0f);
}

void ChisonFourDColorBarWidget::resizeGL(int w, int h)
{
    m_Orientation = w > h ? Qt::Horizontal : Qt::Vertical;
    m_MarginY = m_MarginX * w / h;
    glViewport(0, 0, w, h);
    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    if (m_Orientation == Qt::Vertical)
    {
        glOrtho(0.0 - m_MarginX, 0.5 + m_MarginX, 0.0 - m_MarginY, 1.0 + m_MarginY, -1.0, 1.0);
    }
    else
    {
        glOrtho(0.0 - m_MarginX, 1.0 + m_MarginX, 0.0 - m_MarginY, 0.5 + m_MarginY, -1.0, 1.0);
    }

    glMatrixMode(GL_MODELVIEW);
}

void ChisonFourDColorBarWidget::paintGL()
{
    glClear(GL_COLOR_BUFFER_BIT);
    glEnable(GL_MULTISAMPLE);
    this->drawScene();
}

void ChisonFourDColorBarWidget::drawScene()
{
    glEnable(GL_MULTISAMPLE);

    double clrBarPosX = 0.0;
    double clrBarPosY = 0.0;
    double clrBarWidth = 0.5;
    double clrBarHeight = 1.0;

    if (m_Orientation == Qt::Horizontal)
    {
        swap(clrBarWidth, clrBarHeight);
    }

    glColor3d(m_BorderColor.redF(), m_BorderColor.greenF(), m_BorderColor.blueF());
    glBegin(GL_LINE_LOOP);

    glVertex2d(clrBarPosX, clrBarPosY);
    glVertex2d(clrBarPosX + clrBarWidth, clrBarPosY);
    glVertex2d(clrBarPosX + clrBarWidth, clrBarPosY + clrBarHeight);
    glVertex2d(clrBarPosX, clrBarPosY + clrBarHeight);

    glEnd();

    glBegin(GL_QUAD_STRIP);
    for (int i = 0; i < m_CTFNodeList.size(); ++i)
    {
        glColor3d(m_CTFNodeList[i].m_fRGB_R, m_CTFNodeList[i].m_fRGB_G, m_CTFNodeList[i].m_fRGB_B);

        if (m_Orientation == Qt::Vertical)
        {
            glVertex2d(clrBarPosX, m_CTFNodeList[i].m_dNodePos);
            glVertex2d(clrBarPosX + clrBarWidth, m_CTFNodeList[i].m_dNodePos);
        }
        else
        {
            glVertex2d(m_CTFNodeList[i].m_dNodePos, clrBarPosY);
            glVertex2d(m_CTFNodeList[i].m_dNodePos, clrBarPosY + clrBarHeight);
        }
    }
    glEnd();
}

void ChisonFourDColorBarWidget::swap(double& first, double& second)
{
    first = first + second;
    second = first - second;
    first = first - second;
}
