#ifndef QUERYRETRIEVEWIDGET_H
#define QUERYRETRIEVEWIDGET_H

#include "archive_global.h"
#include <QWidget>
#include <QRunnable>
#include "baseinputabledialogframe.h"
#include <QHash>
#include <QMessageBox>
#include <QFileSystemWatcher>
#include "basewidget.h"
#include "screenorientationmodel.h"

struct DcmQueryResponse
{
    char PatientsName[64]; // VR:PN MaxLen:64 dcmtk:// not correct: max length of PN is 3*64+2 = 194 characters (not
                           // bytes!)
    char PatientID[64];    // VR:LO MaxLen:64
    char PatientsSex[16];  // VR:CS MaxLen:16
    char PatientsBirthDate[10];             // VR:DA MaxLen:10
    char PatientsBirthTime[16];             // VR:DA MaxLen:16
    char StudyDate[10];                     // VR:DA MaxLen:10
    char StudyTime[16];                     // VR:TM MaxLen:16
    char StudyID[16];                       // VR:SH MaxLen:16
    char AccessionNumber[16];               // VR:SH MaxLen:16
    char ReferringPhysiciansName[64];       // VR:PN MaxLen:64
    char RequestingPhysiciansName[64];      // VR:PN MaxLen:64
    char SpecificCharacterSet[32];          // VR:CS MaxLen:32
    char StudyInstanceID[64];               // VR:UI MaxLen:64
    char StudyDescription[64];              // VR:LO MaxLen:64
    char Modality[16];                      // VR:CS MaxLen:16
    char NumberOfStudyRelatedSeries[16];    // VR:IS
    char NumberOfStudyRelatedInstances[16]; // VR:IS
    char PatientSize[16];                   // VR:DS
    char PatientWeight[16];                 // VR:DS
};

class QueryRetrieveWidget;
class QStandardItemModel;
class CommonProgressBarFrame;
class QTextStream;
class QProcess;
class IStateManager;
class ARCHIVESHARED_EXPORT QueryRetrieveDialog : public BaseInputAbleDialogFrame
{
    Q_OBJECT
public:
    explicit QueryRetrieveDialog(IStateManager* stateManager, QWidget* parent = 0);
    void clearWidget();

private slots:
    void doQueryTask();

protected:
    void showEvent(QShowEvent* e);

private:
    QueryRetrieveWidget* m_Child;
};

namespace Ui
{
class QueryRetrieveWidget;
}

class ARCHIVESHARED_EXPORT QueryRetrieveWidget : public BaseWidget
{
    Q_OBJECT
    friend class DicomQueryTask;
    friend class DicomRetrieveTask;
    Q_PROPERTY(bool isHor READ isHor)
public:
    enum SearchKey
    {
        StudyID,
        ExamToday,
        ExamBetween,
        ExamDateBefore,
        ExamDate,
        ExamDateAfter,
        ExamDescription,
        Count
    };
    explicit QueryRetrieveWidget(IStateManager* stateManager, QWidget* parent = 0);
    ~QueryRetrieveWidget();

    void doQueryTask();

    void doRetrieveTask();
    void clearWidget();

    bool isHor();

protected:
    void showEvent(QShowEvent* e);
    void retranslateUi();

private:
    void initTreeView();

    void doDicomQuery();

    void doDicomRetrieve();

    int findScu(const QString& ip, const QString& port, int timeout);

    int moveScu(const QString& title, const QString& ip, const QString& scpport, const QString& localport, int timeout);

    void switchEditState(int index);

    void getSearchValue(QString& studyID, QString& examDescription, QString& studyDate);

    void createPatient(const DcmQueryResponse& rspInfo, QStringList& cmd);

    void prepareRetrieve();

    void setData(const DcmQueryResponse& rsp, bool isQuery = true);

signals:
    void findscuStarted();

    void findscuFinished(int);

    void accept();

    void progressValueChanged(int);
public slots:
    void onClose();

private slots:
    void onFindscuStarted();

    void getRespond(int res);

    void on_queryButton_clicked();

    void on_clearButton_clicked();

    void on_selectAllButton_clicked();

    void on_deselectButton_clicked();

    void on_retrieveButton_clicked();

    void on_cancelButton_clicked();

    void on_comboBox_currentIndexChanged(int index);

    void onLineEditSearchValueClicked();

    void destinationPathChanged(const QString& path);

    void on_cancelRetrieveButton_clicked();

private:
    Ui::QueryRetrieveWidget* ui;
    QStandardItemModel* m_QueryStandardmodel;
    QStandardItemModel* m_RetrieveStandardmodel;
    CommonProgressBarFrame* m_ProgressBar;
    QProcess* m_RetrievingProcess;
    QMessageBox::Button m_ConfirmValue;
    int m_RetrieveDcmFileCount;
    bool m_ReadyClose;
    QStringList m_args;
    QStringList m_RetrieveStudyInstanceID;
    QStringList m_DestinationPathesList;
    QFileSystemWatcher m_FileSystemWatcher;
    QHash<QString, DcmQueryResponse> m_Responses;
    static const char* const m_AnimalString[];
    enum ANIMALSTRING
    {
        Animal_ID,
        Animal_Name
    };
    ScreenOrientationModel* m_Model;
    IStateManager* m_StateManager;
};

class ARCHIVESHARED_EXPORT DicomQueryTask : public QRunnable
{
public:
    DicomQueryTask(QueryRetrieveWidget* pWW)
        : m_pWidget(pWW)
    {
    }

private:
    void run()
    {
        m_pWidget->doDicomQuery();
    }

    QueryRetrieveWidget* m_pWidget;
};

class ARCHIVESHARED_EXPORT DicomRetrieveTask : public QRunnable
{
public:
    DicomRetrieveTask(QueryRetrieveWidget* pWW)
        : m_pWidget(pWW)
    {
    }

private:
    void run()
    {
        m_pWidget->doDicomRetrieve();
    }

    QueryRetrieveWidget* m_pWidget;
};

#endif // QUERYRETRIEVEWIDGET_H
