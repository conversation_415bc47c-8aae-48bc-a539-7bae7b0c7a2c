#include "systemusagelinux.h"
#include "util.h"

SystemUsageLinux::SystemUsageLinux()
{
}

SystemUsageLinux::~SystemUsageLinux()
{
}

unsigned long SystemUsageLinux::getCpuCoresCount()
{
    QString cpuNum = Util::getCmdOut("cat /proc/cpuinfo| grep 'cpu cores' | uniq | awk '{print $4}'");
    return cpuNum.toULong();
}

unsigned long SystemUsageLinux::getCpuThreadCount()
{
    QString cpuThread = Util::getCmdOut("cat /proc/cpuinfo| grep 'siblings' | uniq | awk '{print $3}'");
    return cpuThread.toULong();
}

int SystemUsageLinux::getCpuUsage()
{
    QString cpuRate = Util::getCmdOut("cat /proc/loadavg | awk '{print $1}'");
    int cpuUsage = 100 * cpuRate.toDouble();
    return cpuUsage;
}

int SystemUsageLinux::getCpuTemperature()
{
    QStringList hardwareList = Util::getCmdOut("cat /sys/class/thermal/thermal_zone*/type").split("\n");
    QStringList tempList = Util::getCmdOut("cat /sys/class/thermal/thermal_zone*/temp").split("\n");
    QString cpu_temp;
    for (int i = 0; i < hardwareList.size(); i++)
    {
        if (hardwareList[i] == "x86_pkg_temp")
        {
            cpu_temp = tempList[i];
        }
    }
    return cpu_temp.toInt() / 1000;
}

int SystemUsageLinux::getCurVolumeVal()
{
    QString volumeSystemStr = Util::getCmdOut("pactl list sinks | grep Volume | head -n1 | awk '{print $5}'");
    volumeSystemStr.replace("%", "");
    return volumeSystemStr.toInt();
}

bool SystemUsageLinux::setCurVolumeVal(int nVolumeVal)
{
    if (0 == Util::System("pactl set-sink-volume 1 " + QString::number(nVolumeVal) + "%"))
        return true;
    return false;
}

void SystemUsageLinux::getMemoryUsage(unsigned int& nMemTotal, unsigned int& nMemFree)
{
    QString memoryFree = Util::getCmdOut("free -m | awk 'NR==2{print $3}'");
    QString memoryTotal = Util::getCmdOut("free -m | awk 'NR==2{print $2}'");
    nMemFree = memoryFree.toUInt();
    nMemTotal = memoryTotal.toUInt();
}

bool SystemUsageLinux::playVideo(const QString& fileName)
{
    QString cmd = QString("aplay %1").arg(fileName);
    if (0 == Util::System(cmd))
        return true;
    return false;
}

//#endif
