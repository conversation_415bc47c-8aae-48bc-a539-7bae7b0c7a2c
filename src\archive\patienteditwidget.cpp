#include "patienteditwidget.h"
#include "appsetting.h"
#include "buttonsboxframe.h"
#include "dataaccesslayerhelper.h"
#include "model/patient.h"
#include "model/study.h"
#include "patientworkflow.h"
#include "patientworkflowmodel.h"
#include "ui_patienteditwidget.h"
#include "uiutil.h"
#ifdef USE_ADMINVIEW
#include "adminconfigmodel.h"
#endif
#include "util.h"
#include <QScreen>

const char* const PatientEditWidget::m_AnimalString[] = {QT_TRANSLATE_NOOP("PatientEditWidget", "Animal Information")};

PatientEditDialog::PatientEditDialog(IStateManager* stateManager, QWidget* parent, bool isHuman)
    : BaseInputAbleDialogFrame(parent, isHuman)
    , m_Child(NULL)
{
    setCategory("Patient");
    setShowTopWidget(true);
    setAutoZOrder(false);
    m_ShowButtonsBoxFrame = true;
    m_Child = new PatientEditWidget(stateManager, this);
    this->setContent(m_Child);
    this->setIsShowOk(false);
    UiUtil::moveWidgetToCenterOfPrimaryScreen(this);

    //    connect(this, SIGNAL(buttonClicked(int)), m_Child, SLOT(buttonClicked(int)));
    connect(m_Child, SIGNAL(closeCurrentWidget()), this, SLOT(reject()));
    connect(m_Child, SIGNAL(onlyJump()), this, SIGNAL(onlyJump()), Qt::DirectConnection);
    connect(m_Child, SIGNAL(entryArchiveState()), this, SLOT(reject()));
    connect(m_Child, SIGNAL(entryArchiveState()), this, SIGNAL(entryArchiveState()));
    connect(m_Child, SIGNAL(orientationChanged(bool)), this, SLOT(onOrientationChanged(bool)));
    connect(this, SIGNAL(clearPatientInfo()), m_Child, SLOT(onClearPatientInfo()));
    connect(this, SIGNAL(clearStudyInfo()), m_Child, SLOT(onClearStudyInfo()));
    connect(m_Child, SIGNAL(isNewPatientId(bool)), this, SIGNAL(isNewPatientId(bool)));
    connect(this, SIGNAL(tpButClicked(QString, QString, QString)), m_Child,
            SIGNAL(tpButClicked(QString, QString, QString)));

    titleColorUseQss("patientMenuTitleBar");
    containerColorUseQss("patientChildContainer");

    if (m_FullScreenShow)
    {
        this->setFixedSize(QGuiApplication::primaryScreen()->geometry().width(),
                           QGuiApplication::primaryScreen()->geometry().height());
    }
}

void PatientEditDialog::setPatient(Patient* patient)
{
    m_Child->setPatient(patient);
}

void PatientEditDialog::createPatient(Patient* patient)
{
    m_Child->createPatient(patient);
}

void PatientEditDialog::createStudy(Study* study)
{
    m_Child->createStudy(study);
}

void PatientEditDialog::setPatientWorkflow(PatientWorkflow* patientWorkflow)
{
    m_Child->setPatientWorkflow(patientWorkflow);
}

QString PatientEditDialog::lineEditPatientId()
{
    return m_Child->lineEditPatientId();
}

void PatientEditDialog::setPatietId(const QString& patientId)
{
    m_Child->setPatietId(patientId);
}

void PatientEditDialog::setCurrentWidgetEnabled(bool enabled)
{
    m_Child->setCurrentWidgetEnabled(enabled);
}

bool PatientEditDialog::isNameLengthValid() const
{
    return m_Child->isNameLengthValid();
}

QWidget* PatientEditDialog::parentPtr() const
{
    return m_Child;
}

void PatientEditDialog::showWorklist()
{
    m_Child->showWorklist();
}

void PatientEditDialog::onTPButClicked(const QString& category, const QString& butName, const QString& value)
{
    BaseInputAbleDialogFrame::onTPButClicked(category, butName, value);
    emit tpButClicked(category, butName, value);
}

void PatientEditDialog::onOrientationChanged(bool isHor)
{
    if (m_ButtonsBoxFrame != nullptr)
    {
        m_ButtonsBoxFrame->setIsHor(isHor);
    }
}

QString PatientEditDialog::mppsDcmFileName() const
{
    return m_Child->mppsDcmFileName();
}

void PatientEditDialog::clearMPPSDcmFileName()
{
    m_Child->clearMPPSDcmFileName();
}

void PatientEditDialog::showEvent(QShowEvent* e)
{
    BaseInputAbleDialogFrame::showEvent(e);
    UiUtil::moveWidgetToCenterOfPrimaryScreen(this);
}

PatientEditWidget::PatientEditWidget(IStateManager* stateManager, QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::PatientEditWidget)
    , m_PatientWorkflow(NULL)
    , m_Model(ScreenOrientationModel::getInstance())
{
    ui->setupUi(this);
    ui->widgetTop->setStateManager(stateManager);
    if (AppSetting::isAnimal())
    {
        ui->tabWidget->removeTab(7);
        ui->tabWidget->removeTab(6);
        ui->tabWidget->removeTab(4);
        ui->tabWidget->removeTab(2);
        ui->tabWidget->setTabText(1, tr("Rep"));
        connect(ui->widgetTop, SIGNAL(animalSpeciesIndexChanged(int)), this, SLOT(onAnimalSpeciesIndexChanged(int)));
    }

    m_BaseInputAbleDialogFrame = dynamic_cast<BaseInputAbleDialogFrame*>(parent);
    connect(ui->widgetTop, SIGNAL(clickedArchive()), this, SLOT(archiveButtonClicked()));
    connect(ui->widgetTop, SIGNAL(isNewPatientId(bool)), this, SIGNAL(isNewPatientId(bool)));
    connect(ui->widgetTop, SIGNAL(physiciansNameSetted(QString)), this, SLOT(onPhysiciasNameSetted(QString)));
    connect(ui->widgetTop, SIGNAL(accessionSetted(QString)), this, SLOT(onAccessionSetted(QString)));
    connect(this, SIGNAL(tpButClicked(QString, QString, QString)), this,
            SLOT(onTPButClicked(QString, QString, QString)));
    connect(ui->widgetTop, &PatientTopWidget::updateStudyInfo, this, &PatientEditWidget::setPatient);

    // Modification: Change the main interface button from Cancle to Exit
    if (AppSetting::isAnimalAndChinese())
    {
        m_ButtonsText << QT_TR_NOOP("New Animal") << QT_TR_NOOP("New Study") << QT_TR_NOOP("End Study")
                      << QT_TR_NOOP("OK") << QT_TR_NOOP("Exit");
        Util::setPatient2Animal(this, "PatientEditWidget", m_AnimalString[Animal_Information]);
    }
    else
    {
        m_ButtonsText << QT_TR_NOOP("New Patient") << QT_TR_NOOP("New Study") << QT_TR_NOOP("End Study")
                      << QT_TR_NOOP("OK") << QT_TR_NOOP("Exit");
    }

    m_ButtonsName << "newPatient"
                  << "newStudy"
                  << "endStudy"
                  << "OK"
                  << "Exit";
}

PatientEditWidget::~PatientEditWidget()
{
    delete ui;
}

void PatientEditWidget::setPatient(Patient* patient)
{
    if (patient != NULL)
    {
        ui->widgetTop->setPatient(patient);
        if (patient->firstStudy() != NULL)
        {
            ui->widgetMiddle->setStudy(patient->firstStudy());
            ui->tabWidget->setCurrentIndex(patient->firstStudy()->StudyType());
        }
    }
    else
    {
        onClearPatientInfo();
    }
}

void PatientEditWidget::createPatient(Patient* patient)
{
    if (patient != NULL)
    {
        ui->widgetTop->flawlessPatient(patient);
        if (patient->firstStudy() != NULL)
        {
            Study* study = patient->firstStudy();
            if (NULL != study)
            {
                study->setStudyType(ui->tabWidget->currentIndex());
            }
            ui->widgetMiddle->flawlessStudy(study);
        }
    }
}

void PatientEditWidget::createStudy(Study* study)
{
    if (NULL != study)
    {
        study->setStudyType(ui->tabWidget->currentIndex());
    }
    ui->widgetMiddle->flawlessStudy(study);
}

void PatientEditWidget::setPatientWorkflow(PatientWorkflow* patientWorkflow)
{
    m_PatientWorkflow = patientWorkflow;
}

QString PatientEditWidget::lineEditPatientId()
{
    return ui->widgetTop->patientId();
}

void PatientEditWidget::setCurrentWidgetEnabled(bool enabled)
{
    ui->widgetTop->setCurrentWidgetEnabled(enabled);
    ui->widgetMiddle->setCurrentWidgetEnabled(enabled);
    //    m_BaseInputAbleDialogFrame->setButtonEnabled(enabled, PatientEditDialog::button_patient);
    //    m_BaseInputAbleDialogFrame->setButtonEnabled(enabled, PatientEditDialog::button_study);
    //    m_BaseInputAbleDialogFrame->setButtonEnabled(enabled, PatientEditDialog::button_end);
    //    m_BaseInputAbleDialogFrame->setButtonEnabled(enabled, PatientEditDialog::button_ok);
    //    m_BaseInputAbleDialogFrame->setButtonEnabled(enabled, PatientEditDialog::button_);
}

void PatientEditWidget::setPatietId(const QString& patientId)
{
    ui->widgetTop->setPatientId(patientId);
}

bool PatientEditWidget::isNameLengthValid() const
{
    return ui->widgetTop->isNameLengthValid();
}

void PatientEditWidget::showWorklist()
{
    ui->widgetTop->showWorklist();
}

QString PatientEditWidget::mppsDcmFileName() const
{
    return ui->widgetTop->mppsDcmFileName();
}

void PatientEditWidget::clearMPPSDcmFileName()
{
    ui->widgetTop->clearMPPSDcmFileName();
}

bool PatientEditWidget::isHor()
{
    return m_Model->isHor();
}

void PatientEditWidget::onClearPatientInfo()
{
    ui->widgetTop->createId();
    ui->widgetTop->clearPatientInfo();
    ui->widgetMiddle->clearStudyInfo();
}

void PatientEditWidget::onClearStudyInfo()
{
    ui->widgetTop->clearStudyInfo();
    ui->widgetMiddle->clearStudyInfo();
}

void PatientEditWidget::onAnimalSpeciesIndexChanged(int index)
{
    ui->widgetMiddle->setAnimalSpeciesIndex(index);
}

void PatientEditWidget::retranslateUi()
{
    ui->retranslateUi(this);
    if (AppSetting::isAnimal())
    {
        ui->tabWidget->setTabText(1, tr("Rep"));
    }

    if (AppSetting::isAnimalAndChinese())
    {
        m_BaseInputAbleDialogFrame->setButtonText(QT_TR_NOOP("New Animal"), PatientEditDialog::button_patient);
        Util::setPatient2Animal(this, "PatientEditWidget", m_AnimalString[Animal_Information]);
    }
    else
    {
        m_BaseInputAbleDialogFrame->setButtonText(QT_TR_NOOP("New Patient"), PatientEditDialog::button_patient);
    }
}

void PatientEditWidget::orientationChanged(Qt::ScreenOrientation orientation)
{
    QString className(this->metaObject()->className());
    OrientationConfigItem config;
    m_Model->getInfosByClassName(orientation, className, config);
    UiUtil::ScreenHVChangedUpdateUI(this, ui->gridLayout, config);
#ifdef USE_SIMULATEORIENTATION
    emit orientationChanged(m_Model->isHor());
#else
    emit orientationChanged(orientation == Qt::LandscapeOrientation);
#endif
}

void PatientEditWidget::archiveButtonClicked()
{
    emit onlyJump();
    emit entryArchiveState();
}

void PatientEditWidget::on_tabWidget_currentChanged(int index)
{
    ui->tabWidget->currentWidget()->layout()->addWidget(ui->widgetMiddle);
    ui->widgetMiddle->setCurrentIndex(index);
}

void PatientEditWidget::onPhysiciasNameSetted(const QString& value)
{
    ui->widgetMiddle->setPhysiciansName(value);
}

void PatientEditWidget::onAccessionSetted(const QString& value)
{
    ui->widgetMiddle->setAccession(value);
}

void PatientEditWidget::onTPButClicked(const QString& category, const QString& butName, const QString& value)
{
    qDebug() << "## PatientEditWidget::onTPButClicked " << category << butName << value;
    QStringList btnames = butName.split(":", Qt::SkipEmptyParts);

    if (category == "Patient" && btnames.size() > 2 && btnames.at(0) == "Patient")
    {
        if (btnames.last() == "Archive")
        {
            archiveButtonClicked();
        }
        else if (btnames.last() == "WorkList")
        {
            showWorklist();
        }
        else if (btnames.last() == "QR")
        {
            ui->widgetTop->on_pushButtonQR_clicked();
        }
    }
}
