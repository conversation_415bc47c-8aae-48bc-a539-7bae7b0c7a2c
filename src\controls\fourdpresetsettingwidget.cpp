#include "fourdpresetsettingwidget.h"
#include "fourdparascontainer.h"
#include "ui_fourdparassettingbasewidget.h"

FourDPresetSettingWidget::FourDPresetSettingWidget(QWidget* parent)
    : FourDParasSettingBaseWidget(parent)
    , m_<PERSON>(new FourDPresetWidget(this))
{
    addWidget(m_Child);
    m_SettingName = QString("FourDParasSetting");
    m_ParaType = FourDParasInfo::FourDPreset;
}

void FourDPresetSettingWidget::initalize()
{
    m_Child->initalize();
    ui->addButton->setEnabled(true);
    FourDParasSettingBaseWidget::initalize();
    connect(m_Child, SIGNAL(fourDPresetParasChanged()), this, SIGNAL(fourDPresetParasChanged()));
}

void FourDPresetSettingWidget::setSonoParameters(SonoParameters* sonoParameters)
{
    m_Child->setSonoParameters(sonoParameters);
}

void FourDPresetSettingWidget::currenIndexChanged(const QString& arg1)
{
    if (ui->comboBox->currentIndex() == 0)
    {
        ui->renameButton->setEnabled(false);
        ui->deleteButton->setEnabled(false);
    }
    else if (ui->comboBox->currentIndex() > 0)
    {
        ui->renameButton->setEnabled(true);
        ui->deleteButton->setEnabled(true);
    }
    m_Child->setFourDPresetPara(oneParasT<FourDParasInfo::FourDPresetPara>(arg1));
}

bool FourDPresetSettingWidget::contains(const QString& name)
{
    return isExistNameT<FourDParasInfo::FourDPresetPara>(name);
}

void FourDPresetSettingWidget::add(const QString& name)
{
    addOneParaT<FourDParasInfo::FourDPresetPara>(name, m_Child->fourDPresetPara());
    FourDParasManager::instance().saveFourDPresetParas();
    m_Child->addComboxItem(name);
    emit fourDPresetParasChanged();
}

void FourDPresetSettingWidget::save(const QString& name)
{
    add(name);
}

void FourDPresetSettingWidget::del(const QString& name)
{
    deleteOneParaT<FourDParasInfo::FourDPresetPara>(name);
    FourDParasManager::instance().saveFourDPresetParas();
    m_Child->removeComboxItem(ui->comboBox->currentIndex());
    emit fourdPresetParasDeleted(ui->comboBox->currentIndex());
}

bool FourDPresetSettingWidget::rename(const QString& oldName, const QString& newName)
{
    if (!isExistNameT<FourDParasInfo::FourDPresetPara>(newName))
    {
        renameT<FourDParasInfo::FourDPresetPara>(oldName, newName);
        m_Child->renameComboxItemText(ui->comboBox->currentIndex(), newName);
        return true;
    }
    else
    {
        return false;
    }
}

QStringList FourDPresetSettingWidget::names()
{
    return namesT<FourDParasInfo::FourDPresetPara>();
}
