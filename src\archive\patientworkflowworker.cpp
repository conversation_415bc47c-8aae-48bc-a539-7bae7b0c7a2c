#include "patientworkflowworker.h"
#include "patient.h"
#include "packagesmeasurement.h"
#include "packagemeasurement.h"
#include "measresult.h"
#include "modeldirectorygetter.h"
#include <QDebug>
#include "applogger.h"
#include "ibeamformer.h"
#include "dicomsrmodels.h"
#include "srmeasresults.h"
#include "dicomsrtool.h"
#include "dicomtaskmanager.h"
#include "util.h"

PatientWorkflowWorker::PatientWorkflowWorker(QObject* parent)
    : QObject(parent)
    , m_PackagesMeasurement(NULL)
    , m_BeamFormer(NULL)
    , m_DicomTaskManager(NULL)
{
}

void PatientWorkflowWorker::setPackagesMeasurement(PackagesMeasurement* value)
{
    m_PackagesMeasurement = value;
}

void PatientWorkflowWorker::setBeamFormer(IBeamFormer* value)
{
    m_BeamFormer = value;
}

void PatientWorkflowWorker::setDicomTaskManager(DicomTaskManager* value)
{
    m_DicomTaskManager = value;
}

void PatientWorkflowWorker::doEnd(const Patient& patient, bool appExit)
{
    TimeLogger tl;
    if (m_PackagesMeasurement != NULL)
    {
        PackagesMeasResults results = m_PackagesMeasurement->packagesMeasuredResults(false);
        if (results.hasResult())
        {
            if (!m_BeamFormer->isFrozen())
            {
                m_BeamFormer->suspendRead();
            }

            results.save(PatientPath::instance().measFilePath(patient));
            Util::sync();

            // 系统退出时，暂时不进行sr发送
            if (!appExit)
            {
                PackageMeasurement* pkgMeasurement = m_PackagesMeasurement->currentPackage();
                if (pkgMeasurement != NULL)
                {
                    bool isSupportSr = SRMeasResult::isSupportSR(pkgMeasurement->region());
                    if (!isSupportSr)
                    {
                        return;
                    }
                }

                DicomSrTool srTool(&patient, &results, m_DicomTaskManager);
                srTool.run();

                if (!m_BeamFormer->isFrozen())
                {
                    m_BeamFormer->resumeRead();
                }
            }
        }
    }

    tl.logger("PatientWorkflowWorker doEnd");
}

void PatientWorkflowWorker::doContinue(const Patient& patient)
{
    TimeLogger tl;
    if (m_PackagesMeasurement != NULL)
    {
        PackagesMeasResults results;
        if (results.load(PatientPath::instance().measFilePath(patient)))
        {
            m_PackagesMeasurement->setPackagesResults(results);
        }
    }
    tl.logger("PatientWorkflowWorker doContinue");
}
