#ifndef SONOCARDIACALG_H
#define SONOCARDIACALG_H

#include "algorithm_global.h"
#include <QLibrary>
#ifdef USE_SONOCARDIAC
#include "SonoCardiac64.h"
#else
typedef int ERROR_CODE;
#define ERROR_CODE_OK 0
#define ERROR_CODE_UNKNOWN -1
#define ERROR_CODE_START_SERVER -2
#define ERROR_CODE_KILL_SERVER -3
#define ERROR_CODE_SERVER_TIMEOUT -4

typedef int CARDIAC_VIEW_TYPE;
#define CARDIAC_VIEW_TYPE_UKNOWN 0
#define CARDIAC_VIEW_TYPE_A2C 1
#define CARDIAC_VIEW_TYPE_A3C 2
#define CARDIAC_VIEW_TYPE_A4C 3

typedef int CARDIAC_FEATURE_TYPE;
#define CARDIAC_FEATURE_TYPE_LV 0
#define CARDIAC_FEATURE_TYPE_RV 1
#define CARDIAC_FEATURE_TYPE_LA 2
#define CARDIAC_FEATURE_TYPE_RA 3
#define CARDIAC_FEATURE_TYPE_MV 4
#define CARDIAC_FEATURE_TYPE_TV 5

#define MAX_CARDIAC_FEATURE_NUM 20

typedef int CARDIAC_QUALITY;
#define CARDIAC_QUALITY_BAD 0
#define CARDIAC_QUALITY_MIDDLE 1
#define CARDIAC_QUALITY_GOOD 2

typedef struct _InitInfo64
{
    int nImageWidth;
    int nImageHeight;
    char chAlgLogFolderPath[256];
    char chModelsFolderPath[256];
    char chReserve[400];
} InitInfo64;

typedef struct _UserInfo
{
    CARDIAC_VIEW_TYPE nCardiacViewType;
    int bCardiacFeatureSwitch;
    int bCardiacQualitySwitch;
    char chReserve[40];
} UserInfo;

typedef struct _CardiacFeatureInfo
{
    CARDIAC_FEATURE_TYPE nCardiacFeatureType;
    int nCenterX;
    int nCenterY;
    float fConfidence;
    char chReserve[40];
} CardiacFeatureInfo;

typedef struct _SonoCardiacResult
{
    CARDIAC_VIEW_TYPE nCardiacViewType;
    int nCardiacFeatureNum;
    CardiacFeatureInfo stArrCardiacFeatureInfo[MAX_CARDIAC_FEATURE_NUM];
    CARDIAC_QUALITY nCardiacQuality;
    char chReserve[40];
} SonoCardiacResult;

typedef void* SonoCardiacHandle;
#endif
#include "virtualalg.h"

class ALGORITHMSHARED_EXPORT SonoCardiacAlg : public VirtualAlg
{
    friend class AlgInstance;

private:
    SonoCardiacAlg();

public:
    typedef int (*createSonoCardiacHandle)(SonoCardiacHandle* pSonoCardiacHandle, InitInfo64* pInitInfo64);
    typedef int (*processSonoCardiac)(SonoCardiacHandle hSonoCardiacHandle, UserInfo* pUserInfo, int nImageWidth,
                                      int nImageHeight, int nImageDataSize, unsigned char* pImageData,
                                      SonoCardiacResult* pSonoCardiacResult);
    typedef int (*releaseSonoCardiacHandle)(SonoCardiacHandle* pSonoCardiacHandle);

    int CreateSonoCardiacHandle(SonoCardiacHandle* pSonoCardiacHandle, InitInfo64* pInitInfo64);
    int ProcessSonoCardiac(SonoCardiacHandle hSonoCardiacHandle, UserInfo* pUserInfo, int nImageWidth, int nImageHeight,
                           int nImageDataSize, unsigned char* pImageData, SonoCardiacResult* pSonoCardiacResult);
    int ReleaseSonoCardiacHandle(SonoCardiacHandle* pSonoCardiacHandle);

    SonoCardiacHandle m_HSonoCardiacHandle;
    InitInfo64 m_HInitInfo;

private:
    bool m_IsError;
    createSonoCardiacHandle m_CreateContext;
    processSonoCardiac m_Process;
    releaseSonoCardiacHandle m_RemoveContext;
};

#endif // SONOCARDIACALG_H
