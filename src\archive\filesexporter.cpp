#include "filesexporter.h"
#include "modeldirectorygetter.h"
#include "patientworkflow.h"
#include "dicomtaskmanager.h"
#include "diskselectionwidget.h"
#include "util.h"
#include "resource.h"
#include <QSettings>
#include <QDir>
#include "smbnetfscontroller.h"
#include "messageboxframe.h"
#include "setting.h"
#include "threadmodel.h"
#include "remindgdprwidget.h"
#include "gdprutility.h"

FilesExporter::FilesExporter(PatientWorkflow* workFlow, DicomTaskManager* taskManager, QObject* parent)
    : QObject(parent)
    , m_PatientWorkflow(workFlow)
    , m_DicomTaskManager(taskManager)
    , m_ThreadFinishedState(true)
    , m_IsGDPR(false)
    , m_ColorMapManager(NULL)
{
}

void FilesExporter::doExport(const QStringList& fileNames, bool isGDPR)
{
    m_IsGDPR = isGDPR;
    if (Setting::instance().defaults().isAutoExportToUDisk())
    {
        exportToUDisk(fileNames);
    }

    if (Setting::instance().defaults().isAutoExportToNetWork())
    {
        exportToNetWork(fileNames);
    }
}

bool FilesExporter::threadFinishedState() const
{
    return m_ThreadFinishedState;
}

void FilesExporter::resetThreadFinishedState()
{
    m_ThreadFinishedState = true;
}

void FilesExporter::setColorMapManager(IColorMapManager* colorMapManager)
{
    m_ColorMapManager = colorMapManager;
}

void FilesExporter::exportToUDisk(const QStringList& fileNames)
{
    QString udiskDir = DiskSelectionDialog::getFileUDiskPath("", false);
    if (!udiskDir.isEmpty())
    {
        bool isGDPR = false;
        if (!processGDPRFile(fileNames, isGDPR))
        {
            return;
        }

        QString targetDir = PatientPath::instance().exportStudyPath(m_PatientWorkflow->patient(), udiskDir, isGDPR);

        QDir dir(targetDir);
        if (!dir.exists())
        {
            Util::Mkdir(targetDir);
        }

        if (QDir(targetDir).exists())
        {
            copyFile(fileNames, targetDir, true);
        }
        else
        {
            onThreadFinished(false);
        }
    }
}

void FilesExporter::exportToNetWork(const QStringList& fileNames)
{
    QStringList serviceNames = SmbNetFsController::instance().serviceNames();
    int defaultIndex = SmbNetFsController::instance().currentDefaultIndex();
    QString serviceName;
    if (defaultIndex >= 0 && defaultIndex < serviceNames.count())
    {
        serviceName = serviceNames[defaultIndex];
    }

    if (serviceName.isEmpty())
    {
        //        MessageBoxFrame::warning(tr("Could not find service!"));
        return;
    }

    // 每次发送前检查是否成功挂载，并且重置opened标志
    // 目的是为了防止在程序运行过程中，人为的或者其他因素挂载或者卸载该目录.，导致发送不成功
    SmbNetFsController::instance().checkOpened(SmbNetFsController::instance().getServeNameToDirName(serviceName));

    if (!SmbNetFsController::instance().getDirNameIsMountd(serviceName))
    {
        SmbNetFsController::instance().mountDirAndInsMod(serviceName);
    }

    if (!SmbNetFsController::instance().getDirNameIsMountd(serviceName))
    {
        //        MessageBoxFrame::warning(tr("Can not use network storage share!"));
        onThreadFinished(false);
        return;
    }

    bool isGDPR = false;
    if (!processGDPRFile(fileNames, isGDPR))
    {
        return;
    }

    QString dirName = PatientPath::instance().exportStudyPath(m_PatientWorkflow->patient(), QString(), isGDPR);

    QString targetDir = SmbNetFsController::instance().fileDirName(serviceName, dirName);

    QDir dir(targetDir);
    if (!dir.exists())
    {
        Util::Mkdir(targetDir);
    }

    if (QDir(targetDir).exists())
    {
        copyFile(fileNames, targetDir, false);
    }
    else
    {
        onThreadFinished(false);
    }
}

ImageProcessedCopy::ImageType FilesExporter::imageTypeForExported() const
{
    QSettings settings(Resource::optionsforTransmittedImagesIniName(), QSettings::IniFormat);

    settings.beginGroup(Resource::colormapGeneralDevice);
    QString screenSuffix = settings.value("ScreenSuffix").toString();
    settings.endGroup();

    if (screenSuffix == Resource::jpgSuffix)
    {
        return ImageProcessedCopy::JPG;
    }
    else
    {
        return ImageProcessedCopy::BMP;
    }
}

void FilesExporter::copyFile(const QStringList& fileNames, const QString& targetDir, bool checkDiskSpace)
{
    ThreadModel* threadModel = new ThreadModel();
    ImageProcessedCopy* copy = new ImageProcessedCopy(m_ColorMapManager, threadModel);
    threadModel->setICopy(copy);
    threadModel->setNeedCreateEmptyCopy(true);
    connect(threadModel, SIGNAL(threadFinished(bool)), this, SLOT(onThreadFinished(bool)));

    copy->setTaskManager(m_DicomTaskManager);
    copy->setIsToAvi(true);
    copy->setImageType(imageTypeForExported());
    copy->setPatient(m_PatientWorkflow->patient());

    if (Setting::instance().defaults().isExportGDPR())
    {
        copy->setIsGDPR(Setting::instance().defaults().isRemindExportGDPR() ? m_IsGDPR : true);
    }

    bool ret = threadModel->copy(fileNames, targetDir, checkDiskSpace);
    if (!ret)
    {
        //磁盘空间满,不会启动拷贝线程导致指针无法delete,所以这里手动delete
        delete threadModel;
        threadModel = NULL;
    }
}

bool FilesExporter::processGDPRFile(const QStringList& fileNames, bool& isGDPR)
{
    if (Setting::instance().defaults().isExportGDPR())
    {
        if (!Setting::instance().defaults().isRemindExportGDPR() ||
            (Setting::instance().defaults().isRemindExportGDPR() && m_IsGDPR))
        {
            if (!GDPRUtility::isSupportGDPR(fileNames))
            {
                MessageBoxFrame::warning(QObject::tr("Some images cannot be de-identified!"));
                return false;
            }
            else
            {
                isGDPR = true;
            }
        }
    }
    return true;
}

void FilesExporter::onThreadFinished(bool isSuccessful)
{
    // MessageBoxFrame 阻塞GUI线程导致保存电影动画不消失，这里改为异步，动画消失后再弹出警告
    m_ThreadFinishedState = isSuccessful;

    ThreadModel* threadModel = qobject_cast<ThreadModel*>(sender());
    if (threadModel != NULL)
    {
        threadModel->deleteLater();
    }
}
