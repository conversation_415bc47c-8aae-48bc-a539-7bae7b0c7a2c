#ifndef SYSTEMHINTDEFAULTWIDGET_H
#define SYSTEMHINTDEFAULTWIDGET_H

#include "basewidget.h"

namespace Ui
{
class SystemHintDefaultWidget;
}

class SystemHintModel;

class SystemHintDefaultWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit SystemHintDefaultWidget(QWidget* parent = 0);
    ~SystemHintDefaultWidget();

    void setModel(SystemHintModel* value);

private slots:
    void onWorkStatusChanged();
    void onTBStatusChanged();

protected:
    void retranslateUi();

private:
    Ui::SystemHintDefaultWidget* ui;
    SystemHintModel* m_Model;
};

#endif // SYSTEMHINTDEFAULTWIDGET_H
