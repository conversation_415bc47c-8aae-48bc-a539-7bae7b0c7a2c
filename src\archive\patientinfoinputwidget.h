#ifndef PATIENTINFOINPUTWIDGET_H
#define PATIENTINFOINPUTWIDGET_H

#include <QWidget>
#include <QList>
#include <QString>
#include "basewidget.h"
#include "archive_global.h"
#include "inputunitgroupwidget.h"

namespace Ui
{
class PatientInfoInputWidget;
}

class ARCHIVESHARED_EXPORT PatientInfoInputModel
{
public:
    PatientInfoInputModel();

    void setUnitType(const int value);
    int unitType()
    {
        return m_UnitType;
    }
    void setCurUnitIndex(const int index);
    int curUnitIndex() const;
    void setUnits(const QList<int>& units);
    const QList<int>& units() const;
    int curUnit();
    int curUnitCount();
    void setValue(const double value, const int srcUnit);
    QList<QVariant> curValue();

private:
    int m_UnitType;
    int m_CurUnitIndex;
    QList<int> m_Units;
    QList<QVariant> m_Values;
    QList<QList<int>> m_MaxValue;
};

class ARCHIVESHARED_EXPORT PatientInfoInputWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit PatientInfoInputWidget(QWidget* parent = NULL);
    ~PatientInfoInputWidget();

    void setModel(const PatientInfoInputModel& model);
    void setEnabled(const bool enabled);
    double curValue(const int tarUnit);
    void setValue(const double value, const int srcUnit);
    QStringList curTextValue();
    void setTextValue(const QStringList& textValues);
    void updateView();
    void setUnitMinWidth(const int width);

signals:
    void textEdited();

protected:
    void retranslateUi();

private:
    Ui::PatientInfoInputWidget* ui;
    PatientInfoInputModel m_Model;
    QList<InputUnitGroupWidget*> m_InputWidgets;
};

#endif // PATIENTINFOINPUTWIDGET_H
