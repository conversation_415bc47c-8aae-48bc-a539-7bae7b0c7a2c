#include "userinfo.h"

UserInfo::UserInfo()
    : m_authorityId(0)
    , m_isChanged(false)
{
}

UserInfo::UserInfo(const QString& userid, const QString& username, const QString& pwd, const int authorityid,
                   bool ischanged)
    : m_userId(userid)
    , m_userName(username)
    , m_password(pwd)
    , m_authorityId(authorityid)
    , m_isChanged(ischanged)
{
}

// UserInfo &UserInfo::operator =(const UserInfo &user)
//{
//    this->setUserId(user.userId());
//    this->setUserName(user.userName());
//    this->setPassword(user.password());
//    this->setAuthorityId(user.authorityId());
//    this->setIsChanged(user.isChanged());

//    return *this;
//}

QString UserInfo::userId() const
{
    return m_userId;
}

void UserInfo::setUserId(const QString& userId)
{
    m_userId = userId;
}
QString UserInfo::userName() const
{
    return m_userName;
}

void UserInfo::setUserName(const QString& userName)
{
    m_userName = userName;
}
QString UserInfo::password() const
{
    return m_password;
}

void UserInfo::setPassword(const QString& password)
{
    m_password = password;
}
int UserInfo::authorityId() const
{
    return m_authorityId;
}

void UserInfo::setAuthorityId(int authorityId)
{
    m_authorityId = authorityId;
}
bool UserInfo::isChanged() const
{
    return m_isChanged;
}

void UserInfo::setIsChanged(bool isChanged)
{
    m_isChanged = isChanged;
}
