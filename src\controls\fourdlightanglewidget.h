#ifndef FOURDLIGHTANGLEWIDGET_H
#define FOURDLIGHTANGLEWIDGET_H

#include "controls_global.h"
#include "fourdparasetbasewidget.h"
#include "fourdparasinfo.h"

namespace Ui
{
class FourDLightAngleWidget;
}

class CONTROLSSHARED_EXPORT FourDLightAngleWidget : public FourDParaSetBaseWidget
{
    Q_OBJECT
public:
    explicit FourDLightAngleWidget(QWidget* parent = 0);
    ~FourDLightAngleWidget();
    virtual void initalize();
    void setFourDLightAnglePara(const FourDParasInfo::FourDLightAnglePara& para);
    FourDParasInfo::FourDLightAnglePara fourDLightAnglePara();

protected:
    void retranslateUi();

private:
    Ui::FourDLightAngleWidget* ui;
    FourDParasInfo::FourDLightAnglePara m_FourDLightAnglePara;
};

#endif // FOURDLIGHTANGLEWIDGET_H
