<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FourDRenderModeWidget</class>
 <widget class="QWidget" name="FourDRenderModeWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>341</width>
    <height>242</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>RenderMode</string>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="label">
        <property name="text">
         <string>VirtualHD</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="BaseComboBox" name="comboBoxVirtualHD"/>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_2">
        <property name="text">
         <string>Surface</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="BaseComboBox" name="comboBoxSurface"/>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_3">
        <property name="text">
         <string>Vessel</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="BaseComboBox" name="comboBoxVessel"/>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_4">
        <property name="text">
         <string>XRay</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="BaseComboBox" name="comboBoxXRay"/>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="label_5">
        <property name="text">
         <string>Skeleton</string>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="BaseComboBox" name="comboBoxSkeleton"/>
      </item>
      <item row="5" column="0">
       <widget class="QLabel" name="label_6">
        <property name="text">
         <string>DetphView</string>
        </property>
       </widget>
      </item>
      <item row="5" column="1">
       <widget class="BaseComboBox" name="comboBoxDepthView"/>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>27</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BaseComboBox</class>
   <extends>QComboBox</extends>
   <header>basecombobox.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
