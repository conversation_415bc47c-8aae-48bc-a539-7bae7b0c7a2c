/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#include "burnintest.h"
#include "abstractstate.h"
#include "appsetting.h"
#include "infostruct.h"
#include "istatemanager.h"
#include "messageboxframe.h"
#include "probeselectionframe.h"
#include "resource.h"
#include "stateeventnames.h"
#include <QSettings>
#include <QVector>

#define EVENT_INTERVAL_TIME 5000
#define EVENT_START_INTERVAL_TIME 2000

BurnInTest::BurnInTest(QObject* parent)
    : QObject(parent)
    , m_probeFrame(NULL)
    , m_currentProbeIndex(1)
    , m_probeNum(AppSetting::socketCount())
    , m_saveImageCount(0)
    , m_hasStarted(false)
{
    startTimer().setSingleShot(true);
    connect(&startTimer(), SIGNAL(timeout()), this, SLOT(onStartTimerOut()));

    m_autoTest.setTestMachine(this);

    QStringList eventList = QStringList() << StateEventNames::Probe << StateEventNames::StoreImage;

    m_autoTest.setSpecialEventList(eventList);
}

void BurnInTest::handleProbeEvent()
{
    disconnect(&m_autoTest.eventTimer(), SIGNAL(timeout()), this, SLOT(handleProbeEvent()));
    m_autoTest.connectProcessTask();

    selectProbeExam();

    m_autoTest.setIsHandleSpecialEventThatNeedsWait(false);
    m_autoTest.tryToNextTask();
}

void BurnInTest::selectProbeExam()
{
    const QVector<ProbeDataInfo>& probes = m_probeFrame->currentProbes();
    for (int i = 0; i < probes.size(); i++)
    {
        int idx = (m_currentProbeIndex + i) % probes.size();
        if (idx < probes.size() && !probes[idx].isNull())
        {
            m_currentProbeIndex = idx;
            m_probeFrame->setActiveSocket(m_currentProbeIndex);
            m_probeFrame->setBurnInItemOrFirstOne();
            m_probeFrame->confirmed();
            m_currentProbeIndex++;
            break;
        }
    }
}

void BurnInTest::handleSpecialEvent(const QString& event)
{
    if (event == StateEventNames::Probe)
    {
        if (m_probeNum < 2)
        {
            return;
        }
        else
        {
            m_autoTest.disconnectProcessTask();
            connect(&m_autoTest.eventTimer(), SIGNAL(timeout()), this, SLOT(handleProbeEvent()));

            m_StateManager->postEvent(event);

            m_autoTest.eventTimer().start(EVENT_INTERVAL_TIME);

            m_autoTest.setIsHandleSpecialEventThatNeedsWait(true);
        }
    }
    else if (event == StateEventNames::StoreImage)
    {
        m_StateManager->postEvent(event);
        m_saveImageCount++;
        m_imageModeNames << m_StateManager->currentState()->name();
    }
}

void BurnInTest::onStartTimerOut()
{
    if (!hasStarted())
    {
        m_StateManager->postEvent(StateEventNames::Probe);

        startTimer().start(EVENT_INTERVAL_TIME);

        setHasStarted(true);
    }
    else
    {
        m_probeFrame->setBurnInItemOrFirstOne();
        m_probeFrame->confirmed(true);

        m_autoTest.startTest();
    }
}

void BurnInTest::handleFinishEvent()
{
    int time = m_autoTest.currentTestTime();

    if (m_saveImageCount > 0)
    {

        QString inf = tr("Current test total time is %1s, and save %2 images.\n")
                          .arg(QString::number(time))
                          .arg(QString::number(m_saveImageCount));
        inf += tr("Do you want to send images to U disk?");

        emit sendCurrentFiles(inf);
    }
}

void BurnInTest::resetFlags()
{
    m_saveImageCount = 0;
    m_imageModeNames.clear();
    setHasStarted(false);
    m_currentProbeIndex = 0;

    emit emitResetFlags();
}

void BurnInTest::setHasStarted(bool value)
{
    m_hasStarted = value;
}

bool BurnInTest::hasStarted() const
{
    return m_hasStarted;
}

bool BurnInTest::startTest()
{
    const QVector<ProbeDataInfo>& probes = m_probeFrame->currentProbes();
    bool isValidProbe = false;
    for (int i = 0; i < probes.size(); i++)
    {
        if (!probes[i].isNull())
        {
            isValidProbe = true;
            break;
        }
    }

    if (!isValidProbe)
    {
        return false;
    }

    if (m_autoTest.isIniValid())
    {
        if (m_probeNum < 2)
        {
            m_autoTest.startTest();
        }
        else
        {
            startTimer().start(EVENT_START_INTERVAL_TIME);
        }

        return true;
    }
    else
    {
        MessageBoxFrame::warning(0, QString(), tr("There is no valid autotest script!"));
        return false;
    }
}

void BurnInTest::stopTest()
{
    m_autoTest.stopTest();
}

void BurnInTest::setProbeFrame(BaseProbeSelectionChild* value)
{
    m_probeFrame = value;
}

void BurnInTest::setStateManager(IStateManager* value)
{
    m_StateManager = value;
}

const QStringList& BurnInTest::modeNames() const
{
    return m_imageModeNames;
}

void BurnInTest::updateIniFiles()
{
    m_autoTest.updateIniFiles();
}

void BurnInTest::handleNormalEvent(const QString& event)
{
    m_StateManager->postEvent(event);
}

QTimer& BurnInTest::startTimer()
{
    return m_startTimer;
}
