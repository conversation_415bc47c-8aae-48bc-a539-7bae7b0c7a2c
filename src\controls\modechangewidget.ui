<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ModeChangeWidget</class>
 <widget class="QFrame" name="ModeChangeWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>388</width>
    <height>95</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Frame</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QFrame{
	color : white;
	border-radius : 8px;
    background-color : rgb(46, 46, 46);
    border-top: 2px solid rgba(106, 106, 106, 255);
    border-left: 2px solid  rgba(76, 76, 76, 255);
    border-right: 2px solid rgba(76, 76, 76, 255);     	   
	border-bottom: 2px solid rgba(76, 76, 76, 255);
}</string>
  </property>
  <property name="frameShape">
   <enum>QFrame::StyledPanel</enum>
  </property>
  <property name="frameShadow">
   <enum>QFrame::Raised</enum>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="margin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="1">
    <widget class="QToolButton" name="toolBtn_Up">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>0</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgba(255, 255, 255, 0);</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="icon">
      <iconset resource="../resource/res.qrc">
       <normaloff>:/images/up_disable.png</normaloff>:/images/up_disable.png</iconset>
     </property>
     <property name="autoRaise">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item row="1" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <widget class="QLabel" name="label_preMode">
       <property name="minimumSize">
        <size>
         <width>37</width>
         <height>0</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">border: 0 pix;</string>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Plain</enum>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="toolBtn_Left">
       <property name="styleSheet">
        <string notr="true">background-color: rgba(255, 255, 255, 0);</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../resource/res.qrc">
         <normaloff>:/images/left_scroll_disable.png</normaloff>:/images/left_scroll_disable.png</iconset>
       </property>
       <property name="autoRaise">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="1">
    <widget class="QLabel" name="label_curMode">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>0</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">color: rgb(222, 163, 3);
border: 0 pix;</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item row="1" column="2">
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <widget class="QToolButton" name="toolBtn_Right">
       <property name="styleSheet">
        <string notr="true">background-color: rgba(255, 255, 255, 0);</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../resource/res.qrc">
         <normaloff>:/images/right_scroll_disable.png</normaloff>:/images/right_scroll_disable.png</iconset>
       </property>
       <property name="autoRaise">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_nextMode">
       <property name="minimumSize">
        <size>
         <width>37</width>
         <height>0</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">border: 0 pix;</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="2" column="1">
    <widget class="QToolButton" name="toolBtn_Down">
     <property name="minimumSize">
      <size>
       <width>50</width>
       <height>0</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgba(255, 255, 255, 0);</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="icon">
      <iconset resource="../resource/res.qrc">
       <normaloff>:/images/down_disable.png</normaloff>:/images/down_disable.png</iconset>
     </property>
     <property name="autoRaise">
      <bool>true</bool>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../resource/res.qrc"/>
 </resources>
 <connections/>
</ui>
