/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#ifndef AUTOTESTTASKGENERATOR_H
#define AUTOTESTTASKGENERATOR_H
#include "autotest_global.h"

#include <QHash>
#include <QStringList>

/**
 * 自动测试相关测试小类的生成器，用来生成一系列的任务列表来方便其进行自动测试
 */

struct Task
{
    QStringList eventList;
    int time;
    int timeInterval;
};

class AutoTest;

class AUTOTESTSHARED_EXPORT AutoTestTaskGenerator
{
public:
    AutoTestTaskGenerator();
    void organize(AutoTest* test);
    QList<Task> taskList() const;
    int makeLoopTime(AutoTest* test) const;

private:
    void checkEventOrder(QStringList& eventList);
    QList<Task> m_taskList;
    void generateTaskList(const QMap<int, QStringList>& mapEvent);
    int m_lastEventTimeInterval;
};

#endif // AUTOTESTTASKGENERATOR_H
