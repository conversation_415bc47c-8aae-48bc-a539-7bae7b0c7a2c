#
# The module defines the following variables:
# ZEUS_FOUND - True if zeus found.
# ZEUS_INCLUDE_DIRS - where to find zeusinfostruct.h, etc.
# ZEUS_LIBRARIES - List of libraries when using chisonultrasoundimageprocess.
#

thirdparty_prefix_path(zeus)

if(BUILD_SYS STREQUAL "windows")
    string(TOLOWER ${MODEL} MODEL_DIR)
    set(ZEUS_ROOT ${ZEUS_ROOT}${MODEL_DIR}/)
endif()

find_path ( ZEUS_INCLUDE_DIR
            NAMES
                zeusinfostruct.h usimageprocess.h
            HINTS
                ${ZEUS_ROOT}/include
    )
set(ZEUS_INCLUDE_DIRS ${ZEUS_INCLUDE_DIR})
message("Found zeus headers: ${ZEUS_INCLUDE_DIRS}")

find_library ( USIMAGEPROCESS_LIBRARY
    NAMES 
        usimageprocess
    HINTS
        ${ZEUS_ROOT_DIR}/lib
        ${ZEUS_ROOT}/lib
)
set(ZEUS_LIBRARIES ${USIMAGEPROCESS_LIBRARY})
if(${BUILD_TAR} STREQUAL "aarch")
    find_library ( ZEUS_LIBRARY
                NAMES
                    zeus
                HINTS
                    ${ZEUS_ROOT_DIR}/lib
                    ${ZEUS_ROOT}/lib
                )
    find_library ( ZEUS_MODULE_POSTPROCESS_LIBRARY
        NAMES
            zeus_module_postprocess
        HINTS
            ${ZEUS_ROOT_DIR}/lib
            ${ZEUS_ROOT}/lib
    )
    find_library ( ZEUS_MODULE_BASE_LIBRARY
        NAMES
            zeus_module_base
        HINTS
            ${ZEUS_ROOT_DIR}/lib
            ${ZEUS_ROOT}/lib
    )
    find_library ( ZEUS_CORE_LIBRARY
        NAMES
            zeus_core
        HINTS
            ${ZEUS_ROOT_DIR}/lib
            ${ZEUS_ROOT}/lib
    )
    find_library ( OPENGL_MODULE_LIBRARY
        NAMES
            opengl_module
        HINTS
            ${ZEUS_ROOT_DIR}/lib
            ${ZEUS_ROOT}/lib
    )
    set(ZEUS_LIBRARIES ${ZEUS_LIBRARY} ${USIMAGEPROCESS_LIBRARY} ${ZEUS_MODULE_POSTPROCESS_LIBRARY} ${ZEUS_MODULE_BASE_LIBRARY} ${ZEUS_CORE_LIBRARY} ${OPENGL_MODULE_LIBRARY})
endif()
message("Found zeus libs: ${ZEUS_LIBRARIES}")
