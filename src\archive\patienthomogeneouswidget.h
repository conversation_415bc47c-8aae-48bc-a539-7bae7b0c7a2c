#ifndef PATIENTHOMOGENEOUSWIDGET_H
#define PATIENTHOMOGENEOUSWIDGET_H
#include "archive_global.h"

#include "basewidget.h"

/**
 * @brief 不同的科室病人具有的形同的信息
 */

class Patient;
class Study;
namespace Ui
{
class PatientHomogeneousWidget;
}

class ARCHIVESHARED_EXPORT PatientHomogeneousWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit PatientHomogeneousWidget(QWidget* parent = 0);
    ~PatientHomogeneousWidget();
    /**
     * @brief setPatient 设置一个patient
     *
     * @param patient
     */
    void setPatient(Patient* patient);
    /**
     * @brief setStudy 设置一个study
     *
     * @param study
     */
    void setStudy(Study* study);
    /**
     * @brief flawlessStudy 完善一个study
     *
     * @param study
     */
    void flawlessStudy(Study*& study);
    /**
     * @brief clearStudyInfo 清空检查信息
     */
    void clearStudyInfo();
    void setCurrentWidgetEnabled(bool enabled);
    void setPhysiciansName(const QString& str);
    void setAccession(const QString& str);

protected:
    void retranslateUi();
public slots:
    void deleteDiagnostician(const int& index);
    void deletePhysician(const int& index);
    void deleteSonographer(const int& index);
    void deleteDiagnosticianAllItem();
    void deletePhysicianAllItem();
    void deleteSonographerAllItem();

private:
    Ui::PatientHomogeneousWidget* ui;
};

#endif // PATIENTHOMOGENEOUSWIDGET_H
