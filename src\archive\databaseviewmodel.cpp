#include "databaseviewmodel.h"
#include "resource.h"
#include "setting.h"
#include <QSqlRecord>
#include <QSqlQuery>
#include "util.h"
#include "dataaccesslayerhelper.h"
#include "querycriteria.h"
#include "resource.h"
#include "treeitem.h"
#include <QApplication>
#include "modeldirectorygetter.h"
#include "modeluiconfig.h"
#ifdef USE_ADMINVIEW
#include "adminconfigmodel.h"
#include "userinfo.h"
#include "authorityinfo.h"
#endif
#include "appsetting.h"

DataBaseViewModel::DataBaseViewModel(QObject* parent)
    : QSqlQueryModel(parent)
    , m_FolderIcon(ModelUiConfig::instance().value(ModelUiConfig::FolderSmallIcon).toString())
    , m_Disk(QString())
{
    setHeaderDatas();
    baseQuery();
}

DataBaseViewModel::~DataBaseViewModel()
{
}

QVariant DataBaseViewModel::data(const QModelIndex& index, int role) const
{
    if (!index.isValid() || index.model() != this)
        return QVariant();
    switch (role)
    {
    case Qt::DecorationRole:
        if (index.column() == 0)
        {
            return m_FolderIcon;
        }
        break;
    case Qt::DisplayRole:
        if (index.column() == 1)
        {
            QVariant value = QSqlQueryModel::data(index, role);
            return NameComponent(value.toString()).formattedName();
        }
        else if (index.column() == 8)
        {
            QVariant value = QSqlQueryModel::data(index, role);
#ifdef USE_ADMINVIEW
            return AdminConfigModel::instance()->getUserName(value.toString());
#else
            return QVariant();
#endif
        }
        else
        {
            return QSqlQueryModel::data(index, role);
        }
        break;
    default:
        return QSqlQueryModel::data(index, role);
        break;
    }
    return QVariant();
}

void DataBaseViewModel::sort(int column, Qt::SortOrder order)
{
    QSqlQueryModel::sort(column, order);
    QString ordString = QString();
    switch (order)
    {
    case Qt::AscendingOrder:
        ordString = "ASC";
        break;
    case Qt::DescendingOrder:
        ordString = "DESC";
        break;
    }

    switch (column)
    {
    case 0:
        sortString("PatientID", ordString);
        break;
    case 1:
        sortString("PatientsName", ordString);
        break;
    case 2:
        sortString("PatientsBirthDate", ordString);
        break;
    case 3:
        sortString("PatientsSex", ordString);
        break;
    case 4:
        sortString("StoreTime", ordString, QString("Study."));
        break;
    case 5:
        sortString("StudyTimeRaw", ordString, QString("Study."));
        break;
    case 8:
        sortString("UserId", ordString, QString("Study."));
        break;
    }
    emit sortChanged();
}

void DataBaseViewModel::sortString(const QString& key, const QString& order, QString constStr)
{
    m_QueryStringBase = " order by " + constStr + key + " " + order;
    m_QueryString = m_QueryStringHead + m_QueryStringBase;
    updateQuery();
}

void DataBaseViewModel::setHeaderDatas()
{
    if (AppSetting::isAnimalAndChinese())
    {
        this->setHeaderData(0, Qt::Horizontal, tr("Animal ID"));
    }
    else
    {
        this->setHeaderData(0, Qt::Horizontal, tr("ID"));
    }
    this->setHeaderData(1, Qt::Horizontal, tr("Name"));
    this->setHeaderData(2, Qt::Horizontal, tr("BirthDate"));
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
    this->setHeaderData(
        3, Qt::Horizontal,
        QApplication::translate("Sex", Resource::sexLabel().toUtf8().data(), 0, QApplication::UnicodeUTF8));
#else
    this->setHeaderData(3, Qt::Horizontal, QApplication::translate("Sex", Resource::sexLabel().toUtf8().data()));
#endif
    this->setHeaderData(4, Qt::Horizontal, tr("StoreTime"));
    this->setHeaderData(5, Qt::Horizontal, tr("StudState"));
    this->setHeaderData(6, Qt::Horizontal, tr("StudyTimeRaw"));
    this->setHeaderData(7, Qt::Horizontal, tr("StudyOid"));
    this->setHeaderData(8, Qt::Horizontal, tr("User"));
}

void DataBaseViewModel::baseQuery()
{
    searchSql(QueryCriteria());
}

void DataBaseViewModel::searchSql(const QueryCriteria& criteria)
{
    m_QueryStringHead = QString("select Patient.PatientID, PatientsName, strftime('%1', PatientsBirthDate), case "
                                "PatientsSex %2 end, strftime('%3',StoreTime), StudyState, StudyTimeRaw, StudyOid, "
                                "UserId from Patient INNER JOIN Study ON Patient.PatientID = Study.PatientID ")
                            .arg(sqlDateFormat(), sqlString(), sqlDateFormat() + "T" + "%H:%M:%S");

    if (!criteria.patientId.isEmpty())
    {
        m_QueryStringHead.append(QString("where Patient.PatientID like '%%1%' escape '/' ").arg(criteria.patientId));
    }
    else if (!criteria.patientsName.isEmpty())
    {
        QString nameAppend;
        nameAppend = setupNameQuery(criteria);
        m_QueryStringHead += nameAppend;
    }
#ifdef USE_ADMINVIEW
    UserInfo curUser = AdminConfigModel::instance()->getCurUserInfo();
    if (curUser.authorityId() > AuthorityInfo::Administrator)
    {
        m_QueryStringHead.append(QString("where UserId = '%1' ").arg(curUser.userId()));
    }
#else
#endif
    if (criteria.toStudyDate.isValid() || criteria.fromStudyDate.isValid())
    {
        if (criteria.patientId.isEmpty() && criteria.patientsName.isEmpty())
        {
            m_QueryStringHead.append("where ");
        }
        else
        {
            m_QueryStringHead.append("and ");
        }
    }

    if (!criteria.toStudyDate.isValid() && criteria.fromStudyDate.isValid())
    {
        m_QueryStringHead.append(QString("Study.StoreTime >= '%1' ").arg(dateTimeToStr(criteria.fromStudyDate)));
    }
    else if (criteria.toStudyDate.isValid() && !criteria.fromStudyDate.isValid())
    {
        m_QueryStringHead.append(QString("Study.StoreTime <= '%1' ").arg(dateTimeToStr(criteria.toStudyDate)));
    }
    else if (criteria.toStudyDate.isValid() && criteria.fromStudyDate.isValid())
    {
        m_QueryStringHead.append(QString("Study.StoreTime >= '%1' and Study.StoreTime <= '%2' ")
                                     .arg(dateTimeToStr(criteria.fromStudyDate))
                                     .arg(dateTimeToStr(criteria.toStudyDate)));
    }

    if (m_QueryStringBase.isEmpty())
    {
        m_QueryStringBase = "order by Patient.PatientId DESC";
    }

    m_QueryString = m_QueryStringHead + m_QueryStringBase;
    updateQuery();
}

void DataBaseViewModel::updateQuery()
{
    emit selectionCleared();
    this->setQuery(QSqlQuery(m_QueryString, *DataAccessLayerHelper::sqlDatabase()));
    setHeaderDatas();
    emit update();
}

QFileInfo DataBaseViewModel::fileInfo(const QModelIndex& index)
{
    return QFileInfo();
}

QString DataBaseViewModel::filePath(const QModelIndex& index)
{
    return QString();
}

void DataBaseViewModel::deletePatient(const QFileInfoList& fileInfoList)
{
    foreach (QFileInfo info, fileInfoList)
    {
        DataAccessLayerHelper::delPatient(PatientPath::instance().patientId(info.filePath()));
    }
}

void DataBaseViewModel::deleteStudy(const QFileInfoList& fileInfoList)
{
    foreach (QFileInfo info, fileInfoList)
    {
        DataAccessLayerHelper::delStudy(PatientPath::instance().studyOid(info.filePath()));
    }
}

QString DataBaseViewModel::patientId(const QModelIndex& index)
{
    return QString();
}

QString DataBaseViewModel::studyOid(const QModelIndex& index)
{
    return QString();
}

QString DataBaseViewModel::sqlDateFormat()
{
    QString currentdateFormat = Setting::instance().defaults().dateFormat();
    QStringList currentDateFormatList = currentdateFormat.split("-");
    QString newDateFormat = QString();
    QStringList newDateFormatList = QStringList();

    foreach (QString string, currentDateFormatList)
    {
        if (string == "MM")
        {
            newDateFormatList.append("%m");
        }
        else if (string == "dd")
        {
            newDateFormatList.append("%d");
        }
        else if (string == "yyyy")
        {
            newDateFormatList.append("%Y");
        }
    }

    for (int i = 0; i < newDateFormatList.count(); i++)
    {
        newDateFormat += newDateFormatList.at(i);
        if (i != newDateFormatList.count() - 1)
        {
            newDateFormat += "-";
        }
    }
    return newDateFormat;
}

QString DataBaseViewModel::sqlString()
{
    QString sqlString = QString();
    foreach (QString string, Resource::sexesEx())
    {
        sqlString += "when \"" + string + "\" ";
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
        sqlString +=
            "then \"" +
            QApplication::translate("Sex", Resource::getTrSexEx(string).toUtf8().data(), 0, QApplication::UnicodeUTF8) +
            "\" ";
#else
        sqlString += "then \"" + QApplication::translate("Sex", Resource::getTrSexEx(string).toUtf8().data()) + "\" ";
#endif
    }
    return sqlString;
}

void DataBaseViewModel::setDisk(const QString& dirPath)
{
    m_Disk = dirPath;
}

QString DataBaseViewModel::getDisk() const
{
    return m_Disk;
}

QSqlQuery DataBaseViewModel::getQuery()
{
    return QSqlQuery(m_QueryString, *DataAccessLayerHelper::sqlDatabase());
}

QString DataBaseViewModel::dateTimeToStr(const QDateTime& dateTime)
{
    QString dateFormat = "yyyy-MM-ddThh:mm:ss";
    return dateTime.toString(dateFormat);
}

QString DataBaseViewModel::setupNameQuery(const QueryCriteria& criteria)
{
    QString nameQuery;
    QString sl;

    //"where PatientsName like '%condition1%' escape '/' (AND PatientsName like '%condition2%' escape '/' (AND...))";
    sl = Setting::instance().defaults().shortLanguage();
    if (sl == "zh_CN")
    {
        nameQuery = setupNameQueryCN(criteria);
    }
    else
    {
        nameQuery = setupNameQueryEN(criteria);
    }

    return nameQuery;
}

// CN conditions: ... like '%ch1%' (AND PatientsName like '%ch2%' (AND PatientsName like '%ch3% (AND...)'))
// AND is triggered by inputting new character or space, space is treated as control character.
QString DataBaseViewModel::setupNameQueryCN(const QueryCriteria& criteria)
{
    QString nameQueryHead;
    QString nameQuery;

    //"where PatientsName like '%condition1%' escape '/' (AND PatientsName like '%condition2%' escape '/' (AND...))";
    appendNameQueryHead(nameQueryHead);
    int i = 0;
    while (i < criteria.patientsName.size())
    {
        if (criteria.patientsName.at(i) == ' ')
        {
            if (!nameQuery.isEmpty())
            {
                appendNameQueryTail(nameQuery);
                appendNameQueryAND(nameQuery);
            }
            i++;
            continue;
        }
        // appending escape has been handled in criteria
        nameQuery += criteria.patientsName.at(i);
        if (criteria.patientsName.at(i) == '/' || criteria.patientsName.at(i) == '\'')
        {
            if (i + 1 < criteria.patientsName.size())
            {
                i++;
                nameQuery += criteria.patientsName.at(i);
                appendNameQueryTail(nameQuery);
                appendNameQueryAND(nameQuery);
            }

            i++;
            continue;
        }
        appendNameQueryTail(nameQuery);
        appendNameQueryAND(nameQuery);
        i++;
    }
    appendNameQueryTail(nameQuery);

    nameQueryHead += nameQuery;
    return nameQueryHead;
}

// EN condition: ... like '%ch1ch2ch3...%' (AND PatientsName like '%ch1'ch2'ch3'...%' (AND...))
// AND is triggered by inputtig space, space is treated as control character.
QString DataBaseViewModel::setupNameQueryEN(const QueryCriteria& criteria)
{
    QString nameQueryHead;
    QString nameQuery;

    //"where PatientsName like '%condition1%' escape '/' (AND PatientsName like '%condition2%' escape '/' (AND...))";
    appendNameQueryHead(nameQueryHead);
    int i = 0;
    while (i < criteria.patientsName.size())
    {
        if (criteria.patientsName.at(i) == ' ')
        {
            if (!nameQuery.isEmpty())
            {
                appendNameQueryTail(nameQuery);
                appendNameQueryAND(nameQuery);
            }
            i++;
            continue;
        }
        // appending escape has been handled in criteria
        nameQuery += criteria.patientsName.at(i);
        if (criteria.patientsName.at(i) == '/' || criteria.patientsName.at(i) == '\'')
        {
            if (i + 1 < criteria.patientsName.size())
            {
                i++;
                nameQuery += criteria.patientsName.at(i);
            }

            i++;
            continue;
        }
        i++;
    }
    appendNameQueryTail(nameQuery);

    nameQueryHead += nameQuery;
    return nameQueryHead;
}

void DataBaseViewModel::appendNameQueryHead(QString& nameQuery)
{
    nameQuery += "where PatientsName like '%";
}

void DataBaseViewModel::appendNameQueryAND(QString& nameQuery)
{
    nameQuery += "AND PatientsName like '%";
}

void DataBaseViewModel::appendNameQueryTail(QString& nameQuery)
{
    nameQuery += "%' escape '/' ";
}
