#
# The module defines the following variables:
# USBVIDEOPRINT_FOUND - True if USBVIDEOPRINT found.
# USBVIDEOPRINT_INCLUDE_DIRS - where to find *.h, etc.
# USBVIDEOPRINT_LIBRARIES - List of libraries when using USBVIDEOPRINT.
#

thirdparty_prefix_path(usbvideoprint)

find_path ( USBVIDEOPRINT_INCLUDE_DIR
            NAMES
                vprint.h
            HINTS
	    	${USBVIDEOPRINT_ROOT}/include
            )
set(USBVIDEOPRINT_INCLUDE_DIRS ${USBVIDEOPRINT_INCLUDE_DIR})
message("Found usbvideoprint headers: ${USBVIDEOPRINT_INCLUDE_DIRS}")


find_library ( USBVIDEOPRINT_LIBRARY
            NAMES
                vprint
            HINTS
                ${USBVIDEOPRINT_ROOT_DIR}/lib
                ${USBVIDEOPRINT_ROOT}/lib
             )
set(USBVIDEOPRINT_LIBRARIES ${USBVIDEOPRINT_LIBRARY})
message("Found usbvideoprint libs: ${USBVIDEOPRINT_LIBRARIES}")
