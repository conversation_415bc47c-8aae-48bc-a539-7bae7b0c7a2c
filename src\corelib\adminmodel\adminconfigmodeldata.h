#ifndef ADMINCON<PERSON>GMODELDATA_H
#define ADMINCONFIGMODELDATA_H

#include <QList>
#include <QStringList>
#include <QHash>
#include "adminmodel_global.h"

class AuthorityInfo;
class UserInfo;
class FingerprintInfo;

typedef QString USERID;

class ADMINMODELSHARED_EXPORT AdminConfigModelData
{
private:
    friend class AdminConfigModel;
    AdminConfigModelData();
    const QStringList getAllAuthorityNames() const;
    const QString getAuthorityName(int authorityid) const;
    int getAuthorityId(const QString& authorityname) const;
    int getUserAuthorityId(const QString& userName) const;

    const QStringList getAllUserNames() const;
    bool getUserInfoByName(const QString& username, UserInfo& retUser) const;
    bool getUserInfoById(const QString& userId, UserInfo& retUser) const;

    void updateUsers(const QList<UserInfo>& users);

    USERID getCurUserId() const;
    USERID getPreUserId() const;

    QList<FingerprintInfo> getFingerprintInfo(const QString& userid);
    QList<int> getFingerIndex(const QString& userid);

    bool getFingerByIndex(int fingerIndex, FingerprintInfo& retFinger);

    bool getFingerById(int fingerId, FingerprintInfo& retFinger);

private:
    QList<AuthorityInfo> m_authorities;
    QList<FingerprintInfo> m_fingerprints;

    QHash<USERID, UserInfo> m_baseUsers;
    USERID m_curUserId;
    USERID m_preUserId;
    //    QList<const USERID *> m_newUsers;
    //    QList<const USERID *> m_delUsers;
    //    QList<const USERID *> m_modyfiUsers;
};

#endif // ADMINCONFIGMODELDATA_H
