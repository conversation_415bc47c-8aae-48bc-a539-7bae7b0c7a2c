#ifndef EXAMDATEBETWEENWIDGET_H
#define EXAMDATEBETWEENWIDGET_H

#include "archive_global.h"
#include <QWidget>
#include "baseinputabledialogframe.h"

class ExamDateBetweenWidget;
class ARCHIVESHARED_EXPORT ExamDateBetweenDialog : public BaseInputAbleDialogFrame
{
    Q_OBJECT
public:
    explicit ExamDateBetweenDialog(QWidget* parent = 0);
    QString fromToDateString() const;
    void setFromToDateString(const QString& date);

private:
    ExamDateBetweenWidget* m_Child;
};

namespace Ui
{
class ExamDateBetweenWidget;
}

class ARCHIVESHARED_EXPORT ExamDateBetweenWidget : public QWidget
{
    Q_OBJECT

public:
    explicit ExamDateBetweenWidget(QWidget* parent = 0);
    ~ExamDateBetweenWidget();
    QString fromToDateString() const;
    void setFromToDateString(const QString& date);
signals:
    void accept();
private slots:

    void on_pushButtonOK_clicked();

    void on_pushButtonCancle_clicked();

private:
    Ui::ExamDateBetweenWidget* ui;
    QString m_DateString;
};

#endif // EXAMDATEBETWEENWIDGET_H
