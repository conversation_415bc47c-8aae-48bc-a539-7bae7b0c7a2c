#include "multifuncoperationareaview.h"
#include "ui_multifuncoperationareaview.h"
#include "systemhintmodel.h"
#include <QLabel>

MultiFuncOperationAreaView::MultiFuncOperationAreaView(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::MultiFuncOperationAreaView)
    , m_Model(NULL)
{
    ui->setupUi(this);

    init();
}

MultiFuncOperationAreaView::~MultiFuncOperationAreaView()
{
    delete ui;
}

void MultiFuncOperationAreaView::setModel(SystemHintModel* model)
{
    m_Model = model;
    if (m_Model != NULL)
    {
        connect(m_Model, SIGNAL(rawTBStatusChanged(bool)), this, SLOT(onRawTBStatusChanged(bool)));
        connect(m_Model, SIGNAL(leftButtonStatusChanged()), this, SLOT(onLeftButtonStatusChanged()));
        connect(m_Model, SIGNAL(rightButtonStatusChanged()), this, SLOT(onRightButtonStatusChanged()));

        onRawTBStatusChanged(false);
        onLeftButtonStatusChanged();
        onRightButtonStatusChanged();
    }
}

void MultiFuncOperationAreaView::setTrackballButtonStatus(const QString& status)
{
    QString iconPath = m_TrackballIconHash.value(status);

    QPixmap pixmap = QPixmap(iconPath);
    ui->labelTrackball->resize(pixmap.size());
    ui->labelTrackball->setPixmap(pixmap);
}

void MultiFuncOperationAreaView::setLeftButtonStatus(const QString& status)
{
    QString iconPath = m_LeftButtonIconHash.value(status);

    QPixmap pixmap = QPixmap(iconPath);
    ui->labelLeft->resize(pixmap.size());
    ui->labelLeft->setPixmap(pixmap);
}

void MultiFuncOperationAreaView::setRightButtonStatus(const QString& status)
{
    QString iconPath = m_RightButtonIconHash.value(status);

    QPixmap pixmap = QPixmap(iconPath);
    ui->labelRight->resize(pixmap.size());
    ui->labelRight->setPixmap(pixmap);
}

void MultiFuncOperationAreaView::onRawTBStatusChanged(bool isMeasuring)
{
    if (m_Model == NULL)
    {
        return;
    }
    if (m_Model->rawTBStatus().isEmpty())
    {
        ui->labelTrackball->setPixmap(QPixmap());
        ui->labelTrackball->setText(QString());
    }
    else
    {
        if (!isMeasuring)
        {
            ui->labelTrackball->setPixmap(QPixmap(m_TrackballIconHash.value(m_Model->rawTBStatus())));
            //            ui->labelTrackball->setText(m_Model->rawTBStatus());
        }
        else
        {
            ui->labelTrackball->setPixmap(QPixmap(m_TrackballIconHash.value("Measuring")));
            //            ui->labelTrackball->setText("Measuring");
        }
    }
}

void MultiFuncOperationAreaView::onLeftButtonStatusChanged()
{
    if (m_Model == NULL)
    {
        return;
    }
    if (m_Model->leftButtonStatus().isEmpty())
    {
        ui->labelLeft->setPixmap(QPixmap());
        ui->labelLeft->setText(QString());
    }
    else
    {
        ui->labelLeft->setPixmap(QPixmap(m_LeftButtonIconHash.value(m_Model->leftButtonStatus())));
        //        ui->labelLeft->setText(m_Model->leftButtonStatus());
    }
}

void MultiFuncOperationAreaView::onRightButtonStatusChanged()
{
    if (m_Model == NULL)
    {
        return;
    }
    if (m_Model->rightButtonStatus().isEmpty())
    {
        ui->labelRight->setPixmap(QPixmap());
        ui->labelRight->setText(QString());
    }
    else
    {
        ui->labelRight->setPixmap(QPixmap(m_RightButtonIconHash.value(m_Model->rightButtonStatus())));
        //        ui->labelRight->setText(m_Model->rightButtonStatus());
    }
}

void MultiFuncOperationAreaView::retranslateUi()
{
    ui->retranslateUi(this);
}

void MultiFuncOperationAreaView::init()
{
    setupcontainerWidget();

    // initial trackball hash

    m_TrackballIconHash.insert("4D ROI", ":/images/m600_4DROI_sign.png");
    m_TrackballIconHash.insert("3D ROI", ":/images/m600_3DROI_sign.png");
    m_TrackballIconHash.insert("Cine Play", ":/images/m600_movieplay_sign.png");
    m_TrackballIconHash.insert("Comment", ":/images/m600_comment_sign.png");
    m_TrackballIconHash.insert("ROI & Gate", ":/images/m600_ROI_Gate_sign.png");
    m_TrackballIconHash.insert("Gate", ":/images/m600_Gate_sign.png");
    m_TrackballIconHash.insert("FreeMLine", ":/images/m600_FreeMLine_sign.png");
    m_TrackballIconHash.insert("M Line", ":/images/m600_MLine_sign.png");
    m_TrackballIconHash.insert("ROI", ":/images/m600_ROI_sign.png");
    m_TrackballIconHash.insert("ZOOM Box", ":/images/m600_zoombox_sign.png");
    m_TrackballIconHash.insert("Select Item", ":/images/m600_selectitem_sign.png");
    m_TrackballIconHash.insert("Measuring", ":/images/m600_measuring_sign.png");

    // initial leftbutton hash
    m_LeftButtonIconHash.insert("BD_Move", ":/images/m600_BD_Gate_move_sign.png");
    m_LeftButtonIconHash.insert("BD_Change", ":/images/m600_BD_Gate_chang_sign.png");
    m_LeftButtonIconHash.insert("ROI_Move", ":/images/m600_ROI_move_sign.png");
    m_LeftButtonIconHash.insert("ROI_Change", ":/images/m600_ROI_change_sign.png");

    // initial rightbutton hash
    m_RightButtonIconHash.insert("Zoom_Move", ":/images/m600_zoombox_move.png");
    m_RightButtonIconHash.insert("Zoom_Change", ":/images/m600_zoombox_change_sign.png");
}

void MultiFuncOperationAreaView::setupcontainerWidget()
{
    QSize trackballSize = QSize(90, 90);
    QSize leftSize = QSize(50, 90);
    QSize rightSize = QSize(50, 90);

    ui->widgetLeft->setGeometry(30, 30, leftSize.width(), leftSize.height());
    ui->widgetTrackball->setGeometry(80, 0, trackballSize.width(), trackballSize.height());
    ui->widgetRight->setGeometry(170, 30, rightSize.width(), rightSize.height());
}
