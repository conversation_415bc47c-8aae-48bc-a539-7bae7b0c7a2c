#ifndef FOURDLIGHTANGLESETTINGWIDGET_H
#define FOURDLIGHTANGLESETTINGWIDGET_H

#include "controls_global.h"
#include "fourdparassettingbasewidget.h"
#include "fourdlightanglewidget.h"

class CONTROLSSHARED_EXPORT FourDLightAngleSettingWidget : public FourDParasSettingBaseWidget
{
    Q_OBJECT
public:
    explicit FourDLightAngleSettingWidget(QWidget* parent = 0);
    void initalize();
    void setSonoParameters(SonoParameters* sonoParameters);

protected:
    virtual void currenIndexChanged(const QString& arg1);
    virtual bool contains(const QString& name);
    virtual void add(const QString& name);
    virtual void save(const QString& name);
    virtual void del(const QString& name);
    virtual bool rename(const QString& oldName, const QString& newName);
    virtual QStringList names();

private:
    FourDLightAngleWidget* m_Child;
};

#endif // FOURDLIGHTANGLESETTINGWIDGET_H
