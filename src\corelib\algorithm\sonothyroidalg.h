#ifndef SONOTHYROIDALG_H
#define SONOTHYROIDALG_H

#include "algorithm_global.h"
#include <QLibrary>
#ifdef USE_SONOTHYROID
#include "sonothyroid.h"
#else
struct thyroidOut
{
    float conf = 0.0;
    int clsID = -1;
};

struct boxOut
{
    int X0 = -1;
    int Y0 = -1;
    int X1 = -1;
    int Y1 = -1;
    int DetectedNum;
};
#endif
#include "virtualalg.h"

struct thyroidOut;
struct boxOut;
class ALGORITHMSHARED_EXPORT SonoThyroidAlg : public VirtualAlg
{
    friend class AlgInstance;

private:
    SonoThyroidAlg();

public:
    inline bool isError(void)
    {
        return m_IsError;
    }

    typedef void (*CreateViewContext)(const char* detect_modelpath, const char* cls_modelpath,
                                      const char* clcache_path);
    typedef void (*RemoveViewContext)();
    typedef void (*ThyroidCls)(unsigned char* imgin, int width, int height, boxOut& roi, thyroidOut& out);
    typedef void (*ThyroidDetection)(unsigned char* imgin, int width, int height, boxOut& boxOut);

public:
    void create_Viewontext(const char*, const char*, const char*);
    void removeViewontext();
    void thyroidCls(unsigned char*, int, int, boxOut&, thyroidOut&);
    void thyroidDetection(unsigned char*, int, int, boxOut&);

public:
    CreateViewContext m_CreateContext;
    RemoveViewContext m_RemoveContext;
    ThyroidCls m_ThyroidCls;
    ThyroidDetection m_ThyroidDetection;
};

#endif // SONOTHYROIDALG_H
