#include "fingercontroller.h"
#include "util.h"
#include "hardwarecontrolutil.h"
#include <QJsonParseError>
#include <QJsonDocument>
#include <QJsonValue>
#include <QDebug>
#include "logger.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, FingerController)
#define FingerPrint_Max_Unreset_USB_Count 10

FingerController& FingerController::instance()
{
    static FingerController _instance;
    return _instance;
}

void FingerController::setExecutePath(const QString& path)
{
    m_FingerExcutePath = path;
}

void FingerController::setFingerDeviceId(const QString& deviceId)
{
    m_FingerDeviceId = deviceId;
}

void FingerController::stop()
{
    m_FingerWorker.stop();
}

void FingerController::verify()
{
    log()->info() << PRETTY_FUNCTION;
    m_ControllerMode = FingerController::Verify;
    m_FingerWorker.stop();
    m_FingerWorker.setCmd(m_FingerExcutePath);
    m_FingerWorker.setArgs(QStringList() << "verify");
    m_FingerWorker.start();
}

void FingerController::enroll(const QString& username)
{
    log()->info() << PRETTY_FUNCTION << " username = " << username;
    m_ControllerMode = FingerController::Enroll;
    m_EnrollName = username;
    m_FingerWorker.stop();
    m_FingerWorker.setCmd(m_FingerExcutePath);
    m_FingerWorker.setArgs(QStringList() << "enroll" << username);
    m_FingerWorker.start();
}

void FingerController::deleteFp(const QString& username)
{
    log()->info() << PRETTY_FUNCTION << " username = " << username;
    m_ControllerMode = FingerController::DeleteFp;
    m_FingerWorker.stop();
    m_FingerWorker.setCmd(m_FingerExcutePath);
    m_FingerWorker.setArgs(QStringList() << "delete" << username);
    m_FingerWorker.start();
}

void FingerController::resetDevice()
{
    log()->info() << PRETTY_FUNCTION;
    stop();

    if (!m_FingerDeviceId.isEmpty())
    {
        HardWareControlUtil::resetFingerDevice(m_FingerDeviceId);
    }
    else
    {
        qCritical() << PRETTY_FUNCTION << " finger device id is empty.";
    }

    switch (m_ControllerMode)
    {
    case FingerController::Enroll:
        enroll(m_EnrollName);
        break;
    case FingerController::Verify:
        verify();
        break;
    default:
        break;
    }
}

#ifdef USE_OPENFINGERPRINT
void FingerController::registerVerifyCallback(verifyCallbackFunc callbackFunc, void* object)
{
    log()->info() << PRETTY_FUNCTION;
    m_VerifyCallbackFunc = callbackFunc;
    m_VerifyObject = object;
}

void FingerController::registerEnrollCallback(enrollCallbackFunc callbackFunc, void* object)
{
    log()->info() << PRETTY_FUNCTION;
    m_EnrollCallbackFunc = callbackFunc;
    m_EnrollObject = object;
}
#endif

FingerController::FingerController(QObject* parent)
    : QObject(parent)
    , m_FingerExcutePath("")
#ifdef USE_OPENFINGERPRINT
    , m_VerifyCallbackFunc(nullptr)
    , m_VerifyObject(nullptr)
    , m_EnrollCallbackFunc(nullptr)
    , m_EnrollObject(nullptr)
#endif
    , m_ControllerMode(FingerController::Unknow)
    , m_UnsetCount(0)
{
    connect(&m_FingerWorker, SIGNAL(readProcess(QByteArray&)), this, SLOT(onReadProcess(QByteArray&)));
    connect(&m_FingerWorker, SIGNAL(processFinished(int, QByteArray&)), this,
            SLOT(onProcessFinished(int, QByteArray&)));
}

void FingerController::parseOutput(const QString& outData)
{
    log()->info() << PRETTY_FUNCTION << " outData = " << outData;
#ifdef USE_OPENFINGERPRINT
    QString tmp;
    if (outData.contains(FINGERPRINT_INFO_PREFIX) && outData.contains(FINGERPRINT_INFO_SUFFIX))
    {
        // info
        tmp = outData.right(outData.length() - QString(FINGERPRINT_INFO_PREFIX).length());
        tmp = tmp.left(tmp.length() - QString(FINGERPRINT_INFO_PREFIX).length());

        QJsonParseError err;
        QJsonDocument jsonDoc = QJsonDocument::fromJson(tmp.toUtf8(), &err);
        if (err.error != QJsonParseError::NoError)
        {
            qCritical() << __PRETTY_FUNCTION__ << tmp << " info parse error:" << err.errorString()
                        << " string: " << tmp;
            return;
        }
        QJsonObject obj = jsonDoc.object();
        parseInfo(obj);
    }
    else if (outData.contains(FINGERPRINT_ERROR_PREFIX) && outData.contains(FINGERPRINT_ERROR_SUFFIX))
    {
        // error
        tmp = outData.right(outData.length() - QString(FINGERPRINT_ERROR_PREFIX).length());
        tmp = tmp.left(tmp.length() - QString(FINGERPRINT_ERROR_PREFIX).length());

        QJsonParseError err;
        QJsonDocument jsonDoc = QJsonDocument::fromJson(tmp.toUtf8(), &err);
        if (err.error != QJsonParseError::NoError)
        {
            qCritical() << __PRETTY_FUNCTION__ << tmp << " error parse error:" << err.errorString()
                        << " string: " << tmp;
            return;
        }
        QJsonObject obj = jsonDoc.object();
        parseError(obj);
    }
    qDebug() << outData;
#endif
}

void FingerController::parseInfo(const QJsonObject& obj)
{
#ifdef USE_OPENFINGERPRINT
    int code = obj.value(FINGERPRINT_INFO_CODE_KEY).toString().toInt();
    log()->info() << PRETTY_FUNCTION << " code = " << code;
    switch (code)
    {
    case FingerPrint::START_FP_IDENTIFY:
    {
        // start verify ok
        m_UnsetCount = 0;
    }
    break;
    case FingerPrint::START_FP_ENROLL:
    {
        // start enroll ok
        m_UnsetCount = 0;
    }
    break;
    case FingerPrint::START_FP_DELETE_OK:
    {
        // deletefp ok
    }
    break;
    case FingerPrint::START_FP_VERIFY_CB:
    {
        QString username = obj.value(FINGERPRINT_INFO_VERIFI_NAME).toString();
        bool verified = obj.value(FINGERPRINT_INFO_VERIFI_VLUE).toString().toInt();
        if (nullptr != m_VerifyCallbackFunc)
        {
            m_VerifyCallbackFunc(username.toUtf8(), verified, m_VerifyObject);
        }
    }
    break;
    case FingerPrint::START_FP_ENROLL_CB:
    {
        int allStages = obj.value(FINGERPRINT_INFO_ALLSTAGES).toString().toInt();
        int finishedStage = obj.value(FINGERPRINT_INFO_FINISHEDSTAGE).toString().toInt();
        if (nullptr != m_EnrollCallbackFunc)
        {
            m_EnrollCallbackFunc(finishedStage, allStages, m_EnrollObject);
        }
    }
    break;
    default:
        break;
    }
#endif
}

void FingerController::parseError(const QJsonObject& obj)
{
#ifdef USE_OPENFINGERPRINT
    int code = obj.value(FINGERPRINT_ERROR_CODE_KEY).toString().toInt();
    qDebug() << PRETTY_FUNCTION << " errCode = " << code;
    log()->info() << PRETTY_FUNCTION << " code = " << code;
    switch (code)
    {
    case FingerPrint::OPEN_FINGER_ARG_COUNT_ERR:
    case FingerPrint::OPEN_FINGER_ARG_VALUE_ERR:
    {
        qCritical() << "Execute fingerprint arguments error: " << obj;
    }
    break;
    case FingerPrint::OPEN_FINGER_FP_CONTEXT_NEW_ERR:
    case FingerPrint::OPEN_FINGER_FP_CONTEXT_GET_DEVICES_ERR:
    case FingerPrint::OPEN_FINGER_FP_DEVICE_ISEMPTY_ERR:
    case FingerPrint::OPEN_FINGER_FP_DISCOVER_DEVICE_ERR:
    {
        qCritical() << "Fingerprint device init error: " << obj;
    }
    break;
    case FingerPrint::OPEN_FINGER_FP_DELETE_DIR_NOTEXIST_ERR:
    case FingerPrint::OPEN_FINGER_FP_DELETE_DIR_STAT_ERR:
    case FingerPrint::OPEN_FINGER_FP_DELETE_UBKONW_FILETYPE_ERR:
    {
        qCritical() << "Delete fingerprint device, error: " << obj;
    }
    break;
    case FingerPrint::OPEN_FINGER_DEVICE_FAILED:
    {
        qCritical() << "Open fingerprint device error: " << obj;
        if (FingerPrint_Max_Unreset_USB_Count < m_UnsetCount)
        {
            qCritical() << PRETTY_FUNCTION << "Open device error count is " << m_UnsetCount;
            return;
        }
        m_UnsetCount++;
        //测试了90次，出现了8次异常，异常有两类，都是打开设备返回异常，如下：
        // a、{"errCode":"19","msg":"Failed to open device:USB error on device 2808:9338 : Resource busy [-6]"}
        // b、{"errCode":"19","msg":"Failed to open device:init sensor error!"}
        //实验发现目前遇到的这两类异常，可以通过usbreset 2808:9338命令解决，所以，代码里面先这样处理
        resetDevice();
    }
    break;
    case FingerPrint::OPEN_FINGER_DIR_FAILED:
    {
        //指纹目录不存在，还没录入指纹，或者目录被删除
        qWarning() << "Open finger dir failed.";
    }
    break;
    case FingerPrint::OPEN_FINGER_LOAD_DATA_FAILED:
    {
        // load data failed
        qCritical() << "Finger load data failed, error: " << obj;
    }
    break;
    case FingerPrint::OPEN_FINGER_ENROLL_FAILED:
    {
        // enroll failed
        qCritical() << "Finger enroll failed, error: " << obj;
    }
    break;
    case FingerPrint::OPEN_FINGER_ENROLL_SAVE_FAILED:
    {
        // enroll save failed
        qCritical() << "Finger enroll save failed, error: " << obj;
    }
    break;
    case FingerPrint::OPEN_FINGER_ENROLL_CLOSE_FAILED:
    case FingerPrint::OPEN_FINGER_VERIFY_CLOSE_FAILED:
    {
        // close device failed
        qCritical() << "Finger device close failed, error: " << obj;
    }
    break;
    default:
        break;
    }
#endif
}

void FingerController::onReadProcess(QByteArray& data)
{
    QString str(data);
    QStringList list = str.split("\n");

    foreach (QString item, list)
    {
        parseOutput(item);
    }
}

void FingerController::onProcessFinished(int exitCode, QByteArray& data)
{
    emit processFinished(exitCode, data);
}
