#ifndef ARCHIVEIMAGELABEL_H
#define ARCHIVEIMAGELABEL_H
#include "archive_global.h"
// no use
#include <QLabel>
class ImageLabelInfo;

/**
 * @brief no use
 */
class ARCHIVESHARED_EXPORT ArchiveImageLabel : public QLabel
{
    Q_OBJECT
public:
    ArchiveImageLabel(QWidget* parent = 0);
    void setImageInfo(ImageLabelInfo* info);
    ImageLabelInfo* imageInfo() const;
    bool isSelected();
    void setIsSelected(bool value);
    int imageWidth() const;
    int imageHeight() const;
    void setIndex(const int index);
    int getIndex() const;
signals:
    void clicked(const QString& path);
    void doubleClicked(const QString& path);

protected:
    void mousePressEvent(QMouseEvent*);

private:
    ImageLabelInfo* m_Info;
    bool m_IsSelected;

private:
    void setInfoPixmap();

private:
    int m_Index;
};

#endif // ARCHIVEIMAGELABEL_H
