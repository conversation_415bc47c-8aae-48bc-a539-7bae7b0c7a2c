#include "openfingerprinttask.h"
#include "util.h"
#include <QRunnable>
#include <QDebug>
#include <string>
#include <QEvent>
#include <QApplication>

#define VERIFY_OK 0

static void enrollCBFunc(int finishedStage, int allStages, void* object)
{
    OpenFingerprintTask* task = reinterpret_cast<OpenFingerprintTask*>(object);
    if (task != nullptr)
    {
        task->enrollCallBack(finishedStage, allStages);
    }
}

static void verifyCBFunc(unsigned char* username, bool verified, void* object)
{
}

class OpenFingerprintEnrollTask : public QRunnable
{
public:
    OpenFingerprintEnrollTask(OpenFingerprintTask* fptask)
        : m_fp(fptask)
    {
    }

protected:
    void run()
    {
        m_fp->enroll();
    }

private:
    OpenFingerprintTask* m_fp;
};

class OpenFingerprintVerifyTask : public QRunnable
{
public:
    OpenFingerprintVerifyTask(OpenFingerprintTask* fptask)
        : m_fp(fptask)
    {
    }

protected:
    void run()
    {
        m_fp->verify();
    }

private:
    OpenFingerprintTask* m_fp;
};

OpenFingerprintTask::OpenFingerprintTask(QObject* parent)
    : QObject(parent)
    , m_FingerprintDev(new OpenFingerprintDev)
    , m_IsRunning(false)
{
    m_FingerprintDev->registerEnrollCallback(enrollCBFunc, this);
    m_FingerprintDev->registerVerifyCallback(verifyCBFunc, this);
}

void OpenFingerprintTask::startEnroll()
{
    //    OpenFingerprintEnrollTask *task = new OpenFingerprintEnrollTask(this);
    //    Util::runRunnable(task);
    enroll();
}

void OpenFingerprintTask::setEnrollUserId(const QString& userId)
{
    m_EnrollUserId = userId;
}

void OpenFingerprintTask::startVerify()
{
    //    OpenFingerprintVerifyTask *task  = new OpenFingerprintVerifyTask(this);
    //    Util::runRunnable(task);
    verify();
}

const QString& OpenFingerprintTask::verifiedUserId() const
{
    return m_VerifiedUserId;
}

void OpenFingerprintTask::stopTask()
{
    //    m_FingerprintDev->stopTask();
    m_IsRunning = false;
}

bool OpenFingerprintTask::deleteFp(const QString& userId)
{
    //    if(m_IsRunning)
    //    {
    //        qDebug() << PRETTY_FUNCTION << "finger print is running at preset";
    //        return false;
    //    }
    int ret = m_FingerprintDev->deleteFp(userId.toStdString());
    return ret == 0 ? true : false;
}

void OpenFingerprintTask::enrollCallBack(int finishedStage, int allStages)
{
    if (finishedStage < allStages)
    {
        qDebug() << "finishedStage" << finishedStage << "allStages" << allStages;
        emit pressFinger(finishedStage);
    }
    else if (finishedStage == allStages)
    {
        qDebug() << "enroll OK";
        emit enrollState(0, -1);
    }
    else
    {
        qDebug() << "error finishedStage must less than allStages";
    }
}

void OpenFingerprintTask::verifyCallBack()
{
}

void OpenFingerprintTask::enroll()
{
    //    if(m_IsRunning)
    //    {
    //        qDebug() << PRETTY_FUNCTION << "finger print is running at preset";
    //        return;
    //    }

    QEvent event(QEvent::Wheel);
    QApplication::sendEvent(qApp, &event);

    m_IsRunning = true;
    int ret = m_FingerprintDev->enroll(m_EnrollUserId.toStdString());
    if (ret == 1)
    {
        qDebug() << PRETTY_FUNCTION << "enroll failed.";
    }
    m_IsRunning = false;
}

void OpenFingerprintTask::verify()
{
    //    if(m_IsRunning)
    //    {
    //        qDebug() << PRETTY_FUNCTION << "finger print is running at preset";
    //        return;
    //    }

    m_IsRunning = true;
    m_VerifiedUserId.clear();

    std::string str;
    int ret = m_FingerprintDev->verify(str);
    if (ret == 1)
    {
        qDebug() << PRETTY_FUNCTION << "verify failed";
    }
    else if (ret == 0)
    {
        m_VerifiedUserId = str.c_str();
        emit verifyState(VERIFY_OK);
    }
    m_IsRunning = false;
}
