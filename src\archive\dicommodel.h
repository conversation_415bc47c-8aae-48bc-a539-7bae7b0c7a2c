#ifndef DICOMMODEL_H
#define DICOMMODEL_H
#include "archive_global.h"

#include <QObject>
#include <QString>
#include <QStringList>
#include "dicomtaskmanager.h"
#include "patient.h"
#include "globaldef.h"

class ARCHIVESHARED_EXPORT DicomModel : public QObject
{
    Q_OBJECT
public:
    explicit DicomModel(QObject* parent = 0);
    DicomModel(DicomTaskManager* manager, const QStringList& fileNames, const QStringList& filePathes);
    QList<Patient*> getPatients(const QStringList& value) const;

    void setSpecialServiceGroup(const QString& value);
    void setParentWidget(QWidget* p);
    void setIsGDPR(bool isGDPR);

    int doDicomWork(DicomChison::ServiceIndex index);

private:
    void analysisFiles();
    int addDicomWorkToGDPR(DicomChison::ServiceIndex sindex, Patient* patient, int index, bool isGDPR);
    int doDicomWork(DicomChison::ServiceIndex sindex, Patient* patient, int index);
    QList<Patient*> getPatients() const;
    QStringList getFileNamesInPath(const QString& path) const;

signals:

public slots:

private:
    DicomTaskManager* m_DicomTaskManager;
    QStringList m_FileNames;
    QStringList m_FilePathes;
    QWidget* m_ParentWidget;
    QList<QStringList> m_FileNamesList;
    QString m_SpecialServiceGroup;
    bool m_IsGDPR;
};

#endif // DICOMMODEL_H
