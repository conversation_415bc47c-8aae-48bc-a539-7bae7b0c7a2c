#include "glanceimagesmodel.h"
#include "glanceimages.h"
#include <QWidget>
#include "model/patient.h"
#include "model/study.h"
#include "util.h"
#include "patientworkflow.h"
#include "patientworkflowmodel.h"
#include "dataaccesslayerhelper.h"
#include "modeldirectorygetter.h"
#include "generalworkflowfunction.h"

/**
 * @brief GlanceImagesModel easy界面开始运行，根据archive界面进入或者是当前检查
 *		  直接进入该界面，根据超声图片存放的路径列表分离出id+study时间列表，同时根据id判断
 *		  出name；easyvie的图片区域只显示一个检查的超声图片，可以通过改变id编辑框的当前id
 *		  +study时间来改变图片区域的图片；同时是根据id+stusy时间确定唯一的检查，可以传递此
 * @brief GlanceImagesModel
 *
 * @param parent
 *
 * @return
 */
GlanceImagesModel::GlanceImagesModel(IStateManager* stateManager, QWidget* parent)
    : QObject(parent)
    , m_ImageDialog(NULL)
    , m_PatientWorkflow(NULL)
    , m_Patient(NULL)
    , m_DirPathList(QStringList())
    , m_GeneralWorkflowFunction(NULL)
{
    m_ImageDialog = new GlanceImagesDialog(stateManager, parent);
    connect(m_ImageDialog, SIGNAL(closed()), this, SIGNAL(closed()));
    connect(m_ImageDialog, SIGNAL(onlyJump()), this, SIGNAL(onlyJump()));
    connect(m_ImageDialog, SIGNAL(closed()), this, SLOT(clearDirPathList()));
    connect(m_ImageDialog, SIGNAL(entryPatientState()), this, SIGNAL(entryPatientState()));
    connect(m_ImageDialog, SIGNAL(entryArchiveState()), this, SIGNAL(entryArchiveState()));
    connect(m_ImageDialog, SIGNAL(studyOidChanged(QString)), this, SIGNAL(studyOidChanged(QString)));
    connect(m_ImageDialog, SIGNAL(currentDiskPathChanged(QString)), this, SIGNAL(currentDiskPathChanged(QString)));
    connect(m_ImageDialog, SIGNAL(entryContinueExamState()), this, SIGNAL(entryContinueExamState()));
    connect(m_ImageDialog, SIGNAL(entryEditExamState()), this, SIGNAL(entryEditExamState()));
    connect(m_ImageDialog, SIGNAL(fourdCallBack(QString)), this, SLOT(onFourdCallBack(QString)));
}

GlanceImagesModel::~GlanceImagesModel()
{
    //    Util::SafeDeletePtr(m_ImageDialog);
}

void GlanceImagesModel::exec()
{
    if (!m_DirPathList.isEmpty())
    {
        m_ImageDialog->setPatientInfoList(extractFilePath(m_DirPathList));
    }
    else if (m_PatientWorkflow->patient() != NULL)
    {
        m_ImageDialog->setDiskPath(Resource::hardDiskDir);
        m_ImageDialog->setPatientInfoList(
            extractFilePath(QStringList(PatientPath::instance().path(*m_PatientWorkflow->patient()))));
    }
    else
    {
        m_ImageDialog->setPatientInfoList(QHash<QString, QString>());
    }
    m_ImageDialog->show();
}

GlanceImagesDialog* GlanceImagesModel::glanceImagesDialog() const
{
    return m_ImageDialog;
}

void GlanceImagesModel::setPatientWorkflow(PatientWorkflow* patientWorkflow)
{
    m_PatientWorkflow = patientWorkflow;
    m_ImageDialog->setPatientWorkflow(patientWorkflow);
}

void GlanceImagesModel::setGeneralWorkflowFunction(GeneralWorkflowFunction* generalWorkflowFunction)
{
    m_GeneralWorkflowFunction = generalWorkflowFunction;
    m_ImageDialog->setGeneralWorkflowFunction(generalWorkflowFunction);
}

void GlanceImagesModel::setToolsFacade(IToolsFacade* value)
{
    m_ImageDialog->setToolsFacade(value);
}

void GlanceImagesModel::setColorMapManager(IColorMapManager* colorMapManager)
{
    m_ImageDialog->setColorMapManager(colorMapManager);
}

void GlanceImagesModel::onSendDirPathList(const QStringList& list)
{
    m_DirPathList = list;
}

void GlanceImagesModel::clearDirPathList()
{
    m_DirPathList = QStringList();
}

void GlanceImagesModel::onCurrentDiskPathChanged(const QString& disk)
{
    m_ImageDialog->setDiskPath(disk);
}

void GlanceImagesModel::onFourdCallBack(const QString& filePath)
{
    emit dirPathListChanged(m_DirPathList);
    emit diskPathChanged(m_ImageDialog->diskPath());
    emit fourdCallBack(filePath);
}

QHash<QString, QString> GlanceImagesModel::extractFilePath(const QStringList& pathList)
{
    QHash<QString, QString> exeract;
    foreach (QString path, pathList)
    {
        QString patientId = PatientPath::instance().patientId(path);
        Study* study = DataAccessLayerHelper::getStudy(PatientPath::instance().studyOid(path));
        if (study != NULL)
        {
            QString StoreTime = study->StoreTime().toString("yyyy-MM-ddThh:mm:ss");
            exeract.insert(patientId.append("(").append(StoreTime).append(")"), study->StudyOid());
            delete study;
        }
    }
    return exeract;
}
