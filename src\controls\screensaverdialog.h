#ifndef SCREENSAVERDIALOG_H
#define SCREENSAVERDIALOG_H
#include "controls_global.h"
#include <QDialog>
#include <QTimer>
#include <QPixmap>

namespace Ui
{
class ScreenSaverDialog;
}

class CONTROLSSHARED_EXPORT ScreenSaverDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ScreenSaverDialog(QWidget* parent = 0);
    ~ScreenSaverDialog();
    virtual void doExec(int pictureIndex);
    virtual void doQuit();
public slots:
    void onLabelMoved();

private:
    Ui::ScreenSaverDialog* ui;
    QTimer m_LabelMoveTimer;
    QPixmap m_Pixmap;
};

#endif // SCREENSAVERDIALOG_H
