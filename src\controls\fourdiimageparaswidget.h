#ifndef FOURDIIMAGEPARASWIDGET_H
#define FOURDIIMAGEPARASWIDGET_H

#include "controls_global.h"
#include "baseinputabledialogframe.h"
#include "basewidget.h"

class SonoParameters;
class FourDiImageParasWidget;
class CONTROLSSHARED_EXPORT FourDiImageParasDialog : public BaseInputAbleDialogFrame
{
    Q_OBJECT
public:
    explicit FourDiImageParasDialog(QWidget* parent = 0);
    void setSonoParameters(SonoParameters* value);
    void resetiImageParas();

private:
    FourDiImageParasWidget* m_Child;
};

namespace Ui
{
class FourDiImageParasWidget;
}

class FourDiImageParasWidget : public BaseWidget
{
    Q_OBJECT
public:
    explicit FourDiImageParasWidget(QWidget* parent = 0);
    ~FourDiImageParasWidget();
    void setSonoParameters(SonoParameters* value);
    void resetiImageParas();

protected:
    void retranslateUi();

private slots:
    void onFourDGrayCurveIndexChanged(const QVariant& val);
    void onFourDGammaChanged(const QVariant& val);

    void on_spinBoxGamma_valueChanged(int arg1);

    void on_spinBoxMapOf2D_valueChanged(int arg1);

private:
    Ui::FourDiImageParasWidget* ui;
    SonoParameters* m_SonoParameters;
    int m_GammaValue;
    int m_MapOf2DValue;
};

#endif // FOURDIIMAGEPARASWIDGET_H
