#ifndef FINGERCONTROLLER_H
#define FINGERCONTROLLER_H

#ifdef USE_OPENFINGERPRINT
#include "openfingerprintdev_global.h"
#endif
#include "fingerworker.h"
#include "adminmodel_global.h"
#include <QObject>
#include <QJsonObject>

class ADMINMODELSHARED_EXPORT FingerController : public QObject
{
    Q_OBJECT
public:
    static FingerController& instance();

    enum FingerControllerMode
    {
        Unknow,
        Enroll,
        Verify,
        DeleteFp
    };

    void setExecutePath(const QString& path);
    void setFingerDeviceId(const QString& deviceId);

    void stop();
    void verify();
    void enroll(const QString& username);
    void deleteFp(const QString& username);

    void resetDevice();

#ifdef USE_OPENFINGERPRINT
    void registerVerifyCallback(verifyCallbackFunc callbackFunc, void* object);
    void registerEnrollCallback(enrollCallbackFunc callbackFunc, void* object);
#endif

private:
    explicit FingerController(QObject* parent = nullptr);
    void parseOutput(const QString& outData);
    void parseInfo(const QJsonObject& obj);
    void parseError(const QJsonObject& obj);

signals:
    void readProcess(QByteArray& data);
    void processFinished(int exitCode, QByteArray& data);

public slots:
    void onReadProcess(QByteArray& data);
    void onProcessFinished(int exitCode, QByteArray& data);

private:
    QString m_FingerExcutePath;
    FingerWorker m_FingerWorker;
#ifdef USE_OPENFINGERPRINT
    verifyCallbackFunc m_VerifyCallbackFunc;
    void* m_VerifyObject;
    enrollCallbackFunc m_EnrollCallbackFunc;
    void* m_EnrollObject;
#endif
    FingerController::FingerControllerMode m_ControllerMode;
    QString m_EnrollName;
    QString m_FingerDeviceId;
    quint16 m_UnsetCount;
};
#endif // FINGERCONTROLLER_H
