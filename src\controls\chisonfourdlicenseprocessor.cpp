#include "chisonfourdlicenseprocessor.h"
#include "appsetting.h"
#include "licenseinitializer.h"
#include "fourdapihandler.h"
#include "bfpnames.h"
#include "parameter.h"
#include "sonoparameters.h"

ChisonFourDLicenseProcessor::ChisonFourDLicenseProcessor(QObject* parent)
    : AbstractFourDLicenseProcessor(parent)
{
}

ChisonFourDLicenseProcessor::~ChisonFourDLicenseProcessor()
{
}

void ChisonFourDLicenseProcessor::verifyLicenseState()
{
    //    m_FourDLicenseState =UltimateUser;
    m_FourDOpened = AppSetting::isFourD();
    m_RemainDays = AppSetting::functionRemainDays(LicenseItemKey::Key4D);
}

void ChisonFourDLicenseProcessor::onFourDStatusChanged(bool isOpen)
{
    //    AppSetting::setFunctionEnabled(LicenseItemKey::Key4D, isOpen);
    //    LicenseInitializer::instance().saveToLicenseFile(LicenseItemKey::Key4D, isOpen);
    //    if(!isOpen){
    //        m_FourDOpened = false;
    //        FourDAPIHandler::instance().destroy();
    //        m_RemainDays = 0;
    //        FourDAPIHandler::instance().setRemainDays(m_RemainDays);
    //    }
    verifyLicenseState();
}
