#
# The module defines the following variables:
# PWTRACE_FOUND - True if pwtrace found.
# PWTRACE_INCLUDE_DIRS - where to find pwtrace.h, etc.
# PWTRACE_LIBRARIES - List of libraries when using pwtrace.
#

thirdparty_prefix_path(pwtrace)

find_path ( PWTRACE_INCLUDE_DIR
            NAMES
                traceanalysis.h
            HINTS
                ${PWTRACE_ROOT}/include
            )
set(PWTRACE_INCLUDE_DIRS ${PWTRACE_INCLUDE_DIR})
message("Found pwTrace headers: ${PWTRACE_INCLUDE_DIRS}")


find_library ( PWTRACE_LIBRARY
            NAMES 
                pwtrace
            HINTS
                ${PWTRACE_ROOT_DIR}/lib
                ${PWTRACE_ROOT}/lib
            PATHS
                C:/pwtrace/lib
             )
set(PWTRACE_LIBRARIES ${PWTRACE_LIBRARY})
message("Found pwTrace libs: ${PWTRACE_LIBRARIES}")
