#include "stateevent.h"

QEvent::Type StateEvent::eventType()
{
    static QEvent::Type _t = (QEvent::Type)QEvent::registerEventType(QEvent::User + 100);
    return _t;
}

StateEvent::StateEvent(const QString& eventName, bool isSinglePress)
    : QInputEvent(eventType())
    , m_EventName(eventName)
    , m_IsSinglePress(isSinglePress)
{
}

const QString& StateEvent::eventName() const
{
    return m_EventName;
}

bool StateEvent::isSinglePress() const
{
    return m_IsSinglePress;
}
