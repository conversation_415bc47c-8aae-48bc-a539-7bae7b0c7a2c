/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#include "bodymarkqbitwidget.h"
#include "ui_bodymarkqbitwidget.h"

BodyMarkQBitWidget::BodyMarkQBitWidget(QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::BodyMarkQBitWidget)
{
    ui->setupUi(this);

    ui->bodyMarkWidget->hideArrowButton();
}

BodyMarkQBitWidget::~BodyMarkQBitWidget()
{
    delete ui;
}

ImageSkimManager* BodyMarkQBitWidget::bodyMarkWidget() const
{
    return ui->bodyMarkWidget;
}
