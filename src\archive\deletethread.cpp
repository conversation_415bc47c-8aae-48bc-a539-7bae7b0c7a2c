#include "deletethread.h"
#include "util.h"
#include "syncer.h"
#include <qmath.h>
#include <QDir>
#include "databaseviewmodel.h"
#include "generalworkflowfunction.h"
#include "dataaccesslayerhelper.h"
#include "patient.h"
#include "study.h"
#include "abstractdbconnection.h"
#include "dbtranscation.h"

DeleteThread::DeleteThread(QObject* parent)
    : FileOperationThread(parent)
    , m_model(NULL)
    , m_DelValue(0)
    , m_CurValue(0)
    , m_SourceSize(0)
    , m_DeleteParentDir(false)
{
    connect(this, SIGNAL(finished()), this, SLOT(onFinished()));
    connect(this, SIGNAL(updateQueryView(DataBaseViewModel*)), this, SLOT(onUpdateQueryView(DataBaseViewModel*)),
            Qt::QueuedConnection);
}

void DeleteThread::run()
{
    UTIL_DATA_SYNCER

    m_SourceSize = calcSrcSizes(m_FileInfoList);
    if (m_FileInfoList.isEmpty())
    {
        return;
    }

    prepareBaseNameToDeleteFile();
    int count = 0;
    for (int i = 0; i < m_BaseNameList.count(); i++)
    {
        QDir dirParent = QDir(m_FileInfoList[i].path());
        QFileInfoList parentFileList = dirParent.entryInfoList(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);
        foreach (QFileInfo fileInfo, parentFileList)
        {
            if (fileInfo.fileName().contains(m_BaseNameList.at(i)))
            {
                deleteItem(fileInfo);
            }
            /******************added by jinyuqi 保证删除子文件夹的同时不删除病人文件夹*/
            QFileInfoList fileList = dirParent.entryInfoList(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);
            if (fileList.count() > 0)
                m_DeleteParentDir = false;
            else
                m_DeleteParentDir = true;
            /****************************************************************/

            if (m_DeleteParentDir)
            {
                qDebug() << "DeleteParentDir" << fileInfo.path();
                Util::Rmdir(fileInfo.path());
            }
            count++;
        }
    }

    deletePatienFromDataBase(m_FileInfoList, m_model);

    m_FileInfoList.clear();
}

void DeleteThread::onFinished()
{
    m_DeleteParentDir = false;
}

void DeleteThread::setDeleteArgs(const QFileInfoList& fileInfoList)
{
    m_FileInfoList = fileInfoList;
    //    m_SourceSize = calcSrcSizes(m_FileInfoList);
    m_DelValue = 0;
}

bool DeleteThread::deleteItem(const QFileInfo& fileInfo)
{
    if (fileInfo.isDir())
    {
        QDir dir(fileInfo.filePath());
        // must include hidden files in the filter,otherwise dir wont't be empty,thus delete failed.
        QFileInfoList fileInfoList = dir.entryInfoList(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);
        for (int i = 0; i < fileInfoList.count(); i++)
        {
            deleteItem(fileInfoList.at(i));
        }
        // make sure the dir is empty now, otherwise,will unable to delete the dir.
        dir.rmdir(dir.path());
        Util::sync();
    }
    else if (fileInfo.isFile())
    {
        m_DelValue += fileInfo.size();
        m_CurValue = qCeil(100 * qreal(m_DelValue) / qreal(m_SourceSize));
        {
            fileInfo.absoluteDir().remove(fileInfo.fileName());
            Util::sync();
        }
        emit curValue(m_CurValue);
    }
    return true;
}

void DeleteThread::setIfDeleteParentDir(bool deleteParentDir)
{
    m_DeleteParentDir = deleteParentDir;
}

void DeleteThread::setDataBaseViewModel(DataBaseViewModel* model)
{
    m_model = model;
}

void DeleteThread::deletePatienFromDataBase(const QFileInfoList& fileInfoList, DataBaseViewModel* model)
{
    if (model == NULL)
    {
        return;
    }

    QFileInfoList delPatientInfoList;
    {
        DBTranscation t(*DataAccessLayerHelper::sqlDatabase()); //节省数据库操作时间
        int count = 0;
        foreach (QFileInfo patientInfo, fileInfoList)
        {

            QString tempStudyOid = patientInfo.baseName();
            Patient* delPatent = NULL;
            bool isPatient = false;

            if (tempStudyOid.isEmpty())
            {
                QString tempPatientId = patientInfo.path().split("/").last();
                delPatent = DataAccessLayerHelper::getPatient(tempPatientId, false);
                isPatient = true;
            }
            else
            {
                delPatent = GeneralWorkflowFunction::getPatient(tempStudyOid);
                isPatient = false;
            }

            if (delPatent == NULL)
            {
                emit curValue((++count) * 100 / fileInfoList.count());
                continue;
            }

            //删除数据库中study和patient之前启动deletethread
            Util::processEvents(QEventLoop::ExcludeUserInputEvents);

            if (isPatient)
            {
                QFileInfoList tempListStudy;
                QList<Study*> studies = DataAccessLayerHelper::getStudies(delPatent);
                for (int i = 0; i < studies.count(); i++)
                {
                    tempListStudy.append(QFileInfo(studies[i]->StudyOid()));
                }
                model->deleteStudy(tempListStudy);

                //先删除所有study再删除patient自己
                delPatientInfoList.append(patientInfo);
            }
            else
            {
                if (DataAccessLayerHelper::getStudies(delPatent).count() == 1)
                {
                    QFileInfoList tempListStudy;
                    tempListStudy.append(patientInfo);
                    model->deleteStudy(tempListStudy);

                    //只有一个study的病人,删除完study后需要再删除patient
                    delPatientInfoList.append(patientInfo);
                }
                else if (DataAccessLayerHelper::getStudies(delPatent).count() > 1)
                {
                    QFileInfoList tempListPatient;
                    tempListPatient.append(patientInfo);
                    model->deleteStudy(tempListPatient);
                }
            }

            delete delPatent;

            model->deletePatient(delPatientInfoList);
            delPatientInfoList.clear();

            emit curValue((++count) * 100 / fileInfoList.count());
        }
    } //为了让t 提前释放
    emit updateQueryView(model);
}

void DeleteThread::onUpdateQueryView(DataBaseViewModel* model)
{
    model->updateQuery();
}

void DeleteThread::prepareBaseNameToDeleteFile()
{
    m_BaseNameList.clear();
    foreach (QFileInfo fileInfo, m_FileInfoList)
    {
        //删除整个病人病例
        if (fileInfo.isDir())
        {
            m_BaseNameList.append(fileInfo.baseName());
        }
        else
        {
            //删除图片或电影
            if (fileInfo.fileName().contains(".bmp"))
            {
                m_BaseNameList.append(fileInfo.baseName());
            }
        }
    }
}
