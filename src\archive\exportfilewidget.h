#ifndef EXPORTFILEWIDGET_H
#define EXPORTFILEWIDGET_H
#include "archive_global.h"

#include <QWidget>
#include "basewidget.h"
#include "baseinputabledialogframe.h"
#include "threadmodel.h"
#include "dicomtaskmanager.h"
#include "tool/others/baselock.h"

/**
 * @brief 导出文件
 */
class ProgressBarView;
class ExportFileWidget;
class ImageProcessedCopy;
class DeleteFilesController;
class IStateManager;
class IColorMapManager;
class IDiskDevice;

class ARCHIVESHARED_EXPORT ExportFileWidgetDialog : public BaseInputAbleDialogFrame
{
    Q_OBJECT
public:
    explicit ExportFileWidgetDialog(IStateManager* stateManager, IColorMapManager* colorMapManager,
                                    IDiskDevice* diskDevice, QWidget* parent = 0);
    ExportFileWidget* getFileWidget();
    bool isUDiskMounted() const;

private:
    ExportFileWidget* m_Child;
};

class QFileSystemModel;
class ConfirmDialogFrame;
namespace Ui
{
class ExportFileWidget;
}

class ARCHIVESHARED_EXPORT ExportFileWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit ExportFileWidget(IStateManager* stateManager, IColorMapManager* colorMapManager, IDiskDevice* diskDevice,
                              QWidget* parent = 0);
    ~ExportFileWidget();
    void setDefaultDirName(const QString& dir);
    /**
     * @brief setExportFilePaths 将要被导出的文件列表
     *
     * @param filePaths
     */
    void setExportFilePaths(const QStringList& filePaths);
    bool isUDiskMounted() const;
    void setIsCurrentMultiChoice(bool value);
    void setDeleteAfterSendExam(bool value);
    void setDicomTaskManager(DicomTaskManager* dicomTaskManager);
    void setDeleteFilesController(DeleteFilesController* controller);
    void showDcmSRCheckBox(bool value);
    void setContainRetrieveExam(bool value);
signals:
    void deleteCurrentSelections();
    void closed();
    void threadStarted(CopyThread* copyThread);
    void exportTypeChanged(QString type);

protected:
    void showEvent(QShowEvent* e);
    void retranslateUi();

private:
    void setLineEditAndLableVisible(bool value);
    void checkIfNeedSingleFrameImgData(QStringList& filePaths);
    void responseUIToFilePaths();
    void initRadioButtons();
    void convertRpt2Pdf();
    void setCopyOptions();
    void setCopyStatus(bool bStatus);
    bool getCopyStatus();

public slots:
    void onExportTypeChanged(QString type);
private slots:
    void onConfirmButtonClicked(int value);
    void onInfoShowed(bool isShowInfo);
    void onThreadFinished();
    void on_pushButton_clicked();
    void on_pushButton_2_clicked();
    void stopCopy();
    //    void deleteFileInfo(const QFileInfoList &infoList);
    void on_comboBox_currentIndexChanged(int index);
    void on_radioButtonBMP_toggled(bool checked);
    void on_radioButtonJPG_toggled(bool checked);
    void on_radioButtonPNG_toggled(bool checked);
    void on_radioButtonDCM_toggled(bool checked);
    void on_checkBoxGDPR_toggled(bool checked);

private:
    Ui::ExportFileWidget* ui;
    QFileSystemModel* model;
    QStringList m_FilePaths;
    ProgressBarView* m_ProgressBar;
    ConfirmDialogFrame* m_ConfirmDialog;
    ThreadModel m_ThreadModel;
    ImageProcessedCopy* m_copy;
    DicomTaskManager* m_DicomTaskManager;
    DeleteFilesController* m_DeleteFilesController;
    QString m_ImageType;
    bool m_isCurrentMulticChoice;
    bool m_isCopy;
    bool m_IsGDPR;
    bool m_IsShowTransformInfo;
    bool m_IsCopyThreadStarted; //记录是否开启过拷贝线程
    WmLock cpLock;
    QString m_DefaultDir;
    QString m_DirPath;
    bool m_ContainRetrieveExam;
    static const char* const m_AnimalString[];
    enum ANIMALSTRING
    {
        Delete_animal_exam_after_sending
    };
};

#endif // EXPORTFILEWIDGET_H
