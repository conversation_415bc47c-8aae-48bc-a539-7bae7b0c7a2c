#!/bin/bash

getknown_hosts() 
{
    # 'command -v ssh-agent >/dev/null || ( apk add --update openssh )' 
    #eval $(ssh-agent -s)
    #echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add
    mkdir -p ~/.ssh
    chmod 700 ~/.ssh
    ssh-keyscan ************* > ~/.ssh/known_hosts
    chmod 644 ~/.ssh/known_hosts

    git remote set-url origin git@*************:usplatform/xunit.git
    git config --global user.email "<EMAIL>"
    git config --global user.name "gitlab"
}

getknown_hosts
