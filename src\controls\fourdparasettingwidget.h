#ifndef FOURDPARASETTINGWIDGET_H
#define FOURDPARASETTINGWIDGET_H

#include "controls_global.h"
#include "basewidget.h"
#include "sonoparametersclientbase.h"

namespace Ui
{
class FourDParaSettingWidget;
}

class CONTROLSSHARED_EXPORT FourDParaSettingWidget : public BaseWidget, public SonoParametersClientBase
{
    Q_OBJECT
public:
    explicit FourDParaSettingWidget(QWidget* parent = 0);
    virtual ~FourDParaSettingWidget();
    void initalize();

protected:
    void onSetSonoParameters();
    virtual void retranslateUi();

private slots:
    void on_comboBox_currentIndexChanged(int index);

private:
    Ui::FourDParaSettingWidget* ui;
};

#endif // FOURDPARASETTINGWIDGET_H
