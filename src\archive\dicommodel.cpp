#include "dicommodel.h"
#include "basedicomtool.h"
#include "dicomprinttool.h"
#include "dicomsrtool.h"
#include "dicomstoragetool.h"
#include "filenameorganize.h"
#include "gdprutility.h"
#include "generalworkflowfunction.h"
#include "messageboxframe.h"
#include "resource.h"
#include "serverlist.h"
#include "util.h"
#include <QDir>
#include <QFileInfo>

DicomModel::DicomModel(QObject* parent)
    : QObject(parent)
    , m_ParentWidget(nullptr)
    , m_IsGDPR(false)
{
}

DicomModel::DicomModel(DicomTaskManager* manager, const QStringList& fileNames, const QStringList& filePathes)
    : m_DicomTaskManager(manager)
    , m_FileNames(fileNames)
    , m_FilePathes(filePathes)
    , m_IsGDPR(false)
{
    analysisFiles();
}

QList<Patient*> DicomModel::getPatients(const QStringList& value) const
{
    QList<Patient*> patients;
    foreach (const QString& str, value)
    {
        QFileInfo fileInfo(str);
        QString studyOid = fileInfo.baseName();
        patients.append(GeneralWorkflowFunction::getPatient(studyOid));
    }
    return patients;
}

QList<Patient*> DicomModel::getPatients() const
{
    return getPatients(m_FilePathes);
}

QStringList DicomModel::getFileNamesInPath(const QString& path) const
{
    QStringList list;
    QFileInfo fileInfo(path);
    if (fileInfo.isDir())
    {
        QDir dir(path);
        QFileInfoList fileList = dir.entryInfoList(QDir::Files);
        foreach (const QFileInfo& file, fileList)
        {
            if (file.exists())
            {
                list.append(file.filePath());
            }
        }
    }
    return list;
}

void DicomModel::setSpecialServiceGroup(const QString& value)
{
    m_SpecialServiceGroup = value;
}

void DicomModel::setParentWidget(QWidget* p)
{
    m_ParentWidget = p;
}

void DicomModel::setIsGDPR(bool isGDPR)
{
    m_IsGDPR = isGDPR;
}

int DicomModel::doDicomWork(DicomChison::ServiceIndex index)
{
    if (index == DicomChison::print)
    {
        if (!m_SpecialServiceGroup.isEmpty())
        {
            QStringList keys;
            keys << DicomChison::netKeyOfAetitleName() << DicomChison::netKeyOfIPName()
                 << DicomChison::netKeyOfPortName() << DicomChison::netKeyOfTimeoutName();
            QMap<QString, QString> res =
                DicomChison::getSpecifiedKeysValues(DicomChison::print, m_SpecialServiceGroup, keys);
            ServerList::revisePStatCfgFile(
                res.value(DicomChison::netKeyOfAetitleName()), res.value(DicomChison::netKeyOfIPName()),
                res.value(DicomChison::netKeyOfPortName()), DicomChison::specialPstatFileName());
        }
    }
    int successNumSum = 0;
    QList<Patient*> patients = getPatients();
    for (int i = 0; i < m_FilePathes.count(); i++)
    {
        if (!QFileInfo(m_FilePathes.at(i)).baseName().startsWith(Resource::dicomRetrievePrefix))
        {
            int successNum = doDicomWork(index, patients.at(i), i);
            successNumSum += successNum;
        }
    }
    qDeleteAll(patients);
    return successNumSum;
}

int DicomModel::doDicomWork(DicomChison::ServiceIndex sindex, Patient* patient, int index)
{
    if (m_IsGDPR && (!GDPRUtility::isSupportGDPR(m_FileNamesList.at(index))) && (sindex != DicomChison::sr))
    {
        QStringList GDPRFile;
        MessageBoxFrame::warning(QObject::tr("Some images cannot be de-identified!"));
        return addDicomWorkToGDPR(sindex, patient, index, true);
    }
    else
    {
        return addDicomWorkToGDPR(sindex, patient, index, false);
    }
}

int DicomModel::addDicomWorkToGDPR(DicomChison::ServiceIndex sindex, Patient* patient, int index, bool isGDPR)
{
    BaseDicomTool* base = NULL;
    int successNum = 0;
    foreach (QString fileNamePath, m_FileNamesList.at(index))
    {
        if (fileNamePath.endsWith(Resource::imgSuffix))
        {
            continue;
        }
        if (fileNamePath.endsWith(Resource::bmpSuffix))
        {
            QStringList list(fileNamePath);
            QStringList resultList = FileNameOrganize::toImgScreenFileNames(list);
            if (resultList.count() <= 0)
            {
                continue;
            }
        }
        if (!isGDPR || GDPRUtility::isSupportGDPR(QStringList(fileNamePath)))
        {
            switch (sindex)
            {
            case DicomChison::storage:
                base = new DicomStorageTool(patient, m_DicomTaskManager, this);
                break;
            case DicomChison::print:
                base = new DicomPrintTool(patient, m_DicomTaskManager, this);
                break;
            case DicomChison::sr:
                base = new DicomSrTool(patient, m_DicomTaskManager, this);
                break;
            default:
                break;
            }
            base->setIsGDPR(m_IsGDPR);

            base->setImagePathes(QStringList(fileNamePath));

            base->setSpecialServiceGroup(m_SpecialServiceGroup);
            base->run();
            bool success = base->isSuccess();
            if (success)
            {
                ++successNum;
            }
            delete base;
            base = NULL;
        }
    }
    return successNum;
}

void DicomModel::analysisFiles()
{
    if (!m_FileNames.isEmpty())
    {
        QFileInfo file(m_FileNames.at(0));
        m_FilePathes.clear();
        m_FilePathes.append(file.path());
        m_FileNamesList.append(m_FileNames);
    }
    else
    {
        foreach (const QString& str, m_FilePathes)
        {
            m_FileNamesList.append(getFileNamesInPath(str));
        }
    }
}
