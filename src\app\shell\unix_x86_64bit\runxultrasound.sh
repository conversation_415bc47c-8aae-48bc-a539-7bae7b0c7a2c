#!/bin/sh

touch /tmp/chison_boot_flag
export DCMDICTPATH=res/dicom/dicom.dic
export LD_LIBRARY_PATH=lib:$LD_LIBRARY_PATH:/opt/Qt/5.15.2/gcc_64/lib:ffmpeglib
export COV_LICENSE_LOCATION=res/general
export PATH=$PATH:ffmpeglib
if [ ! -d "/usr/tigerapp/cl_cache" ]; then
    mkdir /usr/tigerapp/cl_cache -p
fi

X11_NUM=`ps aux | grep Xorg | wc -l`
echo X11_NUM = $X11_NUM
if [ $X11_NUM != 2 ]; then
    echo "[runxultrasound]: Xorg Missing ..." > /tmp/chison-xorg.log

    killall xultrasound

    Xorg &

    sh /etc/set_resolution.sh
    sh /etc/set_touchscreen.sh
fi

export DISPLAY=":0.0"

ulimit -s 16384
ulimit -c unlimited
echo 2 > /proc/sys/fs/suid_dumpable
mkdir /var/tmp/corefiles
chmod 777 /var/tmp/corefiles
echo "/var/tmp/corefiles/core-%e">/proc/sys/kernel/core_pattern
rm -rf ~/.cache/System/QtWebEngine

./xultrasound --no-sandbox
#gdb --args ./xultrasound --no-sandbox
#valgrind --log-file=./leaktrace.log --leak-check=full ./xultrasound
#valgrind --log-file=./leaktrace.log --leak-check=full --show-leak-kinds=all ./xultrasound
