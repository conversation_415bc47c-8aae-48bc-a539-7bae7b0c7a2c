#ifndef STATEEVENT_H
#define STATEEVENT_H

#include <QString>
#include <QInputEvent>
#include "autotest_global.h"
class AUTOTESTSHARED_EXPORT StateEvent : public QInputEvent
{
public:
    static QEvent::Type eventType();
    StateEvent(const QString& eventName, bool isSinglePress = true);
    const QString& eventName() const;
    bool isSinglePress() const;

private:
    QString m_EventName;
    bool m_IsSinglePress;
};

#endif // STATEEVENT_H
