<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MotorOperationWidget</class>
 <widget class="QWidget" name="MotorOperationWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>625</width>
    <height>474</height>
   </rect>
  </property>
  <property name="acceptDrops">
   <bool>false</bool>
  </property>
  <property name="windowTitle">
   <string>Motor Operations</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,3">
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <widget class="QPushButton" name="pushButtonGetVer">
       <property name="text">
        <string>Version</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonSendParam">
       <property name="text">
        <string>SendParam</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonstartMotor">
       <property name="text">
        <string>Start</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonstopMotor">
       <property name="text">
        <string>Stop</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonresetMotor">
       <property name="text">
        <string>Reset</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonMotorPosition">
       <property name="text">
        <string>Position</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonMotorInfo">
       <property name="text">
        <string>Info</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QPlainTextEdit" name="plainTextEdit"/>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
