<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ExamDateBetweenWidget</class>
 <widget class="QWidget" name="ExamDateBetweenWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>528</width>
    <height>198</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Exam Between</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>From</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="BaseDateEdit" name="dateEditFrom">
       <property name="calendarPopup">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>To</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="BaseDateEdit" name="dateEditTo">
       <property name="calendarPopup">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>197</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonOK">
       <property name="text">
        <string>OK</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonCancle">
       <property name="text">
        <string>Cancel</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BaseDateEdit</class>
   <extends>QDateEdit</extends>
   <header>basedateedit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
