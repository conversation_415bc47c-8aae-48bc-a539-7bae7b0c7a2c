#ifndef ABSTRACTFOURDLICENSEPROCESSOR_H
#define ABSTRACTFOURDLICENSEPROCESSOR_H
#include "ifourdlicenseprocessor.h"

class CONTROLSSHARED_EXPORT AbstractFourDLicenseProcessor : public IFourDLicenseProcessor
{
    Q_OBJECT
public:
    explicit AbstractFourDLicenseProcessor(QObject* parent = 0);
    virtual ~AbstractFourDLicenseProcessor();
    bool isFourDOpened() const;
    int remainDays() const;
    void processFourDLicenseState(bool showInfo = true);
    void setSonoParameters(SonoParameters* sonoParameters);
public slots:
    void onVirtualHDStatusChanged(bool isOpen);
    void onSystemDateChanged();

protected:
    SonoParameters* m_SonoParameters;
    FourDLicenseState m_FourDLicenseState;
    int m_FourDRenderMax;
    bool m_FourDOpened;
    int m_RemainDays;
};

#endif // ABSTRACTFOURDLICENSEPROCESSOR_H
