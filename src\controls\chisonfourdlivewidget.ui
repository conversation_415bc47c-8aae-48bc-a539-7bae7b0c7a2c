<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ChisonFourDLiveWidget</class>
 <widget class="QWidget" name="ChisonFourDLiveWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>438</width>
    <height>361</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_4">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_3">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <widget class="QWidget" name="fourDRenderWidget" native="true">
       <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,1,0">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>1</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>1</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout">
          <property name="spacing">
           <number>0</number>
          </property>
          <item>
           <spacer name="verticalSpacer">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="ChisonFourDColorBarWidget" name="colorBarOf2DImage" native="true">
            <property name="minimumSize">
             <size>
              <width>22</width>
              <height>256</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="verticalSpacer_2">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <widget class="ChisonFourDRenderWidget" name="renderWidget" native="true">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>200</width>
            <height>200</height>
           </size>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <property name="spacing">
           <number>0</number>
          </property>
          <item>
           <spacer name="verticalSpacer_3">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="ChisonFourDColorBarWidget" name="colorBarOf3DImage" native="true">
            <property name="minimumSize">
             <size>
              <width>22</width>
              <height>256</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="verticalSpacer_4">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_3">
       <property name="spacing">
        <number>0</number>
       </property>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="FourDFreezeBarWidget" name="fourDFreezeBarWidget" native="true">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="FourDTransformWidget" name="fourDTransformWidget" native="true">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <widget class="GrayCurveWidget" name="grayCurveThumbnail" native="true">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="verticalSpacer_5">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>GrayCurveWidget</class>
   <extends>QWidget</extends>
   <header>graycurvewidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>FourDFreezeBarWidget</class>
   <extends>QWidget</extends>
   <header>fourdfreezebarwidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>FourDTransformWidget</class>
   <extends>QWidget</extends>
   <header>fourdtransformwidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>ChisonFourDRenderWidget</class>
   <extends>QWidget</extends>
   <header>chisonfourdrenderwidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>ChisonFourDColorBarWidget</class>
   <extends>QWidget</extends>
   <header>chisonfourdcolorbarwidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
