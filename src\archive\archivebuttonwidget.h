#ifndef ARCHIVEBUTTONWIDGET_H
#define ARCHIVEBUTTONWIDGET_H
#include "archive_global.h"

//#include <QWidget>
#include "basewidget.h"
#include "screenorientationmodel.h"
#include <QStringList>

namespace Ui
{
class ArchiveButtonWidget;
}
/**
 * @brief 控制Archive界面右侧的按钮
 */
class ARCHIVESHARED_EXPORT ArchiveButtonWidget : public BaseWidget
{
    Q_OBJECT

public:
    /**
     * @brief 包含的所有按钮
     */
    enum BUTTON
    {
        PatientInfo,
        Report,
        Delete,
        Backup,
        Send,
        Restore
    };
    explicit ArchiveButtonWidget(QWidget* parent = 0);
    ~ArchiveButtonWidget();
    /**
     * @brief setButtonEnabled 选择性的设置按钮的状态
     *
     * @param enabled
     * @param index
     */
    void setButtonEnabled(bool enabled, ArchiveButtonWidget::BUTTON index);
    bool isButtonEnabled(ArchiveButtonWidget::BUTTON index) const;
    void setDiskPath(const QString& value);

protected:
    /**
     * @brief setAllbuttonsEnabled 控制所有按钮的状态
     *
     * @param enabled
     */
    void setAllbuttonsEnabled(bool enabled);
signals:
    void infoButtonClicked();
    void reportButtonClicked();
    void deleteButtonClicked();
    void backupButtonClicked();
    void sendButtonClicked();
    void restoreButtonClicked();

protected:
    void retranslateUi();
    void orientationChanged(Qt::ScreenOrientation orientation);
private slots:
    void on_pushButtonInfo_clicked();
    void on_pushButtonReport_clicked();
    void on_pushButtonDelete_clicked();
    void on_pushButtonBackup_clicked();
    void on_pushButtonSend_clicked();
    void on_pushButtonRestore_clicked();
    // 防止Multi引发的按钮变灰问题 added by jinyuqi
    void onClearSectionToMakeActiveFalse();
    /**
     * @brief onSelectedModelChanged 当选中的studys改变时，按钮要据此改变
     *
     * @param modelList
     */
    void onSelectedModelChanged(const QStringList& modelList, bool isDir);
    void onMultiClicked(bool multi);
    /**
     * @brief onActiveExamClicked 如果当前选择的是正在进行的检查，需要对按钮delete,backup
     *   特殊处理
     * @param flag
     */
    void onActiveExamClicked(bool flag);

private:
    Ui::ArchiveButtonWidget* ui;
    bool m_Multi;
    QString m_Disc;
    bool m_ActiveExamClicked;
    QString m_AnimalString;
    ScreenOrientationModel* m_Model;
};

#endif // ARCHIVEBUTTONWIDGET_H
