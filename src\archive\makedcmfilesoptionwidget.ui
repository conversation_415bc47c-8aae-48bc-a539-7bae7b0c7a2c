<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MakeDcmFilesOptionWidget</class>
 <widget class="QWidget" name="MakeDcmFilesOptionWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>340</width>
    <height>158</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <widget class="QLabel" name="label">
     <property name="text">
      <string>Compression Mode</string>
     </property>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="BaseComboBox" name="comboBoxCompressionMode"/>
   </item>
   <item row="1" column="0">
    <widget class="QLabel" name="label_2">
     <property name="text">
      <string>Compression Ratio</string>
     </property>
    </widget>
   </item>
   <item row="1" column="1">
    <widget class="BaseComboBox" name="comboBoxCompressionRatio"/>
   </item>
   <item row="2" column="0">
    <widget class="QLabel" name="label_3">
     <property name="text">
      <string>Max Framerate</string>
     </property>
    </widget>
   </item>
   <item row="2" column="1">
    <widget class="BaseComboBox" name="comboBoxMaxFramerate"/>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BaseComboBox</class>
   <extends>QComboBox</extends>
   <header>basecombobox.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
