/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#ifndef TESTSWITCHPROBECOMMAND_H
#define TESTSWITCHPROBECOMMAND_H
#include "autotest_global.h"

#include "itestcommand.h"
#include <QObject>
#include <QTime>

/**
 * 自动切换探头的模拟小测试，其实是可以被AutoTest给取代，在实现这个类之前并没有相关的自动测试机制
 */

class BaseProbeSelectionChild;
class IStateManager;
class QTimer;
class TestTimeCommand;

class AUTOTESTSHARED_EXPORT TestSwitchProbeCommand : public QObject, public ITestCommand
{
    Q_OBJECT
public:
    typedef void (TestSwitchProbeCommand::*Function)();
    TestSwitchProbeCommand(BaseProbeSelectionChild* probeFrame, IStateManager* stateManager);
    virtual ~TestSwitchProbeCommand();
    virtual void execute();
    virtual void undo();
    void setTestTime(int sec);
    BaseProbeSelectionChild* probeFrame() const;
    IStateManager* stateManager() const
    {
        return m_StateManager;
    }
signals:
    void finishTest();
private slots:
    void onTimeOut();

private:
    void organizeFunctionListAndCommand();
    void doTest(int index);
    void doBreak(int index);
    void testProbe(int currentIndex);
    void testProbeFunction();
    void postProbeEvent();
    BaseProbeSelectionChild* m_probeFrame;
    IStateManager* m_StateManager;
    QTimer* m_timer;
    int m_time;
    QList<Function> m_functionList;
    QList<TestTimeCommand*> m_testTimeCommandList;
    int m_currentFunctionIndex;
    int m_currentProbeIndex;
};

#endif // TESTSWITCHPROBECOMMAND_H
