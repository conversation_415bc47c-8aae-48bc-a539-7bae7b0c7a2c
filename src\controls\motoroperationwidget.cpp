#include "motoroperationwidget.h"
#include "ui_motoroperationwidget.h"
#ifdef USE_MOTOR
#include "motorcontrol.h"
#endif

MotorOperationDialog::MotorOperationDialog(QWidget* parent)
    : BaseInputAbleDialogFrame(parent)
{
    this->setIsShowOk(false);
    m_Child = new MotorOperationWidget(this);
    connect(m_Child, SIGNAL(sendParameter()), this, SIGNAL(sendParameter()));
    this->setContent(m_Child);
}

void MotorOperationDialog::setMotorControl(MotorControl* motorCtrl)
{
    m_Child->setMotorControl(motorCtrl);
}

MotorOperationWidget::MotorOperationWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::MotorOperationWidget)
{
    ui->setupUi(this);
}

MotorOperationWidget::~MotorOperationWidget()
{
    delete ui;
}

void MotorOperationWidget::setMotorControl(MotorControl* motorCtrl)
{
    m_MotorCtrl = motorCtrl;
#ifdef USE_MOTOR
    connect(m_MotorCtrl, SIGNAL(dataReached(QByteArray)), this, SLOT(onDataReached(QByteArray)));
#endif
}

void MotorOperationWidget::retranslateUi()
{
    ui->retranslateUi(this);
}

void MotorOperationWidget::on_pushButtonGetVer_clicked()
{
#ifdef USE_MOTOR
    m_MotorCtrl->getVersion();
#endif
}

void MotorOperationWidget::on_pushButtonSendParam_clicked()
{
    qDebug() << " MotorOperationDialog::on_pushButtonSendParam_clicked";
    emit sendParameter();
}

void MotorOperationWidget::on_pushButtonstartMotor_clicked()
{
#ifdef USE_MOTOR
    m_MotorCtrl->startMotor();
#endif
}

void MotorOperationWidget::on_pushButtonstopMotor_clicked()
{
#ifdef USE_MOTOR
    m_MotorCtrl->stopMotor();
#endif
}

void MotorOperationWidget::on_pushButtonresetMotor_clicked()
{
#ifdef USE_MOTOR
    m_MotorCtrl->resetMotor();
#endif
}

void MotorOperationWidget::on_pushButtonMotorPosition_clicked()
{
#ifdef USE_MOTOR
    m_MotorCtrl->getMotorPosition();
#endif
}

void MotorOperationWidget::on_pushButtonMotorInfo_clicked()
{
#ifdef USE_MOTOR
    m_MotorCtrl->getMotorInfo();
#endif
}

void MotorOperationWidget::onDataReached(const QByteArray& data)
{
    ui->plainTextEdit->setPlainText(QString(data.toHex()));
}
