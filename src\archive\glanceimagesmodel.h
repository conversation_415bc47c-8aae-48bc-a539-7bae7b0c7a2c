#ifndef GLANCEIMAGESMODEL_H
#define GLANCEIMAGESMODEL_H
#include "archive_global.h"

#include <QObject>
#include <QFileInfo>
#include <QStringList>

class GlanceImagesDialog;
class QWidget;
class Patient;
class PatientWorkflow;
class PatientWorkflowModel;
class GeneralWorkflowFunction;
class IToolsFacade;
class IStateManager;
class IColorMapManager;

class ARCHIVESHARED_EXPORT GlanceImagesModel : public QObject
{
    Q_OBJECT
public:
    explicit GlanceImagesModel(IStateManager* stateManager, QWidget* parent = 0);
    ~GlanceImagesModel();
    /**
     * @brief exec 运行 EasyView
     *
     * @return
     */
    void exec();
    /**
     * @brief glanceImagesDialog
     *
     * @return GlanceImagesDialog*
     */
    GlanceImagesDialog* glanceImagesDialog() const;
    /**
     *  @brief setPatientWorkflow 设置当前的病人信息
     *
     *  @param patientWorkflow
     *
     *  @return
     */
    void setPatientWorkflow(PatientWorkflow* patientWorkflow);
    void setGeneralWorkflowFunction(GeneralWorkflowFunction* generalWorkflowFunction);
    void setToolsFacade(IToolsFacade* value);
    void setColorMapManager(IColorMapManager* colorMapManager);

signals:
    /**
     * @brief closed 该信号用于链接 PatientWorkflowModel
     *
     * @return
     */
    void closed();
    void onlyJump();
    /**
     * @brief entryPatientState 该信号链接 PatientWorkflowModel的
     *        onEntryPatientState
     *
     * @return
     */
    void entryPatientState();
    /**
     * @brief entryArchiveState 该信号链接 PatientWorkflowModel的
     *        onEntryArchiveState
     *
     * @return
     */
    void entryArchiveState();
    void studyOidChanged(const QString& oid);
    void currentDiskPathChanged(const QString& diskPath);
    void entryContinueExamState();
    void entryEditExamState();
    void fourdCallBack(const QString&);
    void dirPathListChanged(const QStringList&);
    void diskPathChanged(const QString&);
public slots:
    /**
     * @brief onSendDirPathList 链接GlanceImagesImagesDialog的sendDirPathList信号
     *        用于锁定图片放置的文件夹
     *
     * @param list
     *
     * @return
     */
    void onSendDirPathList(const QStringList& list);
    void clearDirPathList();
    void onCurrentDiskPathChanged(const QString& disk);
    void onFourdCallBack(const QString& filePath);

protected:
    QHash<QString, QString> extractFilePath(const QStringList& pathList);

private:
    GlanceImagesDialog* m_ImageDialog;
    PatientWorkflow* m_PatientWorkflow;
    Patient* m_Patient;
    QStringList m_DirPathList;
    GeneralWorkflowFunction* m_GeneralWorkflowFunction;
};

#endif // GLANCEIMAGESMODEL_H
