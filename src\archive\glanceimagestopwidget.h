#ifndef GLANCEIMAGESTOPWIDGET_H
#define GLANCEIMAGESTOPWIDGET_H
#include "archive_global.h"

//#include <QWidget>
#include "basewidget.h"
#include "screenorientationmodel.h"

namespace Ui
{
class GlanceImagesTopWidget;
}

/**
 * @brief easyView界面中，显示Id、name的部分
 */
class ARCHIVESHARED_EXPORT GlanceImagesTopWidget : public BaseWidget
{
    Q_OBJECT
    Q_PROPERTY(bool isHor READ isHor)
public:
    explicit GlanceImagesTopWidget(QWidget* parent = 0);
    ~GlanceImagesTopWidget();
    /**
     * @brief setPatientInfoList 添加patientId列表
     *
     * @param idList
     */
    void setPatientInfoList(const QStringList& idList);
    bool isHor();
signals:
    void currentPatientInfoChanged(const QString& id);

protected:
    void retranslateUi();
    void orientationChanged(Qt::ScreenOrientation orientation);

private:
private slots:
    void on_comboBoxId_currentIndexChanged(const QString& arg1);

private:
    Ui::GlanceImagesTopWidget* ui;
    void setID2AnimalID();
    ScreenOrientationModel* m_Model;
};

#endif // GLANCEIMAGESTOPWIDGET_H
