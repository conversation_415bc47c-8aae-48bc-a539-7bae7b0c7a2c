#include "patientworkflowmodel.h"
#include "archivemanager.h"
#include "glanceimagesmodel.h"
#include "glanceimages.h"
#include "util.h"
#include "patientworkflow.h"
#include "patienteditmodel.h"
#include "patienteditwidget.h"
#include "archivemodel.h"
#include "generalworkflowfunction.h"
#ifdef USE_4D
#include "fourdcallbackmodel.h"
#endif

/**
 * @brief PatientWorkflowModel 控制新建病人、归档管理和超声图片浏览界面之间的相互转换流程，
 *		  保证当前至多有一个界面运行；
 * @param parent
 */
PatientWorkflowModel::PatientWorkflowModel(IStateManager* stateManager, QWidget* parent)
    : QObject(parent)
    , m_Archive(NULL)
    , m_Patient(NULL)
    , m_Glance(NULL)
    , m_PatientWorkflow(NULL)
    , m_GeneralWorkflowFunction(NULL)
    , m_InEditExamState(false)
{
    m_Archive = new ArchiveModel(stateManager, parent);
    m_Patient = new PatientEditModel(stateManager, parent);
    m_Glance = new GlanceImagesModel(stateManager, parent);
#ifdef USE_4D
    m_FourDCallBack = new FourDCallBackModel(parent);
#endif
    m_GeneralWorkflowFunction = new GeneralWorkflowFunction(this);
    m_GeneralWorkflowFunction->setPatientWorkflowModel(this);
    m_Glance->setGeneralWorkflowFunction(m_GeneralWorkflowFunction);
    m_Archive->setGeneralWorkflowFunction(m_GeneralWorkflowFunction);
    m_Patient->setGeneralWorkflowFunction(m_GeneralWorkflowFunction);

    connect(m_Archive, SIGNAL(closed()), this, SIGNAL(archiveDialogClosed()));
    connect(m_Archive, SIGNAL(onlyJump()), this, SIGNAL(onlyJump()));
    connect(m_Patient, SIGNAL(finished(int)), this, SIGNAL(patientDialogClosed(int)));
    connect(m_Patient, SIGNAL(onlyJump()), this, SIGNAL(onlyJump()));
    connect(m_Glance, SIGNAL(closed()), this, SIGNAL(reviewDialogClosed()));
    connect(m_Glance, SIGNAL(onlyJump()), this, SIGNAL(onlyJump()));
    connect(m_Patient, SIGNAL(entryArchiveState()), this, SIGNAL(entryArchiveState()));
    connect(m_Archive, SIGNAL(entryPatientState()), this, SIGNAL(entryPatientState()));
    connect(m_Archive, SIGNAL(entryBrowseState()), this, SIGNAL(entryBrowseState()));
    connect(m_Glance, SIGNAL(entryPatientState()), this, SIGNAL(entryPatientState()));
    connect(m_Glance, SIGNAL(entryArchiveState()), this, SIGNAL(entryArchiveState()));
    connect(m_Archive, SIGNAL(sendDirPathList(QStringList)), m_Glance, SLOT(onSendDirPathList(QStringList)));
    connect(m_Archive, SIGNAL(currentDiskPathChanged(QString)), m_Glance, SLOT(onCurrentDiskPathChanged(QString)));
    connect(m_Patient, SIGNAL(createNewExam()), this, SLOT(onCreateNewExam()));
    connect(m_Archive, SIGNAL(currentDiskPathChanged(QString)), m_Patient, SLOT(onCurrentDiskPathChanged(QString)));
    connect(m_Glance, SIGNAL(currentDiskPathChanged(QString)), m_Patient, SLOT(onCurrentDiskPathChanged(QString)));
    connect(m_Archive, SIGNAL(sendStudyOid(QString)), m_Patient, SLOT(onSetStudyOid(QString)));
    connect(m_Glance, SIGNAL(studyOidChanged(QString)), m_Patient, SLOT(onSetStudyOid(QString)));
    connect(m_Archive, SIGNAL(entryContinueExamState()), this, SIGNAL(entryContinueExamState()));
    connect(m_Archive, SIGNAL(entryEditExamState()), this, SLOT(onEntryEditExamState()));
    connect(m_Glance, SIGNAL(entryContinueExamState()), this, SIGNAL(entryContinueExamState()));
    connect(m_Glance, SIGNAL(entryEditExamState()), this, SLOT(onEntryEditExamState()));
    connect(m_Patient, SIGNAL(entryContinueExamState()), this, SIGNAL(entryContinueExamState()));
    connect(this, SIGNAL(tpButClicked(QString, QString, QString)), this,
            SLOT(onTPButClicked(QString, QString, QString)));
    connect(m_Archive, SIGNAL(autoCallBack(int)), this, SIGNAL(autoCallBack(int)));

    connect(m_Patient->patientEditDialog(), SIGNAL(ntpButClicked(QString, QString, QString)), this,
            SIGNAL(ntpButClicked(QString, QString, QString)));
    connect(m_Archive->archiveManagerDialog(), SIGNAL(ntpButClicked(QString, QString, QString)), this,
            SIGNAL(ntpButClicked(QString, QString, QString)));
    connect(m_Glance->glanceImagesDialog(), SIGNAL(ntpButClicked(QString, QString, QString)), this,
            SIGNAL(ntpButClicked(QString, QString, QString)));

#ifdef USE_4D
    connect(m_Glance, SIGNAL(dirPathListChanged(QStringList)), m_FourDCallBack,
            SLOT(onDirPathListChanged(QStringList)));
    connect(m_Glance, SIGNAL(diskPathChanged(QString)), m_FourDCallBack, SLOT(onDiskPathChanged(QString)));
    connect(m_Glance, SIGNAL(fourdCallBack(QString)), this, SIGNAL(fourdCallBack(QString)));
#endif
}

PatientWorkflowModel::~PatientWorkflowModel()
{
    //    Util::SafeDeletePtr(m_Archive);
    //    Util::SafeDeletePtr(m_Patient);
    //    Util::SafeDeletePtr(m_Glance);
    //    Util::SafeDeletePtr(m_GeneralWorkflowFunction);
}

void PatientWorkflowModel::showArchiveDialog() const
{
    m_Archive->exec();
}

void PatientWorkflowModel::showPatientDialog() const
{
    m_Patient->exec();
}

void PatientWorkflowModel::showGlanceImages()
{
    m_Glance->exec();
}

void PatientWorkflowModel::close()
{
    m_Archive->archiveManagerDialog()->close();
    m_Patient->patientEditDialog()->close();
    m_Glance->glanceImagesDialog()->close();
}

void PatientWorkflowModel::setPatientWorkflow(PatientWorkflow* patientWorkflow)
{
    m_PatientWorkflow = patientWorkflow;
    m_Archive->setPatientWorkflow(m_PatientWorkflow);
    m_Patient->setPatientWorkflow(m_PatientWorkflow);
    m_Glance->setPatientWorkflow(m_PatientWorkflow);
    m_GeneralWorkflowFunction->setPatientWorkflow(m_PatientWorkflow);
}

void PatientWorkflowModel::setDicomTaskManager(DicomTaskManager* dicomTaskManager)
{
    m_Archive->setDicomTaskManager(dicomTaskManager);
    m_Glance->glanceImagesDialog()->setDicomTaskManager(dicomTaskManager);
}

void PatientWorkflowModel::setToolsFacade(IToolsFacade* value)
{
    m_Archive->setToolsFacade(value);
    m_Glance->setToolsFacade(value);
}

void PatientWorkflowModel::setColorMapManager(IColorMapManager* colorMapManager)
{
    m_Archive->setColorMapManager(colorMapManager);
    m_Glance->setColorMapManager(colorMapManager);
}

void PatientWorkflowModel::setDiskDevice(IDiskDevice* diskDevice)
{
    m_Archive->setDiskDevice(diskDevice);
}

PatientEditModel* PatientWorkflowModel::patientEditModel() const
{
    return m_Patient;
}

GlanceImagesModel* PatientWorkflowModel::glanceImagesModel() const
{
    return m_Glance;
}

ArchiveModel* PatientWorkflowModel::archiveModel() const
{
    return m_Archive;
}

#ifdef USE_4D
FourDCallBackModel* PatientWorkflowModel::fourdCallBackModel() const
{
    return m_FourDCallBack;
}
#endif

void PatientWorkflowModel::setIsInEditExamState(bool value)
{
    m_InEditExamState = value;
}

bool PatientWorkflowModel::isInEditExamState() const
{
    return m_InEditExamState;
}

void PatientWorkflowModel::onCreateNewExam()
{
    if (m_Archive->archiveManagerDialog()->isVisible())
    {
        m_Archive->archiveManagerDialog()->reject();
    }

    if (m_Glance->glanceImagesDialog()->isVisible())
    {
        m_Glance->glanceImagesDialog()->reject();
    }
}

void PatientWorkflowModel::onTPButClicked(const QString& category, const QString& butName, const QString& value)
{
    if (category == "Patient")
    {
        if (m_Patient != NULL && m_Patient->patientEditDialog() != NULL)
        {
            m_Patient->patientEditDialog()->onTPButClicked(category, butName, value);
        }
    }
    else if (category == "Archive")
    {
        if (m_Archive != NULL && m_Archive->archiveManagerDialog() != NULL)
        {
            m_Archive->archiveManagerDialog()->onTPButClicked(category, butName, value);
        }
    }
    else if (category == "EasyView")
    {
        if (m_Glance != NULL && m_Glance->glanceImagesDialog() != NULL)
        {
            m_Glance->glanceImagesDialog()->onTPButClicked(category, butName, value);
        }
    }
}

void PatientWorkflowModel::onEntryEditExamState()
{
    m_InEditExamState = true;
    emit entryEditExamState();
}
