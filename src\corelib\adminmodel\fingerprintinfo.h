#ifndef FINGERPRINTINFO_H
#define FINGERPRINTINFO_H

#include <QString>
#include "adminmodel_global.h"

class ADMINMODELSHARED_EXPORT FingerprintInfo
{
public:
    FingerprintInfo();
    FingerprintInfo(int index, int fingerprintid, const QString& fingerprintname, const QString& userid, bool status);
    int fingerprintId() const;
    void setFingerprintId(int fingerprintId);

    QString fingerprintName() const;
    void setFingerprintName(const QString& fingerprintName);

    QString userId() const;
    void setUserId(const QString& userId);

    bool status() const;
    void setStatus(bool status);

    int fingerIndex() const;
    void setFingerIndex(int fingerIndex);

private:
    int m_fingerIndex;
    int m_fingerprintId;
    QString m_fingerprintName;
    QString m_userId;
    bool m_status;
};

#endif // FINGERPRINTINFO_H
