#include "loginmanager.h"
#include "adminconfigmodel.h"
#include "logger.h"
#include "userinfo.h"
#include "authorityinfo.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, LoginManager)

LoginManager::LoginManager(QObject* parent)
    : QObject(parent)
{
    m_model = AdminConfigModel::instance();
}

LoginManager::~LoginManager()
{
}

void LoginManager::initialize()
{
    log()->info() << PRETTY_FUNCTION;
    m_model->fPrintOperator()->verify();
    connect(m_model->fPrintOperator(), &IFingerPrintOperator::finish, this, &LoginManager::finish);
    connect(m_model->fPrintOperator(), &IFingerPrintOperator::send, this, &LoginManager::send);
}

void LoginManager::logout()
{
    m_model->logOut();
}

void LoginManager::stop()
{
    m_model->fPrintOperator()->stop();
}

int LoginManager::login(const QString& username, const QString& pwd)
{
    return m_model->login(username, pwd);
}

int LoginManager::isCorrectPwd(const QString& username, const QString& pwd)
{
    return m_model->isCorrectPwd(username, pwd);
}

void LoginManager::emergency() const
{
    m_model->emergency();
}

const QStringList LoginManager::getAllUserNames() const
{
    return m_model->getAllUserNames();
}

int LoginManager::getUserAuthorityId(const QString& userName) const
{
    return m_model->getUserAuthorityId(userName);
}

bool LoginManager::isLogin() const
{
    return m_model->isLogin();
}

const QStringList LoginManager::getAllUserNamesAndClearData() const
{
    bool bChange = false;
    const QList<UserInfo>& users = m_model->getAllUserInfo();
    foreach (const UserInfo& user, users)
    {
        if (user.userName().isEmpty())
        {
            continue;
        }
        //为处理老版本数据，如果用户名是Service,用户不是m_model的m_serviceId，则删除此用户
        if (user.userId() != m_model->getServiceId() && user.userName() == "Service")
        {
            m_model->removeUserById(user.userId());
            bChange = true;
            continue;
        }
        UserInfo tmpUser(user);
        //为处理老版本数据，如果用户类型是Service,且用户不是则m_model的m_serviceId，修改用户类型为User
        if (user.userId() != m_model->getServiceId() && user.authorityId() == AuthorityInfo::Service)
        {
            m_model->updateUserAuthorityInfoToUser(tmpUser);
            bChange = true;
        }
    }
    if (bChange)
    {
        m_model->updateUsers();
    }
    return m_model->getAllUserNames();
}

void LoginManager::setPreUserId()
{
    m_model->setPreUserId();
}
