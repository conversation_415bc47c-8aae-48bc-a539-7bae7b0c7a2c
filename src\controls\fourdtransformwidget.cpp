#include "fourdtransformwidget.h"
#include "ui_fourdtransformwidget.h"
#include "modeluiconfig.h"
#include "fourdapihandler.h"
#include "resource.h"
#include "sonoparameters.h"
#include "parameter.h"
#include "bfpnames.h"
#include <QTimer>
#include "util.h"

FourDTransformWidget::FourDTransformWidget(QWidget* parent)
    : IBaseWidget(parent)
    , ui(new Ui::FourDTransformWidget)
    , m_MouseActionMode(FourDMouseStateEnum::CurvedLine)
    , m_CurrentMode(Rotate)
    , m_CurrentAxis(AxisTypeEnum::Axis_X)
    , m_isFrozen(false)
    , m_IsStatic3D(false)
    , m_timer(new QTimer)
{
    ui->setupUi(this);
    init();
}

FourDTransformWidget::~FourDTransformWidget()
{
    delete ui;
    QHash<LabelType, LabelWithIconData*>::const_iterator iter = m_LabelHash.constBegin();
    for (; iter != m_LabelHash.constEnd(); iter++)
    {
        delete iter.value();
    }
}

void FourDTransformWidget::init()
{
    m_LabelHash[RotateXAxis] =
        new LabelWithIconData(ui->labelX, ModelUiConfig::instance().value(ModelUiConfig::FourDXIcon).toString(),
                              ModelUiConfig::instance().value(ModelUiConfig::FourDXIcon1).toString());

    m_LabelHash[RotateYAxis] =
        new LabelWithIconData(ui->labelY, ModelUiConfig::instance().value(ModelUiConfig::FourDYIcon).toString(),
                              ModelUiConfig::instance().value(ModelUiConfig::FourDYIcon1).toString());

    m_LabelHash[RotateZAxis] =
        new LabelWithIconData(ui->labelZ, ModelUiConfig::instance().value(ModelUiConfig::FourDZIcon).toString(),
                              ModelUiConfig::instance().value(ModelUiConfig::FourDZIcon1).toString());

    m_LabelHash[ROIXAxis] =
        new LabelWithIconData(ui->labelX, ModelUiConfig::instance().value(ModelUiConfig::FourDROIXIcon).toString(),
                              ModelUiConfig::instance().value(ModelUiConfig::FourDROIXIcon1).toString());

    m_LabelHash[ROIYAxis] =
        new LabelWithIconData(ui->labelY, ModelUiConfig::instance().value(ModelUiConfig::FourDROIYIcon).toString(),
                              ModelUiConfig::instance().value(ModelUiConfig::FourDROIYIcon1).toString());

    m_LabelHash[ROIZAxis] =
        new LabelWithIconData(ui->labelZ, ModelUiConfig::instance().value(ModelUiConfig::FourDROIZIcon).toString(),
                              ModelUiConfig::instance().value(ModelUiConfig::FourDROIZIcon1).toString());

    m_LabelHash[Menu] =
        new LabelWithIconData(ui->labelMenu, ModelUiConfig::instance().value(ModelUiConfig::FourDMenuIcon).toString(),
                              ModelUiConfig::instance().value(ModelUiConfig::FourDMenuIcon1).toString());
    ui->labelLeft->setEnabled(true);
    ui->labelRight->setEnabled(false);
    ui->labelPanning->setEnabled(false);
    m_LabelHash[Menu]->highlightLablel();
    onVirtualHDOnChanged(false);
    onAxisChanged(AxisTypeEnum::Axis_X);
    m_timer->setSingleShot(true);
    connect(m_timer, SIGNAL(timeout()), this, SLOT(onTimeOut()));
}

void FourDTransformWidget::updateFourDGain(const QVariant& val)
{
    ui->labelGainValue->setText(val.toString());
}

void FourDTransformWidget::setIsStatic3D(bool value)
{
    m_IsStatic3D = value;
}

void FourDTransformWidget::onTransformModeChanged(int mode)
{
    Q_ASSERT(mode < 2);
    m_CurrentMode = (TransformMode)mode;
    ui->labelMode->setText(Resource::transformModeList.at(mode));
    highlightCurrentAxis(m_CurrentAxis);
}

void FourDTransformWidget::onAxisChanged(const QVariant& val)
{
    m_CurrentAxis = (AxisTypeEnum::AxisType)val.toInt();
    Q_ASSERT(m_CurrentAxis < 3);
    highlightCurrentAxis(m_CurrentAxis);
}
void FourDTransformWidget::onVirtualHDOnChanged(const QVariant& value)
{
    ui->labelLight->setVisible(value.toBool());
    ui->labelSplash2->setVisible(value.toBool());
}
void FourDTransformWidget::onTransformation(bool increase, int axis)
{
    if (axis == -1 || axis == m_CurrentAxis)
    {
        switch (m_CurrentMode)
        {
        case Rotate:
            fourDImageRotate(increase);
            break;
        case Roi:
            fourDROIZoom(increase);
            break;
        default:
            break;
        }
    }
}

void FourDTransformWidget::onFourdMouseActionChanged(const QVariant& value)
{
    int valueInt = value.toInt();
    Q_ASSERT(valueInt <= FourDMouseStateEnum::VirtualHDLight);
    FourDAPIHandler::instance().switchLightBall(false);
    m_MouseActionMode = (FourDMouseStateEnum::FourDMouseActionMode)valueInt;
    switch (m_MouseActionMode)
    {
    case FourDMouseStateEnum::Loop:
        ui->labelLeft->setEnabled(false);
        ui->labelML2->setEnabled(false);
        ui->labelRight->setEnabled(true);
        ui->labelCurvedLine->setEnabled(false);
        ui->labelSplash->setEnabled(false);
        ui->labelPanning->setEnabled(false);
        ui->labelSplash2->setEnabled(false);
        ui->labelLight->setEnabled(false);
        break;
    case FourDMouseStateEnum::CurvedLine:
        ui->labelLeft->setEnabled(true);
        ui->labelML2->setEnabled(false);
        ui->labelRight->setEnabled(false);
        ui->labelCurvedLine->setEnabled(true);
        ui->labelSplash->setEnabled(false);
        ui->labelPanning->setEnabled(false);
        ui->labelSplash2->setEnabled(false);
        ui->labelLight->setEnabled(false);
        break;
    case FourDMouseStateEnum::Panning:
        ui->labelLeft->setEnabled(true);
        ui->labelML2->setEnabled(false);
        ui->labelRight->setEnabled(false);
        ui->labelCurvedLine->setEnabled(false);
        ui->labelSplash->setEnabled(false);
        ui->labelPanning->setEnabled(true);
        ui->labelSplash2->setEnabled(false);
        ui->labelLight->setEnabled(false);
        break;
    case FourDMouseStateEnum::VirtualHDLight:
        ui->labelLeft->setEnabled(true);
        ui->labelML2->setEnabled(false);
        ui->labelRight->setEnabled(false);
        ui->labelCurvedLine->setEnabled(false);
        ui->labelSplash->setEnabled(false);
        ui->labelPanning->setEnabled(false);
        ui->labelSplash2->setEnabled(false);
        ui->labelLight->setEnabled(true);
        break;
    default:
        break;
    }
}

void FourDTransformWidget::onMouseMovedAction(const QPoint& offset, const QPoint& pos)
{
    if (m_timer->isActive())
    {
        m_timer->stop();
    }
    Q_UNUSED(pos)
    switch (m_MouseActionMode)
    {
    case FourDMouseStateEnum::CurvedLine:
        FourDAPIHandler::instance().shiftROI(offset);
        break;
    case FourDMouseStateEnum::Panning:
        FourDAPIHandler::instance().panningImage(offset);
        break;
    case FourDMouseStateEnum::VirtualHDLight:
        FourDAPIHandler::instance().switchLightBall(true);
        FourDAPIHandler::instance().adjustVHDLight(offset);
        m_timer->start(1000);
        break;
    default:
        break;
    }
}

void FourDTransformWidget::onSplineSlopeChanged(bool increase)
{
    Util::processEvents(QEventLoop::ExcludeUserInputEvents);
    flashMenuLabel(300);
    FourDAPIHandler::instance().adjustROISlope(increase);
}

void FourDTransformWidget::onFreezeChanged(const QVariant& value)
{
    ui->labelGainValue->setVisible(!value.toBool());
    ui->labelGainName->setVisible(!value.toBool());
    /*bool curvedLine = m_MouseActionMode ^ 0x01;

    ui->labelCurvedLine->setEnabled(m_IsStatic3D ? curvedLine : (m_isFrozen ? false: curvedLine));
    ui->labelSplash->setEnabled(m_IsStatic3D ? true : !m_isFrozen);
    ui->labelPanning->setEnabled(m_IsStatic3D ? !curvedLine : (m_isFrozen ? false: !curvedLine));
    ui->labelLeft->setEnabled(m_IsStatic3D ? true : !m_isFrozen);
    ui->labelRight->setEnabled(m_IsStatic3D ? false : m_isFrozen);
    ui->labelGainValue->setEnabled(!m_isFrozen);*/
}

void FourDTransformWidget::retranslateUi()
{
}

void FourDTransformWidget::onFlashAxisLabelEnd()
{
    m_LabelHash[LabelType(m_CurrentMode * 3 + m_CurrentAxis)]->highlightLablel();
}

void FourDTransformWidget::onFlashMenuLabelEnd()
{
    m_LabelHash[Menu]->highlightLablel();
}

void FourDTransformWidget::highlightCurrentAxis(AxisTypeEnum::AxisType axis)
{
    switch (axis)
    {
    case AxisTypeEnum::Axis_X:
        m_LabelHash[LabelType(m_CurrentMode * 3 + AxisTypeEnum::Axis_X)]->highlightLablel();
        m_LabelHash[LabelType(m_CurrentMode * 3 + AxisTypeEnum::AXis_Y)]->lowlightLabel();
        m_LabelHash[LabelType(m_CurrentMode * 3 + AxisTypeEnum::Axis_Z)]->lowlightLabel();
        break;
    case AxisTypeEnum::AXis_Y:
        m_LabelHash[LabelType(m_CurrentMode * 3 + AxisTypeEnum::Axis_X)]->lowlightLabel();
        m_LabelHash[LabelType(m_CurrentMode * 3 + AxisTypeEnum::AXis_Y)]->highlightLablel();
        m_LabelHash[LabelType(m_CurrentMode * 3 + AxisTypeEnum::Axis_Z)]->lowlightLabel();
        break;
    case AxisTypeEnum::Axis_Z:
        m_LabelHash[LabelType(m_CurrentMode * 3 + AxisTypeEnum::Axis_X)]->lowlightLabel();
        m_LabelHash[LabelType(m_CurrentMode * 3 + AxisTypeEnum::AXis_Y)]->lowlightLabel();
        m_LabelHash[LabelType(m_CurrentMode * 3 + AxisTypeEnum::Axis_Z)]->highlightLablel();
        break;
    default:
        break;
    }
}

void FourDTransformWidget::fourDImageRotate(bool increase)
{
    flashAxisLabel();
    FourDAPIHandler::instance().rotate(m_CurrentAxis, increase);
}

void FourDTransformWidget::fourDROIZoom(bool increase)
{
    flashAxisLabel();
    FourDAPIHandler::instance().adjustROI(m_CurrentAxis, increase);
}

void FourDTransformWidget::flashAxisLabel(int time)
{
    m_LabelHash[LabelType(m_CurrentMode * 3 + m_CurrentAxis)]->lowlightLabel();
    QTimer::singleShot(time, this, SLOT(onFlashAxisLabelEnd()));
}

void FourDTransformWidget::flashMenuLabel(int time)
{
    m_LabelHash[Menu]->lowlightLabel();
    QTimer::singleShot(time, this, SLOT(onFlashMenuLabelEnd()));
}
void FourDTransformWidget::onTimeOut()
{
    FourDAPIHandler::instance().switchLightBall(false);
}
