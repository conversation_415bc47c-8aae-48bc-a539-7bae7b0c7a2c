<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FourDTransformWidget</class>
 <widget class="QWidget" name="FourDTransformWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>732</width>
    <height>33</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_5">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <widget class="QLabel" name="labelMode">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>Rotate</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelX">
         <property name="minimumSize">
          <size>
           <width>24</width>
           <height>0</height>
          </size>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelY">
         <property name="minimumSize">
          <size>
           <width>24</width>
           <height>0</height>
          </size>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelZ">
         <property name="minimumSize">
          <size>
           <width>24</width>
           <height>0</height>
          </size>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_4">
       <item>
        <widget class="QLabel" name="labelLeft">
         <property name="text">
          <string>M</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelML2">
         <property name="text">
          <string>/</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelRight">
         <property name="text">
          <string>L</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_3">
       <item>
        <widget class="QLabel" name="labelCurvedLine">
         <property name="text">
          <string>CurvedLine</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelSplash">
         <property name="text">
          <string>/</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelPanning">
         <property name="text">
          <string>Panning</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelSplash2">
         <property name="text">
          <string>/</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelLight">
         <property name="text">
          <string>Light</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QLabel" name="labelMenu">
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QLabel" name="labelGainName">
         <property name="text">
          <string>Gain</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelGainValue">
         <property name="minimumSize">
          <size>
           <width>50</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>50</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>130</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
