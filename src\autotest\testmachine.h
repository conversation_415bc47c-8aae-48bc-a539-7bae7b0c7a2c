/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#ifndef TESTMACHINE_H
#define TESTMACHINE_H
#include "autotest_global.h"

#include "autotest.h"
#include <QTimer>
#include "itestmachine.h"

class AUTOTESTSHARED_EXPORT TestMachine : public ITestMachine
{
public:
    virtual ~TestMachine()
    {
    }
    virtual void handleSpecialEvent(const QString& event)
    {
    }
    virtual void resetFlags()
    {
    }
    virtual void handleFinishEvent()
    {
    }

protected:
    AutoTest m_autoTest;
};

#endif // TESTMACHINE_H
