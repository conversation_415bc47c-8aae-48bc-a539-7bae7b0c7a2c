#ifndef TESTTIMECOMMAND_H
#define TESTTIMECOMMAND_H
#include "autotest_global.h"

#include <QObject>
#include "itestcommand.h"
#include <QTimer>

class AUTOTESTSHARED_EXPORT TestTimeCommand : public QObject, public ITestCommand
{
    Q_OBJECT
public:
    explicit TestTimeCommand(int* time, QTimer* timer);
    explicit TestTimeCommand(int time, QTimer* timer);
    ~TestTimeCommand();
    virtual void execute();
    void setTime(int value);
    int time() const;

private:
    int m_staticTime;
    int* m_time;
    QTimer* m_timer;
};

#endif // TESTTIMECOMMAND_H
