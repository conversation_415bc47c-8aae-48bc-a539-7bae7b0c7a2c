#ifndef FREEHAND3DROICONTROL_H
#define FREEHAND3DROICONTROL_H

#include "controls_global.h"
#include <QObject>
#include <QSize>
#include <QPoint>
#include <QRect>

class ImageWidget;
class SonoParameters;
class FreeHand3DRoiGlyphsControl;
class CONTROLSSHARED_EXPORT FreeHand3DRoiControl : public QObject
{
    Q_OBJECT
public:
    explicit FreeHand3DRoiControl(FreeHand3DRoiGlyphsControl* roiGlyphsControl, ImageWidget* imageWidget,
                                  const QRect& imagerect, QObject* parent = 0);
    ~FreeHand3DRoiControl();
    void refreshFreeHand3DRoi();
public slots:
    void onFreeHand3DRoiOnChanged(const QVariant& value);
    void onBeforecurSonoParametersChanged();
    void onCurSonoParametersChanged();
    void onFreezeChanged(const QVariant& value);
    void onRightButtonReleased();
    void leftButtonPressed(const QPoint& pos);
    void movedAction(const QPoint& offset, const QPoint& pos);

private:
    void changeMouseAction(bool changeRoi = true);

private:
    enum ActionType
    {
        ROIMove,
        ROIChange
    };
    FreeHand3DRoiGlyphsControl* m_RoiController;
    ImageWidget* m_ImageWidget;
    SonoParameters* m_SonoParameters;
    ActionType m_HD3DRoiActionType;
    bool m_ShowFreeHand3DRoiWidget;
    bool m_UpdateFreeHand3DRoi;
    QRect m_ImageRect;
    QSize m_RoiMinSize;
    QSize m_RoiSize;
    QPoint m_RoiPos;
};

#endif // FREEHAND3DROICONTROL_H
