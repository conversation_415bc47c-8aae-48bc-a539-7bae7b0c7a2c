#include "seniormediawidget.h"
#include <QHBoxLayout>
#include <QLabel>
#include "mediaplayercontroller.h"

SeniorMediaWidget::SeniorMediaWidget(QWidget* parent)
    : QWidget(parent)
    , m_ImageLabel(new QLabel(this))
    , m_MediaPlayerController(new MediaPlayerController)
{
    QHBoxLayout* layout = new QHBoxLayout(this);
    layout->setMargin(0);
    layout->addWidget(m_ImageLabel);

    QPalette palette;
    palette.setColor(QPalette::Background, Qt::black);
    m_ImageLabel->setAutoFillBackground(true);
    m_ImageLabel->setPalette(palette);

    connect(m_MediaPlayerController, &MediaPlayerController::ffplayIsRuning, this, &SeniorMediaWidget::ffplayIsRuning);
    connect(m_MediaPlayerController, &MediaPlayerController::ffplayIsEnded, this, &SeniorMediaWidget::ffplayIsEnded);
    connect(m_MediaPlayerController, &MediaPlayerController::ffplayExited, this, &SeniorMediaWidget::ffplayExited);
}

SeniorMediaWidget::~SeniorMediaWidget()
{
    if (m_MediaPlayerController != nullptr)
    {
        delete m_MediaPlayerController;
        m_MediaPlayerController = nullptr;
    }
}

void SeniorMediaWidget::setPaths(const QStringList& paths)
{
    m_MediaPlayerController->setFilePaths(paths);
}

void SeniorMediaWidget::start(const int imageDuration)
{
    m_MediaPlayerController->setImageShowTime(imageDuration);
    m_MediaPlayerController->play();
}

void SeniorMediaWidget::stop()
{
    m_MediaPlayerController->stop();
}

void SeniorMediaWidget::setMute(bool isMute)
{
    m_MediaPlayerController->setMute(isMute);
}
