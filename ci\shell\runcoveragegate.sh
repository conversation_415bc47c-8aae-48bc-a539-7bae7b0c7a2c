#!/bin/bash
gate=$1
coveragetmpfile="tmp.txt"
difffile="diff.txt"
diffline_array=()
diffcount=0
uncoveragecount=0
result=0
#从完整覆盖率文件coverage.xml中截取指定cpp文件的测试覆盖率信息，写入到临时文件tmp.txt
get_coverage_tmp_file() {
local file="$1"
if grep -q $file ./build/coverage.xml ; then
local startline=$(grep -n "$file" ./build/coverage.xml | cut -d: -f 1)
local endline=$(tail -n +$startline ./build/coverage.xml | grep -n -m 1 '</class>' | cut -d: -f 1)
tail -n +$startline ./build/coverage.xml | head -n $endline | grep 'line number=' > $coveragetmpfile
fi
}

# 输入为i,n，i代表修改的行号，n代表修改了几行。n可能没有，可能为0，可能是正整数。
# 没有n时，添加m
# n为0时，跳过
# n为正整数时，添加m,m+1,m+2,...,m+n-1
get_valid_lines(){
IFS=',' read -ra parts <<< $1
index=${parts[0]}
num=${parts[1]:-1}  # 默认值为1，如果逗号后无内容，则默认添加单个数字

# 如果逗号后是0，则跳过本次循环
if [[ $num -eq 0 ]]; then
return 0
fi

# 添加当前数字
diffline_array+=($index)

# 如果逗号后是n（大于0的数字），则添加m+1,m+2,...,m+n-1
if [[ $num -gt 1 ]]; then
for ((i=1; i<$num; i++)); do
diffline_array+=($((index + i)))
done
fi
}

# 生成与盘古分支的差异代码
paths=`ls -d src/*/ | grep -vE "(autotest|thirdparty|resource|test)"`
echo paths has $paths
git diff -U0 origin/pangu -- $paths  | grep -Po '^\+\+\+ ./\K.*|^@@ -[0-9]+(,[0-9]+)? \+\K[0-9]+(,[0-9]+)?(?= @@)' > $difffile

# 统计差异代码行号，使用二维数组存放文件名-行号
declare -A file_validline_map
for line in $(< $difffile); do
if [[ $line == src* ]]; then
filepath=$line
diffline_array=()
else
get_valid_lines $line
if [[ ${#diffline_array[@]} > 0 ]]; then
file_validline_map[$filepath]=${diffline_array[*]}
fi
fi
done


for file in "${!file_validline_map[@]}"; do
validline_array=(${file_validline_map[$file]})
# 过滤非cpp 文件
if [[ $file == *.cpp ]]; then
get_coverage_tmp_file $file
for i in "${validline_array[@]}"; do
# 统计有效增量代码总行数，如果行数不在单元测试报告中，则认为改行代码不统计
tmp=`grep number=\"$i\" $coveragetmpfile`
if [[ ! -z "$tmp" ]]; then
((diffcount++))
# 统计未测试到的代码行数
tmp=`echo $tmp | grep 'hits=\"0\"'`
if [[ ! -z "$tmp" ]]; then
((uncoveragecount++))
fi
fi
done
fi
done

if [ "$diffcount" -eq 0 ]; then
result=100.0
else
result=$(echo "scale=4; ($((diffcount - uncoveragecount)) / $diffcount) * 100" | bc)
# 检查结果是否小于1
if (( $(echo "$result < 1" | bc -l) )); then
# 在小数点前补0
result="0$result"
fi
fi

echo 增量代码总行数：$diffcount  
echo 增量代码未覆盖行数：$uncoveragecount 
echo 增量代码覆盖行数：$((diffcount - uncoveragecount))  
echo 增量代码覆盖率： $result%

#清理临时文件
rm $coveragetmpfile
rm $difffile
ispass=$(echo "$result < $gate" | bc -l)
if [ "$ispass" -eq 1 ]; then
    echo -e "\e[31m ERROR: 覆盖率未达标小于 $gate"
    exit 1
fi