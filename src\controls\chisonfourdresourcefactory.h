#ifndef CHISONFOURDRESOURCEFACTORY_H
#define CHISONFOURDRESOURCEFACTORY_H

#include "controls_global.h"
#include "ifourdresourcefactory.h"

class CONTROLSSHARED_EXPORT ChisonFourDResourceFactory : public IFourDResourceFactory
{
public:
    ChisonFourDResourceFactory();
    ~ChisonFourDResourceFactory();
    IFourDLiveWidget* createFourDLiveWidget();
    IFourDAPI* createFourDAPI();
    IFourDLicenseProcessor* createFourDLicenseProcessor();
};

#endif // CHISONFOURDRESOURCEFACTORY_H
