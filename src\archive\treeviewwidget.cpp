#include "treeviewwidget.h"
#include "ui_treeviewwidget.h"

TreeViewWidget::TreeViewWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::TreeViewWidget)
{
    ui->setupUi(this);
    ui->treeView->setScrollerGestureType(QScroller::LeftMouseButtonGesture);
    connect(ui->treeView, SIGNAL(clicked(QModelIndex)), this, SIGNAL(clicked(QModelIndex)));
    connect(ui->treeView, SIGNAL(doubleClicked(QModelIndex)), this, SIGNAL(doubleClicked(QModelIndex)));

    //    setButtonVisible(false);
    m_scroller = QScroller::scroller(ui->treeView);
}

TreeViewWidget::~TreeViewWidget()
{
    delete ui;
}

void TreeViewWidget::setModel(QAbstractItemModel* model)
{
    ui->treeView->setModel(model);
}

void TreeViewWidget::setSelectionMode(QAbstractItemView::SelectionMode mode)
{
    ui->treeView->SetSelectionMode(mode);
}

void TreeViewWidget::clearSelection()
{
    selectionModel()->clear();
}

QItemSelectionModel* TreeViewWidget::selectionModel() const
{
    return ui->treeView->selectionModel();
}

QAbstractItemView::SelectionMode TreeViewWidget::selectionMode() const
{
    return ui->treeView->selectionMode();
}

void TreeViewWidget::setColumnWidth(int column, int width)
{
    ui->treeView->setColumnWidth(column, width);
}

void TreeViewWidget::hideColumn(int column)
{
    ui->treeView->hideColumn(column);
}

void TreeViewWidget::setRootIsDecorated(bool show)
{
    ui->treeView->setRootIsDecorated(show);
}

bool TreeViewWidget::isExpanded(const QModelIndex& index) const
{
    return ui->treeView->isExpanded(index);
}

void TreeViewWidget::setExpanded(const QModelIndex& index, bool expand)
{
    ui->treeView->setExpanded(index, expand);
}

void TreeViewWidget::setButtonVisible(bool visible)
{
    ui->widgetButton->setVisible(visible);
}

TreeViewButton* TreeViewWidget::buttonWidget() const
{
    return ui->widgetButton;
}

void TreeViewWidget::expandAll()
{
    ui->treeView->expandAll();
}

void TreeViewWidget::collapseAll()
{
    ui->treeView->collapseAll();
}

void TreeViewWidget::selectAll()
{
    ui->treeView->selectAll();
}

void TreeViewWidget::retranslateUi()
{
    ui->retranslateUi(this);
}
