#ifndef FILESEXPORTER_H
#define FILESEXPORTER_H
#include "archive_global.h"
#include <QObject>
#include <QStringList>
#include "imageprocessedcopy.h"

class PatientWorkflow;
class DicomTaskManager;
class IColorMapManager;

class ARCHIVESHARED_EXPORT FilesExporter : public QObject
{
    Q_OBJECT
public:
    explicit FilesExporter(PatientWorkflow* workFlow, DicomTaskManager* taskManager, QObject* parent = 0);
    void doExport(const QStringList& fileNames, bool isGDPR = false);
    bool threadFinishedState() const;
    void resetThreadFinishedState();
    void setColorMapManager(IColorMapManager* colorMapManager);

private:
    void exportToUDisk(const QStringList& fileNames);
    void exportToNetWork(const QStringList& fileNames);
    ImageProcessedCopy::ImageType imageTypeForExported() const;
    void copyFile(const QStringList& fileNames, const QString& targetDir, bool checkDiskSpace);
    bool processGDPRFile(const QStringList& fileNames, bool& isGDPR);
private slots:
    void onThreadFinished(bool isSuccessful);

private:
    PatientWorkflow* m_PatientWorkflow;
    DicomTaskManager* m_DicomTaskManager;
    bool m_ThreadFinishedState;
    bool m_IsGDPR;
    IColorMapManager* m_ColorMapManager;
};

#endif // FILESEXPORTER_H
