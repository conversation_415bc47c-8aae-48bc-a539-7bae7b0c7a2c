#ifndef AUTHORITYPRESETDATAHANDLER_H
#define AUTHORITYPRESETDATAHANDLER_H
#include "adminmodel_global.h"
#include "authorityinfo.h"
#include "fingerprintinfo.h"
#include "userinfo.h"

class UserPresetDataHandlerPrivate;
class IDataBaseManager;
class ADMINMODELSHARED_EXPORT UserPresetDataHandler
{
private:
    UserPresetDataHandler();

public:
    static UserPresetDataHandler* instance();
    ~UserPresetDataHandler();

    void initialize(IDataBaseManager* databaseManager);

    bool getAllAuthorityInfo(QList<AuthorityInfo>& authorities) const;

    bool getAllUserInfo(QList<UserInfo>& users) const;

    bool addUser(const UserInfo& user);

    bool addUsers(const QList<UserInfo>& users);

    bool removeUser(const QString& username);

    bool removeUsers(const QStringList& usernames);

    bool updateUser(const UserInfo& user);

    bool addFingerprint(const FingerprintInfo& value);
    bool addFingerprints(const QList<FingerprintInfo>& value);

    bool removeFingerprint(int id);
    bool removeFingerprints(const QList<int>& value);

    bool updateFingerprint(const FingerprintInfo& value);

    bool getAllFingerprintInfo(QList<FingerprintInfo>& value);

    bool removeUserById(const QString& userId);

private:
    UserPresetDataHandlerPrivate* d;
};

#endif // AUTHORITYPRESETDATAHANDLER_H
