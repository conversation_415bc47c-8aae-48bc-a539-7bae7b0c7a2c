<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DicomServerWidget</class>
 <widget class="QWidget" name="DicomServerWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>335</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_13">
     <item>
      <widget class="QLabel" name="label">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>132</width>
         <height>0</height>
        </size>
       </property>
       <property name="text">
        <string>ServerInfo</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="BaseComboBox" name="comboBox">
       <property name="minimumSize">
        <size>
         <width>200</width>
         <height>0</height>
        </size>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_14" stretch="2,1,1">
     <item>
      <spacer name="horizontalSpacer_4">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QCheckBox" name="checkBoxGDPR">
       <property name="text">
        <string>De-Identification</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonDetail">
       <property name="minimumSize">
        <size>
         <width>100</width>
         <height>0</height>
        </size>
       </property>
       <property name="text">
        <string>Detail &gt;&gt;</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="2" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_15">
     <item>
      <spacer name="verticalSpacer_4">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>0</width>
         <height>158</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QListWidget" name="listWidget"/>
     </item>
    </layout>
   </item>
   <item row="3" column="0">
    <layout class="QHBoxLayout" name="horizontalLayout_16">
     <item>
      <widget class="QPushButton" name="pushButtonExport">
       <property name="maximumSize">
        <size>
         <width>120</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="text">
        <string>Export</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonClose">
       <property name="maximumSize">
        <size>
         <width>120</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="text">
        <string>Close</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BaseComboBox</class>
   <extends>QComboBox</extends>
   <header>basecombobox.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
