#ifndef FINGERPRINTOPERATORBASE_H
#define FINGERPRINTOPERATORBASE_H

#include "ifingerprintoperator.h"

class ADMINMODELSHARED_EXPORT FingerPrintOperatorBase : public IFingerPrintOperator
{
    Q_OBJECT
public:
    FingerPrintOperatorBase(QObject* parent = nullptr);

    virtual int maxCount() override;
    virtual bool enroll(const QString& userId) override;
    virtual bool verify() override;
    virtual void stop() override;
    virtual bool remove(const FingerprintInfo& fpinfo) override;
    virtual const QList<FingerprintInfo> getFingerprintInfo(const QString& userId) const override;
    virtual bool addFingerprint(const QString& fingerName, int fingerId, int status, const QString& userId) override;
    virtual bool hasFingerprint() override;
};

#endif // FINGERPRINTOPERATORBASE_H
