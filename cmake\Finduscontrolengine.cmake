#
# The module defines the following variables:
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>NE_FOUND - True if USCONTROLENGINE found.
# USCON<PERSON><PERSON>ENGINE_INCLUDE_DIRS - where to find uscontrolengineuiapi.h, etc.
# USCONTR<PERSON>ENGINE_LIBRARIES - List of libraries when using USCONTROLENGINE.
#


thirdparty_prefix_path(USCONTROLENGINE)

find_path ( USCONTROLENGINE_INCLUDE_DIR
            NAMES
                uscontrolengineuiapi.h
            HINTS
                ${USCONTROLENGINE_ROOT}/include
            )
set(USCONTR<PERSON><PERSON>GINE_INCLUDE_DIRS ${USCONTROLENGINE_INCLUDE_DIR})
message("Found USCONTROLENGINE headers: ${USCONTR<PERSON>ENGINE_INCLUDE_DIRS}")

find_library ( USCON<PERSON><PERSON><PERSON>GINE_LIBRARY
            NAMES 
                uscontrolengineui
            HINTS
                ${USCONTROLENGINE_LIBPATH}
             )
set(USCONTROLENGINE_LIBRARIES ${USCONTROLENGINE_LIBRARY})
message("Found USCONTROLENGINE libs: ${<PERSON>ON<PERSON><PERSON>ENGINE_LIBRARIES}")
