# This file is a template, and might need editing before it works on your project.
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml

# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/README.html#stages

stages:          # List of stages for jobs, and their order of execution
  - prebuild
  - cppcheck
  - code-format
  - build
  - test
  - deploy
  - release
  - report



variables:
  # use licensemodeldef.h enum Model, like SonoEye, Phoenix, SR9, Apple etc. 
  # on web http://*************/usplatform/xunit/-/pipelines/new
  model: "bypass"             
  MainVersion: "V0.4.2"
  # 表示版本号结尾中会增加-xxx，xxx可以是用于表示此版本的特殊性的词汇，此例为NTRL，表示中性
  # 版本号，此例为V1.4.9.b61e460dA-NTRL。如果临时测试的版本，可以加上功能名，比如txcode，那版本号为
  # V1.4.9.b61e460dA-txcode；如果是普通版本，则不需要增加此参数
  VersionAdd: "alpha"
  # 资源分支
  ResourceBranch: "pangu"
  # C# DEMO分支
  UsapiexampleBranch: "master"
  # 探头
  Probes: "L25-V L25-D"
  # x64
  HostArch: "x86"
  #appcommon分支
  AppcommonBranch: "master"
  # 中性版本，默认为非中性版
  neutral: "false"
  # 上次版本号
  PreVersion: "default" 
  # 单元测试覆盖率阈值
  CodeCoverageThreshold: $CodeCoverageThreshold
  # 远程部署机器的IP
  Deploy_IP: "**************"
include:
  - local: ci/model/*.yml

after_script:
  - date
