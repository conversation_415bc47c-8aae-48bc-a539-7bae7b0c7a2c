#include "jsrgalg.h"

JsrgAlg::JsrgAlg()
    : VirtualAlg()
{
    m_CreateContext = nullptr;
    m_ReleaseContext = nullptr;
    m_JrsgMeasure = nullptr;
    m_Lib.setFileName("jrsg");
}

int JsrgAlg::jrsgCreatectx(const char* modelpath, int type, int radius, int infertype)
{
    if (m_CreateContext == NULL)
    {
        m_CreateContext = (JrsgCreatectx)m_Lib.resolve("jrsg_createctx");
    }
    if (m_CreateContext == NULL)
    {
        m_IsError = true;
        return -1;
    }
    m_CreateContext(modelpath, type, radius, infertype);
    m_Initialized = true;
    return 1;
}

void JsrgAlg::jrsgMeasure(unsigned char* imgptr, int width, int height, std::vector<BPoint>& points, int ptx, int pty,
                          int roiwidth, int roiheight, int& ellipse_x, int& ellipse_y, int& ellipse_width,
                          int& ellipse_height, int& ellipse_angle, bool ellipsefit)
{
    if (m_JrsgMeasure == NULL)
    {
        m_JrsgMeasure = (JrsgMeasure)m_Lib.resolve("jrsg_measure");
    }
    if (m_JrsgMeasure == NULL || !m_Initialized)
    {
        m_IsError = true;
        return;
    }
    m_JrsgMeasure(imgptr, width, height, points, ptx, pty, roiwidth, roiheight, ellipse_x, ellipse_y, ellipse_width,
                  ellipse_height, ellipse_angle, ellipsefit);
}

void JsrgAlg::jrsgRelease()
{
    if (m_ReleaseContext == NULL)
    {
        m_ReleaseContext = (JrsgRelease)m_Lib.resolve("jrsg_release");
    }
    if (!m_Initialized || m_ReleaseContext == NULL)
    {
        m_IsError = true;
        return;
    }
    m_Initialized = false;
    m_ReleaseContext();
}
