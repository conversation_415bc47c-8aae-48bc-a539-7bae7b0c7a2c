/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#ifndef BURNINTESTWIDGET_H
#define BURNINTESTWIDGET_H
#include "controls_global.h"

#include <QWidget>
#include "burnintest.h"
#include "basedialogframe.h"
#include "threadmodel.h"

class BurnInTestWidget;
class IBeamFormer;
class BaseProbeSelectionChild;
class PatientWorkflow;
class ImageSkimManager;
class ImageProcessedCopy;
class IStateManager;
class IColorMapManager;

class CONTROLSSHARED_EXPORT BurnInTestDialog : public BaseDialogFrame
{
    Q_OBJECT
public:
    explicit BurnInTestDialog(IStateManager* stateManager, IColorMapManager* colorMapManager, QWidget* parent = 0);
    void setProbeFrame(BaseProbeSelectionChild* value);
    void setPatientWorkFlow(PatientWorkflow* value);
    void setImageSkimManager(ImageSkimManager* value);
private slots:
    void onHide();

private:
    BurnInTestWidget* m_child;
};

namespace Ui
{
class BurnInTestWidget;
}
class BaseProbeSelectionChild;
class CONTROLSSHARED_EXPORT BurnInTestWidget : public QWidget
{
    Q_OBJECT

public:
    explicit BurnInTestWidget(IStateManager* stateManager, IColorMapManager* colorMapManager, QWidget* parent = 0);
    ~BurnInTestWidget();
    void setProbeFrame(BaseProbeSelectionChild* value);
    void setPatientWorkFlow(PatientWorkflow* value);
    void setImageSkimManager(ImageSkimManager* value);
private slots:
    void on_pushButtonStartTest_clicked();
    void on_pushButtonStopTest_clicked();
    void onSendCurrentFiles(const QString& inf);
    void onThreadFinished();
    void resetFlags();
    void onStart();

private:
    QString getCurrentModeName() const;
    void initThreadModel();
    void renameScreenshotsFiles();
    void renameWithModeName(const QFileInfo& info);
    QString generateModeNameWithPicName(const QString& name);
    void deleteLocalFiles();
    void onDeleteLocalFiles();
    void generateSendFilePaths(QStringList& stringList);
    void setPushButtonWithRunningState(bool value);
    QString generateDirWithMachineIDAndSerialNumber() const;
signals:
    void hide();

private:
    Ui::BurnInTestWidget* ui;
    BurnInTest m_burnInTest;
    ThreadModel m_threadModel;
    ProgressBarView* m_progressBar;
    ImageProcessedCopy* m_copy;
    ConfirmDialogFrame* m_confirmDialog;
    PatientWorkflow* m_patientWorkflow;
    ImageSkimManager* m_imageSkimManager;
    QString m_currentDir;
    int m_currentPicIndex;
    IStateManager* m_StateManager;
    IColorMapManager* m_ColorMapManager;
};

#endif // BURNINTESTWIDGET_H
