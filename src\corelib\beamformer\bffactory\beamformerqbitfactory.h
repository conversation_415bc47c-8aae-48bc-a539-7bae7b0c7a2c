/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#ifndef BEAMFORMERQBITFACTORY_H
#define BEAMFORMERQBITFACTORY_H

#include "ibeamformerfactory.h"

class BeamFormerQBitFactory : public IBeamFormerFactory
{
public:
    virtual IBeamFormer* create(QObject* parent = 0) const;
};

#endif // BEAMFORMERQBITFACTORY_H
