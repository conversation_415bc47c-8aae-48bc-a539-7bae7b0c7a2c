/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#include "vmonitorselect.h"
#include "ui_vmonitorselect.h"
#include "autoswitchprobewidget.h"
#include "itestcommand.h"
#include "burnintestwidget.h"
#include "testswitchprobecommand.h"
#include "probetemperaturewidget.h"
#include "motoroperationwidget.h"
#include "diskselectionwidget.h"
#include "appsetting.h"
#include "messageboxframe.h"
#include "util.h"
#include "fileutil.h"
#include "resource.h"
#include "probeselftestwidget.h"
#include "autotestform.h"

VMonitorSelectDialog::VMonitorSelectDialog(IBeamFormer* beamFormer, QWidget* parent)
    : BaseDialogFrame(parent)
    , m_child(NULL)
{
    m_child = new VMonitorSelect(this);
    this->setContent(m_child);
    this->setFrameTitle("VMonitorSelectDialog");
    m_child->setBeamFormer(beamFormer);
    connect(m_child, SIGNAL(quit()), this, SLOT(accept()));
    connect(m_child, SIGNAL(sendParameter()), this, SIGNAL(sendParameter()));
}

void VMonitorSelectDialog::setVMonitor(QWidget* w)
{
    m_child->setVMonitor(w);
}

void VMonitorSelectDialog::setProbeTemperature(QWidget* w)
{
    m_child->setProbeTemperature(w);
}

void VMonitorSelectDialog::setPatientWorkFlow(PatientWorkflow* value)
{
    m_child->setPatientWorkFlow(value);
}

void VMonitorSelectDialog::setTestCommand(ITestCommand* value)
{
    m_child->setTestCommand(value);
}

void VMonitorSelectDialog::setImageSkimManager(ImageSkimManager* value)
{
    m_child->setImageSkimManager(value);
}

void VMonitorSelectDialog::setMotorControl(MotorControl* value)
{
    Q_ASSERT(value != NULL);
    m_child->setMotorControl(value);
}

void VMonitorSelectDialog::setProbeSelfTest(ChisonUltrasoundContext* value)
{
    m_child->setProbeSelfTest(value);
}

void VMonitorSelectDialog::setAutoTest()
{
    m_child->setAutoTest();
}

void VMonitorSelectDialog::showVMonitor()
{
    m_child->showVMonitor();
}

void VMonitorSelectDialog::setStateManager(IStateManager* value)
{
    m_child->setStateManager(value);
}

void VMonitorSelectDialog::setColorMapManager(IColorMapManager* value)
{
    m_child->setColorMapManager(value);
}

VMonitorSelect::VMonitorSelect(QWidget* parent)
    : m_vmonitor(NULL)
    , m_probeCodeDialog(NULL)
    , m_testCommand(NULL)
    , BaseWidget(parent)
    , m_beamFormer(NULL)
    , m_isDone(false)
    , m_switchProbeWidget(NULL)
    , m_burnInTestDialog(NULL)
    , ui(new Ui::VMonitorSelect)
    , m_patientWorkFlow(NULL)
    , m_imageSkimManager(NULL)
    , m_ProbeTempDialog(NULL)
    , m_MotorOperationDialog(new MotorOperationDialog())
    , m_ProbeSelfTestDialog(NULL)
    , m_AutoTestDialog(NULL)
    , m_StateManager(NULL)
    , m_ColorMapManager(NULL)
{
    ui->setupUi(this);
    connect(m_MotorOperationDialog, SIGNAL(sendParameter()), this, SIGNAL(sendParameter()));
}

VMonitorSelect::~VMonitorSelect()
{
    delete m_switchProbeWidget;
    delete m_burnInTestDialog;
    delete m_MotorOperationDialog;
    delete ui;
}

void VMonitorSelect::setVMonitor(QWidget* w)
{
    m_vmonitor = w;
}

void VMonitorSelect::setProbeTemperature(QWidget* w)
{
    m_ProbeTempDialog = w;
}

void VMonitorSelect::setBeamFormer(IBeamFormer* value)
{
    m_beamFormer = value;
}

void VMonitorSelect::setTestCommand(ITestCommand* value)
{
    m_testCommand = value;
}

void VMonitorSelect::setPatientWorkFlow(PatientWorkflow* value)
{
    m_patientWorkFlow = value;
}

void VMonitorSelect::setImageSkimManager(ImageSkimManager* value)
{
    m_imageSkimManager = value;
}

void VMonitorSelect::setMotorControl(MotorControl* motorCtrl)
{
    m_MotorOperationDialog->setMotorControl(motorCtrl);
}

void VMonitorSelect::setProbeSelfTest(ChisonUltrasoundContext* value)
{
    if (m_ProbeSelfTestDialog == NULL)
    {
        m_ProbeSelfTestDialog = new ProbeSelfTestDialog();
    }

    m_ProbeSelfTestDialog->setChisonUltrasoundContext(value);
}

void VMonitorSelect::setAutoTest()
{
    if (m_AutoTestDialog == NULL)
    {
        m_AutoTestDialog = new AutoTestDialog();
    }

    //    AutoTestDialog->setChisonUltrasoundContext(value);
}

void VMonitorSelect::on_pushButtonVMonitor_clicked()
{
    m_vmonitor->setVisible(true);
    //    m_vmonitor->show();
    emit quit();
}

void VMonitorSelect::on_pushButtonProbeCode_clicked()
{
}

void VMonitorSelect::on_pushButtonSwitchProbe_clicked()
{
    if (m_switchProbeWidget == NULL)
    {
        m_switchProbeWidget = new AutoSwitchProbeDialog;
    }

    m_switchProbeWidget->setTestCommand(m_testCommand);
    m_switchProbeWidget->show();

    emit quit();
}

void VMonitorSelect::on_pushButtonBurnInTest_clicked()
{
    if (m_burnInTestDialog == NULL)
    {
        m_burnInTestDialog = new BurnInTestDialog(m_StateManager, m_ColorMapManager);
    }

    TestSwitchProbeCommand* switchProbeCommand = dynamic_cast<TestSwitchProbeCommand*>(m_testCommand);
    Q_ASSERT(switchProbeCommand);

    m_burnInTestDialog->setProbeFrame(switchProbeCommand->probeFrame());
    m_burnInTestDialog->setPatientWorkFlow(m_patientWorkFlow);
    m_burnInTestDialog->setImageSkimManager(m_imageSkimManager);

    m_burnInTestDialog->show();

    emit quit();
}

void VMonitorSelect::on_pushButtonProbeTemp_clicked()
{
    m_ProbeTempDialog->setVisible(true);
    emit quit();
}

void VMonitorSelect::on_pushButtonMotorTest_clicked()
{
    m_MotorOperationDialog->show();
}

void VMonitorSelect::on_pushButtonFpgaInfo_clicked()
{
}

void VMonitorSelect::on_ExportGrabScreen_clicked()
{
    QString path = DiskSelectionDialog::getFileUDiskPath(QString("%1_GrabScreenPics").arg(AppSetting::model()));
    if (!path.isEmpty())
    {
        MessageBoxFrame* infoMsg = MessageBoxFrame::showInformation(this, QString(), tr("Exporting now, please wait."));

        Util::Rmdir(path);
        Util::Mkdir(path);

        FileUtil::copyDir(Resource::GrabScreenPath, path);
        Util::sync();

        MessageBoxFrame::tipInformation(tr("Screen Pics/Clip have been exported successfully!"));
        infoMsg->close();
    }
}

void VMonitorSelect::on_pushButtonProbeSelfTest_clicked()
{
    m_ProbeSelfTestDialog->show();

    emit quit();
}

void VMonitorSelect::on_pushButtonAutoTest_clicked()
{
    if (m_AutoTestDialog == NULL)
    {
        m_AutoTestDialog = new AutoTestDialog();
    }
    m_AutoTestDialog->show();
    emit quit();
}

void VMonitorSelect::showVMonitor()
{
    on_pushButtonVMonitor_clicked();
}

void VMonitorSelect::setStateManager(IStateManager* value)
{
    m_StateManager = value;
}

void VMonitorSelect::setColorMapManager(IColorMapManager* value)
{
    m_ColorMapManager = value;
}
