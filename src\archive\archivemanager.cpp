#include "archivemanager.h"

#include <QScopedPointer>

#include "ui_archivemanager.h"
//#include "mainmodule.h"
#include "databasepatientviewmodel.h"
#include "databasestudyviewmodel.h"
#include "exportfilewidget.h"
#include <QDebug>
#include <QModelIndex>
#include <QScroller>

//#include "patientinfowidget.h"
#include "inputdialogframe.h"
#include "showpicture.h"
#include <QFont>
#include <QFontMetrics>
#include <QPixmap>
//#include "openreport.h"

#include "resource.h"
#include "udiskpathinfo.h"

#include "dataaccesslayerhelper.h"
#include "databackupwidget.h"
#include "util.h"

#include "abstractdbconnection.h"
#include "appsetting.h"
#include "backupmodel.h"
#include "buttonsboxframe.h"
#include "dbtranscation.h"
#include "deletefilescontroller.h"
#include "dicomregister.h"
#include "dicomseverinfowidget.h"
#include "diskinfo.h"
#include "generalworkflowfunction.h"
#include "glanceimages.h"
#include "globaldef.h"
#include "idiskdevice.h"
#include "imagelabelinfo.h"
#include "imageskimwidget.h"
#include "messageboxframe.h"
#include "model/patient.h"
#include "model/study.h"
#include "modelconfig.h"
#include "modeldirectorygetter.h"
#include "modeluiconfig.h"
#include "netinfo.h"
#include "patientworkflow.h"
#include "patientworkflowmodel.h"
#include "reportfileloadhelper.h"
#include "treeviewbutton.h"
#include "uiutil.h"
#include <QItemSelectionModel>
#include <QScreen>
#include <QSettings>

ArchiveManagerDialog::ArchiveManagerDialog(IStateManager* stateManager, QWidget* patient)
    : BaseInputAbleDialogFrame(patient, true)
    , m_Child(NULL)
{
    setCategory("Archive");
    setShowTopWidget(true);
    setAutoZOrder(false);
    m_Child = new ArchiveManager(stateManager, this);
    this->setContent(m_Child);
    m_Child->setParentDialog(this);
    connect(this, SIGNAL(buttonClicked(int)), m_Child, SLOT(onButtonClicked(int)));
    connect(m_Child, SIGNAL(closedCurrentWidget()), this, SLOT(reject()));
    connect(m_Child, SIGNAL(onlyJump()), this, SIGNAL(onlyJump()));
    connect(m_Child, SIGNAL(entryPatientState()), this, SIGNAL(entryPatientState()));
    connect(m_Child, SIGNAL(entryBrowseState()), this, SIGNAL(entryBrowseState()));
    connect(m_Child, SIGNAL(sendDirPathList(QStringList)), SIGNAL(sendDirPathList(QStringList)));
    connect(m_Child, SIGNAL(sendStudyOid(QString)), this, SIGNAL(sendStudyOid(QString)));
    connect(m_Child, SIGNAL(currentDiskPathChanged(QString)), this, SIGNAL(currentDiskPathChanged(QString)));
    connect(m_Child, SIGNAL(entryContinueExamState()), this, SIGNAL(entryContinueExamState()));
    connect(m_Child, SIGNAL(entryEditExamState()), this, SIGNAL(entryEditExamState()));
    connect(m_Child, SIGNAL(autoCallBack(int)), this, SIGNAL(autoCallBack(int)));
    connect(m_Child, SIGNAL(orientationChanged(bool)), this, SLOT(onOrientationChanged(bool)));
    connect(this, SIGNAL(tpButClicked(QString, QString, QString)), m_Child,
            SIGNAL(tpButClicked(QString, QString, QString)));

    titleColorUseQss("archiveMenuTitleBar");
    containerColorUseQss("archiveChildContainer");
}

void ArchiveManagerDialog::setPatientWorkflow(PatientWorkflow* patientWorkflow)
{
    m_Child->setPatientWorkflow(patientWorkflow);
}

void ArchiveManagerDialog::setGeneralWorkflowFunction(GeneralWorkflowFunction* generalWorkflowFunction)
{
    m_Child->setGeneralWorkflowFunction(generalWorkflowFunction);
}

void ArchiveManagerDialog::setDicomTaskManager(DicomTaskManager* dicomTaskManager)
{
    m_Child->setDicomTaskManager(dicomTaskManager);
}

void ArchiveManagerDialog::setToolsFacade(IToolsFacade* value)
{
    m_Child->setToolsFacade(value);
}

void ArchiveManagerDialog::setColorMapManager(IColorMapManager* value)
{
    m_Child->setColorMapManager(value);
}

void ArchiveManagerDialog::setDiskDevice(IDiskDevice* diskDevice)
{
    m_Child->setDiskDevice(diskDevice);
}

void ArchiveManagerDialog::onTPButClicked(const QString& category, const QString& butName, const QString& value)
{
    BaseInputAbleDialogFrame::onTPButClicked(category, butName, value);
    emit tpButClicked(category, butName, value);
}

void ArchiveManagerDialog::onOrientationChanged(bool isHor)
{
    if (m_ButtonsBoxFrame != nullptr)
    {
        m_ButtonsBoxFrame->setIsHor(isHor);
    }
}

void ArchiveManagerDialog::showEvent(QShowEvent* e)
{
    BaseInputAbleDialogFrame::showEvent(e);
    if (m_Child != NULL)
    {
    }
}

ArchiveManager::ArchiveManager(IStateManager* stateManager, QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::ArchiveManager)
    , m_Model(NULL)
    , m_StudyModel(NULL)
    , m_PatientModel(NULL)
    , m_TreeViewSelectedModel(QStringList())
    , m_BackupModel(NULL)
    , m_CurrentDisk(QString())
    , m_ProgressBar(NULL)
    , m_PatientWorkflow(NULL)
    , m_StudyOid(QString())
    , m_GeneralWorkflowFunction(NULL)
    , m_ItemSelectionModel(NULL)
    , m_DicomTaskManager(NULL)
    , m_Parent(NULL)
    , m_Multi(false)
    , m_IsDir(false)
    , m_ScreenModel(ScreenOrientationModel::getInstance())
    , m_StateManager(stateManager)
    , m_ColorMapManager(NULL)
    , m_DiskDevice(NULL)
{
    ui->setupUi(this);
    onMultiClicked(m_Multi);
    ctrateFileSystemModel();
    m_BackupModel = new BackupModel(stateManager, this);

    initImageSkimManager();

    m_BaseInputAbleDialogFrame = dynamic_cast<BaseInputAbleDialogFrame*>(parent);

    connect(ui->widgetTop, SIGNAL(discChanged(QString)), this, SLOT(onDiskChanged(QString)));
    connect(ui->widgetTop, SIGNAL(MultiClicked(bool)), this, SLOT(treeViewSelectionModel(bool)));
    connect(ui->widgetTop, SIGNAL(MultiClicked(bool)), ui->widgetButton, SLOT(onMultiClicked(bool)));
    connect(ui->widgetTop, SIGNAL(MultiClicked(bool)), this, SLOT(onMultiClicked(bool)));
    connect(this, SIGNAL(selectedModelChanged(QStringList, bool)), ui->widgetButton,
            SLOT(onSelectedModelChanged(QStringList, bool)));
    connect(ui->treeView->buttonWidget(), SIGNAL(buttonPatientClicked()), this, SLOT(onButtonPatientClicked()));
    connect(ui->treeView->buttonWidget(), SIGNAL(buttonStudyClicked()), this, SLOT(onButtonStudyClicked()));
    connect(ui->treeView->buttonWidget(), SIGNAL(buttonExpandClicked()), this, SLOT(onButtonExpandClicked()));
    connect(ui->treeView->buttonWidget(), SIGNAL(buttonCollapseClicked()), this, SLOT(onButtonCollapseClicked()));
    connect(ui->treeView->buttonWidget(), SIGNAL(buttonSelectClicked()), this, SLOT(onButtonSelectClicked()));

    connect(ui->treeView, &TreeViewWidget::doubleClicked, this, [=]() {
        if (m_StudyOid.startsWith(Resource::dicomRetrievePrefix))
        {
            return;
        }
        autoCallBackImage(0);
    });

    connect(ui->widgetButton, SIGNAL(infoButtonClicked()), this, SLOT(onInfoButtonClicked()));
    connect(ui->widgetButton, SIGNAL(reportButtonClicked()), this, SLOT(onReportButtonClicked()));
    connect(ui->widgetButton, SIGNAL(deleteButtonClicked()), this, SLOT(onDeleteButtonClicked()));
    connect(ui->widgetButton, SIGNAL(backupButtonClicked()), this, SLOT(onBackupButtonClicked()));
    connect(ui->widgetButton, SIGNAL(sendButtonClicked()), this, SLOT(onSendButtonClicked()));
    connect(ui->widgetButton, SIGNAL(restoreButtonClicked()), this, SLOT(onRestoreButtonClicked()));
    connect(this, SIGNAL(activeExamClicked(bool)), ui->widgetButton, SLOT(onActiveExamClicked(bool)));
    connect(ui->widgetTop, SIGNAL(comboBoxItemCurrentIndexChanged(int)), this,
            SLOT(onComboBoxItemCurrentIndexChanged(int)));
    connect(ui->widgetTop, SIGNAL(comboBoxTimeCurrentIndexChanged(int)), this,
            SLOT(onComboBoxTimeCurrentIndexChanged(int)));
    connect(ui->widgetTop, SIGNAL(lineEditKeystringChanged(QString)), this, SLOT(onLineEditKeystringChanged(QString)));
    // 防止Multi引发的按钮变灰问题 added by jinyuqi
    connect(ui->widgetTop, SIGNAL(MultiClicked(bool)), ui->widgetButton, SLOT(onClearSectionToMakeActiveFalse()));
    connect(this, SIGNAL(showEventSignal()), ui->widgetButton, SLOT(onClearSectionToMakeActiveFalse()));

    connect(this, SIGNAL(tpButClicked(QString, QString, QString)), this,
            SLOT(onTPButClicked(QString, QString, QString)));

    m_ButtonsText << "" << QT_TR_NOOP("New Exam") << QT_TR_NOOP("Continue Exam") << QT_TR_NOOP("Easy View")
                  << QT_TR_NOOP("Exit");
    m_ButtonsName << ""
                  << "newExam"
                  << "continueExam"
                  << "review"
                  << "exit";

    treeViewSelectionModel(false);
    this->setAttribute(Qt::WA_StyledBackground, true);

    Util::disablePushButtonDefault(this);

    ui->widgetImage->setSupportSlider(false);
}

ArchiveManager::~ArchiveManager()
{
    delete ui;
    Util::SafeDeletePtr(m_Model);
    Util::SafeDeletePtr(m_BackupModel);
    Util::SafeDeletePtr(m_ProgressBar);
}

void ArchiveManager::showEvent(QShowEvent* e)
{
    BaseWidget::showEvent(e);
    onDiskChanged(Resource::hardDiskDir);
    if (m_Parent != NULL)
        m_Parent->setDisabled(false);
    setDisabled(false);
    emit showEventSignal();
}

void ArchiveManager::hideEvent(QHideEvent*)
{
    ui->widgetTop->initArchiveTopWidget();
}

void ArchiveManager::retranslateUi()
{
    ui->retranslateUi(this);
}

void ArchiveManager::orientationChanged(Qt::ScreenOrientation orientation)
{
    QString className(this->metaObject()->className());
    OrientationConfigItem config;
    m_ScreenModel->getInfosByClassName(orientation, className, config);
    UiUtil::ScreenHVChangedUpdateUI(this, ui->gridLayout, config);
#ifdef USE_SIMULATEORIENTATION
    emit orientationChanged(m_ScreenModel->isHor());
#else
    emit orientationChanged(orientation == Qt::LandscapeOrientation);
#endif
}

void ArchiveManager::ctrateFileSystemModel()
{
    m_StudyModel = new DataBaseStudyViewModel(this);
    m_PatientModel = new DataBasePatientViewModel(this);

    setCurrentModel(m_StudyModel);
}

void ArchiveManager::setCurrentModel(DataBaseViewModel* value)
{
    if (m_Model != value)
    {
        if (m_ItemSelectionModel != NULL && !m_ItemSelectionModel->selectedIndexes().isEmpty())
        {
            clearSelection();
        }
        m_Model = value;
        m_Model->searchSql(m_QueryCriteria);
        m_Model->setDisk(m_CurrentDisk);
        ui->treeView->setModel(m_Model);
        ui->treeView->setRootIsDecorated(qobject_cast<DataBasePatientViewModel*>(m_Model) != NULL);
        m_ItemSelectionModel = ui->treeView->selectionModel();

        disconnect(m_ItemSelectionModel, SIGNAL(selectionChanged(QItemSelection, QItemSelection)), this,
                   SLOT(onSelectionChanged(QItemSelection, QItemSelection)));
        connect(m_ItemSelectionModel, SIGNAL(selectionChanged(QItemSelection, QItemSelection)), this,
                SLOT(onSelectionChanged(QItemSelection, QItemSelection)));

        // updatequery前先清除选中项,否则在patient模式下,当item超过512,有选中项时进行updatequery会崩溃
        //原因是更新currentindex时把第513项更新进了m_ItemSelectionModel
        //可以参考QItemSelectionModelPrivate::_q_rowsAboutToBeRemoved函数的end < rowCount(parent)-1条件
        connect(m_Model, SIGNAL(selectionCleared()), m_ItemSelectionModel, SLOT(clear()));
    }
}

void ArchiveManager::setTreeViewsetColumnWidth()
{
    ui->treeView->setColumnWidth(0, (ui->treeView->width()) / 5.0f);
    ui->treeView->setColumnWidth(1, (ui->treeView->width()) / 4.0f);
    ui->treeView->setColumnWidth(2, (ui->treeView->width()) / 6.0f);
    if (AppSetting::isHuman())
    {
        ui->treeView->setColumnWidth(3, (ui->treeView->width()) / 12.0f);
    }
    else
    {
        ui->treeView->setColumnWidth(3, (ui->treeView->width()) / 6.0f);
    }
    ui->treeView->setColumnWidth(4, (ui->treeView->width()) / 6.0f);
    ui->treeView->hideColumn(5);
    ui->treeView->hideColumn(6);
    ui->treeView->hideColumn(7);
    ui->treeView->setColumnWidth(8, (ui->treeView->width()) / 12.0f);
}

QStringList ArchiveManager::getDiskList()
{
    QStringList list;
    list = m_DiskDevice->diskInfo()->uDiskPaths();
    if (list.contains(QString(Resource::pathSeparator)))
    {
        list.removeOne(QString(Resource::pathSeparator));
    }
    return list;
}

QStringList ArchiveManager::prependRootDisc()
{
    QStringList diskList = getDiskList();
    QStringList containDbDiskset;
    diskList.prepend(Resource::hardDiskDir);
    foreach (QString disk, diskList)
    {
        if (QFileInfo(PatientPath::instance().dbFilePathName(disk)).exists())
        {
            containDbDiskset.append(disk);
        }
    }
    return containDbDiskset;
}

void ArchiveManager::savePatientToDisc()
{
}

void ArchiveManager::clearSelection()
{
    setStudyOid(QString());
    setImageSkimWidgetPath(QString());

    ui->treeView->clearSelection();
    m_TreeViewSelectedModel.clear();
    emit selectedModelChanged(m_TreeViewSelectedModel, m_IsDir);
}

void ArchiveManager::mkDateBaseDir(const QString& dir)
{
    if (!QFileInfo(PatientPath::instance().dbFilePathName(dir)).exists())
    {
        Util::Mkdir(PatientPath::instance().databasePath(dir));
        m_BackupModel->copyDataToDisk(PatientPath::instance().databasePath(dir));
        //        ui->widgetTop->addDiskItem(dir);
        ui->widgetTop->updateDiskPathComboBox();
    }
}

void ArchiveManager::toStartQuery()
{
    m_Model->searchSql(m_QueryCriteria);
    clearSelection();
    setTreeViewsetColumnWidth();
}

void ArchiveManager::setIDOrNameCriteria(int index, const QString& text)
{
    QString input = Util::sqliteEscape(text);

    switch (index)
    {
    case PatientId:
        m_QueryCriteria.patientId = input;
        m_QueryCriteria.patientsName.clear();
        break;
    case PatientName:
        m_QueryCriteria.patientsName = input;
        m_QueryCriteria.patientId.clear();
        break;
    }
    toStartQuery();
}

void ArchiveManager::controlDeleteButtonsEnable()
{
    bool isEnable = true;

    if (m_CurrentDisk == Resource::hardDiskDir)
    {
        if (m_TreeViewSelectedModel.isEmpty())
        {
            isEnable = false;
        }
        else
        {
            if (m_PatientWorkflow->patient() != NULL)
            {
                QString patientId = m_PatientWorkflow->patient()->PatientId();
                QString studyId = m_PatientWorkflow->patient()->firstStudy()->StudyOid();
                QString patientPath = PatientPath::instance().path(patientId, QString(), m_CurrentDisk);
                QString studyPath = PatientPath::instance().path(patientId, studyId, m_CurrentDisk);

                if (m_TreeViewSelectedModel.contains(patientPath) || m_TreeViewSelectedModel.contains(studyPath))
                {
                    isEnable = false;
                }
            }
        }
    }
    else
    {
        isEnable = false;
    }

    emit activeExamClicked(isEnable);
}

void ArchiveManager::controlExpandCollapseEnable(bool doExpand)
{
    // 如果有展开项，则全部收起，否则直接进行全选操作会卡住系统
    // 也试过判断如果某项展开，就收起某一项，这样子如果当前是全部展开的话，
    // 这个时间比collapseAll耗时，所以直接使用collapseAll
    // imporved by gongdl

    fetchMore();

    if (m_Model == m_PatientModel)
    {
        for (int i = 0; i < m_Model->rowCount(); i++)
        {
            QModelIndex index = m_Model->index(i, 0);
            if (doExpand)
            {
                if (!ui->treeView->isExpanded(index)) //需要展开才执行expandAll
                {
                    ui->treeView->expandAll();
                    break;
                }
            }
            else
            {
                if (ui->treeView->isExpanded(index)) //需要收起才执行collapseAll
                {
                    ui->treeView->collapseAll();
                    break;
                }
            }
        }
    }
}

void ArchiveManager::fetchMore()
{
    while (m_Model->canFetchMore())
    {
        m_Model->fetchMore();
    }
}

QStringList ArchiveManager::changeToStudyPath(const QStringList& srcPaths) const
{
    QStringList studyPaths;
    foreach (const QString& path, srcPaths)
    {
        QFileInfo info = QFileInfo(path);
        if (!info.baseName().isEmpty())
        {
            // study
            if (!studyPaths.contains(path))
            {
                studyPaths.append(path);
            }
        }
        else
        {
            // patient
            Patient* patient = NULL;
            QString patientId = info.path().split("/").last();
            patient = DataAccessLayerHelper::getPatient(patientId, false);

            if (patient == NULL)
                continue;

            QList<Study*> studies = DataAccessLayerHelper::getStudies(patient);
            for (int i = 0; i < studies.count(); i++)
            {
                QString studyOid = studies[i]->StudyOid();
                QString studyPath = path + studyOid;
                if (!studyPaths.contains(studyPath))
                {
                    studyPaths.append(studyPath);
                }
            }

            delete patient;
        }
    }
    return studyPaths;
}

bool ArchiveManager::isContainsSendingItem() const
{
    if (m_CurrentSendingModels.isEmpty())
    {
        return false;
    }
    else
    {
        QStringList studyPaths = changeToStudyPath(m_TreeViewSelectedModel);
        foreach (const QString& path, studyPaths)
        {
            foreach (const QStringList& models, m_CurrentSendingModels)
            {
                if (models.contains(path))
                {
                    MessageBoxFrame::tipInformation(tr("Some studys are being used now, please retry later!"));
                    return true;
                }
            }
        }
        return false;
    }
}

void ArchiveManager::onTPButClicked(const QString& category, const QString& butName, const QString& value)
{
    QStringList btnames = butName.split(":", Qt::SkipEmptyParts);
    qDebug() << "## ArchiveManager::onTPButClicked " << category << btnames << value;
    if (category != "Archive" || btnames.size() < 2 || btnames.at(0) != "Archive")
    {
        return;
    }

    QString btn = btnames.last();
    qDebug() << "    ** " << btn;
    if (btn == "PatientInfo")
    {
        onInfoButtonClicked();
    }
    else if (btn == "ReviewReport")
    {
        onReportButtonClicked();
    }
    else if (btn == "BackupExam")
    {
        onBackupButtonClicked();
    }
    else if (btn == "RestoreExam")
    {
        onRestoreButtonClicked();
    }
    else if (btn == "SendExam")
    {
        onSendButtonClicked();
    }
    else if (btn == "DeleteExam")
    {
        onDeleteButtonClicked();
    }
    else if (btn == "PatientView")
    {
        onButtonPatientClicked();
    }
    else if (btn == "StudyView")
    {
        onButtonStudyClicked();
    }
    else if (btn == "ExpandAll")
    {
        onButtonExpandClicked();
    }
    else if (btn == "CollapseAll")
    {
        onButtonCollapseClicked();
    }
    else if (btn == "SelectAll")
    {
        onButtonSelectClicked();
    }
}

void ArchiveManager::autoCallBackImage(const int index)
{
    //只支持本地的病例双击回调
    if (ui->widgetTop->isDiskLocal() && !m_IsDir && !m_TreeViewSelectedModel.isEmpty() && !m_Multi)
    {
        onButtonClicked(ArchiveManagerDialog::button_continue);
        emit autoCallBack(index);
    }
}

void ArchiveManager::onDeleteCurrentSelections()
{
    deleteCurrentSelection();
}

void ArchiveManager::initArchiveManager()
{
}

void ArchiveManager::setPatientWorkflow(PatientWorkflow* patientWorkflow)
{
    m_PatientWorkflow = patientWorkflow;
}

void ArchiveManager::setGeneralWorkflowFunction(GeneralWorkflowFunction* generalWorkflowFunction)
{
    m_GeneralWorkflowFunction = generalWorkflowFunction;
}

void ArchiveManager::setDicomTaskManager(DicomTaskManager* dicomTaskManager)
{
    m_DicomTaskManager = dicomTaskManager;
}

void ArchiveManager::setToolsFacade(IToolsFacade* value)
{
    ui->widgetImage->setToolsFacade(value);
}

void ArchiveManager::setColorMapManager(IColorMapManager* value)
{
    m_ColorMapManager = value;
}

void ArchiveManager::setDiskDevice(IDiskDevice* diskDevice)
{
    m_DiskDevice = diskDevice;
    ui->widgetTop->setDiskDevice(diskDevice);
}

void ArchiveManager::on_treeView_clicked(const QModelIndex& index)
{
}

void ArchiveManager::onButtonPatientClicked()
{
    ui->treeView->buttonWidget()->patientView();
    setCurrentModel(m_PatientModel);
}

void ArchiveManager::onButtonStudyClicked()
{
    ui->treeView->buttonWidget()->studyView();
    setCurrentModel(m_StudyModel);
}

void ArchiveManager::onButtonExpandClicked()
{
    controlExpandCollapseEnable(true);
}

void ArchiveManager::onButtonCollapseClicked()
{
    controlExpandCollapseEnable(false);
}

void ArchiveManager::onButtonSelectClicked()
{
    if (m_Multi)
    {
        controlExpandCollapseEnable(false);

        ui->treeView->selectAll();
    }
}

void ArchiveManager::showPicture()
{
    onButtonClicked(ArchiveManagerDialog::button_easyview);
}

void ArchiveManager::onDiskChanged(const QString& diskPath)
{
    if (!diskPath.isEmpty())
    {
        m_CurrentDisk = diskPath;

        clearSelection();
        DataAccessLayerHelper::setDbFileName(PatientPath::instance().dbFilePathName(diskPath));
        m_Model->searchSql(m_QueryCriteria);
        m_Model->setDisk(diskPath);
        setTreeViewsetColumnWidth();
        ui->widgetButton->setDiskPath(diskPath);
    }
}

void ArchiveManager::backupToDisk(const QString& disk)
{
    if (!m_TreeViewSelectedModel.isEmpty())
    {
        if (disk.contains(Resource::uDiskDir)) //  udsik
        {
            mkDateBaseDir(disk);
            m_BackupModel->setDisk(disk);
        }
        m_BackupModel->setDirNameList(m_TreeViewSelectedModel);

#ifdef USE_RUN_MODE_VIRTUAL
        m_BackupModel->saveStudys(PatientPath::instance().databasePath(disk), this);
#else
        if (disk.contains(Resource::uDiskDir)) //  udsik
        {
            m_BackupModel->saveStudys(PatientPath::instance().databasePath(disk), this);
        }
        else //  disc CD or DVD
        {
            m_BackupModel->burnFileToDvd(PatientPath::instance().databasePath(disk), this);
        }
#endif
    }
}

void ArchiveManager::treeViewSelectionModel(bool multi)
{
    if (multi)
    {
        ui->treeView->setSelectionMode(QAbstractItemView::MultiSelection);
    }
    else
    {
        ui->treeView->setSelectionMode(QAbstractItemView::SingleSelection);
    }
    clearSelection();
}

void ArchiveManager::sortChanged()
{
    clearSelection();
}

void ArchiveManager::onButtonClicked(int index)
{
    switch (index)
    {
    case ArchiveManagerDialog::button_new: // new study
        m_GeneralWorkflowFunction->setExamEnd(m_CurrentDisk);
        emit currentDiskPathChanged(m_CurrentDisk);
        emit sendStudyOid(m_StudyOid);
        emit closedCurrentWidget();
        emit entryPatientState();
        break;
    case ArchiveManagerDialog::button_continue:
        if (isContainsSendingItem())
        {
            return;
        }

        if (m_buttonTextIsContinue)
        { //继续检查
            if (m_PatientWorkflow->patient() != NULL &&
                m_PatientWorkflow->patient()->firstStudy()->StudyOid() != m_StudyOid)
            {
                m_PatientWorkflow->setCurrentExamEnd();
                m_GeneralWorkflowFunction->continueStudy(m_StudyOid);
            }
            else if (m_PatientWorkflow->patient() == NULL)
            {
                m_GeneralWorkflowFunction->continueStudy(m_StudyOid);
            }
            emit closedCurrentWidget();
            Util::processEvents(QEventLoop::ExcludeUserInputEvents);
            emit entryContinueExamState();
        }
        else
        { //编辑检查
            if (m_PatientWorkflow->patient() != NULL &&
                m_PatientWorkflow->patient()->firstStudy()->StudyOid() != m_StudyOid)
            {
                m_PatientWorkflow->setCurrentExamEnd();
                m_GeneralWorkflowFunction->editStudy(m_StudyOid);
            }
            else if (m_PatientWorkflow->patient() == NULL)
            {
                m_GeneralWorkflowFunction->editStudy(m_StudyOid);
            }
            /**
             * BUG:18435 该问题是由于，在切换到EditExamState状态过程中，由于机器性能问题，速度较慢，
             * 该过程中如果按下report功能键，那么，就出现BUG18435中的现象,故在这过程中禁止功能键
             */
            AppSetting::setIsIgnoreFunckey(true);
            emit closedCurrentWidget();
            Util::processEvents(QEventLoop::ExcludeUserInputEvents);
            emit entryEditExamState();
            AppSetting::setIsIgnoreFunckey(false);
        }
        break;
    case ArchiveManagerDialog::button_easyview: // easyview
        if (m_Parent != NULL)
            m_Parent->setDisabled(true);
        setDisabled(true);
        emit onlyJump();
        emit sendDirPathList(m_TreeViewSelectedModel);
        emit currentDiskPathChanged(m_CurrentDisk);
        // disable it
        //        emit closedCurrentWidget();
        emit entryBrowseState();
        break;
    case ArchiveManagerDialog::button_null:
        break;
    case ArchiveManagerDialog::button_cancel: // cancel
        emit closedCurrentWidget();
        DataAccessLayerHelper::setDbFileName();
        break;
    }
}

void ArchiveManager::onInfoButtonClicked()
{
    DataAccessLayerHelper::setDbFileName(PatientPath::instance().dbFilePathName(m_CurrentDisk));
    m_GeneralWorkflowFunction->showPatientInfo(m_StudyOid);
}

void ArchiveManager::onReportButtonClicked()
{
    QScopedPointer<Patient> patient(m_GeneralWorkflowFunction->getPatient(m_StudyOid));

    if (m_DicomTaskManager != NULL && AppSetting::isDicom())
    {
        ReportFileLoadHelper::loadReport(patient.data(), m_DicomTaskManager, m_CurrentDisk);
    }
    else
    {
        ReportFileLoadHelper::loadReport(patient.data(), m_CurrentDisk);
    }
}

void ArchiveManager::onDeleteButtonClicked()
{
    if (isContainsSendingItem())
    {
        return;
    }

    if (GeneralWorkflowFunction::ifContinueOperate(this))
    {
        deleteCurrentSelection();
    }
}

void ArchiveManager::deleteCurrentSelection()
{
    if (m_ProgressBar == NULL)
    {
        m_ProgressBar = new ProgressBarView(m_StateManager, this);
    }
    m_ThreadModel.setDeleteDataBaseModel(m_Model);
    GeneralWorkflowFunction::deleteFileList(m_TreeViewSelectedModel, &m_ThreadModel, m_ProgressBar);
    //        m_Model->deleteStudy(GeneralWorkflowFunction::changeStringToFileInfo(m_TreeViewSelectedModel));

    /***************** Modified by JinYuQi********删除没有study的patient*************************/
    //        QFileInfo patientInfo = QFileInfo(m_TreeViewSelectedModel.at(0));

    /*
        QFileInfoList patientInfoList(GeneralWorkflowFunction::changeStringToFileInfo(m_TreeViewSelectedModel));
        QFileInfoList delPatientInfoList;
        DBTranscation t(DataAccessLayerHelper::sqlDatabase()); //节省数据库操作时间

        foreach(QFileInfo patientInfo,patientInfoList)
        {
            QString tempStudyOid = patientInfo.baseName();
            Patient* delPatent = NULL;
            bool isPatient = false;

            if(tempStudyOid.isEmpty())
            {
                QString tempPatientId = patientInfo.path().split("/").last();
                delPatent = DataAccessLayerHelper::getPatient(tempPatientId, false);
                isPatient = true;
            }
            else
            {
                delPatent = GeneralWorkflowFunction::getPatient(tempStudyOid);
                isPatient = false;
            }

            if (delPatent == NULL)
                continue;

            //删除数据库中study和patient之前启动deletethread
            Util::processEvents(QEventLoop::ExcludeUserInputEvents);

            if(isPatient)
            {
                QFileInfoList tempListStudy;
                QList<Study*> studies = DataAccessLayerHelper::getStudies(delPatent);
                for(int i = 0; i < studies.count(); i++)
                {
                    tempListStudy.append(QFileInfo(studies[i]->StudyOid()));
                }
                m_Model->deleteStudy(tempListStudy);

                //先删除所有study再删除patient自己
                delPatientInfoList.append(patientInfo);
            }
            else
            {
                if (DataAccessLayerHelper::getStudies(delPatent).count() == 1)
                {
                    QFileInfoList tempListStudy;
                    tempListStudy.append(patientInfo);
                    m_Model->deleteStudy(tempListStudy);

                    //只有一个study的病人,删除完study后需要再删除patient
                    delPatientInfoList.append(patientInfo);
                }
                else if (DataAccessLayerHelper::getStudies(delPatent).count() > 1)
                {
                    QFileInfoList tempListPatient;
                    tempListPatient.append(patientInfo);
                    m_Model->deleteStudy(tempListPatient);
                }
            }

            delete delPatent;
        }
        m_Model->deletePatient(delPatientInfoList);
     */

    //        patinetInfoList.append(patientInfo);
    //        QString tempStudyOid = patientInfo.baseName();
    //        qDebug() << "PatientID is "   <<     GeneralWorkflowFunction::getPatient(tempStudyOid)->PatientId();
    //        QList<Study*> studies =
    //        DataAccessLayerHelper::getStudies(GeneralWorkflowFunction::getPatient(tempStudyOid)); qDebug() << "count
    //        is " << studies.count(); int studiesCount = studies.count(); if (studiesCount == 1)
    //        {
    //            m_Model->deletePatient(QFileInfoList(patinetInfoList));
    //        }
    //        qDebug() << "2st count is "<< GeneralWorkflowFunction::getPatient(tempStudyOid)->Studies().count();
    //        m_Model->deleteStudy(GeneralWorkflowFunction::changeStringToFileInfo(m_TreeViewSelectedModel));

    //           m_Model->deletePatient(GeneralWorkflowFunction::changeStringToFileInfo(m_TreeViewSelectedModel));
    /**************************************************************************************/

    //    m_Model->updateQuery();
    m_TreeViewSelectedModel = QStringList();
    emit selectedModelChanged(m_TreeViewSelectedModel, m_IsDir);
    setImageSkimWidgetPath(QString());
}

bool ArchiveManager::isHor()
{
    return m_ScreenModel->isHor();
}

void ArchiveManager::setImageSkimSpacing(int spacing)
{
    ui->widgetImage->getSkimWidget()->setSpacing(spacing);
}

void ArchiveManager::onBackupButtonClicked()
{
    if (isContainsSendingItem())
    {
        return;
    }

    DataBackupDialog* backupDlg = new DataBackupDialog(m_DiskDevice, this);
    if (!backupDlg->isUDiskMounted())
    {
        MessageBoxFrame::warning(this, QString(), tr("Please plug U disk!"));
    }
    else
    {
        connect(backupDlg, SIGNAL(clickOK(QString)), this, SLOT(backupToDisk(QString)));
        backupDlg->exec();
    }

    delete backupDlg;
}

void ArchiveManager::onSendButtonClicked()
{
    if (m_TreeViewSelectedModel.isEmpty())
    {
        return;
    }
    bool pflag = false, sflag = false, uflag = false, nflag = false, srflag = false, bflag = false;

    GeneralWorkflowFunction::hasUsableDevice(this, &pflag, &sflag, &uflag, &nflag, &bflag, &srflag);

    if (pflag || sflag || uflag || nflag || srflag || bflag)
    {
        //        DicomSeverInfoDialog dlg(this);
        QScopedPointer<Patient> patient(GeneralWorkflowFunction::getPatient(m_StudyOid));
        //        dlg.setDefaultDirName(PatientPath::instance().exportPatientDirName(patient.data()));//可能传入的值为空
        //        dlg.setExportFilePaths(m_TreeViewSelectedModel);//存放图片的文件夹名
        //        dlg.setDicomTaskManager(m_DicomTaskManager);
        //        dlg.initWidegt();
        //        dlg.exec();
        bool isCurrentMultiChoice = false;
        if ((ui->treeView->selectionMode() == QAbstractItemView::MultiSelection) || m_TreeViewSelectedModel.count() > 1)
        {
            isCurrentMultiChoice = true;
        }

        QStringList studyPaths = changeToStudyPath(m_TreeViewSelectedModel);
        if (!m_CurrentSendingModels.contains(studyPaths))
        {
            m_CurrentSendingModels.append(studyPaths);
        }
        //不使用成员变量是因为避免多次send之后,前一次task未完成,指针就被后一次send取代引起的问题
        DeleteFilesController* controller = new DeleteFilesController(studyPaths, this);
        connect(controller, SIGNAL(deleteExam(QStringList, bool)), this, SLOT(onDeleteExam(QStringList, bool)),
                Qt::AutoConnection);

        GeneralWorkflowFunction::exportFiles(this, PatientPath::instance().exportPatientDirName(patient.data()),
                                             QStringList(), m_TreeViewSelectedModel, m_DicomTaskManager, m_StateManager,
                                             m_ColorMapManager, controller, isCurrentMultiChoice,
                                             ui->widgetButton->isButtonEnabled(ArchiveButtonWidget::Delete), true);
    }
    else
    {
        MessageBoxFrame::warning(this, QString(), tr("There is no available device for export!"));
    }
    //    {
    //        GeneralWorkflowFunction::exportFiles(this, m_TreeViewSelectedModel, m_StudyOid);
    //    }
}

void ArchiveManager::onRestoreButtonClicked()
{
    m_BackupModel->setDirNameList(m_TreeViewSelectedModel);
    // 增加setDisk为了和backup的实现保持一直，方便维护，并且防止restore操作到最后返回自己local数据库 by jinyuqi
    QString curDisk = this->ui->widgetTop->currentDisk();
    QString disk = PatientPath::instance().databasePath(curDisk);
    m_BackupModel->setDisk(disk);
    m_BackupModel->saveStudys(Resource::hardDiskDir, this, false);
    //    m_BackupModel->saveStudys(Resource::hardDiskDir, this);
}

void ArchiveManager::onComboBoxItemCurrentIndexChanged(int index)
{
    switch (index)
    {
    case PatientId:
    {
        ui->widgetTop->clearKeyword();
    }
    break;
    case PatientName:
    {
        QRegExp rxName("[^\\\\^=]+$"); //根据DICOM标准禁用以下字符\^=.
        QValidator* validatorName = new QRegExpValidator(rxName, this);
        ui->widgetTop->setValidatorName(validatorName);
    }
    break;
    default:
        break;
    }
}

void ArchiveManager::onComboBoxTimeCurrentIndexChanged(int index)
{
    switch (index)
    {
    case OneDay:
        m_QueryCriteria.fromStudyDate = QDateTime::currentDateTime().date().startOfDay();
        break;
    case OneWeek:
        m_QueryCriteria.fromStudyDate = QDateTime::currentDateTime().addDays(-7).date().startOfDay();
        break;
    case OneMonth:
        m_QueryCriteria.fromStudyDate = QDateTime::currentDateTime().addMonths(-1).date().startOfDay();
        break;
    case ThreeMonths:
        m_QueryCriteria.fromStudyDate = QDateTime::currentDateTime().addMonths(-3).date().startOfDay();
        break;
    case SixMonths:
        m_QueryCriteria.fromStudyDate = QDateTime::currentDateTime().addMonths(-6).date().startOfDay();
        break;
    case OneYear:
        m_QueryCriteria.fromStudyDate = QDateTime::currentDateTime().addYears(-1).date().startOfDay();
        break;
    case All:
        m_QueryCriteria.fromStudyDate = QDateTime();
        break;
    }
    toStartQuery();
}

void ArchiveManager::onLineEditKeystringChanged(const QString& text)
{
    setIDOrNameCriteria(ui->widgetTop->squeryCurrentIndex(), text);
}

void ArchiveManager::onMultiClicked(bool multi)
{
    m_Multi = multi;
    if (m_Multi)
    {
        m_BaseInputAbleDialogFrame->setButtonEnabled(false, ArchiveManagerDialog::button_new);
        m_BaseInputAbleDialogFrame->setButtonEnabled(false, ArchiveManagerDialog::button_continue);
    }

    ui->treeView->buttonWidget()->setSelectAllButtonEnabled(m_Multi);
}

void ArchiveManager::setStudyOid(const QString& oid)
{
    if (m_StudyOid != oid)
    {
        m_StudyOid = oid;

        if (!m_StudyOid.isEmpty())
        {
            bool continues = m_GeneralWorkflowFunction->isOrNotContinueStudy(m_StudyOid);
            if (continues && m_CurrentDisk == Resource::hardDiskDir) // continues用于确定是继续检查还是编辑检查
            {
                m_BaseInputAbleDialogFrame->setButtonText(QT_TR_NOOP("Continue Exam"),
                                                          ArchiveManagerDialog::button_continue);
                m_buttonTextIsContinue = true;
            }
            else
            {
                m_BaseInputAbleDialogFrame->setButtonText(QT_TR_NOOP("Edit Exam"),
                                                          ArchiveManagerDialog::button_continue);
                m_buttonTextIsContinue = false;
            }
        }

        m_BaseInputAbleDialogFrame->setButtonEnabled(!m_StudyOid.isEmpty(), ArchiveManagerDialog::button_new);
        //非本地文件不可editExam,需要修改edit后实现加载非本地的数据库内容,否则会出现加载无数据现象
        bool enabledValue = !m_StudyOid.isEmpty() && (m_CurrentDisk == Resource::hardDiskDir) &&
                            !m_StudyOid.startsWith(Resource::dicomRetrievePrefix);
        m_BaseInputAbleDialogFrame->setButtonEnabled(enabledValue, ArchiveManagerDialog::button_continue);
    }
}

void ArchiveManager::setImageSkimWidgetPath(const QString& value)
{
    if (m_ImageSkimWidgetPath != value)
    {
        m_ImageSkimWidgetPath = value;
        ui->widgetImage->load(m_ImageSkimWidgetPath);
    }
}

void ArchiveManager::onSelectionChanged(const QItemSelection& selected, const QItemSelection& deselected)
{
    QModelIndexList selectedIndexes = selected.indexes();
    QModelIndexList deselectedIndexes = deselected.indexes();

    if (!selectedIndexes.isEmpty())
    {
        // modified by jinyuqi to prove that the index will all run into this function
        // if come with 1 item, the list will begin from 1 to '8', and from (0,0) to (0,7) in first loop
        // but if come with items, the list will begin from row to row, from(0,0) to (row,0) in first loop

        // improved by gongdl, 使用QSet判断是否已经处理过某一行，这样做速度
        // 相对于用list直接判断contains，提高非常多。此处使用row int型变量比
        // 使用QString的速度也更快
        /*
        QSet<int> rows;
        foreach (const QModelIndex &index, selectedIndexes)
        {
            if(!rows.contains(index.row()))
            {
                rows.insert(index.row());
                m_TreeViewSelectedModel.append(m_Model->fileInfo(index).filePath());
            }
        }
        */

        // fixed by gqh, BUG:8670, EBit
        // StudyView属于TableView,可以通过行标(index.row())来区分每一行
        // PatientView属于TreeView, 每个Item由(row, column, parent)来定位。
        // 可能会有重复的行标，属于不同的parent。所以不能由行标来区分行。
        // 此处的PatientView为每一行绑定了一个TreeItem内部数据结构。所以可以根据TreeItem的地址来区分每一行。
        // StudyView没有绑定特定数据，index.internalPointer()/ID()总是返回0. 所以仍需要通过行标来区分。
        // TreeView行标如下所示，
        // |aaaaa           row0
        //   |-Subaaaaa0    row0
        //   |-Subaaaaa1    row1
        //   |-Subaaaaa2    row2
        // |bbbbb           row1
        //   |-Subbbbbb0    row0
        // |ccccc           row2

        // This QSet stores identification for each row.
        // either row index(index.row()) or address of data bind to row(index.internalID())
        // index.internalID() is the decimal conversion of index.internalPointer()
        QSet<qint64> rows;
        foreach (const QModelIndex& index, selectedIndexes)
        {
            if (index.internalId() == 0)
            {
                // Identify each row by row index
                if (!rows.contains(index.row()))
                {
                    rows.insert(index.row());
                    m_TreeViewSelectedModel.append(m_Model->fileInfo(index).filePath());
                }
            }
            else
            {
                // Identify each row by the address of TreeItem
                if (!rows.contains(index.internalId()))
                {
                    rows.insert(index.internalId());
                    m_TreeViewSelectedModel.append(m_Model->fileInfo(index).filePath());
                }
            }
        }
    }

    if (!deselectedIndexes.isEmpty())
    {
        QSet<qint64> rows;
        foreach (const QModelIndex& index, deselectedIndexes)
        {
            if (index.internalId() == 0)
            {
                if (!rows.contains(index.row()))
                {
                    rows.insert(index.row());
                    m_TreeViewSelectedModel.removeOne(m_Model->fileInfo(index).filePath());
                }
            }
            else
            {
                if (!rows.contains(index.internalId()))
                {
                    rows.insert(index.internalId());
                    m_TreeViewSelectedModel.removeOne(m_Model->fileInfo(index).filePath());
                }
            }
        }
    }

    if (ui->treeView->selectionMode() == QAbstractItemView::SingleSelection)
    {
        QFileInfo fileInfo;

        if (m_TreeViewSelectedModel.count() == 1)
        {
            fileInfo = QFileInfo(m_TreeViewSelectedModel.at(0));
        }

        setImageSkimWidgetPath(fileInfo.filePath());
        setStudyOid(fileInfo.baseName()); // editExam按钮需要根据是否存在图像数据来设置状态
    }

    controlDeleteButtonsEnable();
    if (m_TreeViewSelectedModel.size() >= 1) //判断在Patient View显示模式下选中的item是否是目录
    {
        QString str = m_TreeViewSelectedModel.at(0);
        if (str.endsWith("/"))
        {
            m_IsDir = true;
        }
        else
        {
            m_IsDir = false;
        }
    }
    emit selectedModelChanged(m_TreeViewSelectedModel, m_IsDir);
}

void ArchiveManager::onDeleteExam(const QStringList& studyPaths, bool isDeleteFile)
{
    if (isDeleteFile)
    {
        QFileInfoList studyInfoList(GeneralWorkflowFunction::changeStringToFileInfo(studyPaths));
        QFileInfoList patientInfoList;
        DBTranscation t(*DataAccessLayerHelper::sqlDatabase());

        foreach (QFileInfo studyInfo, studyInfoList)
        {
            QString studyOid = studyInfo.baseName();
            Patient* patient = NULL;
            patient = GeneralWorkflowFunction::getPatient(studyOid);

            if (patient == NULL)
                continue;

            if (DataAccessLayerHelper::getStudies(patient).count() == 1)
            {
                Util::Rmdir(studyInfo.absolutePath()); //直接删除patient文件夹
                QFileInfoList tempListStudy;
                tempListStudy.append(studyInfo);
                m_Model->deleteStudy(tempListStudy);

                //只有一个study的病人,删除完study后需要再删除patient
                patientInfoList.append(studyInfo);
            }
            else if (DataAccessLayerHelper::getStudies(patient).count() > 1)
            {
                Util::Rmdir(studyInfo); //删除一个study文件夹
                QFileInfoList tempListPatient;
                tempListPatient.append(studyInfo);
                m_Model->deleteStudy(tempListPatient);
            }

            delete patient;
        }
        m_Model->deletePatient(patientInfoList);

        m_Model->updateQuery();
        m_TreeViewSelectedModel = QStringList();
        emit selectedModelChanged(m_TreeViewSelectedModel, m_IsDir);
        setImageSkimWidgetPath(QString());
    }

    DeleteFilesController* controller = qobject_cast<DeleteFilesController*>(sender());
    if (controller != NULL)
    {
        QStringList paths = controller->originStudyPaths();
        if (m_CurrentSendingModels.contains(paths))
        {
            m_CurrentSendingModels.removeOne(paths);
        }
        //此槽函数执行完会回到DeleteFilesController中继续执行m_Mutex.unlock(),
        //所以需要deletelater保证在执行完controller中代码再delete
        controller->deleteLater();
    }
}

void ArchiveManager::initImageSkimManager()
{
    int row, column = 0;
    if (ModelUiConfig::instance().readRowAndColumn(ModelUiConfig::ArchiveManagerThumbnailRowAndColumn, row, column))
    {
        ui->widgetImage->setRowAndColumn(row, column);
    }
    ui->widgetImage->getSkimWidget()->setHorizontalSpacing(0);

    ui->widgetImage->changeToolWidgetAndLocation(ThumbnailViewUtil::Normal_Vertical, ThumbnailViewUtil::AtRight);
    ui->widgetImage->setSelectType(ThumbnailViewUtil::Single);
    //    connect(ui->widgetImage->getSkimWidget(), SIGNAL(doubleClicked(const IimageInfo*)), this,
    //    SLOT(showPicture()));

    connect(ui->widgetImage->getSkimWidget(), &ImageSkimWidget::doubleClicked, this, [=](const IimageInfo* info) {
        if (m_StudyOid.startsWith(Resource::dicomRetrievePrefix))
        {
            return;
        }
        for (int index = 0; index < ui->widgetImage->getSkimWidget()->ImageLabelList().count(); index++)
        {
            IimageInfo* imageInfo = ui->widgetImage->getSkimWidget()->imageInfoList().at(index);
            if (imageInfo == info)
            {
                autoCallBackImage(ui->widgetImage->currentPageIndex() * ui->widgetImage->getColumn() + index);
                return;
            }
        }
    });
}
