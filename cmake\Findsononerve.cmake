thirdparty_prefix_path(sononerve)

find_path ( SONONERVE_INCLUDE_DIR
            NAMES
                neural.h
            HINTS
                ${SONONERVE_ROOT}/include
            )
set(SONONERVE_INCLUDE_DIRS ${SONONERVE_INCLUDE_DIR})
message("Found neural headers: ${SONONERVE_INCLUDE_DIRS}")

find_library ( SONONERVE_LIBRARY
            NAMES
                neuraldt
            HINTS
                ${SONONERVE_ROOT}/lib
             )
set(SONONERVE_LIBRARIES ${SONONERVE_LIBRARY})
message("Found neuraldt libs: ${SONONERVE_LIBRARIES}")




