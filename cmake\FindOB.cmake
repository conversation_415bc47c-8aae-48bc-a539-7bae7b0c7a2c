#
# The module defines the following variables:
# IMT_FOUND - True if imt found.
# IMT_INCLUDE_DIRS - where to find imt.h, etc.
# IMT_LIBRARIES - List of libraries when using imt.
#
thirdparty_prefix_path(ob)

find_path ( OB_INCLUDE_DIR
            NAMES
                OB.h
            HINTS
                ${OB_ROOT}/include
            )
set(OB_INCLUDE_DIRS ${OB_INCLUDE_DIR})
message("Found ob headers: ${OB_INCLUDE_DIRS}")


find_library ( OB_LIBRARY
            NAMES 
                ob1.0_CPU
            HINTS
                ${OB_ROOT}/lib
             )


set(OB_LIBRARIES ${OB_LIBRARY})
message("Found ob libs: ${OB_LIBRARIES}")

