#ifndef FOCUSESCOMBINEWIDGET_H
#define FOCUSESCOMBINEWIDGET_H
#include "controls_global.h"
#include "sonoparametersclientbase.h"
#include <QWidget>
#include <QVector>

class FocusesCombineModel;
namespace Ui
{
class FocusesCombineWidget;
}

class CONTROLSSHARED_EXPORT FocusesCombineWidget : public QWidget, public SonoParametersClientBase
{
    Q_OBJECT

public:
    explicit FocusesCombineWidget(QWidget* parent = 0);
    ~FocusesCombineWidget();
    FocusesCombineModel* model() const;
    bool save(bool showTip = false);

public slots:
    void setModel(FocusesCombineModel* model);

private slots:
    void onModelChanged();

    void on_spinBoxCombineStart_valueChanged(const QString& arg1);
    void on_spinBoxCombineLen_valueChanged(const QString& arg1);
    void on_pushButtonSave_clicked();
    void on_pushButtonReload_clicked();
    void on_pushButtonDefault_clicked();

    void onFocusNumBChanged(const QVariant& value);
    void onFocusPosBChanged(const QVariant& value);
    void onSyncModeChanged(const QVariant& value);

signals:
    void changingModel(int focusNum);

private:
    void onSetSonoParameters();
    /**
     * @brief 根据焦点数目 focuses 和档位 focusPos 设置焦点位置
     */
    void setFocusPosWidget(int focuses, int focusPos = 0);

private:
    Ui::FocusesCombineWidget* ui;
    FocusesCombineModel* m_model;
};

#endif // FOCUSESCOMBINEWIDGET_H
