#include "freehand3droicontrol.h"
#include "freehand3droiglyphscontrol.h"
#include "imagewidget.h"
#include "imagetile.h"
#include "sonoparameters.h"
#include "setting.h"
#include "modeluiconfig.h"
#include "toolsfactory.h"
#include "toolnames.h"
#include "icommand.h"
#include "parameter.h"
#include "bfpnames.h"

FreeHand3DRoiControl::FreeHand3DRoiControl(FreeHand3DRoiGlyphsControl* roiGlyphsControl, ImageWidget* imageWidget,
                                           const QRect& imagerect, QObject* parent)
    : QObject(parent)
    , m_RoiController(roiGlyphsControl)
    , m_ImageWidget(imageWidget)
    , m_SonoParameters(m_ImageWidget->imageTile()->sonoParameters())
    , m_HD3DRoiActionType(ROIMove)
    , m_ShowFreeHand3DRoiWidget(false)
    , m_UpdateFreeHand3DRoi(false)
    , m_ImageRect(imagerect)
    , m_RoiMinSize(Setting::instance().defaults().freeHand3DRoiMinSize())
    , m_RoiSize(Setting::instance().defaults().freeHand3DRoiSize())
    , m_RoiPos(m_ImageRect.topLeft() + Setting::instance().defaults().freeHand3DRoiPos())
{
    m_ImageWidget->imageTile()->setFreeHand3DRoiMinSize(m_RoiMinSize);
    connect(m_ImageWidget, SIGNAL(rightButtonReleased()), this, SLOT(onRightButtonReleased()));
    connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onFreezeChanged(QVariant)));
    m_ImageWidget->imageTile()->setFreeHand3DROIGeometry(QRect(m_RoiPos, m_RoiSize), m_ImageRect.topLeft());
}

FreeHand3DRoiControl::~FreeHand3DRoiControl()
{
}

void FreeHand3DRoiControl::refreshFreeHand3DRoi()
{
    m_RoiController->setFreeHand3DRoiVisible(m_ShowFreeHand3DRoiWidget);
    m_RoiController->setFreeHand3DRoiAtResizing(m_HD3DRoiActionType == ROIChange, m_UpdateFreeHand3DRoi);
    m_ImageWidget->imageTile()->setFreeHand3DROIGeometry(QRect(m_RoiPos, m_RoiSize), m_ImageRect.topLeft());
    changeMouseAction(m_UpdateFreeHand3DRoi);
}

void FreeHand3DRoiControl::onFreeHand3DRoiOnChanged(const QVariant& value)
{
    m_ShowFreeHand3DRoiWidget = value.toBool();
    m_UpdateFreeHand3DRoi = value.toBool();
    m_HD3DRoiActionType = ROIMove;
    m_ImageWidget->setShowFreeHand3DRoi(m_ShowFreeHand3DRoiWidget);
    m_RoiPos = m_ImageRect.topLeft() + Setting::instance().defaults().freeHand3DRoiPos();
    m_RoiSize = Setting::instance().defaults().freeHand3DRoiSize();
    refreshFreeHand3DRoi();
}

void FreeHand3DRoiControl::onBeforecurSonoParametersChanged()
{
    disconnect(m_SonoParameters->parameter(BFPNames::FreeHand3DRoiOnStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onFreeHand3DRoiOnChanged(QVariant)));
}

void FreeHand3DRoiControl::onCurSonoParametersChanged()
{
    m_SonoParameters = m_ImageWidget->imageTile()->sonoParameters();
    connect(m_SonoParameters->parameter(BFPNames::FreeHand3DRoiOnStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onFreeHand3DRoiOnChanged(QVariant)));
    onFreeHand3DRoiOnChanged(m_SonoParameters->pV(BFPNames::FreeHand3DRoiOnStr));
}

void FreeHand3DRoiControl::onFreezeChanged(const QVariant& value)
{
    if (!value.toBool())
    {
        m_SonoParameters->setPV(BFPNames::FreeHand3DRoiOnStr, false);
    }
}

void FreeHand3DRoiControl::onRightButtonReleased()
{
    m_UpdateFreeHand3DRoi = !m_UpdateFreeHand3DRoi;
    changeMouseAction(m_UpdateFreeHand3DRoi);
    if (m_ShowFreeHand3DRoiWidget)
    {
        m_RoiController->setFreeHand3DRoiAtResizing(m_HD3DRoiActionType == ROIChange, m_UpdateFreeHand3DRoi);
    }
}

void FreeHand3DRoiControl::leftButtonPressed(const QPoint& pos)
{
    Q_UNUSED(pos)
    if (m_UpdateFreeHand3DRoi)
    {
        if (m_HD3DRoiActionType == ROIMove)
        {
            m_HD3DRoiActionType = ROIChange;
        }
        else
        {
            m_HD3DRoiActionType = ROIMove;
        }
        m_RoiController->setFreeHand3DRoiAtResizing(m_HD3DRoiActionType == ROIChange, true);
    }
}

void FreeHand3DRoiControl::movedAction(const QPoint& offset, const QPoint& pos)
{
    Q_UNUSED(pos)
    if (m_ShowFreeHand3DRoiWidget && m_UpdateFreeHand3DRoi)
    {
        if (m_HD3DRoiActionType == ROIMove)
        {
            if (m_RoiPos.x() + offset.x() <= m_ImageRect.x())
            {
                m_RoiPos.setX(m_ImageRect.x());
            }
            else if (m_RoiPos.x() + m_RoiSize.width() + offset.x() > m_ImageRect.width() + m_ImageRect.x() - 1)
            {
                m_RoiPos.setX(m_ImageRect.width() + m_ImageRect.x() - m_RoiSize.width() - 1);
            }
            else
            {
                m_RoiPos.setX(m_RoiPos.x() + offset.x());
            }

            if (m_RoiPos.y() + offset.y() <= m_ImageRect.y())
            {
                m_RoiPos.setY(m_ImageRect.y());
            }
            else if (m_RoiPos.y() + m_RoiSize.height() + offset.y() > m_ImageRect.height() + m_ImageRect.y() - 1)
            {
                m_RoiPos.setY(m_ImageRect.height() + m_ImageRect.y() - m_RoiSize.height() - 1);
            }
            else
            {
                m_RoiPos.setY(m_RoiPos.y() + offset.y());
            }
        }
        else
        {
            if (offset.x() > 0)
            {
                if (m_RoiPos.x() - m_ImageRect.x() >= offset.x())
                {
                    m_RoiPos.setX(m_RoiPos.x() - offset.x());
                }
                if (m_RoiSize.width() + m_RoiPos.x() - m_ImageRect.x() + 2 * offset.x() <= m_ImageRect.width() - 1)
                {
                    m_RoiSize.setWidth(m_RoiSize.width() + 2 * offset.x());
                }
                else
                {
                    if ((m_RoiSize.width() + m_RoiPos.x() + 1) <= m_ImageRect.width() - 1)
                    {
                        m_RoiSize.setWidth(m_RoiSize.width() + 1);
                    }
                }
            }
            else
            {
                if (m_RoiSize.width() + 2 * offset.x() >= m_RoiMinSize.width())
                {
                    m_RoiPos.setX(m_RoiPos.x() - offset.x());
                    m_RoiSize.setWidth(m_RoiSize.width() + 2 * offset.x());
                }
            }

            if (offset.y() > 0)
            {
                if (m_RoiPos.y() - m_ImageRect.y() >= offset.y())
                {
                    m_RoiPos.setY(m_RoiPos.y() - offset.y());
                }
                if (m_RoiSize.height() + m_RoiPos.y() - m_ImageRect.y() + 2 * offset.y() <= m_ImageRect.height() - 1)
                {
                    m_RoiSize.setHeight(m_RoiSize.height() + 2 * offset.y());
                }
                else
                {
                    if ((m_RoiSize.height() + m_RoiPos.y() + 1) <= m_ImageRect.height() - 1)
                    {
                        m_RoiSize.setHeight(m_RoiSize.height() + 1);
                    }
                }
            }
            else
            {
                if (m_RoiSize.height() + 2 * offset.y() >= m_RoiMinSize.height())
                {
                    m_RoiPos.setY(m_RoiPos.y() - offset.y());
                    m_RoiSize.setHeight(m_RoiSize.height() + 2 * offset.y());
                }
            }
        }
        m_ImageWidget->imageTile()->setFreeHand3DROIGeometry(QRect(m_RoiPos, m_RoiSize), m_ImageRect.topLeft());
    }
}

void FreeHand3DRoiControl::changeMouseAction(bool changeRoi)
{
    ICommand* cinePlayMouseTool = ToolsFactory::instance().command(ToolNames::CinePlayMouseStr);
    ICommand* freeHand3DMouseTool = ToolsFactory::instance().command(ToolNames::FreeHand3DMouseStr);
    if (cinePlayMouseTool != NULL && freeHand3DMouseTool != NULL)
    {
        if (changeRoi)
        {
            ToolsFactory::instance().command(ToolNames::CinePlayMouseStr)->stop();
            ToolsFactory::instance().command(ToolNames::FreeHand3DMouseStr)->run();
        }
        else
        {
            ToolsFactory::instance().command(ToolNames::FreeHand3DMouseStr)->stop();
            ToolsFactory::instance().command(ToolNames::CinePlayMouseStr)->run();
        }
    }
}
