#ifndef DICOMSERVERWIDGET_H
#define DICOMSERVERWIDGET_H
#include "archive_global.h"

#include <QWidget>
#include "globaldef.h"
#include "basewidget.h"
class DicomModel;
class DeleteFilesController;

namespace Ui
{
class DicomServerWidget;
}

class ARCHIVESHARED_EXPORT DicomServerWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit DicomServerWidget(DicomChison::ServiceIndex index, QWidget* parent = 0);
    ~DicomServerWidget();
    void setModel(DicomModel* model);
    void modifyWarningInfo(const QString& value);
    void setDeleteFilesController(DeleteFilesController* controller);

protected:
    void retranslateUi();

private slots:
    void on_pushButtonExport_clicked();

    void on_pushButtonClose_clicked();

    void on_pushButtonDetail_clicked();

    void on_comboBox_currentIndexChanged(int index);

private:
    void updateServerDetailInfo();

signals:
    void closed();

private:
    Ui::DicomServerWidget* ui;
    DicomModel* m_Model;
    QString m_DefaultName;
    DicomChison::ServiceIndex m_ServiceType;
    QString m_WarningInfo;
    DeleteFilesController* m_DeleteFilesController;
};

#endif // DICOMSERVERWIDGET_H
