#include "fingerprinttask.h"
#include <QRunnable>
#ifdef USE_FINGERPRINT
#include "fingerprintdev.h"
#endif
#include "util.h"
#ifdef SYS_WINDOWS
#include "unistd_windows.h"
#else
#include <unistd.h>
#endif
#include <QEvent>
#include <QApplication>
#include <QDebug>

class FingerprintEnrollTask : public QRunnable
{
public:
    FingerprintEnrollTask(FingerprintTask* fptask)
        : m_fp(fptask)
    {
    }

protected:
    void run()
    {
        m_fp->enroll();
    }

private:
    FingerprintTask* m_fp;
};

class FingerprintVerifyTask : public QRunnable
{
public:
    FingerprintVerifyTask(FingerprintTask* fptask)
        : m_fp(fptask)
    {
    }

protected:
    void run()
    {
        m_fp->verify();
    }

private:
    FingerprintTask* m_fp;
};

class FingerprintCalibrationTask : public QRunnable
{
public:
    FingerprintCalibrationTask()
    {
    }

protected:
    void run()
    {
#ifdef USE_FINGERPRINT
        FingerprintDev::calibration();
        usleep(PRIME_DELAY_COMMON);
#endif
    }
};

FingerprintTask::FingerprintTask(QObject* parent)
    : QObject(parent)
    , m_id(-1)
    , m_isRunning(false)
{
}

FingerprintTask::~FingerprintTask()
{
    stopTask();
}

void FingerprintTask::startEnroll()
{
    FingerprintEnrollTask* task = new FingerprintEnrollTask(this);
    Util::runRunnable(task);
}

void FingerprintTask::startVerify()
{
    FingerprintVerifyTask* task = new FingerprintVerifyTask(this);
    Util::runRunnable(task);
}

void FingerprintTask::startCalibration()
{
    Util::runRunnable(new FingerprintCalibrationTask());
}

int FingerprintTask::getId() const
{
    return m_id;
}

void FingerprintTask::stopTask()
{
    if (m_isRunning)
    {
#ifdef USE_FINGERPRINT
        FingerprintDev::abort();
#endif
        m_isRunning = false;
    }
}

bool FingerprintTask::delFp(uint id)
{
#ifdef USE_FINGERPRINT
    return (PRIME_OK == FingerprintDev::deleteFp(id));
#else
    return false;
#endif
}

void FingerprintTask::enroll()
{
#ifdef USE_FINGERPRINT
    if (m_isRunning)
    {
        return;
    }

    m_isRunning = true;

    m_id = -1;

    //    usleep(PRIME_DELAY_COMMON);
    //    FingerprintDev::calibration();
    usleep(PRIME_DELAY_COMMON);
    QList<int> bf = FingerprintDev::getFpList();
    qDebug() << "FingerprintTask::enroll "
             << "finger list = " << bf;
    usleep(PRIME_DELAY_COMMON);
    uint ret = FingerprintDev::enroll();
    qDebug() << "FingerprintTask::enroll "
             << "enroll ret = " << ret;
    if (ret == PRIME_STATUS_FULL)
    {
        m_isRunning = false;
    }
    else if (ret == PRIME_ERROR_COMM)
    {
        FingerprintDev::calibration();
        m_isRunning = false;
    }

    int timer = 0;
    while (m_isRunning)
    {
        usleep(PRIME_DELAY_COMMON);
        ret = FingerprintDev::response();
        printf("\nFingerprintTask::enroll response ret = %d ...\n", ret);
        switch (ret)
        {
        case PRIME_STATUS_WAIT:
        case PRIME_STATUS_IDLE:
            break;
        case PRIME_STATUS_GOOD:
            emit pressFinger(timer++);
            break;
        case PRIME_STATUS_TIMEOUT:
        case PRIME_STATUS_FULL:
        case PRIME_OK:
        case PRIME_STATUS_ABORT:
            m_isRunning = false;
            break;
        default:
            break;
        }
        //防止指纹录入期间出现自动锁屏和自动待机问题
        QEvent event(QEvent::Wheel);
        QApplication::sendEvent(qApp, &event);
    }

    if (ret == PRIME_OK)
    {
        usleep(PRIME_DELAY_COMMON);
        QList<int> af = FingerprintDev::getFpList();
        qDebug() << "old fp list:" << bf << " / new bf list:" << af;
        foreach (int id, af)
        {
            if (!bf.contains(id))
            {
                m_id = id;
                break;
            }
        }
    }
    emit enrollState(ret);
#else
    emit enrollState(PRIME_OK);
#endif
}

void FingerprintTask::verify()
{
#ifdef USE_FINGERPRINT
    if (m_isRunning)
    {
        return;
    }

    m_isRunning = true;

    m_id = -1;

    //    usleep(PRIME_DELAY_COMMON);
    //    FingerprintDev::calibration();
    usleep(PRIME_DELAY_COMMON);
    uint ret = FingerprintDev::verify((uint*)&m_id);

    while (m_isRunning)
    {
        switch (ret)
        {
        case PRIME_STATUS_IDLE:
        case PRIME_STATUS_WAIT:
            break;
        case PRIME_STATUS_MATCH:
            m_isRunning = false;
            break;
        case PRIME_STATUS_NOT_MATCH:
        case PRIME_STATUS_ABORT:
        case PRIME_STATUS_NO_FINGER:
        case PRIME_STATUS_VERIFY_FAILED:
        case PRIME_STATUS_TIMEOUT:
            m_isRunning = false;
            m_id = -1;
            break;
        default:
            break;
        }
        if (m_isRunning)
        {
            usleep(PRIME_DELAY_COMMON);
            ret = FingerprintDev::verifyState((uint*)&m_id);
        }
    }

    emit verifyState(ret);
#endif
}
