#include "systemhintdefaultwidget.h"
#include "ui_systemhintdefaultwidget.h"
#include "systemhintmodel.h"
#include "util.h"

SystemHintDefaultWidget::SystemHintDefaultWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::SystemHintDefaultWidget)
    , m_Model(NULL)
{
    ui->setupUi(this);
}

SystemHintDefaultWidget::~SystemHintDefaultWidget()
{
    delete ui;
}

void SystemHintDefaultWidget::setModel(SystemHintModel* value)
{
    m_Model = value;
    connect(m_Model, SIGNAL(workStatusChanged()), this, SLOT(onWorkStatusChanged()));
    connect(m_Model, SIGNAL(tBStatusChanged()), this, SLOT(onTBStatusChanged()));

    retranslateUi();
}

void SystemHintDefaultWidget::onWorkStatusChanged()
{
    if (m_Model != NULL)
    {
        ui->workStatusLabel->setText(Util::translate(m_Model, m_Model->workStatus()));
    }
}

void SystemHintDefaultWidget::onTBStatusChanged()
{
    if (m_Model != NULL)
    {
        if (m_Model->tBStatus().isEmpty())
        {
            ui->touchBallStatusLabel->setText(QString());
        }
        else
        {
            ui->touchBallStatusLabel->setText(tr("TB:") + /*Util::translate(m_Model, */ m_Model->tBStatus() /*)*/);
        }
    }
}
void SystemHintDefaultWidget::retranslateUi()
{
    if (m_Model != NULL)
    {
        onWorkStatusChanged();
        onTBStatusChanged();
    }
}
