<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FourDPresetWidget</class>
 <widget class="QWidget" name="FourDPresetWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>279</width>
    <height>349</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>6</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>Copy</string>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="label">
        <property name="text">
         <string>From</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="BaseComboBox" name="comboBoxFrom"/>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_2">
        <property name="text">
         <string>To</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="BaseComboBox" name="comboBoxTo"/>
      </item>
      <item row="2" column="1">
       <widget class="QPushButton" name="copyButton">
        <property name="text">
         <string>Copy</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_2">
     <property name="title">
      <string>preset para</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="0" column="0">
       <widget class="QLabel" name="label_3">
        <property name="text">
         <string>Threshold</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QSpinBox" name="spinBoxThreshold"/>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_5">
        <property name="text">
         <string>SmoothInx</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QSpinBox" name="spinBoxSmooth"/>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_6">
        <property name="text">
         <string>PaletteInx</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QSpinBox" name="spinBoxPalette"/>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_4">
        <property name="text">
         <string>Dynamic</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QSpinBox" name="spinBoxDynamic"/>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="label_7">
        <property name="text">
         <string>Gain</string>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="QSpinBox" name="spinBoxGain"/>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>41</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BaseComboBox</class>
   <extends>QComboBox</extends>
   <header>basecombobox.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
