#ifndef FILIATIONTREEVIEW_H
#define FILIATIONTREEVIEW_H
#include "archive_global.h"
#include <QObject>
#include <QTreeView>
#include <QFileInfo>

class QModelIndex;
class FileSystemModel;
class FiliationTreeViewModel;

/**
 * @brief 导出文件时的界面，可以对磁盘文件进行操作
 *   包括新建、删除文件夹
 */
class ARCHIVESHARED_EXPORT FiliationTreeView : public QTreeView
{
    Q_OBJECT
public:
    FiliationTreeView(QWidget* patient = 0);
    ~FiliationTreeView();
    /**
     * @brief back 回到上一层目录，直到根目录
     */
    void back();
    /**
     * @brief makeDir 新建一个文件夹
     */
    void makeDir();
    /**
     * @brief deleteFileList 删除文件
     *
     * @return
     */
    QFileInfoList deleteFileList();
    /**
     * @brief currentFileInfo 当前所在的目录
     *
     * @return
     */
    QFileInfo currentFileInfo();
public slots:
    void clickTreeView(const QModelIndex& index);
    void setInitFileInfo(const QModelIndex& index);

protected:
    FileSystemModel* getModel();
    QFileInfo getInfo(const QModelIndex& index);

private:
    QFileInfoList m_FileInfoList;
    FiliationTreeViewModel* m_Model;
};

#endif // FILIATIONTREEVIEW_H
