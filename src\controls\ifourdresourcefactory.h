#ifndef IFOURDRESOURCEFACTORY_H
#define IFOURDRESOURCEFACTORY_H

#include "controls_global.h"

class IFourDLiveWidget;
class IFourDAPI;
class IFourDLicenseProcessor;

class CONTROLSSHARED_EXPORT IFourDResourceFactory
{
public:
    virtual ~IFourDResourceFactory()
    {
    }
    virtual IFourDLiveWidget* createFourDLiveWidget() = 0;
    virtual IFourDAPI* createFourDAPI() = 0;
    virtual IFourDLicenseProcessor* createFourDLicenseProcessor() = 0;
};

#endif // IFOURDRESOURCEFACTORY_H
