/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#ifndef BURNINTEST_H
#define BURNINTEST_H
#include "autotest_global.h"

#include <QObject>
#include <QStringList>
#include <QTimer>
#include "autotesttaskgenerator.h"
#include <QDateTime>
#include "autotest.h"
#include "testmachine.h"

class QSettings;
class IStateManager;
class BaseProbeSelectionChild;

class AUTOTESTSHARED_EXPORT BurnInTest : public QObject, public TestMachine
{
    Q_OBJECT
public:
    BurnInTest(QObject* parent = 0);
    bool startTest();
    void stopTest();
    void setProbeFrame(BaseProbeSelectionChild* value);
    void setStateManager(IStateManager* value);
    const QStringList& modeNames() const;
    void updateIniFiles();
    virtual void handleNormalEvent(const QString& event);

private:
    QTimer& startTimer();
    void selectProbeExam();
    void handleSpecialEvent(const QString& event);
    void handleFinishEvent();
    void resetFlags();
    void setHasStarted(bool value);
    bool hasStarted() const;
private slots:
    void onStartTimerOut();
signals:
    void sendCurrentFiles(const QString& inf);
    void emitResetFlags();
private slots:
    void handleProbeEvent();

private:
    static const QStringList m_normalEvent;
    IStateManager* m_StateManager;
    BaseProbeSelectionChild* m_probeFrame;
    int m_currentProbeIndex;
    int m_probeNum;
    int m_saveImageCount;
    QTimer m_startTimer;
    bool m_hasStarted;
    QStringList m_imageModeNames;
};

#endif // BURNINTEST_H
