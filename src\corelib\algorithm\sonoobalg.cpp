#include "sonoobalg.h"

SonoOBAlg::SonoOBAlg()
    : VirtualAlg()
{
    m_Lib.setFileName("ob1.0_CPU");
}

int SonoOBAlg::bpdInit(const char* model_path_detect, const char* model_path_neijie, const char* model_path_waijie,
                       float conf_thres, float thres)
{
    BpdInit func = (BpdInit)m_Lib.resolve("bpd_init");
    if (func == NULL)
    {
        m_Initialized = false;
        m_IsError = true;
        return false;
    }
    int res = func(model_path_detect, model_path_neijie, model_path_waijie, conf_thres, thres);
    m_Initialized = true;
    return res;
}

int SonoOBAlg::bpdMeasure(uint8_t* imgdata, int imgw, int imgh, int OP, BPDS* bpdsp)
{
    BpdMeasure func = (BpdMeasure)m_Lib.resolve("bpd_measure");
    if (func == NULL || !m_Initialized)
    {
        m_IsError = true;
        return 0;
    }
    return func(imgdata, imgw, imgh, OP, bpdsp);
}

void SonoOBAlg::bpdRelease()
{
    BpdRelease func = (BpdRelease)m_Lib.resolve("bpd_release");
    if (func == NULL)
    {
        m_IsError = true;
        return;
    }

    m_Initialized = false;
    func();
}

int SonoOBAlg::stomachInit(const char* model_path_detect, const char* model_path, float conf_thres, float thres)
{
    StomachInit func = (StomachInit)m_Lib.resolve("stomach_init");
    if (func == NULL)
    {
        m_Initialized = false;
        m_IsError = true;
        return -1;
    }
    int res = func(model_path_detect, model_path, conf_thres, thres);
    m_Initialized = true;
    return res;
}

int SonoOBAlg::stomachMeasure(uint8_t* imgdata, int imgw, int imgh, ACS* acp)
{
    StomachMeasure func = (StomachMeasure)m_Lib.resolve("stomach_measure");
    if (func == NULL || m_Initialized)
    {
        m_IsError = true;
        return 0;
    }
    return func(imgdata, imgw, imgh, acp);
}

void SonoOBAlg::stomachRelease()
{
    StomachRelease func = (StomachRelease)m_Lib.resolve("stomach_release");
    if (func == NULL)
    {
        m_IsError = true;
        return;
    }
    m_Initialized = false;
    func();
}

int SonoOBAlg::fhlInit(const char* seg_model_path, const char* detect_model_path, float seg_thres, float conf_thres)
{
    FhlInit func = (FhlInit)m_Lib.resolve("fhl_init");
    if (func == NULL)
    {
        m_Initialized = false;
        m_IsError = true;
        return -1;
    }
    int res = func(seg_model_path, detect_model_path, seg_thres, conf_thres);
    m_Initialized = true;
    return res;
}

int SonoOBAlg::fhlMeasure(uint8_t* imgdata, int imgw, int imgh, FLS* flp)
{
    FhlMeasure func = (FhlMeasure)m_Lib.resolve("fhl_measure");
    if (func == NULL || !m_Initialized)
    {
        m_IsError = true;
        return 0;
    }
    return func(imgdata, imgw, imgh, flp);
}

void SonoOBAlg::fFhlRelease()
{
    FhlRelease func = (FhlRelease)m_Lib.resolve("fhl_release");
    if (func == NULL)
    {
        m_IsError = true;
        return;
    }
    m_Initialized = false;
    func();
}

int SonoOBAlg::initStandardBpdac(const char* det_model_path1, const char* det_model_path2, float conf_thres)
{
    InitStandardBpdac func = (InitStandardBpdac)m_Lib.resolve("init_standard_bpdac");
    if (func == NULL)
    {
        m_Initialized = false;
        m_IsError = true;
        return -1;
    }
    int res = func(det_model_path1, det_model_path2, conf_thres);
    m_Initialized = true;
    return res;
}

int SonoOBAlg::standardBpdacDetectobject(uint8_t* imgdata, int width, int height, OBJ* objsp)
{
    StandardBpdacDetectobject func = (StandardBpdacDetectobject)m_Lib.resolve("init_standard_bpdac");
    if (func == NULL || !m_Initialized)
    {
        m_IsError = true;
        return -1;
    }
    return func(imgdata, width, height, objsp);
}

void SonoOBAlg::standardBpdacRelease()
{
    StandardBpdacRelease func = (StandardBpdacRelease)m_Lib.resolve("standard_bpdac_release");
    if (func == NULL)
    {
        m_IsError = true;
        return;
    }
    m_Initialized = false;
    func();
}

int SonoOBAlg::initStandardFhl(const char* det_model_path, float conf_thres)
{
    InitStandardFhl func = (InitStandardFhl)m_Lib.resolve("init_standard_fhl");
    if (func == NULL)
    {
        m_Initialized = false;
        m_IsError = true;
        return -1;
    }
    m_Initialized = true;
    return func(det_model_path, conf_thres);
}

int SonoOBAlg::standardFhlDetectobject(uint8_t* imgdata, int width, int height, OBJ* objsp)
{
    StandardFhlDetectobject func = (StandardFhlDetectobject)m_Lib.resolve("standard_fhl_detectobject");
    if (func == NULL || !m_Initialized)
    {
        m_IsError = true;
        return -1;
    }
    return func(imgdata, width, height, objsp);
}

void SonoOBAlg::standardFhlRelease()
{
    StandardFhlRelease func = (StandardFhlRelease)m_Lib.resolve("standard_fhl_release");
    if (func == NULL)
    {
        m_IsError = true;
        return;
    }
    m_Initialized = false;
    func();
}
