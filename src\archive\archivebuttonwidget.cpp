#include "archivebuttonwidget.h"
#include "ui_archivebuttonwidget.h"
#include "resource.h"
#include "modeluiconfig.h"
#include "appsetting.h"
#include "util.h"
#include "uiutil.h"
#include "buttonsboxframe.h"
#include <QScreen>
ArchiveButtonWidget::ArchiveButtonWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::ArchiveButtonWidget)
    , m_Multi(false)
    , m_Disc(QString())
    , m_ActiveExamClicked(false)
    , m_AnimalString(QString())
    , m_Model(ScreenOrientationModel::getInstance())
{
    ui->setupUi(this);
    setAllbuttonsEnabled(false);

    if (!AppSetting::isPhoenixSeries())
    {
        ui->pushButtonDelete->setIcon(QIcon(ModelUiConfig::instance().value(ModelUiConfig::LjtIcon).toString()));
    }

    m_AnimalString = QT_TRANSLATE_NOOP("ArchiveButtonWidget", "Animal Info");
    if (AppSetting::isAnimalAndChinese())
    {
        Util::setPatient2Animal(ui->pushButtonInfo, "ArchiveButtonWidget", m_AnimalString);
    }
}

ArchiveButtonWidget::~ArchiveButtonWidget()
{
    delete ui;
}

void ArchiveButtonWidget::setButtonEnabled(bool enabled, ArchiveButtonWidget::BUTTON index)
{
    switch (index)
    {
    case PatientInfo:
        ui->pushButtonInfo->setEnabled(enabled);
        break;
    case Report:
        ui->pushButtonReport->setEnabled(enabled);
        break;
    case Delete:
        ui->pushButtonDelete->setEnabled(enabled);
        break;
    case Backup:
        ui->pushButtonBackup->setEnabled(enabled);
        break;
    case Send:
        ui->pushButtonSend->setEnabled(enabled);
        break;
    case Restore:
        ui->pushButtonRestore->setEnabled(enabled);
        break;
    }
}

bool ArchiveButtonWidget::isButtonEnabled(ArchiveButtonWidget::BUTTON index) const
{
    switch (index)
    {
    case ArchiveButtonWidget::PatientInfo:
        return ui->pushButtonInfo->isEnabled();
    case ArchiveButtonWidget::Report:
        return ui->pushButtonReport->isEnabled();
    case ArchiveButtonWidget::Delete:
        return ui->pushButtonDelete->isEnabled();
    case ArchiveButtonWidget::Backup:
        return ui->pushButtonBackup->isEnabled();
    case ArchiveButtonWidget::Send:
        return ui->pushButtonSend->isEnabled();
    case ArchiveButtonWidget::Restore:
        return ui->pushButtonRestore->isEnabled();
    default:
        return false;
    }
}

void ArchiveButtonWidget::setDiskPath(const QString& value)
{
    m_Disc = value;
}

void ArchiveButtonWidget::setAllbuttonsEnabled(bool enabled)
{
    setButtonEnabled(enabled, PatientInfo);
    setButtonEnabled(enabled, Report);
    setButtonEnabled(enabled, Delete);
    setButtonEnabled(enabled, Backup);
    setButtonEnabled(enabled, Send);
    setButtonEnabled(enabled, Restore);
}

void ArchiveButtonWidget::retranslateUi()
{
    ui->retranslateUi(this);
    if (AppSetting::isAnimalAndChinese())
    {
        Util::setPatient2Animal(ui->pushButtonInfo, "ArchiveButtonWidget", m_AnimalString);
    }
}

void ArchiveButtonWidget::orientationChanged(Qt::ScreenOrientation orientation)
{
    QString className(this->metaObject()->className());
    OrientationConfigItem config;
    m_Model->getInfosByClassName(orientation, className, config);
    UiUtil::ScreenHVChangedUpdateUI(this, ui->gridLayout, config);
}

void ArchiveButtonWidget::on_pushButtonInfo_clicked()
{
    emit infoButtonClicked();
}

void ArchiveButtonWidget::on_pushButtonReport_clicked()
{
    emit reportButtonClicked();
}

void ArchiveButtonWidget::on_pushButtonDelete_clicked()
{
    emit deleteButtonClicked();
}

void ArchiveButtonWidget::on_pushButtonBackup_clicked()
{
    emit backupButtonClicked();
}

void ArchiveButtonWidget::on_pushButtonSend_clicked()
{
    emit sendButtonClicked();
}

void ArchiveButtonWidget::on_pushButtonRestore_clicked()
{
    emit restoreButtonClicked();
}

void ArchiveButtonWidget::onClearSectionToMakeActiveFalse()
{
    m_ActiveExamClicked = false;
}

void ArchiveButtonWidget::onSelectedModelChanged(const QStringList& modelList, bool isDir)
{
    if (modelList.isEmpty())
    {
        setAllbuttonsEnabled(false);
    }
    else
    {
        if (m_Multi)
        {
            setButtonEnabled(false, PatientInfo);
            setButtonEnabled(false, Report);
            setButtonEnabled(true, Send);
        }
        else
        {
            if (modelList.count() > 1 || isDir == true)
            {
                setButtonEnabled(false, PatientInfo);
                setButtonEnabled(false, Report);
                setButtonEnabled(true, Send);
            }
            else
            {
                setButtonEnabled(true, PatientInfo);
                setButtonEnabled(true, Report);
                setButtonEnabled(true, Send);
            }
        }

        if (m_Disc != Resource::hardDiskDir)
        {
            setButtonEnabled(true, Restore);
        }
        else
        {
            setButtonEnabled(false, Restore);
        }

        setButtonEnabled(m_ActiveExamClicked, Delete);
        setButtonEnabled(m_ActiveExamClicked, Backup);
    }
}

void ArchiveButtonWidget::onMultiClicked(bool multi)
{
    m_Multi = multi;
}

void ArchiveButtonWidget::onActiveExamClicked(bool flag)
{

    m_ActiveExamClicked = flag;
}
