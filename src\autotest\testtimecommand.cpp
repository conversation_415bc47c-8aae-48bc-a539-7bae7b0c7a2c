#include "testtimecommand.h"

void TestTimeCommand::setTime(int value)
{
    *m_time = value;
}

int TestTimeCommand::time() const
{
    return *m_time;
}

TestTimeCommand::TestTimeCommand(int* time, QTimer* timer)
    : m_time(time)
    , m_timer(timer)
    , m_staticTime(0)
{
}

TestTimeCommand::TestTimeCommand(int time, QTimer* timer)
    : m_time(NULL)
    , m_staticTime(time)
    , m_timer(timer)
{
}

TestTimeCommand::~TestTimeCommand()
{
}

void TestTimeCommand::execute()
{
    if (m_time)
    {
        m_timer->start(*m_time * 1000);
    }
    else
    {
        m_timer->start(m_staticTime * 1000);
    }
}
