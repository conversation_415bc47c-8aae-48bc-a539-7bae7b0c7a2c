#ifndef SYNCFINGERPRINTOPERATOR_H
#define SY<PERSON><PERSON>NGERPRINTOPERATOR_H

#include "fingerprintoperatorbase.h"
#include <QScopedPointer>

class AdminConfigModel;
class FingerprintTask;
class ADMINMODELSHARED_EXPORT SyncFingerPrintOperator : public FingerPrintOperatorBase
{
    Q_OBJECT
public:
    SyncFingerPrintOperator(AdminConfigModel* model, QObject* parent = nullptr);
    ~SyncFingerPrintOperator();

    virtual FPrintType fprintType() const;
    virtual int maxCount() override;
    virtual bool enroll(const QString& userId) override;
    virtual bool verify() override;
    virtual void stop() override;
    virtual bool remove(const FingerprintInfo& fpinfo) override;
    virtual const QList<FingerprintInfo> getFingerprintInfo(const QString& userId) const override;
    virtual bool addFingerprint(const QString& fingerName, int fingerId, int status, const QString& userId) override;
    virtual bool hasFingerprint() override;

private slots:
    void onFpVerify(uint state);
    void onEnrollStatus(uint stats);
    void onPressFinger(int timer);

private:
    AdminConfigModel* m_AdminModel;
    QScopedPointer<FingerprintTask> m_fpTask;
};

#endif // SYNCFINGERPRINTOPERATOR_H
