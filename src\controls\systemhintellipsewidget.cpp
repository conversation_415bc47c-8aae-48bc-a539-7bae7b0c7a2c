#include "systemhintellipsewidget.h"
#include "ui_systemhintellipsewidget.h"
#include "systemhintmodel.h"
#include "util.h"
#include <QDebug>

SystemHintEllipseWidget::SystemHintEllipseWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::SystemHintEllipseWidget)
    , m_Generated(false)
{
    ui->setupUi(this);
}

SystemHintEllipseWidget::~SystemHintEllipseWidget()
{
    delete ui;
}

void SystemHintEllipseWidget::setModel(SystemHintModel* value)
{
    m_Model = value;
    connect(m_Model, SIGNAL(workStatusChanged()), this, SLOT(onWorkStatusChanged()));
    connect(m_Model, SIGNAL(tBStatusChanged()), this, SLOT(onTBStatusChanged()));

    retranslateUi();
}

void SystemHintEllipseWidget::onWorkStatusChanged()
{
    ui->labelUp->setText(Util::translate(m_Model, m_Model->workStatus()));
}

void SystemHintEllipseWidget::onTBStatusChanged()
{
    if (m_Model->tBStatus().isEmpty())
    {
        ui->labelDown->setText(QString());
    }
    else
    {
        ui->labelDown->setText(tr("TB:") + /*Util::translate(m_Model, */ m_Model->tBStatus() /*)*/);
    }
}

void SystemHintEllipseWidget::retranslateUi()
{
    onWorkStatusChanged();
    onTBStatusChanged();
}

void SystemHintEllipseWidget::paintEvent(QPaintEvent* ev)
{
    if (!m_Generated)
    {
        setPaintParameters();
        m_Generated = true;
    }
    QWidget::paintEvent(ev);
}

void SystemHintEllipseWidget::setPaintParameters()
{
    m_RegionGenerator.setBaseRegion(this->rect());

    QPoint topLeft = ui->labelUp->mapTo(this, ui->labelUp->rect().topLeft());
    QPoint bottomRight = ui->labelUp->mapTo(this, ui->labelUp->rect().bottomRight());
    qDebug() << "topleft" << topLeft << "bottomRight" << bottomRight;
    m_RegionGenerator.generate(QRect(topLeft, bottomRight));
    ui->labelUp->setPaintPath(m_RegionGenerator.intersectRegion());
    ui->labelUp->setPaintTopLine(m_RegionGenerator.topLine());
    ui->labelUp->setPaintBottomLine(m_RegionGenerator.bottomLine());

    topLeft = ui->labelDown->mapTo(this, ui->labelDown->rect().topLeft());
    bottomRight = ui->labelDown->mapTo(this, ui->labelDown->rect().bottomRight());
    qDebug() << "topleft" << topLeft << "bottomRight" << bottomRight;
    m_RegionGenerator.generate(QRect(topLeft, bottomRight));
    ui->labelDown->setPaintPath(m_RegionGenerator.intersectRegion());
    ui->labelDown->setPaintTopLine(m_RegionGenerator.topLine());
    ui->labelDown->setPaintBottomLine(m_RegionGenerator.bottomLine());
}
