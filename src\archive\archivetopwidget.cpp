#include "archivetopwidget.h"
#include "ui_archivetopwidget.h"
#include "resource.h"
#include "modeldirectorygetter.h"
#include "comboboxreverter.h"
#include "appsetting.h"
#include "uiutil.h"
#include <QScreen>
#include <QDebug>

ArchiveTopWidget::ArchiveTopWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::ArchiveTopWidget)
    , m_Model(ScreenOrientationModel::getInstance())
{
    ui->setupUi(this);
    initArchiveTopWidget();
    connect(ui->checkBox, SIGNAL(clicked(bool)), this, SIGNAL(MultiClicked(bool)));
    connect(ui->comboBoxItem, SIGNAL(currentIndexChanged(int)), this, SIGNAL(comboBoxItemCurrentIndexChanged(int)));
    connect(ui->comboBoxTime, SIGNAL(currentIndexChanged(int)), this, SIGNAL(comboBoxTimeCurrentIndexChanged(int)));
    connect(ui->lineEditkey, SIGNAL(textEdited(QString)), this, SIGNAL(lineEditKeystringChanged(QString)));

    if (AppSetting::isAnimalAndChinese())
    {
        setID2AnimalID();
    }
}

ArchiveTopWidget::~ArchiveTopWidget()
{
    delete ui;
}

void ArchiveTopWidget::initArchiveTopWidget()
{
    ui->comboBoxItem->setCurrentIndex(0);
    ui->comboBoxTime->setCurrentIndex(6);
    // TODO：关闭前disk设置成hardisk，防止还打开db文件
    setDiskIndex(0);
    clearKeyword();
}

void ArchiveTopWidget::addDiskItem(const QString& disk)
{
    ui->comboBoxDisk->addItem(disk);
}

void ArchiveTopWidget::updateDiskPathComboBox()
{
    ui->comboBoxDisk->updateDiskInfo();
}

void ArchiveTopWidget::setDiskIndex(int index)
{
    if (index >= 0 && index < ui->comboBoxDisk->count())
    {
        ui->comboBoxDisk->setCurrentIndex(index);
    }
}

int ArchiveTopWidget::squeryCurrentIndex() const
{
    return ui->comboBoxItem->currentIndex();
}

int ArchiveTopWidget::timeperiodCurrentIndex() const
{
    return ui->comboBoxTime->currentIndex();
}

QString ArchiveTopWidget::keyString() const
{
    return ui->lineEditkey->text();
}

QString ArchiveTopWidget::currentDisk() const
{
    return ui->comboBoxDisk->currentDiskPath();
}

bool ArchiveTopWidget::isDiskLocal() const
{
    return (ui->comboBoxDisk->currentIndex() == 0);
}

void ArchiveTopWidget::setValidatorName(QValidator* validatorName)
{
    updateKeyValidator(validatorName);
}

void ArchiveTopWidget::setDiskDevice(IDiskDevice* diskDevice)
{
    ui->comboBoxDisk->setDiskDevice(diskDevice);
    //以下两句代码的执行，需要m_DiskDevice对象初始化完成
    ui->comboBoxDisk->setDiskType(DiskPathComboBox::ALL);
    ui->comboBoxDisk->setContainsFilter(PatientPath::instance().dbFilePathName(QString()));
}

void ArchiveTopWidget::retranslateUi()
{
    ComboBoxReverter keyCr(ui->comboBoxItem);
    ComboBoxReverter periodCr(ui->comboBoxTime);
    ui->retranslateUi(this);
    if (AppSetting::isAnimalAndChinese())
    {
        setID2AnimalID();
    }
}

void ArchiveTopWidget::orientationChanged(Qt::ScreenOrientation orientation)
{
    QString className(this->metaObject()->className());
    OrientationConfigItem config;
    m_Model->getInfosByClassName(orientation, className, config);
    UiUtil::ScreenHVChangedUpdateUI(this, ui->gridLayout, config);
}

void ArchiveTopWidget::on_comboBoxDisk_currentIndexChanged(int index)
{
    emit discChanged(ui->comboBoxDisk->currentDiskPath());
}

void ArchiveTopWidget::clearKeyword()
{
    if (!ui->lineEditkey->text().isEmpty())
    {
        ui->lineEditkey->clear();
        emit lineEditKeystringChanged(QString());
    }
}

void ArchiveTopWidget::updateKeyValidator(QValidator* validator)
{
    ui->lineEditkey->setValidator(validator);
    clearKeyword();
}

void ArchiveTopWidget::setID2AnimalID()
{
    for (int i = 0; i < ui->comboBoxItem->count(); i++)
    {
        if ((ui->comboBoxItem->itemText(i) == tr("ID")))
        {
            ui->comboBoxItem->setItemText(i, tr("Animal ID"));
            break;
        }
    }
}
