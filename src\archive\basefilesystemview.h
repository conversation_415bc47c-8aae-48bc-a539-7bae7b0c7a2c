#ifndef BASEFILESYSTEMVIEW_H
#define BASEFILESYSTEMVIEW_H
#include "archive_global.h"

//#include <QWidget>
#include <QTreeView>
#include "basewidget.h"

namespace Ui
{
class BaseFileSystemView;
}

/**
 * @brief no use
 */
class ARCHIVESHARED_EXPORT BaseFileSystemView : public BaseWidget
{
    Q_OBJECT
public:
    explicit BaseFileSystemView(QWidget* parent = 0);
    ~BaseFileSystemView();
    void setModel(QAbstractItemModel* model);
    void setSelectionMode(QAbstractItemView::SelectionMode mode);
    QAbstractItemView::SelectionMode selectionMode() const;
    bool isSortingEnabled() const;
    void setSortingEnabled(bool enable);
    void selectAll();

protected:
    void retranslateUi();
private slots:
    void on_treeView_clicked(const QModelIndex& index);

private:
    Ui::BaseFileSystemView* ui;
};

#endif // BASEFILESYSTEMVIEW_H
