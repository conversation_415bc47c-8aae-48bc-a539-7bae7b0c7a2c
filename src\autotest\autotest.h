/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#ifndef AUTOTEST_H
#define AUTOTEST_H
#include "autotest_global.h"

/** 使用AutoTest自动测试类规则(可以参考BurnInTest),首先继承ITestMachine接口，由这个接口我们可以看到
 *  主要就是处理两种事件，一个是包括在AutoTest类内的普通事件NormalEvent(默认已经由其子类做默认处理StateManager::instance().postEvent(event))
 *  另外一个是不包括在AutoTest中的特殊事件SpecialEvent,这种事件需要手动继承TestMachine之后进行覆写处理
 *  Update:
 *  开放setSpecialEvent接口，即让AutoTest知道需要特殊处理的事件，如果是普通事件则直接PostEvent
 *  Update:
 *  目前自动测试脚本已经支持自测试，也就是说，无论是特殊事件还是普通事件，只要是StateEventNames中有的，可以直接使用的，都可以放在测试脚本
 *  上来进行自动测试。
 *  自动测试脚本介绍：
 * 【General】 TestTotalTime 表示测试的总时间，如果超过这个时间会自动停止
 * 【CircleScripts】表示循环事件，简单的说就是每隔EventCircleTime秒之后会自动触发一次EventName事件
 * 【EventScripts】 不同于循环事件，需要你事先编排好执行顺序，依次间隔TimeInterval之后进行触发EventName事件
 **/

#include <QObject>
#include <QHash>
#include <QStringList>
#include <QSettings>
#include "autotesttaskgenerator.h"
#include <QDateTime>
#include <QTimer>
#include <QFileInfoList>

struct TinyEvent
{
    QStringList eventList;
    int timeInterval;
};

class ITestMachine;

class AUTOTESTSHARED_EXPORT AutoTest : public QObject
{
    Q_OBJECT
public:
    AutoTest(QObject* parent = 0);
    QTimer& eventTimer();
    void setTestMachine(ITestMachine* machine);
    void tryToNextTask();
    void disconnectProcessTask();
    void connectProcessTask();
    void startTest(int msec = 0);
    void stopTest();
    void setIsHandleSpecialEventThatNeedsWait(bool isHandleSpecialEventThatNeedWait);
    const QHash<int, TinyEvent>& eventScripts() const;
    int currentTestTime() const;
    const QHash<QString, int>& circleEvents() const;
    void setSpecialEventList(const QStringList& eventList);
    bool isIniValid() const;
    void updateIniFiles();

private:
    // 以后可以自定义扩展比如接受别的路径的test文档
    void setTestScriptDirName(const QString& dirName);
    void setScriptName(const QString& value);
    // 这边可以扩展成进行combox的下拉或者勾选，进行选择测试脚本
    void getIniFiles(const QString& dirName);
    void nextTask();
    void organizeCircleScripts(QSettings& settings);
    void handleTask();
    void finishHandler();
    bool containsSpecialEvent(const QString& event);
    void checkMemberValue();
    void readIni();
    int currentTaskIndex() const;
    void setCurrentTaskIndex(int value);
    int testTotalTime() const;
    void setTestTotalTime(int testTotalTime);
    void insertEventScripts(int key, TinyEvent value);
    const AutoTestTaskGenerator& taskGenerator() const;
    void checktaskLists();
    void checkIniLegal();
    void organizeEventTables();
    void organizeEventScripts(QSettings& settings);
    const QDateTime& startTime() const;
    void setStartTime(const QDateTime& value);
    bool checkIfBeyondTestTime();
    const QStringList& currentHandeEventList() const;
    void setCurrentHandeEventList(const QStringList& currentHandeEventList);
    bool isInHandleEventList() const;
    void setIsInHandleEventList(bool isInHandleEventList);
    void makeCurrentHandleEventList(const QStringList& eventList);
    void handleEvent(const QString& event);
    bool isHandleSpecialEventThatNeedsWait() const;
    void resetFlags();
    void handleEventList(const QStringList& eventList);
    void checkLegalAndMoveToRes(const QFileInfoList& infoList);
    void prepareFirstIni();
    void finishCurrentTest();
    void gotoNextTest();
    void clearItems();
private slots:
    void processTask();

private:
    int m_currentTaskIndex;
    int m_testTotalTime;
    bool m_isInHandleEventList;
    bool m_isHandleSpecialEventThatNeedsWait;
    QDateTime m_startTime;
    QDateTime m_originStartTime;
    QHash<int, TinyEvent> m_eventScripts;
    QHash<QString, int> m_circleEvents;
    AutoTestTaskGenerator m_taskGenerator;
    QStringList m_currentHandeEventList;
    QString m_currentScriptName;
    QTimer m_eventTimer;
    ITestMachine* m_machine;
    int m_currentTestTime;
    QStringList m_specialEvent;
    bool m_isIniValid;
    QFileInfoList m_iniFiles;
    int m_iniIndex;
    bool m_isStarted;
};

#endif // AUTOTEST_H
