build-xbit-job:
  stage: build
  tags:
    - XBit
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  script:
    - ./configure_windows_x86.bat -a x64 -m XBit
  cache:
    key: build-XBit-cache
    paths:
      - build/XBit
    policy: push

release-xbit-job:
  stage: release
  tags:
    - XBit
  rules:
    - if: $model == "XBit"
  before_script:
    - bash.exe ./ci/shell/getknown_hosts.sh
    - bash.exe ./ci/shell/pullrelatedrep.sh -m XBit -r $ResourceBranch -x windows -a $AppcommonBranch
  script:
    - bash.exe ./ci/shell/runrelease.sh -m XBit -v $MainVersion -a $VersionAdd -x windows
    - ./ci/shell/exit_ConsoleWindowHost.bat


