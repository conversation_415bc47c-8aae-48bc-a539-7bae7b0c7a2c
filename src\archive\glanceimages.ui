<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>GlanceImages</class>
 <widget class="QWidget" name="GlanceImages">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>917</width>
    <height>569</height>
   </rect>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Easy View</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QFrame" name="frame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout_3">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="0" column="0">
       <widget class="GlanceImagesTopWidget" name="widgetTop" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="BorderedQFrame" name="frame_glanceImage">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <property name="spacing">
          <number>0</number>
         </property>
         <item row="0" column="0">
          <widget class="ImageSkimManager" name="widgetImages" native="true">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>339</width>
             <height>0</height>
            </size>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QWidget" name="widget_4" native="true">
        <property name="minimumSize">
         <size>
          <width>180</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <layout class="QGridLayout" name="gridLayout_4">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <property name="spacing">
          <number>0</number>
         </property>
         <item row="0" column="0">
          <widget class="QWidget" name="widget" native="true">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_2">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QGroupBox" name="groupBoxGlance">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="title">
               <string>Layout</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
              </property>
              <layout class="QGridLayout" name="gridLayout_2">
               <property name="leftMargin">
                <number>0</number>
               </property>
               <property name="topMargin">
                <number>0</number>
               </property>
               <property name="rightMargin">
                <number>0</number>
               </property>
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <property name="spacing">
                <number>0</number>
               </property>
               <item row="0" column="0">
                <widget class="QRadioButton" name="radioButton4">
                 <property name="text">
                  <string>      4*4</string>
                 </property>
                 <property name="checked">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QRadioButton" name="radioButton2">
                 <property name="text">
                  <string>      2*2</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="0">
                <widget class="QRadioButton" name="radioButton1">
                 <property name="text">
                  <string>      1*1</string>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QWidget" name="widgetbaseInfoButton" native="true">
           <layout class="QGridLayout" name="gridLayout_5">
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <property name="spacing">
             <number>0</number>
            </property>
            <item row="0" column="0">
             <widget class="QPushButton" name="pushButtonInformation">
              <property name="text">
               <string>Patient Info</string>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QPushButton" name="pushButtonReport">
              <property name="text">
               <string>Report</string>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QPushButton" name="pushButtonSend">
              <property name="text">
               <string>Send images</string>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QPushButton" name="pushButtonPrint">
              <property name="text">
               <string>Print images</string>
              </property>
             </widget>
            </item>
            <item row="4" column="0">
             <widget class="QPushButton" name="pushButtonDelete">
              <property name="text">
               <string>Delete images</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item row="1" column="0">
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QWidget" name="widgetUpDownButton" native="true">
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>93</height>
         </size>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonUp">
           <property name="toolTip">
            <string>Page Up</string>
           </property>
           <property name="text">
            <string>Pre Page</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonDown">
           <property name="toolTip">
            <string>Page Down</string>
           </property>
           <property name="text">
            <string>Next Page</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonSelectAll">
           <property name="text">
            <string>Select All</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonDeselectAll">
           <property name="text">
            <string>Deselect All</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ImageSkimManager</class>
   <extends>QWidget</extends>
   <header>imageskimmanager.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>BorderedQFrame</class>
   <extends>QFrame</extends>
   <header>borderedqframe.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>GlanceImagesTopWidget</class>
   <extends>QWidget</extends>
   <header>glanceimagestopwidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
