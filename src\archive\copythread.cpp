#include "copythread.h"
#include <QDir>
#include <QFileInfoList>
#include "util.h"
#include "syncer.h"
#include "diskinfo.h"
#include "diskvolumevalidator.h"
#include "resource.h"
//#include <QDebug>
#include "modeldirectorygetter.h"
#include "generalworkflowfunction.h"
#include "patient.h"

CopyThread::CopyThread(QObject* parent)
    : FileOperationThread(parent)
    , m_Confirm(false)
    , m_Value(-1)
    , m_IsmkPatientDir(false)
    , m_isCopyFailed(false)
    , m_IsmkPatientWithNameDir(false)
{
    connect(&m_Copy, SIGNAL(curValue(qint64)), this, SIGNAL(curValue(qint64)));
    connect(&m_Copy, SIGNAL(curHaveCopyed(qint64)), this, SIGNAL(curHaveCopyed(qint64)));
    connect(this, SIG<PERSON><PERSON>(finished()), this, SLOT(setMkPatientDirFall()));
    connect(this, SIGNAL(finished()), this, SLOT(setMkPatientDirWithNameFall()));
    connect(&m_Copy, SIGNAL(rptFileCopyed(QString)), this, SIGNAL(rptFileCopyed(QString)));
}

void CopyThread::run()
{
    UTIL_DATA_SYNCER
    m_Copy.setCopyState(true);
    m_isCopyFailed = false;
    m_SameList = calcSameFiles(m_CopyList, m_CurPath);
    m_SameBaseName = sameBaseNames(m_SameList);
    copy(m_CopyList, m_CurPath);
    m_CopyList.clear();
    m_Copy.clearNotCopyFileSuffixFilters();
    m_Value = -1;
}

void CopyThread::checkIfNeedToChangeFileList(QFileInfoList& list)
{
    m_Copy.checkIfNeedSingleFrameImgData(list);
}

void CopyThread::setCopyArgs(const QStringList& list)
{
    m_CopyList = list;
    m_Copy.setSourceSize(m_Copy.calcSrcSizes(list));
}

void CopyThread::setCurPath(const QString& path)
{
    m_CurPath = path;
}

void CopyThread::setRootPath(const QString& rootPath)
{
    m_Copy.setRootPath(rootPath);
}

void CopyThread::setICopy(ICopy* copy)
{
    m_Copy.setICopy(copy);
}

void CopyThread::setConfirmValue(int value)
{
    m_Value = value;
    emit changeValue(m_Value);
}

void CopyThread::setCopyState(bool state)
{
    m_Copy.setCopyState(state);
}

bool CopyThread::isVolumeEnough()
{
    return DiskVolumeValidator::isVolumeEnough(m_CurPath, m_Copy.calcSrcSizes(m_CopyList));
}

void CopyThread::setChangeModTime(bool value)
{
    m_Copy.changeModTime(value);
}

// return the name list under folder path
QStringList CopyThread::fileNameList(const QString& path)
{
    QStringList names;
    QFileInfoList list = QDir(path).entryInfoList(QDir::AllEntries | QDir::NoDotAndDotDot);
    m_Copy.checkIfNeedSingleFrameImgData(list);

    foreach (QFileInfo info, list)
    {
        if (info.suffix() != Resource::cineSuffix && info.suffix() != Resource::imgSuffix &&
            info.suffix() != Resource::reportSuffix)
        {
            names.append(info.fileName());
        }
    }
    return names;
}

// return the path list under folder path
QStringList CopyThread::filePathList(const QString& path)
{
    QStringList paths;
    QFileInfoList list = QDir(path).entryInfoList(QDir::AllEntries | QDir::NoDotAndDotDot);
    m_Copy.checkIfNeedSingleFrameImgData(list);

    foreach (QFileInfo info, list)
    {
        paths.append(info.filePath());
    }
    return paths;
}

void CopyThread::copy(const QStringList& paths, const QString& dest)
{
    for (int i = 0; i < paths.count(); i++)
    {
        QFileInfo tmpInfo(paths.at(i));

        if (dest.startsWith(tmpInfo.filePath()))
        {
            suspend();
            emit denyPaste(COPY_IN_ITSELF);
            tryWait();
            continue;
        }
        if (tmpInfo.absolutePath() == dest)
        {
            suspend();
            emit denyPaste(COPY_IN_CURDIR);
            tryWait();
            return;
        }
        else
        {
            // if (fileNameList(dest).contains(tmpInfo.fileName()))
            QString currentDest = dest;
            if (m_IsmkPatientDir)
            {
                currentDest = QDir(dest).filePath(PatientPath::instance().patientId(tmpInfo.filePath()));
            }
            else if (m_IsmkPatientWithNameDir)
            {
                QScopedPointer<Patient> patient(GeneralWorkflowFunction::getPatient(tmpInfo.baseName()));
                currentDest = QDir(dest).filePath(PatientPath::instance().exportPatientDirName(patient.data()));
            }

            if (isFileExists(m_Copy.destFileName(tmpInfo.fileName()), currentDest) &&
                tmpInfo.suffix() != Resource::cineSuffix && tmpInfo.suffix() != Resource::imgSuffix &&
                tmpInfo.suffix() != Resource::reportSuffix)
            {
                m_SameList.removeOne(tmpInfo.fileName());
                if (m_Value == -1 || m_Value == 3 || m_Value == 4)
                {
                    suspend();
                    m_CopyingFile = m_Copy.destFileName(tmpInfo.fileName()); // modified 2017-08-07 by liujia
                    // m_IsmkPatientDir在back和restore时为true,而back和restore的操作类似多选发送,需要将m_IsmkPatientDir加入条件判断
                    if (m_IsmkPatientWithNameDir || m_IsmkPatientDir)
                    {
                        emit showConfirmDlg(false);
                    }
                    else
                    {
                        emit showConfirmDlg(m_SameList.isEmpty());
                    }

                    tryWait();
                    m_Confirm = true;
                }
                else if (m_Value == 1)
                {
                    m_Copy.setCurHaveCopyed(m_Copy.calcSrcSize(tmpInfo.filePath()));
                    continue;
                }
                else if (m_Value == 2)
                {
                    if (!m_Copy.copy(tmpInfo.filePath(), currentDest))
                    {
                        m_isCopyFailed = true;
                    }

                    continue;
                }

                if (m_Confirm)
                {
                    switch (m_Value)
                    {
                    case 0:
                        m_CopyList.clear();
                        return;
                        break;

                    case 1:
                        m_Copy.setCurHaveCopyed(m_Copy.calcSrcSize(tmpInfo.filePath()));
                        continue;
                        break;

                    case 2:
                    {
                        if (tmpInfo.isFile())
                        {
                            if (!m_Copy.copy(tmpInfo.filePath(), currentDest))
                            {
                                m_isCopyFailed = true;
                            }
                        }
                        else
                        {
                            QString str;
                            if (m_IsmkPatientDir)
                            {
                                str = currentDest;
                            }
                            else
                            {
                                str = currentDest + '/' + tmpInfo.baseName();
                            }
                            copy(filePathList(tmpInfo.filePath()), str);
                        }
                        break;
                    }

                    case 3:
                        m_Copy.setCurHaveCopyed(m_Copy.calcSrcSize(tmpInfo.filePath()));
                        continue;
                        break;

                    case 4:
                    {
                        if (tmpInfo.isFile())
                        {
                            if (!m_Copy.copy(tmpInfo.filePath(), currentDest))
                            {
                                m_isCopyFailed = true;
                            }
                        }
                        else
                        {
                            QString str;
                            if (m_IsmkPatientDir)
                            {
                                //应为上面143行的代码会把tmpInfo.baseName()自己加到currentDest后面
                                //所以此处不需要加，不然会导致路径中出现两个连着的tmpInfo.baseName()
                                //导致路径错误，进而导致拷贝等操作失败
                                str = currentDest;
                            }
                            else
                            {
                                str = currentDest + '/' + tmpInfo.baseName();
                            }
                            copy(filePathList(tmpInfo.filePath()), str);
                        }
                        break;
                    }
                        m_Confirm = false;
                    }
                }
            }
            else
            {
                if (m_SameBaseName.contains(tmpInfo.baseName()))
                {
                    if (m_Value == 1 || m_Value == 3)
                    {
                        m_Copy.setCurHaveCopyed(m_Copy.calcSrcSize(tmpInfo.filePath()));
                    }
                    else
                    {
                        if (!m_Copy.copy(tmpInfo.filePath(), currentDest))
                        {
                            m_isCopyFailed = true;
                        }
                    }
                }
                else
                {
                    if (!m_Copy.copy(tmpInfo.filePath(), currentDest))
                    {
                        m_isCopyFailed = true;
                    }
                }
            }
        }
    }
}

// return the list of names of files exist in both list and target
QStringList CopyThread::calcSameFiles(const QStringList& list, const QString& target)
{
    QStringList tmplist;
    foreach (QString str, list)
    {
        QFileInfo info(str);
        if (isFileExists(info.fileName(), target))
        {
            if (info.suffix() != Resource::cineSuffix && info.suffix() != Resource::imgSuffix &&
                info.suffix() != Resource::reportSuffix && info.suffix() != Resource::paraSuffix)
            {
                tmplist.append(info.fileName());
            }

            if (info.isDir())
            {
                tmplist.append(calcSameFiles(filePathList(info.filePath()), target + '/' + info.baseName()));
            }
        }
    }
    return tmplist;
}

const QString& CopyThread::copyingFile() const
{
    return m_CopyingFile;
}

bool CopyThread::isFileExists(const QString& fileName, const QString& path)
{
    QDir dir(path);
    QFileInfo info(dir.filePath(fileName));
    return info.exists();
}

void CopyThread::setIsMkPatientDir(bool enable)
{
    m_IsmkPatientDir = enable;
}

bool CopyThread::isCopyFailed() const
{
    return m_isCopyFailed;
}

void CopyThread::setIsMkPatientWithNameDir(bool enable)
{
    m_IsmkPatientWithNameDir = enable;
}

void CopyThread::setNeedSingleFrameImgData(bool value)
{
    m_Copy.setNeedSingleFrameImgData(value);
}

void CopyThread::setNeedCreateEmptyCopy(bool value)
{
    m_Copy.setNeedCreateEmptyCopy(value);
}

void CopyThread::setNotCopyFileSuffixFilters(const QStringList& value)
{
    m_Copy.setNotCopyFileSuffixFilters(value);
}

QStringList CopyThread::sameBaseNames(const QStringList& list)
{
    QStringList baseNames;
    foreach (QString str, list)
    {
        QFileInfo info(str);
        baseNames.append(info.baseName());
    }
    return baseNames;
}

void CopyThread::setMkPatientDirFall()
{
    m_IsmkPatientDir = false;
}

void CopyThread::setMkPatientDirWithNameFall()
{
    m_IsmkPatientWithNameDir = false;
}
