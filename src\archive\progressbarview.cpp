#include "progressbarview.h"
#include "progressbar.h"
#include "util.h"
#include <QWidget>
#include <QApplication>
#include <QDesktopWidget>
#include "statefilterlocker.h"
#include "stateeventnames.h"
#include <QEvent>

ProgressBarView::ProgressBarView(IStateManager* value, QWidget* parent)
    : QObject(parent)
    , m_ProgressBar(NULL)
    , m_StateManager(value)
{
    progressBar(parent);
}

ProgressBarView::~ProgressBarView()
{
    Util::SafeDeletePtr(m_ProgressBar);
}

void ProgressBarView::setText(const QString& value)
{
    if (m_ProgressBar != NULL)
    {
        m_ProgressBar->setText(value);
    }
}

void ProgressBarView::setBusyIndicative(bool value)
{
    if (m_ProgressBar != NULL)
    {
        m_ProgressBar->setBusyIndicative(value);
    }
}

void ProgressBarView::setCancelButtonVisible(bool value)
{
    if (m_ProgressBar != NULL)
    {
        m_ProgressBar->setCancelButtonVisible(value);
    }
}

void ProgressBarView::move(const QPoint& point)
{
    if (m_ProgressBar != NULL)
    {
        m_ProgressBar->move(point);
    }
}

QSize ProgressBarView::size() const
{
    QSize size = QSize(0, 0);
    if (m_ProgressBar != NULL)
    {
        size = m_ProgressBar->size();
    }
    return size;
}

ProgressBarFrame* ProgressBarView::progressBar(QWidget* widget)
{
    if (widget != NULL && m_ProgressBar == NULL)
    {
        m_ProgressBar = new ProgressBarFrame(widget);
    }
    connect(m_ProgressBar, SIGNAL(cancelButtonClicked()), this, SIGNAL(cancelButtonClicked()));
    return m_ProgressBar;
}

void ProgressBarView::showProgressBar()
{
    if (m_ProgressBar != NULL)
    {
        m_ProgressBar->show();
    }
}

void ProgressBarView::execProgressBar()
{
    if (m_ProgressBar != NULL)
    {
        StateFilterLocker locker(m_StateManager, StateEventNames::NothingStateFilter());
        m_ProgressBar->exec();
    }
}

void ProgressBarView::hideProgressBar()
{
    if (m_ProgressBar != NULL)
    {
        m_ProgressBar->hide();
    }
}

void ProgressBarView::setProgressBarText(const QString& text)
{
    if (m_ProgressBar != NULL)
    {
        m_ProgressBar->setText(text);
    }
}

void ProgressBarView::setProgressBarTaskInfoForText(const QString& text)
{
    if (m_ProgressBar != NULL)
    {
        m_ProgressBar->setTaskInfoForText(text);
    }
}

void ProgressBarView::setProgressBarTextVisible(bool bValue)
{
    if (m_ProgressBar != NULL)
    {
        m_ProgressBar->setInfoLabelTextVisible(bValue);
    }
}

void ProgressBarView::updateProgressBarValue(qint64 value)
{
    if (m_ProgressBar != NULL)
    {
        m_ProgressBar->setValue(value);
    }
}

//用于链接不同线程的信号,响应信号队列;不能链接同线程信号,否则exec会阻塞
void ProgressBarView::updateProgressBar(qint64 value)
{
    QEvent event(QEvent::Wheel);
    QApplication::sendEvent(qApp, &event);
    m_ProgressBar->setValue(value); //放在后面第一次!isVisible时候执行不到
    if (!m_ProgressBar->isVisible())
    {
        StateFilterLocker locker(m_StateManager, StateEventNames::NothingStateFilter());
        m_ProgressBar->exec(); // hide()结束阻塞
    }
}

QWidget* ProgressBarView::widget() const
{
    return m_ProgressBar;
}
