/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#ifndef AUTOSWITCHPROBEWIDGET_H
#define AUTOSWITCHPROBEWIDGET_H
#include "controls_global.h"

#include "basewidget.h"
#include "baseinputabledialogframe.h"

namespace Ui
{
class AutoSwitchProbeWidget;
}

class ITestCommand;
class AutoSwitchProbeWidget;
class HTMLExporter;

class CONTROLSSHARED_EXPORT AutoSwitchProbeDialog : public BaseInputAbleDialogFrame
{
    Q_OBJECT
public:
    explicit AutoSwitchProbeDialog(QWidget* parent = 0);
    void setTestCommand(ITestCommand* command);

private:
    AutoSwitchProbeWidget* m_child;
};

class CONTROLSSHARED_EXPORT AutoSwitchProbeWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit AutoSwitchProbeWidget(QWidget* parent = 0);
    ~AutoSwitchProbeWidget();
    void setTestCommand(ITestCommand* command);

protected:
    void retranslateUi()
    {
    }
private slots:
    void on_pushButtonStart_clicked();
    void on_pushButtonApply_clicked();
    void on_pushButtonExport_clicked();
    void on_pushButtonStop_clicked();
    void onFinish();

private:
    QString generateHTML();
    Ui::AutoSwitchProbeWidget* ui;
    ITestCommand* m_command;
    bool m_isStarted;
    int m_finishTimes;
    QString m_startTime;
    QString m_endTime;
    static const QString m_testExportFileName;
    static const QString m_exportSuffix;
    HTMLExporter* m_exporter;
    int m_htmlRow;
    int m_htmlCol;
};

#endif // AUTOSWITCHPROBEWIDGET_H
