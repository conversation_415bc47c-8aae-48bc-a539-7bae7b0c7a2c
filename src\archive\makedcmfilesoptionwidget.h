#ifndef MAKEDCMFILESOPTIONWIDGET_H
#define MAKEDCMFILESOPTIONWIDGET_H

#include "basewidget.h"

namespace Ui
{
class MakeDcmFilesOptionWidget;
}

class MakeDcmFilesOptionWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit MakeDcmFilesOptionWidget(QWidget* parent = 0);
    ~MakeDcmFilesOptionWidget();
    void save();

protected:
    void retranslateUi();

private:
    void fillInCompressionComboxes();    //填充comboxs
    void initCompressionComboxesIndex(); //初始化comboxs索引

private:
    Ui::MakeDcmFilesOptionWidget* ui;
    int compressRatiosIndex[2];

private slots:
    void on_comboBoxCompressionMode_currentIndexChanged(int index);
    void on_comboBoxCompressionRatio_currentIndexChanged(int index);
};

#endif // MAKEDCMFILESOPTIONWIDGET_H
