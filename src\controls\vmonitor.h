#ifndef VMONITOR_H
#define VMONITOR_H
#include "controls_global.h"

#include "voltagemonitordatastruct.h"
#include <QWidget>
#include <QQueue>
#include "basewidget.h"
#include "baseinputabledialogframe.h"
#include <QSettings>
#include <QVector>
#include <QTimer>

class VMonitor;
class CONTROLSSHARED_EXPORT VMonitorDialog : public BaseInputAbleDialogFrame
{
    Q_OBJECT
public:
    explicit VMonitorDialog(QWidget* parent = 0);
    void startWriteFileTimer();
    void stopWriteFileTimer();
public slots:
    void fillData(const QVector<VoltageData>& data, const QVector<unsigned short>& states);
    void fillHeadData(uchar* data, int len);

protected:
    void showEvent(QShowEvent* e);
    void hideEvent(QHideEvent* e);
    void closeEvent(QCloseEvent* e);

private:
    VMonitor* m_Child;
    int m_Speed;
};

namespace Ui
{
class VMonitor;
}

class ISystemUsage;
class CONTROLSSHARED_EXPORT VMonitor : public BaseWidget
{
    Q_OBJECT

public:
    explicit VMonitor(QWidget* parent = 0);
    ~VMonitor();
    // public slots:
    //    void fillData(const Voltage &info);
    void fillData(const QVector<VoltageData>& data, const QVector<unsigned short>& states);
    void fillHeadData(uchar* data, int len);
    inline int getSPeed()
    {
        return m_Speed;
    }
    inline bool isRunning()
    {
        return m_Run;
    }

    void startTimer(); /*{m_Timer.start();}*/
    void stopTimer();  /*{m_Timer.stop();}*/

private:
    void fillCPUTemp();
    void fillCPUUsage();
    void fillMemory();
    void volumeControler();
    void fillAllFanSpeed();
    void fillAllFanRelatedInfo();
    int caculatorFanSpeed(const unsigned short& speed);
    /**
     * @brief caculatorType1 乘以0.0098，电压、电流使用
     * @return
     */
    double caculatorType1(const unsigned short& value);
    /**
     * @brief caculatorType1 乘以0.125，温度使用
     * @return
     */
    double caculatorType2(const qint8& value);
    /**
     * @brief stringNumber 返回指定小数位数的字符串
     * @param value 传入的浮点数
     * @param decimal 显示的小数位数
     * @return
     */
    QString stringNumber(const double& value, int decimal = 4);
    void initSystemUsage();

protected:
    void retranslateUi();
private slots:
    void on_pushButton_clicked();

    void on_accelButton_clicked();

    void on_decelButton_clicked();

    void writeIntoIni();

    void fillComputerInfo();

    int setVolume(int volumeNum);

    void playAudio();

    /**
     * @brief 写入EC version和USB3014 version信息
     */
    void fillHWInfo();

public:
private:
    //    void writeIntoIni();
    Ui::VMonitor* ui;

    int m_Speed;
    bool m_Run;
    QSettings setting;
    QTimer m_Timer;
    QTimer m_cpuTimer;
    double m_cpu_total = 0;
    double m_cpu_use = 0;

    QTimer* m_TimerSay;
    qint64 time;

    ISystemUsage* m_pSystemUsage;
};

#endif // VMONITOR_H
