#
# The module defines the following variables:
# AUTOCYSTIC_FOUND - True if autocystic found.
# AUTOCYSTIC_INCLUDE_DIRS - where to find Ofeli_c.h, etc.
# AUTOCYSTIC_LIBRARIES - List of libraries when using autocystic.
#
thirdparty_prefix_path(autocystic)

find_path ( AUTOCYSTIC_INCLUDE_DIR
            NAMES
                ofeli_c.h
                jrsg.h
            HINTS
                ${AUTOCYSTIC_ROOT}/include
            )
#find_path ( JRSG_INCLUDE_DIR
#            NAMES
#                jrsg.h
#            HINTS
#                ${AUTOCYSTIC_ROOT}/include
#             )
set(AUTOCYSTIC_INCLUDE_DIRS ${AUTOCYSTIC_INCLUDE_DIR})
message("Found autocystic headers: ${AUTOCYSTIC_INCLUDE_DIRS}")


find_library ( AUTOCYSTIC_LIBRARY
            NAMES 
                Ofeli_c
            HINTS
                ${AUTOCYSTIC_ROOT_DIR}/lib
                ${AUTOCYSTIC_ROOT}/lib
               )
find_library ( JRSG_LIBRARY
           NAMES
               jrsg
           HINTS
               ${AUTOCYSTIC_ROOT_DIR}/lib
               ${AUTOCYSTIC_ROOT}/lib
              )
set(AUTOCYSTIC_LIBRARIES ${AUTOCYSTIC_LIBRARY} ${JRSG_LIBRARY})
message("Found autocystic libs: ${AUTOCYSTIC_LIBRARIES}")
