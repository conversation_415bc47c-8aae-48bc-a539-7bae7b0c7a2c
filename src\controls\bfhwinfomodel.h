#ifndef BFHWINFOMODEL_H
#define BFHWINFOMODEL_H
#include "controls_global.h"

#include "voltagemonitordatastruct.h"
#include <QHash>
#include <QObject>
#include <QWidget>

class VMonitorDialog;
class ProbeTemperatureDialog;
class IBeamFormer;
class ITestCommand;
class VMonitorSelectDialog;
class PatientWorkflow;
class ImageSkimManager;
class MotorControl;
class ChisonUltrasoundContext;
class IStateManager;
class IColorMapManager;
class IProbeDataSet;
class CONTROLSSHARED_EXPORT BFHWInfoModel : public QObject
{
    Q_OBJECT
public:
    explicit BFHWInfoModel(QObject* parent = 0);
    void setViewParent(QWidget* value);
    void setBeamFormer(IBeamFormer* beamFormer);
    void setTestCommand(ITestCommand* value);
    void setPatientWorkFlow(PatientWorkflow* value);
    void setImageSkimManager(ImageSkimManager* value);
    void setMotorControl(MotorControl* value);
    void setChisonUltrasoundContext(ChisonUltrasoundContext* value);
    void showVMonitor();
    void setStateManager(IStateManager* stateManager);
    void setColorMapManager(IColorMapManager* colorMapManager);
    void setProbeDataSet(IProbeDataSet* probeDataSet);

signals:
    void sendParameter();
private slots:
    void showView();
    void onHighTemperatureDetected();
    // added by shentao
    void onVoltageInfoChanged(const QVector<VoltageData>& datas, const QVector<unsigned short>& states);
    void onHeadDataUpdated(uchar* data, int len);
    void onWarningMsgDestroyed();
    VMonitorDialog* vmDialog() const;
    void onProbeTemperatureWarning(int temp);
    void resetProbeTemperature();
    void onTeeProbeTemperatureWarning(int temp, int angle);

private:
    void initHWInfoDlg();
    void checkTeeProbeTemperature(double temp);

private:
    QWidget* m_ViewParent;
    VMonitorDialog* m_VMonitorDialog;
    ProbeTemperatureDialog* m_ProbeTemperatureDialog;
    IBeamFormer* m_beamFormer;
    ITestCommand* m_testCommand;
    VMonitorSelectDialog* m_selectDialog;
    PatientWorkflow* m_patientWorkflow;
    ImageSkimManager* m_imageSkimManager;
    MotorControl* m_MotorControl;
    ChisonUltrasoundContext* m_ChisonUltrasoundContext;
    bool m_WarningMsgOpened;
    int m_ProbeTempOverLimtTimes;
    bool m_DoesUserDiscard;
    bool m_HasShowWarning;
    IStateManager* m_StateManager;
    IColorMapManager* m_ColorMapManager;
    IProbeDataSet* m_ProbeDataSet;
};

#endif // BFHWINFOMODEL_H
