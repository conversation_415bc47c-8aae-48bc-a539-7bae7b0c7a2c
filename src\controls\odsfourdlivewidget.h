#ifndef ODSFOURDLIVEWIDGET_H
#define ODSFOURDLIVEWIDGET_H

#include "controls_global.h"
#include "abstractfourdlivewidget.h"
#include "odsfourdparameter.h"

namespace Ui
{
class ODSFourDLiveWidget;
}
class IFourDParameter;
class IFourDDataSender;
class AviPlayDialog;

class CONTROLSSHARED_EXPORT ODSFourDLiveWidget : public AbstractFourDLiveWidget
{
    Q_OBJECT

public:
    explicit ODSFourDLiveWidget(IFourDDataSender* dataSender, QWidget* parent = 0);
    ~ODSFourDLiveWidget();
    void setContext(ImageTile* context, ILineBufferManager* bufferManager);
    void init();
    void reset();
    void saveCurGroupParas();
    IFourDRenderWidget* fourDRenderWidget() const;
    FourDTransformWidget* transformWidget() const;
    GrayCurveWidget* fourdGrayCurveWidget() const;
    void onAviPlay(const QString& aviFileName);

protected:
    void showEvent(QShowEvent* e);
public slots:
    void onBeforecurSonoParametersChanged();
    void onCurSonoParametersChanged();
    void onCurvedLineCtrlPointMoved(const QString& direction);
private slots:
    void onPresetChanged(const PresetParameters& presets);
    void onFreezeChanged(const QVariant& value);
    void onFourDGainChanged(const QVariant& val);

private:
    void setupRects();
    static void callbackFrom4D(int wParam, int lParam);
    void createFourD(void* callbackFunc);
    void connectSignals();
    void disconnectSignals();
    void updateFourDGain(const QVariant& val);

private:
    Ui::ODSFourDLiveWidget* ui;
    bool m_IsInited;
    AviPlayDialog* m_AviPlayDialog;
    static ODSFourDLiveWidget* m_ODSFourDLiveWidget;
};

#endif // ODSFOURDLIVEWIDGET_H
