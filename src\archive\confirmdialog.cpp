#include "confirmdialog.h"
#include "ui_confirmdialog.h"
#include "util.h"

ConfirmDialog::ConfirmDialog(QWidget* parent)
    : BaseDialog(parent)
    , ui(new Ui::ConfirmDialog)
    , m_Operation(Count)
{
    ui->setupUi(this);
}

ConfirmDialog::~ConfirmDialog()
{
    delete ui;
}

void ConfirmDialog::retranslateUi()
{
    ui->retranslateUi(this);
}

void ConfirmDialog::on_cancelButton_clicked()
{
    m_Operation = Cancel;
    emit clickedButton(m_Operation);
    emit hideDlg();
}

void ConfirmDialog::on_skipAllButton_clicked()
{
    m_Operation = SkipAll;
    emit clickedButton(m_Operation);
    emit hideDlg();
}

void ConfirmDialog::on_replaceAllButton_clicked()
{
    m_Operation = ReplaceAll;
    emit clickedButton(m_Operation);
    emit hideDlg();
}

void ConfirmDialog::on_skipButton_clicked()
{
    m_Operation = SkipOnce;
    emit clickedButton(m_Operation);
    emit hideDlg();
}

void ConfirmDialog::on_replaceButton_clicked()
{
    m_Operation = ReplaceOnce;
    emit clickedButton(m_Operation);
    emit hideDlg();
}

void ConfirmDialog::onExit()
{
    if (m_Operation == Count)
    {
        on_cancelButton_clicked();
    }
}

ConfirmDialog::Operation ConfirmDialog::opeResult()
{
    return m_Operation;
}

void ConfirmDialog::setButtonState(bool value)
{
    ui->skipAllButton->setHidden(value);
    ui->replaceAllButton->setHidden(value);
    if (!value)
    {
        ui->replaceAllButton->setFocus();
    }
    else
    {
        ui->replaceButton->setFocus();
    }
}

void ConfirmDialog::setText(const QString& str)
{
    ui->label->setText(QString(str + QString(" ") + tr("already exists! Replace it?")));
}

ConfirmDialogFrame::ConfirmDialogFrame(QWidget* parent)
    : BaseFrame(parent)
{
    m_Dlg = new ConfirmDialog(this);
    m_Dlg->setWindowFlags(Qt::Widget);
    m_Dlg->setWindowTitle(Util::translate("AppTitle", QApplication::applicationName()));
    connect(m_Dlg, SIGNAL(hideDlg()), this, SLOT(onClose()));
    connect(m_Dlg, SIGNAL(clickedButton(int)), this, SIGNAL(clickedButton(int)));
    connect(this, SIGNAL(closed()), m_Dlg, SLOT(onExit()));
    this->setContent(m_Dlg);

    installEventFilter(this);
}

void ConfirmDialogFrame::onClose()
{
    m_Dlg->setVisible(false);
    this->close();
}

void ConfirmDialogFrame::setButtonState(bool value)
{
    m_Dlg->setButtonState(value);
}

void ConfirmDialogFrame::setText(const QString& str)
{
    m_Dlg->setText(str);
}

void ConfirmDialogFrame::excuteFrame()
{
    m_Dlg->setVisible(true);
    this->exec();
}

bool ConfirmDialogFrame::eventFilter(QObject* obj, QEvent* event)
{
    if (event->type() == QEvent::WindowDeactivate)
    {
        raise();
    }
    else if (event->type() == QEvent::WindowActivate)
    {
        adjustSize();
        updateGeometry();
    }
    return BaseFrame::eventFilter(obj, event);
}
