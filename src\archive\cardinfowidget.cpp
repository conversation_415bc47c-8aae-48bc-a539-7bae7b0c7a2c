#include "cardinfowidget.h"
#include "ui_cardinfowidget.h"
#include "model/study.h"
#include "formula.h"
#include "doublevalidator.h"
#include "util.h"
#include "patientinfounit.h"
#include "measurementdef.h"
#include "realcompare.h"
#include "appsetting.h"

CardInfoWidget::CardInfoWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::CardInfoWidget)
    , m_AnimalSpeciesIndex(Formula::Canine)
{
    ui->setupUi(this);
    initSet();
    connect(ui->wPatientHeight, SIGNAL(textEdited()), this, SLOT(patientInfo_textEdited()));
    connect(ui->wPatientWeight, SIGNAL(textEdited()), this, SLOT(patientInfo_textEdited()));
}

CardInfoWidget::~CardInfoWidget()
{
    delete ui;
}

void CardInfoWidget::initSet()
{
    QList<PatientInfoUnitGroup>& groups = PatientInfoUnit::instance().patientInfoUnitGroups();
    for (int index = 0; index < groups.count(); index++)
    {
        switch (groups[index].unitType())
        {
        case Measurement::PatientInfoType_Height:
            m_PatientHeightModel.setUnitType(groups[index].unitType());
            m_PatientHeightModel.setCurUnitIndex(groups[index].curIndex());
            m_PatientHeightModel.setUnits(groups[index].unitName());
            break;
        case Measurement::PatientInfoType_Weight:
            m_PatientWeightModel.setUnitType(groups[index].unitType());
            m_PatientWeightModel.setCurUnitIndex(groups[index].curIndex());
            m_PatientWeightModel.setUnits(groups[index].unitName());
            break;
        default:
            break;
        }
    }

    m_PatientHeightModel.setValue(0.0, Measurement::Unit_cm);
    ui->wPatientHeight->setModel(m_PatientHeightModel);
    m_PatientWeightModel.setValue(0.0, Measurement::Unit_kg);
    ui->wPatientWeight->setModel(m_PatientWeightModel);
    //    ui->lineEditHeight->setValidator(new DoubleValidator(0, 999, 2, this));
    //    ui->lineEditWeight->setValidator(new DoubleValidator(0, 999, 2, this));
    ui->lineEditBsa->setValidator(new DoubleValidator(0, 999, 2, this));
    ui->lineEditHr->setValidator(new QIntValidator(0, 999, this));
    ui->lineEditbpl->setValidator(new QIntValidator(0, 999, this));
    ui->lineEditBph->setValidator(new QIntValidator(0, 999, this));
    ui->lineEditRap->setValidator(new QIntValidator(0, 999, this));
    ui->lineEditTmp->setValidator(new DoubleValidator(0, 50, 1, this));
    ui->lineEditBsa->setReadOnly(true);
}

void CardInfoWidget::setAnimalSpeciesIndex(int index)
{
    m_AnimalSpeciesIndex = index;
    calBsa();
}

void CardInfoWidget::setStudy(Study* study)
{
    if (study != NULL)
    {
        ui->wPatientHeight->setValue(study->Height(), Measurement::Unit_cm);
        ui->wPatientWeight->setValue(study->Weight(), Measurement::Unit_kg);
        //        ui->lineEditHeight->setText(QString::number(study->Height(), 'f', 2));
        //        ui->lineEditWeight->setText(QString::number(study->Weight(), 'f', 2));
        ui->lineEditBsa->setText(QString::number(study->BSA(), 'f', 2));
        ui->lineEditHr->setText(QString::number(study->HR()));
        ui->lineEditbpl->setText(QString::number(study->BPL()));
        ui->lineEditBph->setText(QString::number(study->BPH()));
        ui->lineEditRap->setText(QString::number(study->RAP()));
        //人超，提示温度（默认）37，兽超无提示温度
        if (!AppSetting::isAnimal())
        {
            ui->lineEditTmp->setText(QString::number(study->Temperature(), 'f', 1));
        }
    }
}

void CardInfoWidget::flawlessStudy(Study*& study)
{
    if (study != NULL)
    {
        //        study->setHeight(ui->lineEditHeight->text().toFloat());
        //        study->setWeight(ui->lineEditWeight->text().toFloat());
        study->setHeight(ui->wPatientHeight->curValue(Measurement::Unit_cm));
        study->setWeight(ui->wPatientWeight->curValue(Measurement::Unit_kg));
        study->setBSA(ui->lineEditBsa->text().toFloat());
        study->setHR(ui->lineEditHr->text().toInt());
        study->setBPL(ui->lineEditbpl->text().toInt());
        study->setBPH(ui->lineEditBph->text().toInt());
        study->setRAP(ui->lineEditRap->text().toInt());
        study->setTemperature(ui->lineEditTmp->text().toFloat());
    }
}

void CardInfoWidget::clearStudyInfo()
{
    //    ui->lineEditHeight->setText("0.00");
    //    ui->lineEditWeight->setText("0.00");
    ui->wPatientHeight->setValue(0.0, Measurement::Unit_cm);
    ui->wPatientWeight->setValue(0.0, Measurement::Unit_kg);
    ui->lineEditBsa->setText("0.00");
    ui->lineEditHr->setText("0");
    ui->lineEditbpl->setText("0");
    ui->lineEditBph->setText("0");
    ui->lineEditRap->setText("0");
    if (AppSetting::isAnimal())
    {
        ui->lineEditTmp->setText("");
    }
    else
    {
        ui->lineEditTmp->setText("37.0");
    }
}

void CardInfoWidget::setCurrentWidgetEnabled(bool enabled)
{
    //    ui->lineEditHeight->setEnabled(enabled);
    //    ui->lineEditWeight->setEnabled(enabled);
    ui->wPatientHeight->setEnabled(enabled);
    ui->wPatientWeight->setEnabled(enabled);
    ui->lineEditBsa->setEnabled(enabled);
    ui->lineEditHr->setEnabled(enabled);
    ui->lineEditbpl->setEnabled(enabled);
    ui->lineEditBph->setEnabled(enabled);
    ui->lineEditRap->setEnabled(enabled);
}

void CardInfoWidget::setInfo(const QMap<QString, QStringList>& info)
{
    //    ui->lineEditHeight->setText(info.value("height"));
    //    ui->lineEditWeight->setText(info.value("weight"));
    ui->wPatientHeight->setTextValue(info.value("height"));
    ui->wPatientWeight->setTextValue(info.value("weight"));
    ui->lineEditBsa->setText(info.value("bsa")[0]);
    ui->lineEditHr->setText(info.value("hr")[0]);
}

void CardInfoWidget::onPatientInfoUnitChanged(const QString& key, const int unitIndex)
{
    if (key == "height")
    {
        double height = ui->wPatientHeight->curValue(Measurement::Unit_cm);
        m_PatientHeightModel.setCurUnitIndex(unitIndex);
        m_PatientHeightModel.setValue(height, Measurement::Unit_cm);
        ui->wPatientHeight->setModel(m_PatientHeightModel);
    }
    else if (key == "weight")
    {
        double weight = ui->wPatientWeight->curValue(Measurement::Unit_kg);
        m_PatientWeightModel.setCurUnitIndex(unitIndex);
        m_PatientWeightModel.setValue(weight, Measurement::Unit_kg);
        ui->wPatientWeight->setModel(m_PatientWeightModel);
    }
}

const QMap<QString, QStringList> CardInfoWidget::infoList()
{
    QMap<QString, QStringList> info;
    //    info.insert("height", ui->lineEditHeight->text());
    //    info.insert("weight", ui->lineEditWeight->text());
    info.insert("height", ui->wPatientHeight->curTextValue());
    info.insert("weight", ui->wPatientWeight->curTextValue());
    info.insert("bsa", QStringList() << ui->lineEditBsa->text());
    info.insert("hr", QStringList() << ui->lineEditHr->text());
    return info;
}

void CardInfoWidget::calBsa()
{
    if (AppSetting::isAnimal())
    {
        if (Formula::Canine == m_AnimalSpeciesIndex || Formula::Feline == m_AnimalSpeciesIndex)
        {
            ui->lineEditBsa->setReadOnly(true);
            double weight = ui->wPatientWeight->curValue(Measurement::Unit_kg);

            ui->lineEditBsa->setText(QString::number(Formula::calAnimalBsa(m_AnimalSpeciesIndex, weight), 'f', 2));
        }
        else
        {
            ui->lineEditBsa->clear();
            ui->lineEditBsa->setText(QString::number(0.00, 'f', 2));
            ui->lineEditBsa->setReadOnly(false);
        }
    }
    else
    {
        double height = ui->wPatientHeight->curValue(Measurement::Unit_cm);
        double weight = ui->wPatientWeight->curValue(Measurement::Unit_kg);
        //    if(RealCompare::AreEqual(ui->lineEditHeight->text().toFloat(), 0)
        //            || RealCompare::AreEqual(ui->lineEditWeight->text().toFloat(), 0))

        ui->lineEditBsa->setText(QString::number(Formula::calBsa(height, weight), 'f', 2));
    }
}

void CardInfoWidget::retranslateUi()
{
    ui->retranslateUi(this);
}

// void CardInfoWidget::on_lineEditHeight_editingFinished()
//{
//    calBsa();
//    emit baseInfo(infoList());
//}

// void CardInfoWidget::on_lineEditWeight_editingFinished()
//{
//    calBsa();
//    emit baseInfo(infoList());
//}

void CardInfoWidget::on_lineEditBsa_editingFinished()
{
    emit baseInfo(infoList());
}

void CardInfoWidget::on_lineEditHr_editingFinished()
{
    emit baseInfo(infoList());
}

// void CardInfoWidget::on_lineEditHeight_textEdited(const QString &arg1)
//{

//    // 保存当前光标所在位置
//    int pos = ui->lineEditHeight->cursorPosition();
//    calBsa();
//    emit baseInfo(infoList());
//    ui->lineEditHeight->setCursorPosition(pos);

//}

// void CardInfoWidget::on_lineEditWeight_textEdited(const QString &arg1)
//{

//    // 保存当前光标所在位置
//    int pos = ui->lineEditWeight->cursorPosition();
//    calBsa();
//    emit baseInfo(infoList());
//    ui->lineEditWeight->setCursorPosition(pos);

//}

void CardInfoWidget::on_lineEditHr_textEdited(const QString& arg1)
{
    emit baseInfo(infoList());
}

void CardInfoWidget::patientInfo_textEdited()
{
    calBsa();
    emit baseInfo(infoList());
}
