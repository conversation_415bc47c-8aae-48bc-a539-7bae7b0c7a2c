<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TreeViewButton</class>
 <widget class="QWidget" name="TreeViewButton">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>686</width>
    <height>44</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>65</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonPatient">
     <property name="text">
      <string>Patient View</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonStudy">
     <property name="text">
      <string>Study View</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonExpand">
     <property name="text">
      <string>Expand All</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonCollapse">
     <property name="text">
      <string>Collapse All</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonSelect">
     <property name="enabled">
      <bool>false</bool>
     </property>
     <property name="text">
      <string>Select All</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
