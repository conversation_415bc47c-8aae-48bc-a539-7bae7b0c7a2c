#include "obinfowidget.h"
#include "ui_obinfowidget.h"
#include "model/study.h"
#include "formula.h"
#include <QDebug>
#include "measurementdef.h"
#include "doublevalidator.h"
#include "util.h"
#include "realcompare.h"
#include "patientinfounit.h"
#include "appsetting.h"

#define Animal_Gestations_Count (10)

ObInfoWidget::ObInfoWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::ObInfoWidget)
    , m_isLoadStudy(false)
    , m_AnimalSpeciesIndex(Formula::Canine)
    , m_Model(ScreenOrientationModel::getInstance())
{
    ui->setupUi(this);
    initSet();
    connect(ui->lineEditLmp, SIGNAL(dateChanged(QDate)), this, SLOT(onLineEditLmpDateChanged(QDate)));
    connect(ui->lineEditGa, SIGNAL(textChanged(QString)), this, SLOT(onLineEditGaChanged(QString)));
    connect(ui->lineEditGa, SIGNAL(editingFinished()), this, SLOT(onLineEditGaFinished()));
    connect(ui->lineEditEdd, SIGNAL(dateChanged(QDate)), this, SLOT(onLineEditEddDateChanged(QDate)));
    connect(ui->lineEditOvil, SIGNAL(dateChanged(QDate)), this, SLOT(onLineEditOvulDateChaned(QDate)));
    connect(ui->lineEditEstab, SIGNAL(dateChanged(QDate)), this, SLOT(onLineEditEstabDateChanged(QDate)));
    connect(ui->wPatientHeight, SIGNAL(textEdited()), this, SLOT(patientInfo_textEdited()));
    connect(ui->wPatientWeight, SIGNAL(textEdited()), this, SLOT(patientInfo_textEdited()));
    ui->lineEditEstab->setTodayLimit(false);
    ui->lineEditEdd->setTodayLimit(false);
    ui->lineEditGa->setPlaceholderText("xxwxd");

    int currentGestationsCount =
        Measurement::Fetus_Count; // 人超 多胞胎数量上限较低 与 测量菜单 与 报告页的生长曲线 有逻辑关联。
    if (AppSetting::isAnimal())
    {
        // 兽超 多胞胎数量上限较高 只是一个Study信息，没有其他逻辑关联。
        currentGestationsCount = Animal_Gestations_Count;
    }

    for (int i = 0; i < currentGestationsCount; i++)
    {
        ui->comboBoxGestations->addItem(QString::number(i + 1));
    }

    QRegExp rx("(^[0-9]{1,2})|(^([0-9]{1,2}w)[0-6])|(^([0-9]{1,2}w)?([0-6]d)?)");
    QRegExpValidator* pReg = new QRegExpValidator(rx, this);
    ui->lineEditGa->setValidator(pReg);

    if (AppSetting::isAnimal())
    {
        ui->lineEditGa->hide();
        ui->label_2->hide();
        ui->lineEditEdd->hide();
        ui->label_3->hide();
        ui->lineEditEstab->hide();
        ui->label_4->hide();
        ui->lineEditOvil->hide();
        ui->label_5->hide();
        ui->label_10->hide();
        ui->comboBoxGestations->hide();
    }
}

ObInfoWidget::~ObInfoWidget()
{
    delete ui;
}

void ObInfoWidget::initSet()
{
    QList<PatientInfoUnitGroup>& groups = PatientInfoUnit::instance().patientInfoUnitGroups();
    for (int index = 0; index < groups.count(); index++)
    {
        switch (groups[index].unitType())
        {
        case Measurement::PatientInfoType_Height:
            m_PatientHeightModel.setUnitType(groups[index].unitType());
            m_PatientHeightModel.setCurUnitIndex(groups[index].curIndex());
            m_PatientHeightModel.setUnits(groups[index].unitName());
            break;
        case Measurement::PatientInfoType_Weight:
            m_PatientWeightModel.setUnitType(groups[index].unitType());
            m_PatientWeightModel.setCurUnitIndex(groups[index].curIndex());
            m_PatientWeightModel.setUnits(groups[index].unitName());
            break;
        default:
            break;
        }
    }

    m_PatientHeightModel.setValue(0.0, Measurement::Unit_cm);
    ui->wPatientHeight->setModel(m_PatientHeightModel);

    m_PatientWeightModel.setValue(0.0, Measurement::Unit_kg);
    ui->wPatientWeight->setModel(m_PatientWeightModel);

    ui->lineEditCycle->setValidator(new QIntValidator(0, 999, this));
    ui->lineEditEctopic->setValidator(new QIntValidator(0, 999, this));
    ui->lineEditGravide->setValidator(new QIntValidator(0, 999, this));
    ui->lineEditPare->setValidator(new QIntValidator(0, 999, this));
    ui->lineEditAbort->setValidator(new QIntValidator(0, 999, this));
    //    ui->lineEditHeight->setValidator(new DoubleValidator(0, 999, 2, this));
    //    ui->lineEditWeight->setValidator(new DoubleValidator(0, 999, 2, this));
    ui->lineEditHr->setValidator(new QIntValidator(0, 999, this));
    ui->lineEditPhr->setValidator(new QIntValidator(0, 999, this));
    ui->lineEditLmp->setMaximumDate(QDate::currentDate()); //设置最大日期
    ui->lineEditBsa->setValidator(new DoubleValidator(0, 999, 2, this));
    ui->lineEditBsa->setReadOnly(true);
}

void ObInfoWidget::setStudy(const Study* study)
{
    if (study != NULL)
    {
        m_isLoadStudy = true;
        ui->lineEditLmp->setDate(study->LMP().date());
        ui->lineEditGa->setText(study->LMP_GA());
        ui->lineEditEdd->setDate(study->LMP_EDD().date());
        ui->lineEditEstab->setDate(study->Estabdd().date());
        ui->lineEditOvil->setDate(study->ExpOvul().date());
        m_isLoadStudy = false;

        if (study->Gestations() <= 0)
        {
            ui->comboBoxGestations->setCurrentIndex(0);
        }
        else
        {
            ui->comboBoxGestations->setCurrentIndex(study->Gestations() - 1);
        }
        ui->lineEditCycle->setText(QString::number(study->DayofCycle()));
        ui->lineEditEctopic->setText(QString::number(study->Ectopic()));
        ui->lineEditGravide->setText(QString::number(study->Gravida()));
        ui->lineEditPare->setText(QString::number(study->Para()));
        ui->lineEditAbort->setText(QString::number(study->Aborta()));
        //        ui->lineEditHeight->setText(QString::number(study->Height(), 'f', 2));
        //        ui->lineEditWeight->setText(QString::number(study->Weight(), 'f', 2));
        ui->wPatientHeight->setValue(study->Height(), Measurement::Unit_cm);
        ui->wPatientWeight->setValue(study->Weight(), Measurement::Unit_kg);

        ui->lineEditBsa->setText(QString::number(study->BSA(), 'f', 2));
        ui->lineEditHr->setText(QString::number(study->HR()));
        ui->lineEditPhr->setText(QString::number(study->FHR()));
    }
}

void ObInfoWidget::flawlessStudy(Study* study)
{
    if (study != NULL)
    {
        study->setLMP(ui->lineEditLmp->getDate().startOfDay());
        study->setLMP_GA(ui->lineEditGa->text());
        study->setLMP_EDD(ui->lineEditEdd->getDate().startOfDay());
        study->setEstabdd(ui->lineEditEstab->getDate().startOfDay());
        study->setExpOvul(ui->lineEditOvil->getDate().startOfDay());

        study->setGestations(ui->comboBoxGestations->currentIndex() + 1);
        study->setDayofCycle(ui->lineEditCycle->text().toInt());
        study->setEctopic(ui->lineEditEctopic->text().toInt());
        study->setGravida(ui->lineEditGravide->text().toInt());
        study->setPara(ui->lineEditPare->text().toInt());
        study->setAborta(ui->lineEditAbort->text().toInt());
        //        study->setHeight(ui->lineEditHeight->text().toFloat());
        //        study->setWeight(ui->lineEditWeight->text().toFloat());
        study->setHeight(ui->wPatientHeight->curValue(Measurement::Unit_cm));
        study->setHeight(ui->wPatientWeight->curValue(Measurement::Unit_kg));

        study->setBSA(ui->lineEditBsa->text().toFloat());
        study->setHR(ui->lineEditHr->text().toInt());
        study->setFHR(ui->lineEditPhr->text().toInt());
    }
}

void ObInfoWidget::clearStudyInfo()
{
    ui->lineEditLmp->setDate(QDate());
    ui->lineEditGa->setText("");
    ui->lineEditEdd->setDate(QDate());
    ui->lineEditEstab->setDate(QDate());
    ui->lineEditOvil->setDate(QDate());

    ui->comboBoxGestations->setCurrentIndex(0);
    ui->lineEditCycle->setText("0");
    ui->lineEditEctopic->setText("0");
    ui->lineEditGravide->setText("0");
    ui->lineEditPare->setText("0");
    ui->lineEditAbort->setText("0");

    //    ui->lineEditHeight->setText("0.00");
    //    ui->lineEditWeight->setText("0.00");
    ui->wPatientHeight->setValue(0.0, Measurement::Unit_cm);
    ui->wPatientWeight->setValue(0.0, Measurement::Unit_kg);

    ui->lineEditBsa->setText("0.00");
    ui->lineEditHr->setText("0");
    ui->lineEditPhr->setText("0");
}

void ObInfoWidget::setCurrentWidgetEnabled(bool enabled)
{
    ui->lineEditLmp->setEnabled(enabled);
    ui->lineEditGa->setEnabled(enabled);
    ui->lineEditEdd->setEnabled(enabled);
    ui->lineEditEstab->setEnabled(enabled);
    ui->lineEditOvil->setEnabled(enabled);
    ui->comboBoxGestations->setEnabled(enabled);
    ui->lineEditCycle->setEnabled(enabled);
    ui->lineEditEctopic->setEnabled(enabled);
    ui->lineEditGravide->setEnabled(enabled);
    ui->lineEditPare->setEnabled(enabled);
    ui->lineEditAbort->setEnabled(enabled);

    //    ui->lineEditHeight->setEnabled(enabled);
    ui->wPatientHeight->setEnabled(enabled);
    //    ui->lineEditWeight->setEnabled(enabled);
    ui->wPatientWeight->setEnabled(enabled);

    ui->lineEditBsa->setEnabled(enabled);
    ui->lineEditHr->setEnabled(enabled);
    ui->lineEditPhr->setEnabled(enabled);
}

void ObInfoWidget::updateGA()
{
    onLineEditLmpDateChanged(ui->lineEditLmp->getDate());
}

void ObInfoWidget::setAnimalSpeciesIndex(int index)
{
    m_AnimalSpeciesIndex = index;
    calBsa();
}

bool ObInfoWidget::isHor()
{
    return m_Model->isHor();
}

void ObInfoWidget::setInfo(const QMap<QString, QStringList>& info)
{
    //    ui->lineEditHeight->setText(info.value("height"));
    //    ui->lineEditWeight->setText(info.value("weight"));
    ui->wPatientHeight->setTextValue(info.value("height"));
    ui->wPatientWeight->setTextValue(info.value("weight"));
    ui->lineEditBsa->setText(info.value("bsa")[0]);
    ui->lineEditHr->setText(info.value("hr")[0]);
}

void ObInfoWidget::onPatientInfoUnitChanged(const QString& key, const int unitIndex)
{
    if (key == "height")
    {
        double height = ui->wPatientHeight->curValue(Measurement::Unit_cm);
        m_PatientHeightModel.setCurUnitIndex(unitIndex);
        m_PatientHeightModel.setValue(height, Measurement::Unit_cm);
        ui->wPatientHeight->setModel(m_PatientHeightModel);
    }
    else if (key == "weight")
    {
        double weight = ui->wPatientWeight->curValue(Measurement::Unit_kg);
        m_PatientWeightModel.setCurUnitIndex(unitIndex);
        m_PatientWeightModel.setValue(weight, Measurement::Unit_kg);
        ui->wPatientWeight->setModel(m_PatientWeightModel);
    }
}

const QMap<QString, QStringList> ObInfoWidget::infoList()
{
    QMap<QString, QStringList> info;
    //    info.insert("height", ui->lineEditHeight->text());
    //    info.insert("weight", ui->lineEditWeight->text());
    info.insert("height", ui->wPatientHeight->curTextValue());
    info.insert("weight", ui->wPatientWeight->curTextValue());
    info.insert("bsa", QStringList() << ui->lineEditBsa->text());
    info.insert("hr", QStringList() << ui->lineEditHr->text());
    return info;
}

void ObInfoWidget::calBsa()
{
    if (AppSetting::isAnimal())
    {
        if (Formula::Canine == m_AnimalSpeciesIndex || Formula::Feline == m_AnimalSpeciesIndex)
        {
            ui->lineEditBsa->setReadOnly(true);
            double weight = ui->wPatientWeight->curValue(Measurement::Unit_kg);

            ui->lineEditBsa->setText(QString::number(Formula::calAnimalBsa(m_AnimalSpeciesIndex, weight), 'f', 2));
        }
        else
        {
            ui->lineEditBsa->clear();
            ui->lineEditBsa->setText(QString::number(0.00, 'f', 2));
            ui->lineEditBsa->setReadOnly(false);
        }
    }
    else
    {
        double height = ui->wPatientHeight->curValue(Measurement::Unit_cm);
        double weight = ui->wPatientWeight->curValue(Measurement::Unit_kg);
        //    if(RealCompare::AreEqual(ui->lineEditHeight->text().toFloat(), 0)
        //            || RealCompare::AreEqual(ui->lineEditWeight->text().toFloat(), 0))

        ui->lineEditBsa->setText(QString::number(Formula::calBsa(height, weight), 'f', 2));
    }
}

void ObInfoWidget::retranslateUi()
{
    ui->retranslateUi(this);
}

void ObInfoWidget::orientationChanged(Qt::ScreenOrientation orientation)
{
    QString className(this->metaObject()->className());
    OrientationConfigItem config;
    m_Model->getInfosByClassName(orientation, className, config);
}

void ObInfoWidget::hideEvent(QHideEvent* e)
{
    ui->lineEditLmp->hideChildren();
    ui->lineEditEstab->hideChildren();
    ui->lineEditOvil->hideChildren();
    BaseWidget::hideEvent(e);
}

void ObInfoWidget::onLineEditLmpDateChanged(const QDate& date)
{
    if (date.isValid())
    {
        ui->lineEditGa->setText(Formula::calLmpGa(date.startOfDay()));
        ui->lineEditEdd->setDate(Formula::calLmpEdd(date.startOfDay()).date());
    }
    else
    {
        ui->lineEditGa->clear();
        ui->lineEditEdd->clear();
    }
}

void ObInfoWidget::onLineEditGaChanged(const QString& wd)
{
    if (m_isLoadStudy || wd.isEmpty())
    {
        return;
    }

    static QString lastwd = wd;
    if (Formula::isRegFormat("^[0-9]{1,2}w[0-6]$", wd))
    {
        QString tmpwd = wd;
        if (Formula::isRegFormat("^[0-9]{1,2}w[0-6]d$", lastwd))
        {
            tmpwd.replace(QRegExp("(.*)w(.*)"), "\\1w");
        }
        else
        {
            tmpwd.replace(QRegExp("(.*)w(.*)"), "\\1w\\2d");
        }

        ui->lineEditGa->setText(tmpwd);
        lastwd = tmpwd;
        return;
    }

    lastwd = wd;

    if (!wd.isEmpty() && Formula::isRegFormat("^([0-9]{1,2}w)?([0-6]d)?$", wd))
    {
        QDateTime current = QDateTime::currentDateTime();
        int day = Formula::wd2d(wd);
        QDateTime lmp = current.addDays(-1 * day);
        ui->lineEditLmp->setDate(lmp.date());
    }
    else
    {
        ui->lineEditLmp->clear();
        ui->lineEditEdd->clear();
    }
}

void ObInfoWidget::onLineEditGaFinished()
{
    if (m_isLoadStudy)
    {
        return;
    }

    QString wd = ui->lineEditGa->text();

    if (!Formula::isRegFormat("(^[0-9]{1,2}w[0-6]d$)|(^[0-9]{1,2}w)", wd))
    {
        ui->lineEditGa->clear();
    }
}

void ObInfoWidget::onLineEditEddDateChanged(const QDate& date)
{
    if (m_isLoadStudy)
    {
        return;
    }
    if (date.isValid())
    {
        ui->lineEditLmp->setDate(date.addDays(-280));
    }
    else
    {
        ui->lineEditLmp->clear();
        ui->lineEditGa->clear();
    }
}

void ObInfoWidget::onLineEditOvulDateChaned(const QDate& date)
{
    if (date.isValid())
    {
        ui->lineEditLmp->setDate(Formula::CalculateLMPByBBT(date.startOfDay()).date());
    }
}

void ObInfoWidget::onLineEditEstabDateChanged(const QDate& date)
{
    if (!date.isValid() || date < (ui->lineEditEstab->minimumDate()))
    {
        ui->lineEditEstab->clear();
    }
}

// void ObInfoWidget::on_lineEditHeight_editingFinished()
//{
//    calBsa();
//    emit baseInfo(infoList());
//}

// void ObInfoWidget::on_lineEditWeight_editingFinished()
//{
//    calBsa();
//    emit baseInfo(infoList());
//}

void ObInfoWidget::on_lineEditBsa_editingFinished()
{
    emit baseInfo(infoList());
}

void ObInfoWidget::on_lineEditHr_editingFinished()
{
    emit baseInfo(infoList());
}

// added by jyq to fix bug that bsa and height weight proble
// void ObInfoWidget::on_lineEditHeight_textEdited(const QString &arg1)
//{
//    // 保存当前光标所在位置
//    int pos = ui->lineEditHeight->cursorPosition();
//    calBsa();
//    emit baseInfo(infoList());
//    ui->lineEditHeight->setCursorPosition(pos);

//}

// void ObInfoWidget::on_lineEditWeight_textEdited(const QString &arg1)
//{
//    // 保存当前光标所在位置
//    int pos = ui->lineEditWeight->cursorPosition();
//    calBsa();
//    emit baseInfo(infoList());
//    ui->lineEditWeight->setCursorPosition(pos);

//}

// make sure bsa's value will change if user input method by jinyuqi
void ObInfoWidget::on_lineEditBsa_textEdited(const QString& arg1)
{
    emit baseInfo(infoList());
}

void ObInfoWidget::on_lineEditHr_textEdited(const QString& arg1)
{
    emit baseInfo(infoList());
}

void ObInfoWidget::patientInfo_textEdited()
{
    calBsa();
    emit baseInfo(infoList());
}
