#include "probeselftestwidget.h"
#include "ui_probeselftestwidget.h"
#include "probeselftestcontrol.h"
#include "chisonultrasoundcontext.h"

ProbeSelfTestDialog::ProbeSelfTestDialog(QWidget* parent)
    : BaseInputAbleDialogFrame(parent)
    , m_child(NULL)
{
    m_child = new ProbeSelfTestWidget(this);
    this->setContent(m_child);
    this->setFrameTitle(tr("Probe Self Test"));
    connect(this, &ProbeSelfTestDialog::closed, m_child, &ProbeSelfTestWidget::closeTest);
}

void ProbeSelfTestDialog::setChisonUltrasoundContext(ChisonUltrasoundContext* value)
{
    m_child->setChisonUltrasoundContext(value);
}

void ProbeSelfTestDialog::show()
{
    m_child->openTest();

    QWidget::show();
}

ProbeSelfTestWidget::ProbeSelfTestWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::ProbeSelfTestWidget)
    , m_ChisonUltrasoundContext(NULL)
    , m_IsAutoTest(false)
    , m_IsTestRunning(false)
    , m_TestType(0)
    , m_ElementIndex(0)
    , m_ValidElementCounts(0)
{
    ui->setupUi(this);
    ui->buttonGroup->setExclusive(true);
    connect(ui->buttonGroup, SIGNAL(buttonClicked(QAbstractButton*)), this,
            SLOT(on_radioBtn_clicked(QAbstractButton*)));
    ui->radioBtnManual->click();
    ui->elementSlider->setSingleStep(1);
    connect(ui->elementSlider, &QSlider::valueChanged, this, &ProbeSelfTestWidget::on_elementSlider_valueChanged);
}

ProbeSelfTestWidget::~ProbeSelfTestWidget()
{
    delete ui;
}

void ProbeSelfTestWidget::setChisonUltrasoundContext(ChisonUltrasoundContext* value)
{
    if (value == NULL)
    {
        return;
    }

    m_ChisonUltrasoundContext = value;

    connect(m_ChisonUltrasoundContext, &ChisonUltrasoundContext::elementTesting, this,
            &ProbeSelfTestWidget::on_elementTesting);
}

bool ProbeSelfTestWidget::openTest()
{
    bool result = m_ChisonUltrasoundContext->openProbeSelfTest();

    m_ChisonUltrasoundContext->validElementCounts(&m_ValidElementCounts);

    ui->elementSlider->setMaximum(m_ValidElementCounts > 0 ? getMaxElementIndex() : 0);

    ui->labelElementEnd->setText(QString::number(m_ValidElementCounts > 0 ? getMaxElementIndex() : 0));

    ui->labelElementBegin->setText("0");

    return result;
}

bool ProbeSelfTestWidget::closeTest()
{
    stopAutoTest();
    return m_ChisonUltrasoundContext->closeProbeSelfTest();
}

void ProbeSelfTestWidget::on_pushBtnStart_clicked()
{
    if (m_IsTestRunning)
    {
        stopAutoTest();
    }
    else
    {
        if (m_IsAutoTest)
        {
            startAutoTest();
        }
        else
        {
            singleElementTest();
        }
    }
}

void ProbeSelfTestWidget::on_radioBtn_clicked(QAbstractButton* button)
{
    if (!button->objectName().compare("radioBtnManual"))
    {
        m_IsAutoTest = false;
    }

    if (!button->objectName().compare("radioBtnAuto"))
    {
        m_IsAutoTest = true;
    }
}

void ProbeSelfTestWidget::on_elementTesting(int elementIndex)
{
    m_ElementIndex = elementIndex;
    ui->elementSlider->setValue(elementIndex);
    if (elementIndex == getMaxElementIndex())
    {
        m_IsTestRunning = false;
        ui->pushBtnStart->setText(tr("start"));
    }
}

void ProbeSelfTestWidget::on_elementSlider_valueChanged(int value)
{
    ui->labelElementCurrent->setText(QString::number(value));
    m_ElementIndex = value;
}

void ProbeSelfTestWidget::startAutoTest()
{
    m_ChisonUltrasoundContext->autoProbeSelfTest(2, 1);
    m_IsTestRunning = true;
    ui->pushBtnStart->setText(tr("stop"));
}

void ProbeSelfTestWidget::stopAutoTest()
{
    m_IsTestRunning = false;
    m_ChisonUltrasoundContext->stopProbeSelfTest();
    ui->pushBtnStart->setText(tr("start"));
}

void ProbeSelfTestWidget::singleElementTest()
{
    m_ChisonUltrasoundContext->singleProbeSelfTest(2, m_ElementIndex);
}

int ProbeSelfTestWidget::getMaxElementIndex()
{
    return m_ValidElementCounts - 1;
}
