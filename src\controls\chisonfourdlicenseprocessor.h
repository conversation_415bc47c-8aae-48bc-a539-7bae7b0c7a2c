#ifndef CHISONFOURDLICENSEPROCESSOR_H
#define CHISONFOURDLICENSEPROCESSOR_H

#include "abstractfourdlicenseprocessor.h"

class CONTROLSSHARED_EXPORT ChisonFourDLicenseProcessor : public AbstractFourDLicenseProcessor
{
    Q_OBJECT
public:
    explicit ChisonFourDLicenseProcessor(QObject* parent = 0);
    ~ChisonFourDLicenseProcessor();
    void verifyLicenseState();
public slots:
    void onFourDStatusChanged(bool isOpen);
};

#endif // CHISONFOURDLICENSEPROCESSOR_H
