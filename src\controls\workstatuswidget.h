#ifndef WORKSTATUSWIDGET_H
#define WORKSTATUSWIDGET_H

#include "controls_global.h"
#include "basewidget.h"

namespace Ui
{
class WorkStatusWidget;
}

class SystemHintModel;

class CONTROLSSHARED_EXPORT WorkStatusWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit WorkStatusWidget(QWidget* parent = 0);
    ~WorkStatusWidget();

    void setModel(SystemHintModel* value);

private slots:
    void onWorkStatusChanged();

protected:
    void retranslateUi();

private:
    Ui::WorkStatusWidget* ui;
    SystemHintModel* m_Model;
};

#endif // WORKSTATUSWIDGET_H
