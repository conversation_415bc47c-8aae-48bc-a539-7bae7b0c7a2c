#ifndef FPSINFOWIDGET_H
#define FPSINFOWIDGET_H

#include "controls_global.h"
#include <QHash>
#include <QLabel>
#include "basewidget.h"

namespace Ui
{
class FpsInfoWidget;
}

class CONTROLSSHARED_EXPORT FpsInfoWidget : public BaseWidget
{
    Q_OBJECT
    Q_PROPERTY(int column WRITE setColumn)
public:
    explicit FpsInfoWidget(QWidget* parent = nullptr);
    ~FpsInfoWidget();

    void setColumn(int column);

protected:
    virtual void mousePressEvent(QMouseEvent* event);
    virtual void mouseMoveEvent(QMouseEvent* event);

public slots:
    void onFpsChanged(const QString& name, const float fps);

    void onPresetModeChanged(bool preset);

private:
    void startShow();
private slots:
    void doShow();

protected:
    /*!
     * 所有对控件的text,item的赋值,都放在这里,使用tr("text")标注字符串
     * ui->retranslateUi()函数页放在此函数中调用，用于统一国际化
     * 此函数还必须在每个widget的构造函数中
     */
    void retranslateUi();

    void paintEvent(QPaintEvent* event);

private:
    Ui::FpsInfoWidget* ui;
    int m_Column;
    bool m_ShowFpsInfo;
    QHash<QString, QLabel*> m_Values;
    QPoint m_PressedPt;
};

#endif // FPSINFOWIDGET_H
