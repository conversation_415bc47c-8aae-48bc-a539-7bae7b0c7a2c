#include "sononervealg.h"

SonoNerveAlg::SonoNerveAlg()
    : VirtualAlg()
{
    m_Lib.setFileName("neuraldt");
    m_Initialized = false;
    m_IsError = false;
    m_CreateContext = NULL;
    m_RemoveContext = NULL;
    m_NeuralsgProcess = NULL;
}

void SonoNerveAlg::neuralsgCreatectx(const char* modelpath, int type, int infertype)
{
    if (m_CreateContext == NULL)
    {
        m_CreateContext = (NeuralsgCreatectx)m_Lib.resolve("neuralsg_createctx");
        if (m_CreateContext == NULL)
        {
            m_Initialized = false;
            m_IsError = true;
            return;
        }
    }
    m_CreateContext(modelpath, type, infertype);
    m_Initialized = true;
}

void SonoNerveAlg::neuralsgRelease()
{
    if (m_RemoveContext == NULL)
    {
        m_RemoveContext = (NeuralsgRelease)m_Lib.resolve("neuralsg_release");
    }
    if (m_RemoveContext == NULL || !m_Initialized)
    {
        m_IsError = true;
        return;
    }
    m_Initialized = false;
    m_RemoveContext();
}

void SonoNerveAlg::neuralsgProcess(unsigned char* imgptr, int width, int height, bool fliplr, bool flipul,
                                   std::vector<Nerver::BPoint>& points)
{
    if (m_NeuralsgProcess == NULL)
    {
        m_NeuralsgProcess = (NeuralsgProcess)m_Lib.resolve("neuralsg_process2");
    }
    if (m_NeuralsgProcess == NULL || !m_Initialized)
    {
        m_IsError = true;
        return;
    }
    m_NeuralsgProcess(imgptr, width, height, fliplr, flipul, points);
}
