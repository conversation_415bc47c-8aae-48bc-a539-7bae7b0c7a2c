#include "sonoimtinfowidget.h"
#include "ui_sonoimtinfowidget.h"
#include "resource.h"
#include "model/study.h"

SonoIMTInfoWidget::SonoIMTInfoWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::SonoIMTInfoWidget)
{
    ui->setupUi(this);

    translateSmoker();
    translateDia();

    initSet();
}

void SonoIMTInfoWidget::translateSmoker()
{
    QStringList tredSmoker;
    foreach (QString smo, Resource::trSmoker())
    {
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
        tredSmoker.append(QApplication::translate("Smoker", smo.toUtf8().data(), 0, QApplication::UnicodeUTF8));
#else
        tredSmoker.append(QApplication::translate("Smoker", smo.toUtf8().data()));
#endif
    }
    ui->comboBoxSmoker->clear();
    ui->comboBoxSmoker->insertItems(0, tredSmoker);
}

void SonoIMTInfoWidget::translateDia()
{
    QStringList tredDia;
    foreach (QString dia, Resource::trDiabetes())
    {
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
        tredDia.append(QApplication::translate("Diabetes", dia.toUtf8().data(), 0, QApplication::UnicodeUTF8));
#else
        tredDia.append(QApplication::translate("Diabetes", dia.toUtf8().data()));
#endif
    }
    ui->comboBoxDia->clear();
    ui->comboBoxDia->insertItems(0, tredDia);
}

SonoIMTInfoWidget::~SonoIMTInfoWidget()
{
    delete ui;
}

void SonoIMTInfoWidget::initSet()
{
    QRegExp rx("^[1-9]{1}[0-9]{0,2}$");
    QRegExpValidator* pReg = new QRegExpValidator(rx, this);
    ui->lineEditTotal->setValidator(pReg);
    ui->lineEditHdl->setValidator(pReg);
    ui->lineEditBP1->setValidator(pReg);
    ui->lineEditBP2->setValidator(pReg);

    //    ui->lineEditTotal->setValidator(new QIntValidator(1, 999, this));
    //    ui->lineEditHdl->setValidator(new QIntValidator(1, 999, this));
    //    ui->lineEditBP1->setValidator(new QIntValidator(1, 999, this));
    //    ui->lineEditBP2->setValidator(new QIntValidator(1, 999, this));

    ui->lineEditTotal->setText("");
    ui->lineEditHdl->setText("");
    ui->lineEditBP1->setText("");
    ui->lineEditBP2->setText("");
    ui->comboBoxSmoker->setCurrentIndex(Resource::smokerIndex(""));
    ui->comboBoxDia->setCurrentIndex(Resource::diabetesIndex(""));
}

void SonoIMTInfoWidget::flawlessStudy(Study* study)
{
    if (study != NULL)
    {
        study->setSmoker(Resource::getSmoker(ui->comboBoxSmoker->currentIndex()));
        study->setDiabetes(Resource::getDiabetes(ui->comboBoxDia->currentIndex()));
        // study->setSmoker(ui->comboBoxSmoker->currentText());
        // study->setDiabetes(ui->comboBoxDia->currentText());
        study->setTotalCholesterol(ui->lineEditTotal->text());
        study->setHDLCholesterol(ui->lineEditHdl->text());
        study->setBP1(ui->lineEditBP1->text());
        study->setBP2(ui->lineEditBP2->text());
    }
}

void SonoIMTInfoWidget::retranslateUi()
{
    ui->retranslateUi(this);

    translateSmoker();
    translateDia();
}

void SonoIMTInfoWidget::setStudy(const Study* study)
{
    if (study != NULL)
    {
        ui->comboBoxSmoker->setCurrentIndex(Resource::smokerIndex(study->Smoker()));
        ui->comboBoxDia->setCurrentIndex(Resource::diabetesIndex(study->Diabetes()));
        ui->lineEditTotal->setText(study->TotalCholesterol());
        ui->lineEditHdl->setText(study->HDLCholesterol());
        ui->lineEditBP1->setText(study->BP1());
        ui->lineEditBP2->setText(study->BP2());
    }
}

void SonoIMTInfoWidget::clearStudyInfo()
{
    ui->comboBoxSmoker->setCurrentIndex(Resource::smokerIndex(""));
    ui->comboBoxDia->setCurrentIndex(Resource::diabetesIndex(""));
    ui->lineEditTotal->setText("");
    ui->lineEditHdl->setText("");
    ui->lineEditBP1->setText("");
    ui->lineEditBP2->setText("");
}

void SonoIMTInfoWidget::setCurrentWidgetEnabled(bool enabled)
{
    ui->comboBoxSmoker->setEnabled(enabled);
    ui->comboBoxDia->setEnabled(enabled);
    ui->lineEditTotal->setEnabled(enabled);
    ui->lineEditHdl->setEnabled(enabled);
    ui->lineEditBP1->setEnabled(enabled);
    ui->lineEditBP2->setEnabled(enabled);
}

void SonoIMTInfoWidget::on_lineEditTotal_editingFinished()
{
}
