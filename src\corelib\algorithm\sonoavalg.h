#ifndef SONOAVALG_H
#define SONOAVALG_H

#include "algorithm_global.h"
#include <QLibrary>
#include "virtualalg.h"
#ifdef USE_SONOAV
#include "VasDet.h"
#else
enum MODE_TYPE
{
    MODEB,
    MODEC,
    MODED
};
namespace AV
{
typedef struct OBJS_STRUCT
{
    int x;
    int y;
    int w;
    int h;
    int nameid;
    float conf;
} OBJ;
} // namespace AV
#define MAXINITPARAMS 6
#define MAXCAROTID_VESSEL_NUM 3  //颈动脉检测最大血管数量
#define MAXARM_VESSEL_NUM 5      //手臂血管检测最大血管数量
#define MAXAXILLARY_VESSEL_NUM 1 //腋下血管检测最大血管数量
typedef unsigned char uint8_t;
typedef void (*callback_acquire_image_mode)(MODE_TYPE mode, float* paramsin, uint8_t* imgptr, int* width, int* height,
                                            float* paramsout);
#endif

class ALGORITHMSHARED_EXPORT SonoAVAlg : public VirtualAlg
{
    friend class AlgInstance;

private:
    SonoAVAlg();

public:
    inline bool isError(void)
    {
        return m_IsError;
    }

    typedef void (*VasdetModelPathSet)(char* path);
    typedef bool (*CompareRectangle)(int x1, int y1, int width1, int height1, int x2, int y2, int width2, int height2,
                                     float compare_iou_thre);
    typedef int (*CarotidVesselDetInit)(float conf, float params[MAXINITPARAMS]);
    typedef int (*CarotidVesselDet)(uint8_t* imgdata, int width, int height, int roix, int roiy, int roiwidth,
                                    int roiheight, AV::OBJ* objsp);
    typedef void (*CarotidVesselDetRelease)();
    typedef int (*ArmVesselDetInit)(float conf, float params[MAXINITPARAMS]);
    typedef int (*ArmVesselDet)(uint8_t* imgdata, int width, int height, int roix, int roiy, int roiwidth,
                                int roiheight, AV::OBJ* objsp);
    typedef void (*ArmVesselDetRelease)();
    typedef int (*AxillaryVesselDetInit)(float conf, float params[MAXINITPARAMS]);
    typedef int (*AxillaryVesselDet)(uint8_t* imgdata, int width, int height, int roix, int roiy, int roiwidth,
                                     int roiheight, AV::OBJ* objsp);
    typedef void (*AxillaryVesselDetRelease)();
    typedef int (*VesselPwClsInit)(float conf, float params[MAXINITPARAMS]);
    typedef int (*VesselPwCls)(uint8_t* imgdata, int width, int height, float* probability);
    typedef void (*VesselPwClsRelease)();

    void vasdetModelPathSet(char* path);
    bool compareRectangle(int x1, int y1, int width1, int height1, int x2, int y2, int width2, int height2,
                          float compare_iou_thre);
    int carotidVesselDetInit(float conf, float params[MAXINITPARAMS]);
    int carotidVesselDet(uint8_t* imgdata, int width, int height, int roix, int roiy, int roiwidth, int roiheight,
                         AV::OBJ* objsp);
    void carotidVesselDetRelease();
    int armVesselDetInit(float conf, float params[MAXINITPARAMS]);
    int armVesselDet(uint8_t* imgdata, int width, int height, int roix, int roiy, int roiwidth, int roiheight,
                     AV::OBJ* objsp);
    void armVesselDetRelease();
    int axillaryVesselDetInit(float conf, float params[MAXINITPARAMS]);
    int axillaryVesselDet(uint8_t* imgdata, int width, int height, int roix, int roiy, int roiwidth, int roiheight,
                          AV::OBJ* objsp);
    void axillaryVesselDetRelease();
    int vesselPwClsInit(float conf, float params[MAXINITPARAMS]);
    int vesselPwCls(uint8_t* imgdata, int width, int height, float* probability);
    void vesselPwClsRelease();

    VasdetModelPathSet m_VasdetModelPathSet;
    CompareRectangle m_CompareRectangle;
    CarotidVesselDetInit m_CarotidVesselDetInit;
    CarotidVesselDet m_CarotidVesselDet;
    CarotidVesselDetRelease m_CarotidVesselDetRelease;
    ArmVesselDetInit m_ArmVesselDetInit;
    ArmVesselDet m_ArmVesselDet;
    ArmVesselDetRelease m_ArmVesselDetRelease;
    AxillaryVesselDetInit m_AxillaryVesselDetInit;
    AxillaryVesselDet m_AxillaryVesselDet;
    AxillaryVesselDetRelease m_AxillaryVesselDetRelease;
    VesselPwClsInit m_VesselPwClsInit;
    VesselPwCls m_VesselPwCls;
    VesselPwClsRelease m_VesselPwClsRelease;

    bool carotidInit() const;
    bool ArmInit() const;
    bool AxillaryInit() const;
    bool VesselInit() const;

private:
    bool m_CarotidInit;
    bool m_ArmInit;
    bool m_AxillaryInit;
    bool m_VesselInit;
};

#endif // SONOAVALG_H
