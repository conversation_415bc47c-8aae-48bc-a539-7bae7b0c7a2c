add_definitions(-DARCHIVE_LIBRARY)

if(${USE_4D})
    set(FOURD_INCLUDE_DIR_NAME fourd)
	set(FOURD_LIB_NAME fourd)
endif()

include_depends(thumbnailview patientworkflow systeminfo reportview dicomtools beamformer dicommodel dicomview measurementmodel utilitymodel ${FOURD_INCLUDE_DIR_NAME} adminmodel)

add_library_qt(archive 
    archivebuttonwidget.cpp
    archiveimagelabel.cpp
    archivemanager.cpp
    archivemodel.cpp
    archivetopwidget.cpp
    backupmodel.cpp
    basefilesystemview.cpp
    bodyinfowidget.cpp
    cardinfowidget.cpp
    confirmdialog.cpp
    copythread.cpp
    databackupwidget.cpp
    databasepatientviewmodel.cpp
    databasestudyviewmodel.cpp
    databaseviewmodel.cpp
    deletethread.cpp
    exportfilewidget.cpp
    networkstoragefilewidget.cpp
    fileoperationthread.cpp
    filesystemmodel.cpp
    filetreeview.cpp
    filiationtreeview.cpp
    filiationtreeviewmodel.cpp
    generalworkflowfunction.cpp
    glanceimages.cpp
    glanceimagesmodel.cpp
    glanceimagestopwidget.cpp
    obinfowidget.cpp
    patienteditmodel.cpp
    patienteditwidget.cpp
    patienthaterinfowidget.cpp
    patienthomogeneouswidget.cpp
    patientmiddlemodel.cpp
    patientmiddlewidget.cpp
    patienttopwidget.cpp
    patientworkflowmodel.cpp
    progressbarview.cpp
    showpicture.cpp
    threadmodel.cpp
    treeitem.cpp
    treeviewbutton.cpp
    treeviewwidget.cpp
	dicomseverinfowidget.cpp
	dicommodel.cpp
	dicomserverwidget.cpp
	worklistsearchwidget.cpp
    imageprocessedcopy.cpp
    imageunprocessedcopy.cpp
	makedcmfilesoptionwidget.cpp
    patientworkflowworker.cpp
	filesexporter.cpp
    burnthread.cpp
	patientinfoinputwidget.cpp
	inputunitgroupwidget.cpp
	examdatebetweenwidget.cpp
	queryretrievewidget.cpp
	mppssendcontrol.cpp
        bluetoothstoragefilewidget.cpp
        sonoimtinfowidget.cpp
        )

target_link_libraries(archive dicomtools thumbnailview patientworkflow systeminfo reportview dicommodel dicomview  measurementmodel utilitymodel ${FOURD_LIB_NAME} adminmodel)
