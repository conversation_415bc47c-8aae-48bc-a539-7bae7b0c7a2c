#ifndef CONFIRMDIALOG_H
#define CONFIRMDIALOG_H
#include "archive_global.h"

#include <QDialog>
#include "baseframe.h"
#include "basedialog.h"
#include "basewidget.h"

/**
 * @brief 复制文件时提示信息
 */
class ConfirmDialog;
class ARCHIVESHARED_EXPORT ConfirmDialogFrame : public BaseFrame
{
    Q_OBJECT

public:
    explicit ConfirmDialogFrame(QWidget* parent = 0);

    void setButtonState(bool value);
    void setText(const QString& str);
    void excuteFrame();
signals:
    void clickedButton(int value);

protected:
    bool eventFilter(QObject* obj, QEvent* event);
private slots:
    void onClose();

private:
    ConfirmDialog* m_Dlg;
};

namespace Ui
{
class ConfirmDialog;
}

class ARCHIVESHARED_EXPORT ConfirmDialog : public BaseDialog
{
    Q_OBJECT

public:
    explicit ConfirmDialog(QWidget* parent = 0);
    ~ConfirmDialog();
    enum Operation
    {
        Cancel,
        SkipAll,
        ReplaceAll,
        SkipOnce,
        ReplaceOnce,
        Count
    };
    Operation opeResult();
    void setButtonState(bool value);
    void setText(const QString& str);

signals:
    void clickedButton(int value);
    void hideDlg();

protected:
    void retranslateUi();

private slots:
    void on_cancelButton_clicked();
    void on_skipAllButton_clicked();
    void on_replaceAllButton_clicked();
    void on_skipButton_clicked();
    void on_replaceButton_clicked();
    void onExit();

private:
    Ui::ConfirmDialog* ui;
    Operation m_Operation;
};

#endif // CONFIRMDIALOG_H
