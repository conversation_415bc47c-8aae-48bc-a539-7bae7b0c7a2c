#ifndef COPYTHREAD_H
#define COPYTHREAD_H
#include "archive_global.h"

#include <QThread>
#include "fileoperationthread.h"
#include "threadcontroller.h"
#include "customcopy.h"

class ARCHIVESHARED_EXPORT CopyThread : public FileOperationThread, public ThreadController
{
    Q_OBJECT
public:
    const static int COPY_IN_CURDIR = 0;
    const static int COPY_IN_ITSELF = 1;
    explicit CopyThread(QObject* parent = 0);
    void setCopyArgs(const QStringList& list);
    void setCurPath(const QString& path);
    void setRootPath(const QString& rootPath);
    void setICopy(ICopy* copy);

    void setConfirmValue(int value);
    void setCopyState(bool state);
    void setChangeModTime(bool value);
    bool isVolumeEnough();
    const QString& copyingFile() const;
    static bool isFileExists(const QString& fileName, const QString& path);
    void setIsMkPatientDir(bool enable);
    bool isCopyFailed() const;
    void setIsMkPatientWithNameDir(bool enable);
    void setNeedSingleFrameImgData(bool value);
    void setNeedCreateEmptyCopy(bool value);
    void setNotCopyFileSuffixFilters(const QStringList& value);

protected:
    void run();
    virtual void checkIfNeedToChangeFileList(QFileInfoList& list);

private:
    QStringList fileNameList(const QString& path);
    QStringList filePathList(const QString& path);
    void copy(const QStringList& paths, const QString& dest);
    QStringList calcSameFiles(const QStringList& list, const QString& target);
    QStringList sameBaseNames(const QStringList& list);

signals:
    void showConfirmDlg(bool value);
    void curValue(qint64 value);
    void curHaveCopyed(qint64 value);
    void denyPaste(int value);
    void changeValue(int value);
    void rptFileCopyed(const QString& dirName);
private slots:
    void setMkPatientDirFall();
    void setMkPatientDirWithNameFall();

private:
    QStringList m_CopyList;
    QString m_CurPath;
    CustomCopy m_Copy;
    bool m_Confirm;

    //---
    // m_Value
    // 0 = Cancel
    // 1 = SkipAll
    // 2 = ReplaceAll
    // 3 = SkipOnce
    // 4 = ReplaceOnce
    int m_Value;
    QStringList m_SameList;
    QStringList m_SameBaseName;
    QString m_CopyingFile;
    bool m_IsmkPatientDir;
    bool m_isCopyFailed;
    bool m_IsmkPatientWithNameDir;
};

#endif // COPYTHREAD_H
