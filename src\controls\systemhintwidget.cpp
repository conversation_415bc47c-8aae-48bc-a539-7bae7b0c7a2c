#include "systemhintwidget.h"
#include "ui_systemhintwidget.h"
#include "systemhintmodel.h"
#include "multifuncoperationareamodel.h"

SystemHintWidget::SystemHintWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::SystemHintWidget)
{
    ui->setupUi(this);
}

SystemHintWidget::~SystemHintWidget()
{
    delete ui;
}

void SystemHintWidget::setModel(SystemHintModel* value)
{
    if (ui->stackedWidget->currentIndex() == 0)
    {
        ui->widgetDefault->setModel(value);
    }
    else if (ui->stackedWidget->currentIndex() == 1)
    {
        ui->widgetMultiFunc->setModel(value);
    }
}

void SystemHintWidget::setCurrentIndex(int index)
{
    if (index >= 0 && index < ui->stackedWidget->count())
    {
        ui->stackedWidget->setCurrentIndex(index);
    }
}

void SystemHintWidget::retranslateUi()
{
}
