#include "examdatebetweenwidget.h"
#include "ui_examdatebetweenwidget.h"

ExamDateBetweenDialog::ExamDateBetweenDialog(QWidget* parent)
    : BaseInputAbleDialogFrame(parent)
    , m_Child(NULL)
{
    setIsShowOk(false);
    m_Child = new ExamDateBetweenWidget(this);
    connect(m_Child, SIGNAL(accept()), this, SLOT(accept()));
    this->setContent(m_Child);
}

QString ExamDateBetweenDialog::fromToDateString() const
{
    return m_Child->fromToDateString();
}

void ExamDateBetweenDialog::setFromToDateString(const QString& date)
{
    m_Child->setFromToDateString(date);
}

ExamDateBetweenWidget::ExamDateBetweenWidget(QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::ExamDateBetweenWidget)
    , m_DateString(QString())
{
    ui->setupUi(this);

    ui->dateEditFrom->setDate(QDate::currentDate());
    ui->dateEditTo->setDate(QDate::currentDate());
}

ExamDateBetweenWidget::~ExamDateBetweenWidget()
{
    delete ui;
}

QString ExamDateBetweenWidget::fromToDateString() const
{
    return m_DateString;
}

void ExamDateBetweenWidget::setFromToDateString(const QString& date)
{
    QStringList dateList = date.split("-");
    if (dateList.count() == 2)
    {
        QString from(dateList.at(0));
        QString to(dateList.at(1));
        ui->dateEditFrom->setDate(QDate::fromString(from, "yyyyMMdd"));
        ui->dateEditTo->setDate(QDate::fromString(to, "yyyyMMdd"));
    }
}

void ExamDateBetweenWidget::on_pushButtonOK_clicked()
{
    QString fromDate = ui->dateEditFrom->text();
    QString toDate = ui->dateEditTo->text();
    m_DateString = fromDate.replace("-", "") + "-" + toDate.replace("-", "");
    emit accept();
}

void ExamDateBetweenWidget::on_pushButtonCancle_clicked()
{
    m_DateString.clear();
    emit accept();
}
