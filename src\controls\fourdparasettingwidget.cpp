#include "fourdparasettingwidget.h"
#include "ui_fourdparasettingwidget.h"
#include "parameter.h"
#include "bfpnames.h"

FourDParaSettingWidget::FourDParaSettingWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::FourDParaSettingWidget)
{
    ui->setupUi(this);
}

FourDParaSettingWidget::~FourDParaSettingWidget()
{
    delete ui;
}

void FourDParaSettingWidget::initalize()
{
    ui->fourdQualityWidget->setSonoParameters(m_SonoParameters);
    ui->fourdQualityWidget->initalize();

    ui->fourdLightAngleWidget->setSonoParameters(m_SonoParameters);
    ui->fourdLightAngleWidget->initalize();

    ui->fourdPresetWidget->setSonoParameters(m_SonoParameters);
    ui->fourdPresetWidget->initalize();

    ui->fourdRenderModeWidget->setSonoParameters(m_SonoParameters);
    ui->fourdRenderModeWidget->initalize();

    connect(ui->fourdPresetWidget, SIGNAL(fourDPresetParasChanged()), ui->fourdRenderModeWidget,
            SLOT(onFourDPresetParasChanged()));
    connect(ui->fourdPresetWidget, SIGNAL(fourdPresetParasDeleted(int)), ui->fourdRenderModeWidget,
            SLOT(onFourDPresetParaDeleted(int)));
    on_comboBox_currentIndexChanged(ui->comboBox->currentIndex());
}

void FourDParaSettingWidget::onSetSonoParameters()
{
    initalize();
}

void FourDParaSettingWidget::retranslateUi()
{
    ui->retranslateUi(this);
}

void FourDParaSettingWidget::on_comboBox_currentIndexChanged(int index)
{
    if (index >= 0 && index < ui->stackedWidget_fourd->count())
    {
        ui->stackedWidget_fourd->setCurrentIndex(index);
    }
}
