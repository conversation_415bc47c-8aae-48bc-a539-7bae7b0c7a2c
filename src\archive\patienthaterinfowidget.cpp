#include "patienthaterinfowidget.h"
#include "ui_patienthaterinfowidget.h"
#include "setting.h"
#include "appsetting.h"

PatientHaterInfoWidget::PatientHaterInfoWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::PatientHaterInfoWidget)
{
    ui->setupUi(this);
    setCurrentIndex(0);
    connect(ui->widgetBody, SIGNAL(baseInfo(QMap<QString, QStringList>)), this,
            SLOT(setInfo(QMap<QString, QStringList>)));
    connect(ui->widgetOB, SIGNAL(baseInfo(QMap<QString, QStringList>)), this,
            SLOT(setInfo(QMap<QString, QStringList>)));
    connect(ui->widgetCard, SIGNAL(baseInfo(QMap<QString, QStringList>)), this,
            SLOT(setInfo(QMap<QString, QStringList>)));
    connect(this, SIGNAL(patientInfoUnitChanged(QString, int)), ui->widgetBody,
            SLOT(onPatientInfoUnitChanged(QString, int)));
    connect(this, SIGNAL(patientInfoUnitChanged(QString, int)), ui->widgetOB,
            SLOT(onPatientInfoUnitChanged(QString, int)));
    connect(this, SIGNAL(patientInfoUnitChanged(QString, int)), ui->widgetCard,
            SLOT(onPatientInfoUnitChanged(QString, int)));
    connect(&Setting::instance().defaults(), SIGNAL(settingChanged(QString)), this,
            SLOT(onPatientInfoUnitChanged(QString)));
}

PatientHaterInfoWidget::~PatientHaterInfoWidget()
{
    delete ui;
}

void PatientHaterInfoWidget::setCurrentIndex(const int index)
{
    int realIndex = index;
    if (AppSetting::isAnimal())
    {
        realIndex = getRealIndex(index);
    }

    switch (realIndex)
    {
    case 0:
        ui->stackedWidget->setCurrentIndex(URO);
        ui->widgetBody->setIMTVisible(false);
        showWidget(2);
        break;
    case 1:
        ui->stackedWidget->setCurrentIndex(OB);
        ui->widgetBody->setIMTVisible(false);
        showWidget(0);
        break;
    case 2:
        ui->stackedWidget->setCurrentIndex(OB);
        ui->widgetBody->setIMTVisible(false);
        showWidget(0);
        break;
    case 3:
        ui->stackedWidget->setCurrentIndex(CARD);
        ui->widgetBody->setIMTVisible(false);
        showWidget(1);
        break;
    case 4:
        ui->stackedWidget->setCurrentIndex(URO);
        ui->widgetBody->setIMTVisible(false);
        showWidget(3);
        break;
    case 5:
        ui->stackedWidget->setCurrentIndex(URO);
        ui->widgetBody->setIMTVisible(false);
        showWidget(2);
        break;
    case 6:
        ui->stackedWidget->setCurrentIndex(URO);
        ui->widgetBody->setIMTVisible(false);
        showWidget(2);
        break;
    case 7:
        ui->stackedWidget->setCurrentIndex(URO);
        ui->widgetBody->setIMTVisible(true);
        showWidget(2);
        break;
    }
}

void PatientHaterInfoWidget::setStudy(Study* study)
{
    ui->widgetOB->setStudy(study);
    ui->widgetBody->setStudy(study);
    ui->widgetCard->setStudy(study);
}

void PatientHaterInfoWidget::flawlessStudy(Study* study)
{
    ui->widgetOB->flawlessStudy(study);
    ui->widgetBody->flawlessStudy(study);
    ui->widgetCard->flawlessStudy(study);
}

void PatientHaterInfoWidget::clearStudyInfo()
{
    ui->widgetOB->clearStudyInfo();
    ui->widgetBody->clearStudyInfo();
    ui->widgetCard->clearStudyInfo();
}

void PatientHaterInfoWidget::setCurrentWidgetEnabled(bool enabled)
{
    if (enabled)
    {
        ui->widgetOB->updateGA();
    }
    ui->widgetOB->setCurrentWidgetEnabled(enabled);
    ui->widgetBody->setCurrentWidgetEnabled(enabled);
    ui->widgetCard->setCurrentWidgetEnabled(enabled);
}

void PatientHaterInfoWidget::setAnimalSpeciesIndex(int index)
{
    ui->widgetOB->setAnimalSpeciesIndex(index);
    ui->widgetBody->setAnimalSpeciesIndex(index);
    ui->widgetCard->setAnimalSpeciesIndex(index);
}

void PatientHaterInfoWidget::setWidgetVisible(bool visable)
{
    ui->widgetBody->setVisible(visable);
    ui->widgetCard->setVisible(visable);
    ui->widgetOB->setVisible(visable);
}

void PatientHaterInfoWidget::showWidget(int index)
{
    //    setWidgetVisible(false);
    switch (index)
    {
    case 0:
        ui->widgetBody->setVisible(false);
        ui->widgetCard->setVisible(false);
        ui->widgetOB->setVisible(true);
        break;
    case 1:
        ui->widgetBody->setVisible(false);
        ui->widgetCard->setVisible(true);
        ui->widgetOB->setVisible(false);
        break;
    case 2:
        ui->widgetBody->setVisible(true);
        ui->widgetBody->setWidgetVisible(false);
        ui->widgetCard->setVisible(true);
        ui->widgetOB->setVisible(false);
        break;
    case 3:
        ui->widgetBody->setVisible(true);
        ui->widgetBody->setWidgetVisible(true);
        ui->widgetCard->setVisible(true);
        ui->widgetOB->setVisible(false);
        break;
    }
}

void PatientHaterInfoWidget::retranslateUi()
{
    ui->retranslateUi(this);
}

void PatientHaterInfoWidget::setInfo(const QMap<QString, QStringList>& info)
{
    ui->widgetBody->setInfo(info);
    ui->widgetOB->setInfo(info);
    ui->widgetCard->setInfo(info);
}

void PatientHaterInfoWidget::onPatientInfoUnitChanged(const QString& str)
{
    if (str == "patientHeightUnit")
    {
        emit patientInfoUnitChanged("height", Setting::instance().defaults().patientHeightUnitIndex());
    }
    else if (str == "patientWeightUnit")
    {
        emit patientInfoUnitChanged("weight", Setting::instance().defaults().patientWeightUnitIndex());
    }
}

int PatientHaterInfoWidget::getRealIndex(const int index)
{
    // human  0 1 2 3 4 5 6 7 -->> 0 1 2 3 4 5 6 7
    // vet    0 1 2 3         -->> 0 1 3 5

    int ret = index;
    if (AppSetting::isAnimal())
    {
        switch (index)
        {
        case 2:
            ret = 3;
            break;
        case 3:
            ret = 5;
            break;
        default:
            break;
        }
    }
    return ret;
}
