<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ArchiveTopWidget</class>
 <widget class="QWidget" name="ArchiveTopWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1013</width>
    <height>67</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout" columnstretch="0,0" columnminimumwidth="0,0">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QWidget" name="widget" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout_5">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0">
        <property name="spacing">
         <number>0</number>
        </property>
        <item>
         <widget class="QLabel" name="label">
          <property name="text">
           <string>Item</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="BaseComboBox" name="comboBoxItem">
          <item>
           <property name="text">
            <string>ID</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Name</string>
           </property>
          </item>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="0,0">
        <property name="spacing">
         <number>0</number>
        </property>
        <item>
         <widget class="QLabel" name="label_2">
          <property name="text">
           <string>Keyword</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="lineEditkey"/>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="0,0">
        <property name="spacing">
         <number>0</number>
        </property>
        <item>
         <widget class="QLabel" name="label_4">
          <property name="text">
           <string>Period</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="BaseComboBox" name="comboBoxTime">
          <property name="currentIndex">
           <number>4</number>
          </property>
          <item>
           <property name="text">
            <string>Today</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>One Week</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>One Month</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Three Months</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Six Months</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>One Year</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>All</string>
           </property>
          </item>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="QWidget" name="widget_2" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout_6" stretch="0,0">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QCheckBox" name="checkBox">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Multiple Choice</string>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="0,0">
        <property name="spacing">
         <number>0</number>
        </property>
        <item>
         <widget class="QLabel" name="label_3">
          <property name="text">
           <string>Data Source</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="DiskPathComboBox" name="comboBoxDisk">
          <property name="minimumSize">
           <size>
            <width>120</width>
            <height>0</height>
           </size>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BaseComboBox</class>
   <extends>QComboBox</extends>
   <header>basecombobox.h</header>
  </customwidget>
  <customwidget>
   <class>DiskPathComboBox</class>
   <extends>QComboBox</extends>
   <header>diskpathcombobox.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
