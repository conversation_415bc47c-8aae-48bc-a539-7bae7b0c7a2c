#
# The module defines the following variables:
# IMGVER_FOUND - True if IMGVE<PERSON> found.
# IMGVER_INCLUDE_DIRS - where to find *.h, etc.
# IMGVER_LIBRARIES - List of libraries when using IMGVER.
#

thirdparty_prefix_path(imgver)

find_path ( IMGVER_INCLUDE_DIR
            NAMES
                getimgver.h
            HINTS
                ${IMGVER_ROOT}/include
            )
set(IMGVER_INCLUDE_DIRS ${IMGVER_INCLUDE_DIR})
message("Found imgver headers: ${IMGVER_INCLUDE_DIRS}")


find_library ( IMGVER_LIBRARY
            NAMES
                imgver
            HINTS
                ${IMGVER_ROOT_DIR}/lib
                ${IMGVER_ROOT}/lib
             )
set(IMGVER_LIBRARIES ${IMGVER_LIBRARY})
message("Found imgver libs: ${IMGVER_LIBRARIES}")
