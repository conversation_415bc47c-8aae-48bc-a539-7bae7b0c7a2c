#!/bin/bash

echo_usage()
{
    cat <<-END
usage: 
    ./check_autoupgrade_config.sh [-h] [-m <model>]

Options:
    -h                              help
    -m <model>                      model, like Phoenix, Apple etc.     

examples:


END
    exit 0
}

while getopts "hm:" arg
do
    case $arg in
        h)  echo_usage;;
        m)  model=$OPTARG;;
    esac
done

func_read_update_ini(){
while read line
do
    if [[ -n $line ]];then
        group_str=$(echo "$line"  | grep -n '\[*\]' | awk -F "[" '{print $2}' | awk -F "]" '{print $1}') 
        if [[ -n $group_str ]];then
            group=$group_str
            isGroup=1
        fi
    else
        func_init     
    fi
    func_get_value $isGroup

    func_ini_is_correct

done <<< "$(cat $1)"
}

func_init(){
    type=-1
    path=""
    version=""
    group=""
    isGroup=0      
}

func_get_value(){
    if [[ "$1" == "1" ]];then
        if [[ "${line:0:1}" != "#" ]];then 
            if [[ "$(echo "$line" | awk -F '=' '{print $1}')" = "Type" ]];then
                type="$(echo "$line" | awk -F '=' '{print $2}')"
            fi
                 if [[ "$(echo "$line" | awk -F '=' '{print $1}')" = "Path" ]];then
                path="$(echo "$line" | awk -F '=' '{print $2}')"
            fi
                        if [[ "$(echo "$line" | awk -F '=' '{print $1}')" = "Version" ]];then
                version="$(echo "$line" | awk -F '=' '{print $2}')"
            fi
        fi
    fi
}

func_ini_is_correct(){
    if [[ -n $path && -n $version && $type >0 ]]; then
        newType="$(echo $version | sed 's/[^0-9]//g')"
        if [[ ${newType:0:1} == "$type" ]];then
            isPathExisted
            ver=`awk -F ':' '{print $2}' $txt`
            cd $updateDir
            if [[ $ver != $version ]];then
                echo "version is not match $group ver is $version , $txt ver is $ver"
                exit 1
            fi
        else
            echo "type is not match $group type is $type , version is $version"
            exit 1
        fi
    fi

    if [[ -n $path && $type = 0 ]]; then
        isPathExisted
    fi
}

isPathExisted(){
    upgradePath=$PWD/$path`ls $PWD/$path`
    if [[ ! -d $upgradePath ]]; then
        echo "$upgradePath path is not existed"
        exit 1
    fi
    cd $upgradePath
    txt=${upgradePath##*_}".txt"
    if [[ ! -f $txt ]]; then
        echo "$PWD/$txt file is not existed"
        exit 1
    fi
}

xunitDir=$PWD
updateDir=~/code/resource-xunit/res/modelfiles/$model/update
if [[ ! -d $updateDir ]]; then
    exit 0
fi
cd $updateDir
updateDir=$PWD
func_init
func_read_update_ini "update.ini"
cd $xunitDir

