#include "queryretrievewidget.h"
#include "PersonName.h"
#include "applogger.h"
#include "appsetting.h"
#include "commonprogressbar.h"
#include "dataaccesslayerhelper.h"
#include "dicomregister.h"
#include "examdatebetweenwidget.h"
#include "globaldef.h"
#include "i18nstrings.h"
#include "messageboxframe.h"
#include "modeldirectorygetter.h"
#include "patient.h"
#include "patientsage.h"
#include "resource.h"
#include "setting.h"
#include "stateeventnames.h"
#include "statefilterlocker.h"
#include "study.h"
#include "syncer.h"
#include "ui_queryretrievewidget.h"
#include "util.h"
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QFileSystemWatcher>
#include <QProcess>
#include <QStandardItem>
#include <QStandardItemModel>
#include <QThreadPool>

const char* const QueryRetrieveWidget::m_AnimalString[] = {QT_TRANSLATE_NOOP("QueryRetrieveWidget", "Animal ID"),
                                                           QT_TRANSLATE_NOOP("QueryRetrieveWidget", "Animal Name")};

QueryRetrieveDialog::QueryRetrieveDialog(IStateManager* stateManager, QWidget* parent)
    : BaseInputAbleDialogFrame(parent)
    , m_Child(NULL)
{
    setIsShowOk(false);
    m_Child = new QueryRetrieveWidget(stateManager, this);
    connect(m_Child, SIGNAL(accept()), this, SLOT(accept()));
    connect(this, SIGNAL(closed()), m_Child, SLOT(onClose()));
    this->setContent(m_Child);
}

void QueryRetrieveDialog::clearWidget()
{
    m_Child->clearWidget();
}

void QueryRetrieveDialog::doQueryTask()
{
    m_Child->doQueryTask();
}

void QueryRetrieveDialog::showEvent(QShowEvent* e)
{
    BaseInputAbleDialogFrame::showEvent(e);
    //采用异步的方式执行初始查询,解决查询后edit不能编辑的问题,具体原因不确定,只能知道大概原因:
    // WorkListSearchDialog.exec函数中会触发showEvent,如果使用同步方式这时会被m_ProgressBar阻塞,
    //导致dialog.exec函数未执行完毕
    metaObject()->invokeMethod(this, "doQueryTask", Qt::QueuedConnection);
}

QueryRetrieveWidget::QueryRetrieveWidget(IStateManager* stateManager, QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::QueryRetrieveWidget)
    , m_QueryStandardmodel(NULL)
    , m_RetrieveStandardmodel(NULL)
    , m_ProgressBar(new CommonProgressBarFrame(nullptr))
    , m_RetrievingProcess(NULL)
    , m_ConfirmValue(QMessageBox::NoButton)
    , m_RetrieveDcmFileCount(0)
    , m_ReadyClose(false)
    , m_args(QStringList())
    , m_Model(ScreenOrientationModel::getInstance())
    , m_StateManager(stateManager)
{
    m_ProgressBar->setFrameTitle(tr("Querying..."));
    m_ProgressBar->hide();
    ui->setupUi(this);

    ui->retrieveProgressBar->hide();
    ui->cancelRetrieveButton->hide();
    ui->retrieveProgressBar->setValue(0);
    ui->retrieveProgressBar->setFormat(QString());

    ui->dateEdit->hide();
    initTreeView();
    connect(this, SIGNAL(findscuStarted()), this, SLOT(onFindscuStarted()));
    connect(this, SIGNAL(findscuFinished(int)), this, SLOT(getRespond(int)));
    connect(ui->lineEditSearchValue, SIGNAL(clicked()), this, SLOT(onLineEditSearchValueClicked()));
    connect(this, SIGNAL(progressValueChanged(int)), ui->retrieveProgressBar, SLOT(setValue(int)));

    if (AppSetting::isAnimalAndChinese())
    {
        ui->labelPatientID->setText(
            QCoreApplication::translate("QueryRetrieveWidget", m_AnimalString[Animal_ID], nullptr));
        ui->labelPatientName->setText(
            QCoreApplication::translate("QueryRetrieveWidget", m_AnimalString[Animal_Name], nullptr));
    }
}

QueryRetrieveWidget::~QueryRetrieveWidget()
{
    delete ui;
}

void QueryRetrieveWidget::doQueryTask()
{
    if (DicomRegister::instance().checkIfDicomQRUsable())
    {
        if (m_QueryStandardmodel->rowCount() > 0)
        {
            m_QueryStandardmodel->removeRows(0, m_QueryStandardmodel->rowCount());
        }
        ui->selectAllButton->setEnabled(false);
        ui->deselectButton->setEnabled(false);
        ui->retrieveButton->setEnabled(false);

        DicomQueryTask* task = new DicomQueryTask(this);

        Util::runRunnable(task);
    }
}

void QueryRetrieveWidget::doRetrieveTask()
{
    if (DicomRegister::instance().checkIfDicomQRUsable())
    {
        DicomRetrieveTask* task = new DicomRetrieveTask(this);

        Util::runRunnable(task);
    }
}

void QueryRetrieveWidget::clearWidget()
{
    on_clearButton_clicked();
    ui->retrieveProgressBar->hide();
    ui->cancelRetrieveButton->hide();
    ui->retrieveProgressBar->setValue(0);
    ui->dateEdit->hide();
    m_ReadyClose = false;
    if (m_QueryStandardmodel->rowCount() > 0)
    {
        m_QueryStandardmodel->removeRows(0, m_QueryStandardmodel->rowCount());
    }

    if (m_RetrieveStandardmodel->rowCount() > 0)
    {
        m_RetrieveStandardmodel->removeRows(0, m_RetrieveStandardmodel->rowCount());
    }
}

bool QueryRetrieveWidget::isHor()
{
    return m_Model->isHor();
}

void QueryRetrieveWidget::showEvent(QShowEvent* e)
{
    ui->dateEdit->setFixedWidth(ui->lineEditSearchValue->width());
    QWidget::showEvent(e);
}

void QueryRetrieveWidget::retranslateUi()
{
    ui->retranslateUi(this);
    if (AppSetting::isAnimalAndChinese())
    {
        ui->labelPatientID->setText(
            QCoreApplication::translate("QueryRetrieveWidget", m_AnimalString[Animal_ID], nullptr));
        ui->labelPatientName->setText(
            QCoreApplication::translate("QueryRetrieveWidget", m_AnimalString[Animal_Name], nullptr));
        m_QueryStandardmodel->horizontalHeaderItem(0)->setText(
            QCoreApplication::translate("QueryRetrieveWidget", m_AnimalString[Animal_ID], nullptr));
        m_QueryStandardmodel->horizontalHeaderItem(1)->setText(
            QCoreApplication::translate("QueryRetrieveWidget", m_AnimalString[Animal_Name], nullptr));
        m_RetrieveStandardmodel->horizontalHeaderItem(0)->setText(
            QCoreApplication::translate("QueryRetrieveWidget", m_AnimalString[Animal_ID], nullptr));
        m_RetrieveStandardmodel->horizontalHeaderItem(1)->setText(
            QCoreApplication::translate("QueryRetrieveWidget", m_AnimalString[Animal_Name], nullptr));

        if (!ui->labelRecords->text().isEmpty())
        {
            ui->labelRecords->setText(
                tr("Animal(Source) %1 record(s) are listed.").arg(m_QueryStandardmodel->rowCount()));
        }
    }
    else
    {
        m_QueryStandardmodel->horizontalHeaderItem(0)->setText(
            QCoreApplication::translate("QueryRetrieveWidget", "Patient ID", nullptr));
        m_QueryStandardmodel->horizontalHeaderItem(1)->setText(
            QCoreApplication::translate("QueryRetrieveWidget", "Patient Name", nullptr));
        m_RetrieveStandardmodel->horizontalHeaderItem(0)->setText(
            QCoreApplication::translate("QueryRetrieveWidget", "Patient ID", nullptr));
        m_RetrieveStandardmodel->horizontalHeaderItem(1)->setText(
            QCoreApplication::translate("QueryRetrieveWidget", "Patient Name", nullptr));

        if (!ui->labelRecords->text().isEmpty())
        {
            ui->labelRecords->setText(
                tr("Patient(Source) %1 record(s) are listed.").arg(m_QueryStandardmodel->rowCount()));
        }
    }
}

void QueryRetrieveWidget::initTreeView()
{
    QStringList header;

    if (AppSetting::isAnimalAndChinese())
    {
        header << tr("Animal ID") << tr("Animal Name");
    }
    else
    {
        header << tr("Patient ID") << tr("Patient Name");
    }

    header << QApplication::translate("Sex", Resource::sexLabel().toUtf8().data(), 0) << tr("BirthDate") << tr("Acc#")
           << tr("Doctor") << tr("Images") << tr("Study Date") << "StudyInstanceID";

    m_QueryStandardmodel = new QStandardItemModel(0, header.count(), this);
    m_QueryStandardmodel->setHorizontalHeaderLabels(header);

    m_RetrieveStandardmodel = new QStandardItemModel(0, header.count(), this);
    m_RetrieveStandardmodel->setHorizontalHeaderLabels(header);

    QStandardItem* itemPrototypeQuery = new QStandardItem;
    itemPrototypeQuery->setEditable(false);
    m_QueryStandardmodel->setItemPrototype(itemPrototypeQuery);

    QStandardItem* itemPrototypeRetrieve = new QStandardItem;
    itemPrototypeRetrieve->setEditable(false);
    m_RetrieveStandardmodel->setItemPrototype(itemPrototypeRetrieve);

    ui->treeViewQuery->setRootIsDecorated(false); //隐藏root
    ui->treeViewQuery->setModel(m_QueryStandardmodel);
    ui->treeViewQuery->hideColumn(header.size() - 1);

    ui->treeViewRetrieve->setRootIsDecorated(false); //隐藏root
    ui->treeViewRetrieve->setModel(m_RetrieveStandardmodel);
    ui->treeViewRetrieve->hideColumn(header.size() - 1);

    ui->treeViewQuery->setColumnWidth(0, 180);
    ui->treeViewQuery->setColumnWidth(1, 200);
    ui->treeViewQuery->setColumnWidth(2, 40);
    ui->treeViewQuery->setColumnWidth(3, 140);
    ui->treeViewQuery->setColumnWidth(4, 160);
    ui->treeViewQuery->setColumnWidth(5, 160);
    ui->treeViewQuery->setColumnWidth(6, 80);
    ui->treeViewQuery->setColumnWidth(7, 140);

    ui->treeViewRetrieve->setColumnWidth(0, 180);
    ui->treeViewRetrieve->setColumnWidth(1, 200);
    ui->treeViewRetrieve->setColumnWidth(2, 40);
    ui->treeViewRetrieve->setColumnWidth(3, 140);
    ui->treeViewRetrieve->setColumnWidth(4, 160);
    ui->treeViewRetrieve->setColumnWidth(5, 160);
    ui->treeViewRetrieve->setColumnWidth(6, 80);
    ui->treeViewRetrieve->setColumnWidth(7, 140);
}

void QueryRetrieveWidget::doDicomQuery()
{
    emit findscuStarted();
    QStringList keys;
    keys << DicomChison::netKeyOfIPName() << DicomChison::netKeyOfPortName() << DicomChison::netKeyOfTimeoutName();

    QMap<QString, QString> keyValues = DicomChison::getDefaultServerInfo(DicomChison::qr, keys);

    int res = -1;
    if (!keyValues.isEmpty())
    {
        res = findScu(keyValues.value(DicomChison::netKeyOfIPName()), keyValues.value(DicomChison::netKeyOfPortName()),
                      keyValues.value(DicomChison::netKeyOfTimeoutName()).toInt());
    }
    emit findscuFinished(res);
}

void QueryRetrieveWidget::doDicomRetrieve()
{
    if (m_RetrieveStudyInstanceID.count() > 0)
    {
        ui->cancelButton->setEnabled(false);
        emit progressValueChanged(10);
        connect(&m_FileSystemWatcher, SIGNAL(directoryChanged(QString)), this, SLOT(destinationPathChanged(QString)));
        QProcess process;

        m_RetrievingProcess = &process;
        process.start(Resource::dicomMoveScuCmd, m_args);
        int res = process.waitForFinished(-1) ? 0 : -1;

        if (res == 0)
        {
            if (!m_ReadyClose)
            {
                foreach (QString path, m_DestinationPathesList)
                {
                    DcmQueryResponse retrievedInfo =
                        m_Responses[QFileInfo(path).baseName().remove(Resource::dicomRetrievePrefix).replace("-", ".")];
                    QString imageNumber = QString::number(
                        QDir(path)
                            .entryInfoList(QStringList() << Resource::bmpFilter, (QDir::Files | QDir::NoSymLinks))
                            .count());

                    strncpy(retrievedInfo.NumberOfStudyRelatedInstances, imageNumber.toStdString().c_str(),
                            sizeof(retrievedInfo.NumberOfStudyRelatedInstances));
                    setData(retrievedInfo, false);
                }

                emit progressValueChanged(100);
            }
            qDebug() << ("chison_movescu succeed\n");
        }
        else
        {
            qDebug() << "chison_movescu failed\n";
        }
        m_RetrievingProcess = NULL;
        m_FileSystemWatcher.removePaths(m_DestinationPathesList);
        m_DestinationPathesList.clear();
        disconnect(&m_FileSystemWatcher, SIGNAL(directoryChanged(QString)), this,
                   SLOT(destinationPathChanged(QString)));

        ui->cancelButton->setEnabled(true);
    }
    ui->retrieveProgressBar->hide();
    ui->cancelRetrieveButton->hide();
    ui->retrieveButton->setEnabled(true);
}

int QueryRetrieveWidget::findScu(const QString& ip, const QString& port, int timeout)
{
    QStringList args;
    if (ip.isEmpty() || port.isEmpty())
    {
        return -1;
    }
    if (timeout <= 0)
    {
        timeout = 10;
    }

    QString patientId = ui->lineEditPatientID->text();
    QString patientName = ui->lineEditPatientName->text();
    QString accessionNumber = ui->lineEditAccession->text();

    QString studyID;
    QString examDescription;
    QString studyDate;

    getSearchValue(studyID, examDescription, studyDate);

    int res = 0;
    args << "-S"
         << "-X"
         << "-to" << QString::number(timeout);
    if (AppLogger::dicomDebug())
    {
        args << "-d";
    }
    if (AppLogger::dicomVerbose())
    {
        args << "-v";
    }
    args << ip << port << "-k" << QString("0008,0005=%1").arg("") << "-k" << QString("0008,0020=%1").arg(studyDate)
         << "-k" << QString("0008,0030=%1").arg("") << "-k" << QString("0008,0050=%1").arg(accessionNumber) << "-k"
         << QString("0008,0052=%1").arg("STUDY") << "-k" << QString("0008,0061=%1").arg("") << "-k"
         << QString("0008,0090=%1").arg("") << "-k" << QString("0008,1030=%1").arg("") << "-k"
         << QString("0010,0010=%1").arg(patientName) << "-k" << QString("0010,0020=%1").arg(patientId) << "-k"
         << QString("0010,0030=%1").arg("") << "-k" << QString("0010,0032=%1").arg("") << "-k"
         << QString("0010,0040=%1").arg("") << "-k" << QString("0010,1020=%1").arg("") << "-k"
         << QString("0010,1030=%1").arg("") << "-k" << QString("0020,000D=%1").arg("") << "-k"
         << QString("0020,0010=%1").arg("") << "-k" << QString("0020,1206=%1").arg("") << "-k"
         << QString("0020,1208=%1").arg("") << "-k" << QString("0032,1032=%1").arg("");

    if (AppLogger::dicomVerbose())
    {
        qDebug() << args;
    }
    QProcess process;
    process.start(Resource::dicomFindScuCmd, args);
    res = process.waitForFinished(-1) ? 0 : -1;

    if (res == 0)
    {
        qDebug() << ("chison_findscu succeed\n");
    }
    else
    {
        qDebug() << "chison_findscu failed error no is %d in DicomFindscu()\n";
    }
    return res;
}

int QueryRetrieveWidget::moveScu(const QString& title, const QString& ip, const QString& scpport,
                                 const QString& localport, int timeout)
{
    if (title.isEmpty())
    {
        return -1;
    }
    QModelIndexList selectRows =
        ui->treeViewQuery->selectionModel()->selectedRows(m_QueryStandardmodel->columnCount() - 1);

    if (selectRows.count() <= 0)
    {
        return -1;
    }

    QString localAETitle = DicomChison::getLocalAETitle();

    if (ip.isEmpty() || scpport.isEmpty() || localport.isEmpty())
    {
        return -1;
    }
    if (timeout <= 0)
    {
        timeout = 10;
    }

    m_args << "-S"
           << "-to" << QString::number(timeout) << "-aec" << title << "-aet" << localAETitle << "-aem" << localAETitle
           << "-dir" << Resource::hardDiskDir + Resource::pathSeparator + Resource::dataStoreDir << "+P" << localport
           << ip << scpport;

    if (AppLogger::dicomDebug())
    {
        m_args << "-d";
    }
    if (AppLogger::dicomVerbose())
    {
        m_args << "-v";
    }

    foreach (QModelIndex index, selectRows)
    {
        QString m_CurQueryStandardmodel = m_QueryStandardmodel->data(index).toString();
        createPatient(m_Responses[m_CurQueryStandardmodel], m_args);
    }
    if (m_DestinationPathesList.count() > 0)
    {
        ui->retrieveProgressBar->show();
        ui->cancelRetrieveButton->show();
        emit progressValueChanged(0);
        m_FileSystemWatcher.addPaths(m_DestinationPathesList);
    }
    return 0;
}

void QueryRetrieveWidget::switchEditState(int index)
{
    bool isDateEdit = index != StudyID && index != ExamDescription && index != ExamBetween;
    ui->dateEdit->setVisible(isDateEdit);
    ui->lineEditSearchValue->setVisible(!isDateEdit);
    ui->lineEditSearchValue->setClickListen(index == ExamBetween);
}

void QueryRetrieveWidget::getSearchValue(QString& studyID, QString& examDescription, QString& studyDate)
{
    switch (ui->comboBox->currentIndex())
    {
    case StudyID:
        studyID = ui->lineEditSearchValue->text();
        break;
    case ExamToday:
        studyDate = ui->dateEdit->text().replace("-", "");
        break;
    case ExamBetween:
        studyDate = ui->lineEditSearchValue->text();
        break;
    case ExamDateBefore:
        studyDate = "-" + ui->dateEdit->text().replace("-", "");
        break;
    case ExamDate:
        studyDate = ui->dateEdit->text().replace("-", "");
        break;
    case ExamDateAfter:
        studyDate = ui->dateEdit->text().replace("-", "") + "-";
        break;
    case ExamDescription:
        examDescription = ui->lineEditSearchValue->text();
        break;
    }
}

void QueryRetrieveWidget::createPatient(const DcmQueryResponse& rspInfo, QStringList& cmd)
{
    Patient* patient = NULL;
    Study* study = NULL;
    if (DataAccessLayerHelper::isPatientExists(rspInfo.PatientID))
    {
        patient = DataAccessLayerHelper::getPatient(rspInfo.PatientID);
    }
    else
    {
        patient = new Patient();
    }

    if (patient != NULL)
    {

        QDateTime birthDate(QDate::fromString(rspInfo.PatientsBirthDate, "yyyyMMdd").startOfDay());
        patient->setPatientId(QString(rspInfo.PatientID).trimmed());
        patient->setPatientsName(rspInfo.PatientsName);
        patient->setPatientsSex(QString(rspInfo.PatientsSex).trimmed());
        patient->setPatientsBirthDate(birthDate);
        patient->setPatientsBirthDateRaw(rspInfo.PatientsBirthDate);
        patient->setPatientsName(rspInfo.PatientsName);
        PatientsAge patientsAge;
        patientsAge.setBirthDate(birthDate);
        patient->setPatientsAge(patientsAge.ageYear());
    }
    else
    {
        return;
    }
    QString studyOid = Resource::dicomRetrievePrefix + QString(rspInfo.StudyInstanceID).replace(".", "-").trimmed();

    bool replace = false;

    if (DataAccessLayerHelper::isStudyExists(studyOid))
    {
        if (m_ConfirmValue != QMessageBox::YesToAll)
        {
            m_ConfirmValue = MessageBoxFrame::question(this, tr("Confirm"),
                                                       tr("StudyID:%1\n"
                                                          "A Study with this Study ID already exists.\n"
                                                          "Some exam information may be overwritten.\n"
                                                          " \n"
                                                          "What to continue?")
                                                           .arg(studyOid)
                                                           .toStdString()
                                                           .c_str(),
                                                       QMessageBox::No | QMessageBox::YesToAll | QMessageBox::Yes);

            if (m_ConfirmValue == QMessageBox::YesToAll || m_ConfirmValue == QMessageBox::Yes)
            {
                for (int i = 0; i < m_RetrieveStandardmodel->rowCount(); i++)
                {
                    if (m_RetrieveStandardmodel->item(i, m_RetrieveStandardmodel->columnCount() - 1)->text() ==
                        rspInfo.StudyInstanceID)
                    {
                        m_RetrieveStandardmodel->removeRow(i);
                    }
                }
            }
        }

        if (m_ConfirmValue == QMessageBox::YesToAll || m_ConfirmValue == QMessageBox::Yes)
        {
            study = DataAccessLayerHelper::getStudy(studyOid);
            study->setPatient(patient);
            replace = true;
        }
        else
        {
            delete patient;
            return;
        }
    }
    else
    {
        study = new Study(patient);
    }

    if (study != NULL)
    {
        study->setStudyOid(studyOid);
        study->setStudyInstanceUid(rspInfo.StudyInstanceID);

        study->setStudyDate(QDate::fromString(rspInfo.StudyDate, "yyyyMMdd").startOfDay());
        study->setStudyDateRaw(rspInfo.StudyDate);
        study->setStoreTime(QDate::fromString(rspInfo.StudyDate, "yyyyMMdd").startOfDay());
        study->setStudyTimeRaw(rspInfo.StudyTime);
        study->setReferringPhysiciansName(rspInfo.ReferringPhysiciansName);
        study->setAccessionNumber(rspInfo.AccessionNumber);
        study->setStudyDescription(rspInfo.StudyDescription);
        study->setNumberOfStudyRelatedSeries(QString(rspInfo.NumberOfStudyRelatedSeries).toInt());
        study->setNumberOfStudyRelatedInstances(QString(rspInfo.NumberOfStudyRelatedInstances).toInt());
        study->setHeight(QString(rspInfo.PatientSize).toFloat());
        study->setWeight(QString(rspInfo.PatientWeight).toFloat());
        study->setStudyState(1);
        patient->Studies().append(study);
    }
    else
    {
        delete patient;
        return;
    }
    m_RetrieveStudyInstanceID.append(rspInfo.StudyInstanceID);

    DataAccessLayerHelper::saveOrUpdate(*patient);
    DataAccessLayerHelper::saveOrUpdate(*study);

    QString destination = PatientPath::instance().path(*patient);

    if (replace)
    {
        Util::Rmdir(destination);
    }
    Util::Mkdir(destination);

    m_DestinationPathesList.append(destination);

    m_RetrieveDcmFileCount += QString(rspInfo.NumberOfStudyRelatedInstances).toInt();

    QString queryFile = Resource::dicomRspTempDir + rspInfo.StudyInstanceID + Resource::dcmExt;

    cmd << queryFile;

    delete patient;

    return;
}

void QueryRetrieveWidget::prepareRetrieve()
{
    QStringList keys;
    keys << DicomChison::netKeyOfAetitleName() << DicomChison::netKeyOfIPName() << DicomChison::netKeyOfPortName()
         << DicomChison::localPortName() << DicomChison::netKeyOfTimeoutName();

    QMap<QString, QString> keyValues = DicomChison::getDefaultServerInfo(DicomChison::qr, keys);

    m_ConfirmValue = QMessageBox::NoButton;
    m_RetrieveStudyInstanceID.clear();
    m_RetrieveDcmFileCount = 0;
    m_args.clear();

    if (!keyValues.isEmpty())
    {
        moveScu(keyValues.value(DicomChison::netKeyOfAetitleName()), keyValues.value(DicomChison::netKeyOfIPName()),
                keyValues.value(DicomChison::netKeyOfPortName()), keyValues.value(DicomChison::localPortName()),
                keyValues.value(DicomChison::netKeyOfTimeoutName()).toInt());
    }
}

void QueryRetrieveWidget::setData(const DcmQueryResponse& rsp, bool isQuery)
{
    QStandardItemModel* model = NULL;
    model = isQuery ? m_QueryStandardmodel : m_RetrieveStandardmodel;
    QStringList temp;
    temp << rsp.PatientID << rsp.PatientsName << rsp.PatientsSex << rsp.PatientsBirthDate << rsp.AccessionNumber
         << rsp.ReferringPhysiciansName << rsp.NumberOfStudyRelatedInstances << rsp.StudyDate << rsp.StudyInstanceID;

    //显示在界面
    int rowCount = model->rowCount();
    model->insertRow(rowCount);
    for (int i = 0; i < temp.size(); ++i)
    {
        model->setData(model->index(rowCount, i), temp.at(i));
    }
    temp.clear();
}

void QueryRetrieveWidget::onClose()
{
    m_ReadyClose = true;
    on_cancelRetrieveButton_clicked();

    Util::Rmdir(Resource::dicomRspTempDir, true, true);
}

void QueryRetrieveWidget::onFindscuStarted()
{
    StateFilterLocker locker(m_StateManager, StateEventNames::NothingStateFilter());
    m_ProgressBar->exec(); // hide()结束阻塞
}

void QueryRetrieveWidget::getRespond(int res)
{
    m_ProgressBar->hide();
    if (res == 0)
    {
        FILE* pFile;

        QByteArray tempByt = DicomChison::findscuResultFileName().toLatin1();
        char* findscuname = tempByt.data();
        if ((pFile = fopen(findscuname, "rb")) != NULL)
        {
            DcmQueryResponse respond;

            for (; fread(&respond, sizeof(DcmQueryResponse), 1, pFile);)
            {
                // order :model的显示顺序
                setData(respond);
                m_Responses[respond.StudyInstanceID] = respond;
            }
            fclose(pFile);
            if (m_QueryStandardmodel->rowCount() > 0)
            {
                ui->selectAllButton->setEnabled(true);
                ui->deselectButton->setEnabled(true);
                ui->retrieveButton->setEnabled(true);
            }

            QFile::remove(DicomChison::findscuResultFileName());
        }
        else
        {
            qDebug() << " Can not open file " << DicomChison::findscuResultFileName();
        }
    }

    if (AppSetting::isAnimalAndChinese())
    {
        ui->labelRecords->setText(tr("Animal(Source) %1 record(s) are listed.").arg(m_QueryStandardmodel->rowCount()));
    }
    else
    {
        ui->labelRecords->setText(tr("Patient(Source) %1 record(s) are listed.").arg(m_QueryStandardmodel->rowCount()));
    }
}

void QueryRetrieveWidget::on_queryButton_clicked()
{
    doQueryTask();
}

void QueryRetrieveWidget::on_clearButton_clicked()
{
    ui->lineEditPatientID->clear();
    ui->lineEditPatientName->clear();
    ui->lineEditAccession->clear();
    ui->comboBox->setCurrentIndex(0);
}

void QueryRetrieveWidget::on_selectAllButton_clicked()
{
    ui->treeViewQuery->selectAll();
}

void QueryRetrieveWidget::on_deselectButton_clicked()
{
    ui->treeViewQuery->clearSelection();
}

void QueryRetrieveWidget::on_retrieveButton_clicked()
{
    ui->retrieveButton->setEnabled(false);
    prepareRetrieve();
    doRetrieveTask();
}

void QueryRetrieveWidget::on_cancelButton_clicked()
{
    emit accept();
}

void QueryRetrieveWidget::on_comboBox_currentIndexChanged(int index)
{
    switchEditState(index);
    this->activateWindow();

    switch (index)
    {
    case StudyID:
        ui->lineEditSearchValue->setReadOnly(false);
        ui->lineEditSearchValue->clear();
        break;
    case ExamToday:
        ui->dateEdit->setDate(QDate::currentDate());
        ui->dateEdit->setReadOnly(true);
        break;
    case ExamBetween:
        ui->lineEditSearchValue->setReadOnly(true);
        ui->lineEditSearchValue->clear();
        onLineEditSearchValueClicked();
        break;
    case ExamDateBefore:
        ui->dateEdit->setReadOnly(false);
        ui->dateEdit->setDate(QDate::currentDate());
        break;
    case ExamDate:
        ui->dateEdit->setReadOnly(false);
        ui->dateEdit->setDate(QDate::currentDate());
        break;
    case ExamDateAfter:
        ui->dateEdit->setReadOnly(false);
        ui->dateEdit->setDate(QDate::currentDate());
        break;
    case ExamDescription:
        ui->lineEditSearchValue->setReadOnly(false);
        ui->lineEditSearchValue->clear();
        break;
    default:
        break;
    }
}

void QueryRetrieveWidget::onLineEditSearchValueClicked()
{
    ExamDateBetweenDialog dialog(this);
    if (!ui->lineEditSearchValue->text().isEmpty())
    {
        dialog.setFromToDateString(ui->lineEditSearchValue->text());
    }
    dialog.exec();
    ui->lineEditSearchValue->setText(dialog.fromToDateString());
    Util::processEvents(QEventLoop::ExcludeUserInputEvents);
    this->activateWindow();
}

void QueryRetrieveWidget::destinationPathChanged(const QString& path)
{
    Q_UNUSED(path)
    QStringList imgFilters = QStringList() << Resource::bmpFilter;
    int retrievedFileCount = 0;
    foreach (QString dirpath, m_DestinationPathesList)
    {
        QDir dir(dirpath);
        QFileInfoList infoList = dir.entryInfoList(imgFilters, (QDir::Files | QDir::NoSymLinks));
        retrievedFileCount += infoList.count();
    }
    ui->retrieveProgressBar->setValue(10 + retrievedFileCount / (float)m_RetrieveDcmFileCount * 70);
    qDebug() << "QueryRetrieveWidget::destinationPathChanged" << path << retrievedFileCount << Util::currentTime();
}

void QueryRetrieveWidget::on_cancelRetrieveButton_clicked()
{
    if (m_RetrievingProcess != NULL)
    {
        m_RetrievingProcess->terminate();
    }
}
