<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CardInfoWidget</class>
 <widget class="QWidget" name="CardInfoWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>723</width>
    <height>105</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="12">
    <widget class="QLabel" name="label_13">
     <property name="text">
      <string>m²</string>
     </property>
    </widget>
   </item>
   <item row="1" column="4">
    <widget class="QLabel" name="label_3">
     <property name="text">
      <string>BP</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="1" column="5">
    <widget class="QLineEdit" name="lineEditbpl">
     <property name="text">
      <string>0</string>
     </property>
    </widget>
   </item>
   <item row="1" column="12">
    <widget class="QLabel" name="label_6">
     <property name="text">
      <string>mmHg</string>
     </property>
    </widget>
   </item>
   <item row="0" column="0">
    <widget class="QLabel" name="label_8">
     <property name="text">
      <string>Height</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="1" column="10">
    <widget class="QLabel" name="label_7">
     <property name="text">
      <string>RAP</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="2" column="2">
    <widget class="QLabel" name="label_11">
     <property name="text">
      <string>℃</string>
     </property>
    </widget>
   </item>
   <item row="1" column="6">
    <widget class="QLabel" name="label_4">
     <property name="text">
      <string>/</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item row="0" column="9">
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="0" column="10">
    <widget class="QLabel" name="label_12">
     <property name="text">
      <string>BSA</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="1" column="1">
    <widget class="QLineEdit" name="lineEditHr">
     <property name="text">
      <string>0</string>
     </property>
    </widget>
   </item>
   <item row="1" column="11">
    <widget class="QLineEdit" name="lineEditRap">
     <property name="text">
      <string>0</string>
     </property>
    </widget>
   </item>
   <item row="2" column="0">
    <widget class="QLabel" name="label_9">
     <property name="text">
      <string>Temperature</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="0" column="4">
    <widget class="QLabel" name="label_10">
     <property name="text">
      <string>Weight</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="0" column="11">
    <widget class="QLineEdit" name="lineEditBsa">
     <property name="text">
      <string notr="true">0.00</string>
     </property>
    </widget>
   </item>
   <item row="0" column="1" colspan="2">
    <widget class="PatientInfoInputWidget" name="wPatientHeight" native="true"/>
   </item>
   <item row="0" column="3">
    <spacer name="horizontalSpacer_2">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="1" column="0">
    <widget class="QLabel" name="label">
     <property name="text">
      <string>HR</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="2" column="1">
    <widget class="QLineEdit" name="lineEditTmp"/>
   </item>
   <item row="1" column="7">
    <widget class="QLineEdit" name="lineEditBph">
     <property name="text">
      <string>0</string>
     </property>
    </widget>
   </item>
   <item row="1" column="2">
    <widget class="QLabel" name="label_2">
     <property name="text">
      <string>bpm</string>
     </property>
    </widget>
   </item>
   <item row="0" column="5" colspan="4">
    <widget class="PatientInfoInputWidget" name="wPatientWeight" native="true"/>
   </item>
   <item row="1" column="8">
    <widget class="QLabel" name="label_5">
     <property name="text">
      <string>mmHg</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>PatientInfoInputWidget</class>
   <extends>QWidget</extends>
   <header>patientinfoinputwidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
