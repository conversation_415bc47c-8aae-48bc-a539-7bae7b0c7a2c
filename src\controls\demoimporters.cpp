#include "demoimporters.h"
#include "demoimporter.h"
#include "setting.h"
#include "resource.h"
#include "util.h"
#include "messageboxframe.h"
#include <QDir>

DemoImporters::DemoImporters(QObject* parent)
    : IImporter(parent)
    , m_TotalFilesCount(0)
    , m_Index(0)
{
    m_Importers.append(new DemoImporter(false, this));
    m_Importers.append(new DemoImporter(true, this));

    foreach (DemoImporter* importer, m_Importers)
    {
        m_TotalFilesCount += importer->externalFilesCount();
        connect(importer, SIGNAL(progressValueChanged()), this, SLOT(onProgressValueChanged()));
    }
}

void DemoImporters::import()
{
    if (m_TotalFilesCount > 0)
    {
        if (MessageBoxFrame::question(NULL, QString(), tr("Do you want to import the demo files?"),
                                      QMessageBox::Yes | QMessageBox::No, QMessageBox::Yes) == QMessageBox::Yes)
        {
            m_ProgressBar.setText(tr("Importing demo..."));
            m_ProgressBar.show();
            QString importResultStrings;
            bool importSuccess = true;
            foreach (DemoImporter* importer, m_Importers)
            {
                importer->import();
                importResultStrings.append(importer->importInfo());
                importResultStrings.append("\n");
                if (importer->importResult() == DemoImporter::Failed)
                {
                    importSuccess = false;
                }
            }

            //满足导入demo条件后,导入过程中就直接删除旧版本的demo图片
            QDir internalOldPath(Resource::internalSlideShowDir);
            QStringList internalOldFiles = internalOldPath.entryList(Util::pictureFilters(), QDir::Files);
            foreach (const QString& oldName, internalOldFiles)
            {
                internalOldPath.remove(oldName);
            }

            if (importSuccess)
            {
                if (Setting::instance().defaults().needUpdateSlideShowFiles())
                {
                    Setting::instance().defaults().setNeedUpdateSlideShowFiles(false);
                }
            }
            m_ProgressBar.hide();
            //            MessageBoxFrame::information(NULL, QString(), importResultStrings);
        }
    }
}

void DemoImporters::check()
{
    QString checkResultStrings;
    foreach (DemoImporter* importer, m_Importers)
    {
        importer->check();
        checkResultStrings.append(importer->checkInfo());
        checkResultStrings.append("\n");
    }
    MessageBoxFrame::informationNonModal(NULL, QString(), checkResultStrings, QMessageBox::Ok, QMessageBox::Ok);
}

void DemoImporters::onProgressValueChanged()
{
    m_Index++;
    m_ProgressBar.setValue(m_Index * 100 / m_TotalFilesCount);
}
