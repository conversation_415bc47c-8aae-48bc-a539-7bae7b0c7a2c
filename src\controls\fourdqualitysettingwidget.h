#ifndef FOURDQUALITYSETTINGWIDGET_H
#define FOURDQUALITYSETTINGWIDGET_H

#include "controls_global.h"
#include "fourdparassettingbasewidget.h"
#include "fourdqualitywidget.h"

class CONTROLSSHARED_EXPORT FourDQualitySettingWidget : public FourDParasSettingBaseWidget
{
    Q_OBJECT
public:
    explicit FourDQualitySettingWidget(QWidget* parent = 0);
    void initalize();
    void setSonoParameters(SonoParameters* sonoParameters);

protected:
    virtual void currenIndexChanged(const QString& arg1);
    virtual bool contains(const QString& name);
    virtual void add(const QString& name);
    virtual void save(const QString& name);
    virtual void del(const QString& name);
    virtual bool rename(const QString& oldName, const QString& newName);
    virtual QStringList names();

private:
    FourDQualityWidget* m_<PERSON>;
};

#endif // FOURDQUALITYSETTINGWIDGET_H
