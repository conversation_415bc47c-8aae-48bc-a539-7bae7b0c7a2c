#ifndef FILEOPERATIONTHREAD_H
#define FILEOPERATIONTHREAD_H
#include "archive_global.h"

#include <QThread>
#include <QFileInfoList>
#include <QStringList>

class FileOperationTask;
class ARCHIVESHARED_EXPORT FileOperationThread : public QThread
{
    Q_OBJECT
public:
    explicit FileOperationThread(QObject* parent = 0);

    qint64 calcSrcSize(const QString& path);
    qint64 calcSrcSizes(const QStringList& list);
    qint64 calcSrcSizes(const QFileInfoList& list);
    void setCurTaskName(const QString& str);
    QString curTaskName();

protected:
    void run();
    virtual void checkIfNeedToChangeFileList(QFileInfoList& list)
    {
    }
signals:
    void curValue(qint64 value);

private:
    QString m_CurTaskName;
};

#endif // FILEOPERATIONTHREAD_H
