<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SystemHintWidget</class>
 <widget class="QWidget" name="SystemHintWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>413</width>
    <height>364</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_2">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QStackedWidget" name="stackedWidget">
     <widget class="QWidget" name="page">
      <layout class="QHBoxLayout" name="horizontalLayout_3">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="SystemHintDefaultWidget" name="widgetDefault" native="true"/>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="page_2">
      <layout class="QHBoxLayout" name="horizontalLayout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="MultiFuncOperationAreaView" name="widgetMultiFunc" native="true"/>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>SystemHintDefaultWidget</class>
   <extends>QWidget</extends>
   <header>systemhintdefaultwidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MultiFuncOperationAreaView</class>
   <extends>QWidget</extends>
   <header>multifuncoperationareaview.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
