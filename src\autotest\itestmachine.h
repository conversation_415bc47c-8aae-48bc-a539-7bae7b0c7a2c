/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#ifndef ITESTMACHINE_H
#define ITESTMACHINE_H
#include "autotest_global.h"

class QString;

class AUTOTESTSHARED_EXPORT ITestMachine
{
public:
    virtual ~ITestMachine();
    virtual void handleSpecialEvent(const QString& event) = 0;
    virtual void handleNormalEvent(const QString& event) = 0;
    virtual void handleFinishEvent() = 0;
    virtual void resetFlags() = 0;
};

#endif // ITESTMACHINE_H
