#ifndef ARCHIVEMODEL_H
#define ARCHIVEMODEL_H
#include "archive_global.h"

#include <QObject>
class ArchiveManagerDialog;
class QWidget;
class PatientWorkflow;
class Patient;
class QFileInfo;
class GeneralWorkflowFunction;
class DicomTaskManager;
class IToolsFacade;
class IStateManager;
class IColorMapManager;
class IDiskDevice;
/**
 * @brief 维护archive 界面的运行与外界的联系
 */
class ARCHIVESHARED_EXPORT ArchiveModel : public QObject
{
    Q_OBJECT
public:
    explicit ArchiveModel(IStateManager* stateManager, QWidget* parent = 0);
    ~ArchiveModel();
    /**
     * @brief exec 运行ArchiveManagerDialog之前可以做一些初始化
     * @return
     */
    void exec();
    ArchiveManagerDialog* archiveManagerDialog() const;
    void setPatientWorkflow(PatientWorkflow* patientWorkflow);
    void setGeneralWorkflowFunction(GeneralWorkflowFunction* generalWorkflowFunction);
    void setDicomTaskManager(DicomTaskManager* dicomTaskManager);
    void setToolsFacade(IToolsFacade* value);
    void setColorMapManager(IColorMapManager* value);
    void setDiskDevice(IDiskDevice* diskDevice);
signals:
    void closed();
    void onlyJump();
    void entryPatientState();
    void entryBrowseState();
    /**
     * @brief sendDirPathList 链接PatientWorkflowModel
     *
     * @param list
     *
     * @return
     */
    void sendDirPathList(const QStringList& list);
    /**
     * @brief sendStudyOid 检查的studyoid
     *
     * @param id
     */
    void sendStudyOid(const QString& id);
    /**
     * @brief currentDiskPathChanged 当前的磁盘路径改变
     *
     * @param disk
     */
    void currentDiskPathChanged(const QString& disk);
    void entryContinueExamState();
    void entryEditExamState();
    void autoCallBack(int index);

private:
    ArchiveManagerDialog* m_ArchiveManagerDialog;
    GeneralWorkflowFunction* m_GeneralWorkflowFunction;
};

#endif // ARCHIVEMODEL_H
