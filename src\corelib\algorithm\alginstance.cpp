#include "alginstance.h"

AlgInstance& AlgInstance::algInstance()
{
    static AlgInstance _instance;
    return _instance;
}

AlgInstance::AlgInstance()
{
}

AutoBladderAlg* AlgInstance::getAutoBladder()
{
    return &m_AutoBladder;
}

SonoThyroidAlg* AlgInstance::getSonoThyroidAlg()
{
    return &m_SonoThyroidAlg;
}

AutoEfAlg* AlgInstance::getAutoEfAlg()
{
    return &m_AutoEfAlg;
}

SonoAVAlg* AlgInstance::getSonoAVAlg()
{
    return &m_SonoAvAlg;
}

SonoOBAlg* AlgInstance::getSonoOBAlg()
{
    return &m_SonoOBAlg;
}

SonoNerveAlg* AlgInstance::getSonoNerveAlg()
{
    return &m_SonoNerveAlg;
}

SonoMskAlg* AlgInstance::getSonoMskAlg()
{
    return &m_SonoMskAlg;
}

SonoDiaphAlg* AlgInstance::getSonoDiaphAlg()
{
    return &m_SonoDiaphAlg;
}

SonoCarotidGuideAlg* AlgInstance::getSonoCarotidGuideAlg()
{
    return &m_SonoCarotidGuideAlg;
}

SonoCardiacAlg* AlgInstance::getSonoCardiacAlg()
{
    return &m_SonoCardiacAlg;
}

AutoCystisAlg* AlgInstance::getAutoCystisAlg()
{
    return &m_AutoCystisAlg;
}

RtimtAlg* AlgInstance::getRtimtAlg()
{
    return &m_RtimtAlg;
}

JsrgAlg* AlgInstance::getJsrgAlg()
{
    return &m_JsrgAlg;
}
