#ifndef FREEHAND3DWIDGET_H
#define FREEHAND3DWIDGET_H
#include "controls_global.h"
#include "ibasewidget.h"
#include "dataarg.h"

namespace Ui
{
class FreeHand3DWidget;
}

class ILineBufferManager;
class FreeHand3DModel;
class ProgressBarView;
class QThread;
class ImageTile;
class SonoParameters;
class ImageWidget;

class CONTROLSSHARED_EXPORT FreeHand3DWidget : public IBaseWidget
{
    Q_OBJECT
public:
    explicit FreeHand3DWidget(ImageWidget* imagewidget, ILineBufferManager* buffermanager, QWidget* parent = 0);
    ~FreeHand3DWidget();
    void sendImageData();

private:
    typedef enum
    {
        XAxis,
        YAxis,
        ZAxis
    } AxisType;
    void init();
    void highlightCurrentAxis(AxisType axis);
    void flashAxisLabel(int time = 300);
    void setupRects();

protected:
    void retranslateUi();
    void showEvent(QShowEvent* e);

public slots:
    void onAxisChanged(const QVariant& val);
    void onRotate(bool increase);
    void onFreeHand3DZoom(bool increase);
    void onFreeHand3DReset();
    void onBeforecurSonoParametersChanged();
    void onCurSonoParametersChanged();
private slots:
    void onVolumeDataSended();
    void onFlashAxisLabelEnd();
signals:
    void sendVolumeData();
    void show3DImage();

private:
    Ui::FreeHand3DWidget* ui;
    ImageWidget* m_ImageWidget;
    ImageTile* m_ImageTile;
    SonoParameters* m_SonoParameters;
    FreeHand3DModel* m_Model;
    ProgressBarView* m_ProgressBar;
    QThread* m_SendThread;
    AxisType m_CurrentAxis;
    float m_ZooomValue;
    bool m_IsInit;
    QHash<AxisType, LabelWithIconData*> m_LabelHash;
};

#endif // FREEHAND3DWIDGET_H
