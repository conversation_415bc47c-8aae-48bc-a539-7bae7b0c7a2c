/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#include "networkstoragefilewidget.h"
#include "ui_networkstoragefilewidget.h"
#include "smbnetfscontroller.h"
#include <QDir>
#include "messageboxframe.h"
#include "util.h"
#include "imageprocessedcopy.h"
#include "confirmdialog.h"
#include "progressbarview.h"
#include "resource.h"
#include "rpt2pdfconvert.h"
#include <QSettings>
#include "deletefilescontroller.h"
#include "setting.h"
#include "generalworkflowfunction.h"
#include "checkinfofilter.h"
#include "dicomsrmodels.h"
#include "gdprutility.h"
#include "appsetting.h"

const char* const NetworkStorageFileWidget::m_AnimalString[] = {
    QT_TRANSLATE_NOOP("NetworkStorageFileWidget", "Delete animal exam after sending")};

NetworkStorageFileWidget::NetworkStorageFileWidget(IStateManager* value, IColorMapManager* colorMapManager,
                                                   QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::NetworkStorageFileWidget)
    , m_progressBar(NULL)
    , m_confirmDialog(NULL)
    , m_copy(NULL)
    , m_DicomTaskManager(NULL)
    , m_DeleteFilesController(NULL)
    , m_ImageType(QString())
    , m_isCurrentMulticChoice(false)
    , m_isCopy(false)
    , m_IsShowTransformInfo(false)
    , m_IsCopyThreadStarted(false)
    , m_DefaultDir(QString())
    , m_ContainRetrieveExam(false)
{
    ui->setupUi(this);

    if (m_progressBar == NULL)
    {
        m_progressBar = new ProgressBarView(value, this);
    }

    m_confirmDialog = new ConfirmDialogFrame(this);
    connect(m_confirmDialog, SIGNAL(clickedButton(int)), this, SLOT(onConfirmButtonClicked(int)));
    m_threadModel.setProgressBar(m_progressBar);
    m_threadModel.setConfirmDialog(m_confirmDialog);
    m_copy = new ImageProcessedCopy(colorMapManager, &m_threadModel);
    connect(m_copy, SIGNAL(infoShowed(bool)), this, SLOT(onInfoShowed(bool)));
    m_threadModel.setICopy(m_copy);

    connect(&m_threadModel, SIGNAL(threadFinished(bool)), this, SLOT(onThreadFinished(bool)));

    QRegExp rxName("[^/:*?<>|`\"\\\\]+$"); //根据windows文件夹命名方式禁用以下字符/:*?<>|"\.
    QValidator* validatorName = new QRegExpValidator(rxName, this);
    ui->lineEditDir->setValidator(validatorName);

    ui->checkBoxVideo->setChecked(true);
    initRadioButtons();
    if (AppSetting::isAnimalAndChinese())
    {
        Util::setPatient2Animal(ui->checkBoxDeleteAfterSending, "NetworkStorageFileWidget",
                                m_AnimalString[Delete_animal_exam_after_sending]);
    }
}

void NetworkStorageFileWidget::initRadioButtons()
{
    ui->checkBoxDcmSR->setChecked(false);
    ui->dcmOptionwidget->setVisible(false);

    QSettings settings(Resource::optionsforTransmittedImagesIniName(), QSettings::IniFormat);
    settings.beginGroup(Resource::colormapGeneralDevice);
    QString screenSuffix = settings.value("ScreenSuffix").toString();
    settings.endGroup();

    if (Resource::bmpSuffix == screenSuffix)
    {
        m_ImageType = Resource::bmpSuffix;
        ui->radioButtonBMP->setChecked(true);
    }
    else if (Resource::jpgSuffix == screenSuffix)
    {
        m_ImageType = Resource::jpgSuffix;
        ui->radioButtonJPG->setChecked(true);
    }
    else if (Resource::pngSuffix == screenSuffix)
    {
        m_ImageType = Resource::pngSuffix;
        ui->radioButtonPNG->setChecked(true);
    }
    else if (Resource::dcmSuffix == screenSuffix)
    {
        m_ImageType = Resource::dcmSuffix;
        ui->radioButtonDCM->setChecked(true);
        ui->dcmOptionwidget->setVisible(true);
    }
}

void NetworkStorageFileWidget::convertRpt2Pdf()
{
    QStringList rptFileNames = m_threadModel.currentRptFileNames();
    Rpt2PdfConvert convert;
    convert.setIsGDPR(ui->checkBoxGDPR->isChecked());
    foreach (QString rptFile, rptFileNames)
    {
        QFileInfo rptFileInfo(rptFile);
        convert.tryToConvertRpt2pdf(rptFileInfo.dir().absolutePath());
    }
}

void NetworkStorageFileWidget::convertRpt2PdfSelf(QString& selfFile)
{
    QStringList rptFileNames(selfFile);
    Rpt2PdfConvert convert(false);
    convert.setIsGDPR(ui->checkBoxGDPR->isChecked());
    m_PDFPath.clear();
    foreach (QString rptFile, rptFileNames)
    {
        QFileInfo rptFileInfo(rptFile);
        convert.tryToConvertRpt2pdf(rptFileInfo.dir().absolutePath());
        m_PDFPath.append(convert.pdfFileName());
    }
}

void NetworkStorageFileWidget::rptToPdfSelf()
{
    for (QString selfFile : m_filePaths)
    {
        QDir selfDir(selfFile);
        if (selfDir.exists())
        {
            QFileInfoList selfFileInfoList = selfDir.entryInfoList();
            for (QFileInfo selfFileInfo : selfFileInfoList)
            {
                if (selfFileInfo.suffix() == Resource::reportSuffix)
                {
                    QString selfRptFile = selfFileInfo.absoluteFilePath();
                    convertRpt2PdfSelf(selfRptFile);
                }
            }
        }
    }
}

void NetworkStorageFileWidget::deleteRPT(QString destPath)
{
    QDir selfDir(destPath);
    if (selfDir.exists())
    {
        QFileInfoList list = selfDir.entryInfoList(QDir::AllEntries | QDir::NoDotAndDotDot);

        for (QFileInfo selfFileInfo : list)
        {
            if (selfFileInfo.isDir())
            {
                deleteRPT(selfFileInfo.absoluteFilePath());
            }
            if (selfFileInfo.suffix() == Resource::reportSuffix)
            {
                QString selfRptFile = selfFileInfo.absoluteFilePath();
                QFile::remove(selfRptFile);
                Util::sync();
            }
        }
    }
}

void NetworkStorageFileWidget::setCopyOptions()
{
    QStringList suffixFilters = QStringList() << Resource::calcSuffix;
    suffixFilters << Resource::iniSuffix << "bin"
                  << "txt"; // for exclude max image number file, added 2017-07-20 by liujia
    suffixFilters << Resource::singleFourdImgSuffix << Resource::multiFourdImgSuffix; // 4D c3d and c4d
    m_threadModel.setNeedSingleFrameImgData(false);
    m_threadModel.setNeedCreateEmptyCopy(true);
    checkIfNeedSingleFrameImgData(m_filePaths);
    m_copy->setCinetransTo(ui->checkBoxVideo->isChecked() ? ui->comboBox_2->currentText() : QString());
    if (ui->checkBoxDcmSR->isVisible())
    {
        m_copy->setIsExportDcmSR(ui->checkBoxDcmSR->isChecked());
    }
    m_copy->setTaskManager(m_DicomTaskManager);
    if (m_DeleteFilesController != NULL)
    {
        m_DeleteFilesController->setIsDeleteExamAfterSending(ui->checkBoxDeleteAfterSending->isChecked());
    }
    m_copy->setDeleteFilesController(m_DeleteFilesController);
    m_threadModel.setNotCopyFileSuffixFilters(suffixFilters);
    ui->dcmOptionwidget->save();
}

void NetworkStorageFileWidget::onExportTypeChanged(QString type)
{
    if (Resource::bmpSuffix == type)
    {
        m_ImageType = Resource::bmpSuffix;
        ui->radioButtonBMP->setChecked(true);
    }
    else if (Resource::jpgSuffix == type)
    {
        m_ImageType = Resource::jpgSuffix;
        ui->radioButtonJPG->setChecked(true);
    }
    else if (Resource::pngSuffix == type)
    {
        m_ImageType = Resource::pngSuffix;
        ui->radioButtonPNG->setChecked(true);
    }
    else if (Resource::dcmSuffix == type)
    {
        m_ImageType = Resource::dcmSuffix;
        ui->radioButtonDCM->setChecked(true);
        ui->dcmOptionwidget->setVisible(true);
    }
}

void NetworkStorageFileWidget::onConfirmButtonClicked(int value)
{
    if (value == ConfirmDialog::Cancel && m_DeleteFilesController != NULL)
    {
        m_DeleteFilesController->setIsCanceled(true);
    }
}

void NetworkStorageFileWidget::onInfoShowed(bool isShowInfo)
{
    m_IsShowTransformInfo = isShowInfo;
}

NetworkStorageFileWidget::~NetworkStorageFileWidget()
{
    //在窗口退出时先通知子线程退出,否则子线程访问的地址将非法.
    m_threadModel.stopThread();

    delete ui;
    if (!isHidden() && !m_IsCopyThreadStarted && m_DeleteFilesController != NULL)
    {
        m_DeleteFilesController->setIsCopyThreadStarted(m_IsCopyThreadStarted);
        m_DeleteFilesController->doDelete();
    }

    QSettings settings(Resource::optionsforTransmittedImagesIniName(), QSettings::IniFormat);
    settings.beginGroup(Resource::colormapGeneralDevice);
    settings.setValue("ScreenSuffix", m_ImageType);
    settings.endGroup();
}

void NetworkStorageFileWidget::setDefaultDirName(const QString& dirName)
{
    ui->lineEditDir->setText(dirName);
}

void NetworkStorageFileWidget::setExportFilePaths(const QStringList& filePaths)
{
    m_filePaths = filePaths;
    responseUIToFilePaths();
}

void NetworkStorageFileWidget::initServiceNames()
{
    QStringList serviceNames = SmbNetFsController::instance().serviceNames();

    ui->comboBoxNetworkServer->addItems(serviceNames);

    ui->comboBoxNetworkServer->setCurrentIndex(SmbNetFsController::instance().currentDefaultIndex());
}

void NetworkStorageFileWidget::setIsCurrentMultiChoice(bool value)
{
    m_isCurrentMulticChoice = value;

    setLineEditAndLableVisible(!value);
}

void NetworkStorageFileWidget::setDeleteAfterSendExam(bool value)
{
    if (!value)
    {
        ui->checkBoxDeleteAfterSending->setEnabled(false);
    }
    else
    {
        ui->checkBoxDeleteAfterSending->setEnabled(true);
    }
}

void NetworkStorageFileWidget::setDicomTaskManager(DicomTaskManager* dicomTaskManager)
{
    m_DicomTaskManager = dicomTaskManager;
}

void NetworkStorageFileWidget::setDeleteFilesController(DeleteFilesController* controller)
{
    m_DeleteFilesController = controller;
}

bool NetworkStorageFileWidget::isWorking()
{
    return m_threadModel.copyThreadIsRunning();
}

void NetworkStorageFileWidget::showDcmSRCheckBox(bool value)
{
    ui->checkBoxDcmSR->setVisible(value);
}

void NetworkStorageFileWidget::setContainRetrieveExam(bool value)
{
    m_ContainRetrieveExam = value;
    if (m_ContainRetrieveExam)
    {
        ui->radioButtonPNG->setEnabled(false);
        ui->radioButtonJPG->setEnabled(false);
        ui->checkBoxDcmSR->setEnabled(false);
        ui->checkBoxGDPR->setEnabled(false);
        if (m_ImageType == Resource::jpgSuffix || m_ImageType == Resource::pngSuffix)
        {
            ui->radioButtonBMP->setChecked(true);
        }
    }
}

void NetworkStorageFileWidget::setLineEditAndLableVisible(bool value)
{
    ui->lineEditDir->setVisible(value);
    ui->label_3->setVisible(value);
}

void NetworkStorageFileWidget::retranslateUi()
{
    ui->retranslateUi(this);
    if (AppSetting::isAnimalAndChinese())
    {
        Util::setPatient2Animal(ui->checkBoxDeleteAfterSending, "NetworkStorageFileWidget",
                                m_AnimalString[Delete_animal_exam_after_sending]);
    }
}

void NetworkStorageFileWidget::on_pushButtonClose_clicked()
{
    if (isWorking())
    {
        MessageBoxFrame::tipWarning(this, QString(), tr("Background task is running, please wait for a moment."));
        return;
    }
    emit closed();
}

void NetworkStorageFileWidget::onThreadFinished(bool isSuccessful)
{
    if (!isSuccessful)
    {
        MessageBoxFrame::warning(tr("Export files failed!"));
    }
    else
    {
        foreach (QString path, m_PDFPath)
        {
            QFile::remove(path);
            Util::sync();
        }
        deleteRPT(m_FinalDir);
    }

    if (m_DeleteFilesController != NULL)
    {
        m_DeleteFilesController->setIsCopyThreadFinished(true);
        m_DeleteFilesController->doDelete();
    }

    //有avi任务时,暂不umount. TODO:
    // SmbNetFsController::instance().uMountDirAndRemoveMod();

    m_isCopy = false;
    if (ui->checkBoxDcmSR->isChecked() && m_copy->dcmSRSuccessNum() == 0)
    {
        ui->checkBoxDcmSR->setChecked(false);
        MessageBoxFrame::warning(this, QString(), tr("There are no measurement results for DICOM SR!"));
    }
    emit closed();

    if (m_IsShowTransformInfo)
    {
        m_IsShowTransformInfo = false;
        MessageBoxFrame::tipInformation(tr("Transform Task has been added to the Task Queue!"));
    }
}

void NetworkStorageFileWidget::on_pushButtonExport_clicked()
{
    MessageBoxFrame* infoMsg = MessageBoxFrame::showInformation(this, QString(), tr("Prepare to copy, please wait..."));
    QTimer::singleShot(1000, this, [=]() {
        if (ui->checkBoxGDPR->isChecked())
        {
            if (!GDPRUtility::isSupportGDPR(m_filePaths))
            {
                MessageBoxFrame::warning(QObject::tr("Some images cannot be de-identified!"));
            }
        }
        if (!m_isCopy)
        {
            m_isCopy = true;

            if (ui->comboBoxNetworkServer->currentText().isEmpty())
            {
                infoMsg->close();
                MessageBoxFrame::warning(tr("Could not find service!"));
                m_isCopy = false;
                return;
            }
            QString servicename = ui->comboBoxNetworkServer->currentText();

            // 每次发送前检查是否成功挂载，并且重置opened标志
            // 目的是为了防止在程序运行过程中，人为的或者其他因素挂载或者卸载该目录.，导致发送不成功
            SmbNetFsController::instance().checkOpened(
                SmbNetFsController::instance().getServeNameToDirName(servicename));

            if (!SmbNetFsController::instance().getDirNameIsMountd(servicename))
            {
                SmbNetFsController::instance().mountDirAndInsMod(servicename);
            }

            if (!SmbNetFsController::instance().getDirNameIsMountd(servicename))
            {
                infoMsg->close();
                MessageBoxFrame::warning(tr("Can not use network storage share!"));
                m_isCopy = false;
                return;
            }

            if (!m_isCurrentMulticChoice)
            {
                QString dirName = ui->lineEditDir->text().trimmed();

                if (dirName.isEmpty())
                {
                    infoMsg->close();
                    MessageBoxFrame::warning(tr("Enter the file name!"));
                    m_isCopy = false;
                }
                else
                {
                    QString finalDir =
                        SmbNetFsController::instance().fileDirName(ui->comboBoxNetworkServer->currentText(), dirName);

                    QDir dir(finalDir);

                    if (!dir.exists())
                    {
                        if (Util::Mkdir(finalDir) == 0)
                        {
                            infoMsg->close();
                            setCopyOptions();
                            rptToPdfSelf();
                            m_IsCopyThreadStarted = m_threadModel.copy(m_filePaths, finalDir, false);
                            m_FinalDir = finalDir;
                        }
                        else
                        {
                            infoMsg->close();
                            MessageBoxFrame::tipWarning(this, QString(), tr("Invalid network storage path"));
                            emit closed();
                        }
                    }
                    else
                    {
                        infoMsg->close();
                        int ret = MessageBoxFrame::question(this, tr("Export"),
                                                            tr("Folder already exists!\nAre you sure to combine?"),
                                                            QMessageBox::Yes | QMessageBox::No);

                        if (ret == QMessageBox::Yes)
                        {
                            setCopyOptions();
                            rptToPdfSelf();
                            m_IsCopyThreadStarted = m_threadModel.copy(m_filePaths, finalDir, false);
                            m_FinalDir = finalDir;
                        }
                        else if (ret == QMessageBox::No)
                        {
                            m_isCopy = false;
                        }
                    }
                }
            }
            else
            {
                infoMsg->close();
                QString finalDir = SmbNetFsController::instance().dirName(ui->comboBoxNetworkServer->currentText());

                QDir dir(finalDir);

                m_threadModel.setIsMkPatientWithNameDir(true);
                setCopyOptions();
                rptToPdfSelf();
                m_IsCopyThreadStarted = m_threadModel.copy(m_filePaths, dir.absolutePath(), false);
                m_FinalDir = dir.absolutePath();
            }
        }
    });
}

void NetworkStorageFileWidget::checkIfNeedSingleFrameImgData(QStringList& filePaths)
{
    QFileInfoList infoList;

    foreach (QString fileName, filePaths)
    {
        infoList << QFileInfo(fileName);
    }

    //    if (!ui->checkBoxSingleFrameWithRawData->isChecked())
    {
        QFileInfoList tempInfoList = infoList;
        QSet<QString> filters;

        foreach (QFileInfo info, tempInfoList)
        {
            if (info.completeSuffix() == Resource::jpgSuffix || info.completeSuffix() == Resource::bmpSuffix)
            {
                filters << info.baseName();
            }
        }

        foreach (QFileInfo info, tempInfoList)
        {
            if (filters.contains(info.baseName()) && info.completeSuffix() == Resource::imgSuffix)
            {
                infoList.removeOne(info);
            }
        }
    }

    QStringList resPaths;

    foreach (QFileInfo info, infoList)
    {
        resPaths << info.absoluteFilePath();
    }

    filePaths = resPaths;
}

void NetworkStorageFileWidget::on_radioButtonBMP_toggled(bool checked)
{
    if (checked)
    {
        m_ImageType = Resource::bmpSuffix;
        m_copy->setImageType(ImageProcessedCopy::BMP);
        emit exportTypeChanged(m_ImageType);
    }
}

void NetworkStorageFileWidget::on_radioButtonJPG_toggled(bool checked)
{
    if (checked)
    {
        m_ImageType = Resource::jpgSuffix;
        m_copy->setImageType(ImageProcessedCopy::JPG);
        emit exportTypeChanged(m_ImageType);
    }
}

void NetworkStorageFileWidget::on_radioButtonDCM_toggled(bool checked)
{
    if (checked)
    {
        m_ImageType = Resource::dcmSuffix;
        m_copy->setImageType(ImageProcessedCopy::DCM);
        emit exportTypeChanged(m_ImageType);
    }
    ui->dcmOptionwidget->setVisible(checked);
}

void NetworkStorageFileWidget::responseUIToFilePaths()
{
    foreach (QString fileName, m_filePaths)
    {
        QFileInfo file(fileName);

        if (file.suffix() == Resource::imgSuffix || file.suffix() == Resource::jpgSuffix ||
            file.suffix() == Resource::bmpSuffix)
        {
            ui->checkBoxDeleteAfterSending->hide();
            return;
        }
    }
}

void NetworkStorageFileWidget::on_checkBoxGDPR_toggled(bool checked)
{
    m_copy->setIsGDPR(checked);
    if (checked)
    {
        m_DefaultDir = ui->lineEditDir->text();
        QDateTime current_date_time = QDateTime::currentDateTime();
        QString current_date_time_ = current_date_time.toString("yyyyMMdd.hhmmss.z");
        setDefaultDirName(current_date_time_);
        ui->lineEditDir->setEnabled(false);
    }
    else
    {
        setDefaultDirName(m_DefaultDir);
        ui->lineEditDir->setEnabled(true);
    }
}

void NetworkStorageFileWidget::on_radioButtonPNG_toggled(bool checked)
{
    if (checked)
    {
        m_ImageType = Resource::pngSuffix;
        m_copy->setImageType(ImageProcessedCopy::PNG);
        emit exportTypeChanged(m_ImageType);
    }
}
