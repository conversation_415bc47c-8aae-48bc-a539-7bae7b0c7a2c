<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FreeHand3DWidget</class>
 <widget class="QWidget" name="FreeHand3DWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>540</width>
    <height>534</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="FreeHand3DRenderWidget" name="freehand3DRenderWidget" native="true">
     <property name="minimumSize">
      <size>
       <width>540</width>
       <height>512</height>
      </size>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QLabel" name="labelMode">
       <property name="minimumSize">
        <size>
         <width>60</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>60</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="text">
        <string>Rotate</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <property name="spacing">
        <number>3</number>
       </property>
       <property name="leftMargin">
        <number>9</number>
       </property>
       <item>
        <widget class="QLabel" name="labelX">
         <property name="minimumSize">
          <size>
           <width>24</width>
           <height>0</height>
          </size>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelY">
         <property name="minimumSize">
          <size>
           <width>24</width>
           <height>0</height>
          </size>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelZ">
         <property name="minimumSize">
          <size>
           <width>24</width>
           <height>0</height>
          </size>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>FreeHand3DRenderWidget</class>
   <extends>QWidget</extends>
   <header>freehand3drenderwidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
