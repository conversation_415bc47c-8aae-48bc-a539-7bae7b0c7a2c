/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#include "autotesttaskgenerator.h"
#include "burnintest.h"
#include "autotest.h"
#include "stateeventnames.h"
#include "formula.h"
#include <QDebug>

AutoTestTaskGenerator::AutoTestTaskGenerator()
    : m_lastEventTimeInterval(0)
{
}

void AutoTestTaskGenerator::organize(AutoTest* test)
{
    const QHash<int, TinyEvent>& eventScripts = test->eventScripts();
    int eventScriptsCount = eventScripts.count();

    m_lastEventTimeInterval = eventScripts.value(eventScriptsCount).timeInterval;

    int loopTime = makeLoopTime(test);

    QMap<int, QStringList> mapEvent;
    mapEvent[0] = eventScripts[1].eventList;

    const QHash<QString, int>& circleEvents = test->circleEvents();
    for (int i = 1, eventCount = 0, loop = 1; i < loopTime + 1; ++i, ++loop)
    {
        QHash<QString, int>::const_iterator iter = circleEvents.constBegin();

        while (iter != circleEvents.constEnd())
        {
            if (i % iter.value() == 0)
            {
                QStringList strList = iter.key().split(",");
                foreach (QString str, strList)
                {
                    QStringList strList = mapEvent[i];
                    strList << str;
                    mapEvent[i] = strList;
                }
            }

            ++iter;
        }

        if (eventScriptsCount > 0 &&
            loop % eventScripts.value((eventCount % eventScriptsCount) + 1).timeInterval == 0 && i != loopTime)
        {
            QStringList strList = mapEvent[i];
            strList << eventScripts.value(((eventCount + 1) % eventScriptsCount) + 1).eventList;
            mapEvent[i] = strList;
            eventCount++;
            loop = 0;
        }
    }

    generateTaskList(mapEvent);
}

QList<Task> AutoTestTaskGenerator::taskList() const
{
    return m_taskList;
}

int AutoTestTaskGenerator::makeLoopTime(AutoTest* test) const
{
    int loopTime = 0;

    const QHash<int, TinyEvent>& eventScripts = test->eventScripts();
    int eventScriptsCount = eventScripts.count();

    int eventTotalTime = 0;
    for (int i = 0; i < eventScriptsCount; i++)
    {
        eventTotalTime += eventScripts.value(i + 1).timeInterval;
    }

    const QHash<QString, int>& circleEvents = test->circleEvents();

    QHash<QString, int>::const_iterator iter = circleEvents.constBegin();

    if (eventTotalTime != 0)
    {
        loopTime = eventTotalTime;
    }
    else
    {
        loopTime = iter.value();
    }

    while (iter != circleEvents.constEnd())
    {
        loopTime = Formula::leastCommonMultiple(loopTime, iter.value());

        ++iter;
    }

    Q_ASSERT(loopTime != 0);

    return loopTime;
}

void AutoTestTaskGenerator::checkEventOrder(QStringList& eventList)
{
    QStringList resList = eventList;

    if (resList.contains(StateEventNames::Probe))
    {
        resList.removeOne(StateEventNames::Probe);
        resList.prepend(StateEventNames::Probe);
    }

    if (resList.contains(StateEventNames::StoreImage))
    {
        resList.removeOne(StateEventNames::StoreImage);
        resList.prepend(StateEventNames::StoreImage);
    }

    eventList = resList;
}

void AutoTestTaskGenerator::generateTaskList(const QMap<int, QStringList>& mapEvent)
{
    if (mapEvent.count() == 0)
    {
        return;
    }

    m_taskList.clear();

    QMap<int, QStringList>::const_iterator i = mapEvent.constBegin();

    while (i != mapEvent.constEnd())
    {
        Task task;
        task.time = i.key();
        task.eventList = i.value();

        if (m_taskList.count() != 0)
        {
            m_taskList.last().timeInterval = task.time - m_taskList.last().time;
        }

        ++i;

        m_taskList << task;
    }

    m_taskList.last().timeInterval = m_lastEventTimeInterval;
}
