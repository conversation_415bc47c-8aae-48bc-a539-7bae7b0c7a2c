<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ODSFourDLiveWidget</class>
 <widget class="QWidget" name="ODSFourDLiveWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>640</width>
    <height>624</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>3</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="ODSFourDRenderWidget" name="fourDRenderWidget" native="true">
     <property name="minimumSize">
      <size>
       <width>640</width>
       <height>556</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>640</width>
       <height>556</height>
      </size>
     </property>
    </widget>
   </item>
   <item>
    <widget class="FourDFreezeBarWidget" name="fourDFreezeBarWidget" native="true">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>20</height>
      </size>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="FourDTransformWidget" name="fourDTransformWidget" native="true"/>
     </item>
     <item>
      <widget class="GrayCurveWidget" name="grayCurveThumbnail" native="true">
       <property name="minimumSize">
        <size>
         <width>40</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>40</width>
         <height>16777215</height>
        </size>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>FourDFreezeBarWidget</class>
   <extends>QWidget</extends>
   <header>fourdfreezebarwidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>FourDTransformWidget</class>
   <extends>QWidget</extends>
   <header>fourdtransformwidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>GrayCurveWidget</class>
   <extends>QWidget</extends>
   <header>graycurvewidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>ODSFourDRenderWidget</class>
   <extends>QWidget</extends>
   <header>odsfourdrenderwidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
