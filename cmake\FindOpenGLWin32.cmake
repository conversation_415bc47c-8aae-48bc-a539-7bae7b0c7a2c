# Win32 OpenGL文件放置在源代码的目录下的第三方库目录
#

#set(OPENGL_INCLUDE_ROOT "C:/Program Files (x86)/Windows Kits/8.1/Include")
#set(OPENGL_LIB_ROOT "C:/Program Files (x86)/Windows Kits/8.1/Lib/winv6.3/um/x86")
set(OPENGL_INCLUDE_ROOT "C:/Program Files (x86)/Windows Kits/10/Include/10.0.18362.0")
set(OPENGL_LIB_ROOT "C:/Program Files (x86)/Windows Kits/10/Lib/10.0.18362.0/um/x64")

set(OPENGL_INCLUDE_DIR "${OPENGL_INCLUDE_ROOT}/um/"
                       "${OPENGL_INCLUDE_ROOT}/" CACHE STRING "opengl include")
set(OPENGL_LIBRARY "${OPENGL_LIB_ROOT}/OpenGL32.Lib"
                   "${OPENGL_LIB_ROOT}/GlU32.Lib" CACHE STRING "opengl library")
