<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>GlanceImagesTopWidget</class>
 <widget class="QWidget" name="GlanceImagesTopWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>761</width>
    <height>49</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="0,0">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>ID</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="BaseComboBox" name="comboBoxId">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>160</width>
         <height>0</height>
        </size>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <widget class="QLabel" name="label_3">
       <property name="text">
        <string>Name</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditName">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BaseComboBox</class>
   <extends>QComboBox</extends>
   <header>basecombobox.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
