/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#ifndef BODYMARKQBITWIDGET_H
#define BODYMARKQBITWIDGET_H
#include "controls_global.h"

#include <QWidget>

class ImageSkimManager;

namespace Ui
{
class BodyMarkQBitWidget;
}

class CONTROLSSHARED_EXPORT BodyMarkQBitWidget : public QWidget
{
    Q_OBJECT

public:
    explicit BodyMarkQBitWidget(QWidget* parent = 0);
    ~BodyMarkQBitWidget();
    ImageSkimManager* bodyMarkWidget() const;

private:
    Ui::BodyMarkQBitWidget* ui;
};

#endif // BODYMARKQBITWIDGET_H
