#ifndef THREADMODEL_H
#define THREADMODEL_H
#include "archive_global.h"

#include <QObject>
#include "copythread.h"
#include "deletethread.h"
#include "burnthread.h"

class ProgressBarView;
class ConfirmDialogFrame;
class DataBaseViewModel;
class BurnThread;
class BackupModel;

class ARCHIVESHARED_EXPORT ThreadModel : public QObject
{
    Q_OBJECT
public:
    explicit ThreadModel(QObject* parent = 0);
    ~ThreadModel();
    void setProgressBar(ProgressBarView* bar);
    void setConfirmDialog(ConfirmDialogFrame* fram);
    void setICopy(ICopy* copy);
    /**
     * @brief copy 判断磁盘空间是否够，然后启动拷贝线程
     *
     * @param fileList
     * @param dir
     *
     * @return false:磁盘空间不够, true:已经开始拷贝线程
     */
    bool copy(const QStringList& fileList, const QString& dir, bool checkDiskSpace = true);
    void deleteFile(const QFileInfoList& fileList);
    void setDeleteDataBaseModel(DataBaseViewModel* model);
    void setValue(int value);
    void setIsMkPatientDir(bool enable);
    void setIsMkPatientWithNameDir(bool enable);
    void setIfDeleteParentDir(bool deleteParentDir);
    void setNeedSingleFrameImgData(bool value);
    const QStringList& currentRptFileNames() const;
    void setNotCopyFileSuffixFilters(const QStringList& value);
    void stopThread();
    bool copyThreadIsRunning();
    bool burn(const QStringList& fileList, bool checkDiskSpace = false);
    void setBackupModel(BackupModel* backupModel);
    void setBackupPatients(const QList<Patient*>& patients);
    void setNeedCreateEmptyCopy(bool value = false);
    /**
     * @brief setRootPath
     * @param rootPath 目标根目录
     */
    void setRootPath(const QString& rootPath);
signals:
    void deleteThreadFinished();
    void threadStarted(CopyThread* thread);
    void threadFinished(bool);
    void burnThreadFinished(); //刻录完成
public slots:
    void emitThreadStarted();
    void emitThreadFinished();
    void showConfirmDlg(bool show);
    void resumeCopy(int value);
    bool terminateBurnThread();
private slots:
    // 进行rpt转换
    void onRptFileCopyed(const QString& fileName);

private:
    ProgressBarView* m_ProgressBar;
    ConfirmDialogFrame* m_ConfirmDialog;
    CopyThread m_CopyThread;
    DeleteThread m_DeleteThread;
    QStringList m_currentRptFileNames;
    BurnThread m_BurnThread;
    QString m_RootPath;
};

#endif // THREADMODEL_H
