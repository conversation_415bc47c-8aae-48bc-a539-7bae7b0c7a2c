#include "glanceimages.h"

#include <QScopedPointer>

#include "appsetting.h"
#include "aviplaywidget.h"
#include "buttonsboxframe.h"
#include "dicomregister.h"
#include "dicomseverinfowidget.h"
#include "exportfilewidget.h"
#include "functional"
#include "generalworkflowfunction.h"
#include "imagelabelinfo.h"
#include "imagereport.h"
#include "imageskimtoolwidget.h"
#include "imageskimwidget.h"
#include "istatemanager.h"
#include "messageboxframe.h"
#include "model/patient.h"
#include "model/study.h"
#include "modeldirectorygetter.h"
#include "netinfo.h"
#include "patientworkflow.h"
#include "progressbarview.h"
#include "reportfileloadhelper.h"
#include "resource.h"
#include "systeminfo.h"
#include "thumbnailviewutil.h"
#include "ui_glanceimages.h"
#include "uiutil.h"
#include "util.h"
#include <QDir>
#include <QScreen>

const char* const GlanceImages::m_AnimalString[] = {QT_TRANSLATE_NOOP("GlanceImages", "Animal Information")};

GlanceImagesDialog::GlanceImagesDialog(IStateManager* stateManager, QWidget* parent)
    : BaseInputAbleDialogFrame(parent, true)
    , m_Child(NULL)
{
    setCategory("EasyView");
    setShowTopWidget(true);
    setAutoZOrder(false);
    m_Child = new GlanceImages(this);
    this->setContent(m_Child);
    m_Child->setParentDialog(this);
    m_Child->setStateManager(stateManager);
    connect(this, SIGNAL(buttonClicked(int)), m_Child, SLOT(onButtonClicked(int)));
    connect(m_Child, SIGNAL(entryPatientState()), this, SIGNAL(entryPatientState()));
    connect(m_Child, SIGNAL(closedCurrentWidget()), this, SLOT(reject()));
    connect(m_Child, SIGNAL(onlyJump()), this, SIGNAL(onlyJump()));
    connect(m_Child, SIGNAL(entryArchiveState()), this, SIGNAL(entryArchiveState()));
    connect(m_Child, SIGNAL(studyOidChanged(QString)), this, SIGNAL(studyOidChanged(QString)));
    connect(m_Child, SIGNAL(currentDiskPathChanged(QString)), this, SIGNAL(currentDiskPathChanged(QString)));
    connect(m_Child, SIGNAL(orientationChanged(bool)), this, SLOT(onOrientationChanged(bool)));
    connect(this, SIGNAL(closed()), m_Child, SLOT(onClosed()));
    connect(m_Child, SIGNAL(entryContinueExamState()), this, SIGNAL(entryContinueExamState()));
    connect(m_Child, SIGNAL(entryEditExamState()), this, SIGNAL(entryEditExamState()));
    connect(m_Child, SIGNAL(fourdCallBack(QString)), this, SIGNAL(fourdCallBack(QString)));

    connect(this, SIGNAL(tpButClicked(QString, QString, QString)), m_Child,
            SIGNAL(tpButClicked(QString, QString, QString)));

    titleColorUseQss("glanceMenuTitleBar");
    containerColorUseQss("glanceChildContainer");

    //解决将qglwidget还成qopenglwidget后，这个界面不刷新的问题。具体原因未知  --TODO
    setFixedSize(this->geometry().width() - 1, this->geometry().height());
}

GlanceImages* GlanceImagesDialog::glanceImages() const
{
    return m_Child;
}

void GlanceImagesDialog::load(const QString& fileName)
{
    m_Child->load(fileName);
}

void GlanceImagesDialog::load(const QStringList& list)
{
    m_Child->load(list);
}

void GlanceImagesDialog::setPatientWorkflow(PatientWorkflow* patientWorkflow)
{
    m_Child->setPatientWorkflow(patientWorkflow);
}

void GlanceImagesDialog::setGeneralWorkflowFunction(GeneralWorkflowFunction* generalWorkflowFunction)
{
    m_Child->setGeneralWorkflowFunction(generalWorkflowFunction);
}

void GlanceImagesDialog::setPatientInfoList(const QHash<QString, QString>& idInfo)
{
    m_Child->setPatientInfoList(idInfo);
}

void GlanceImagesDialog::setDiskPath(const QString& path)
{
    m_Child->setDiskPath(path);
}

void GlanceImagesDialog::setDicomTaskManager(DicomTaskManager* manager)
{
    m_Child->setDicomTaskManager(manager);
}

void GlanceImagesDialog::print()
{
    m_Child->print();
}

QString GlanceImagesDialog::diskPath() const
{
    return m_Child->diskPath();
}

QStringList GlanceImagesDialog::printableImages() const
{
    return m_Child->printableImages();
}

void GlanceImagesDialog::setToolsFacade(IToolsFacade* value)
{
    m_Child->setToolsFacade(value);
}

void GlanceImagesDialog::setColorMapManager(IColorMapManager* colorMapManager)
{
    m_Child->setColorMapManager(colorMapManager);
}

void GlanceImagesDialog::onTPButClicked(const QString& category, const QString& butName, const QString& value)
{
    BaseInputAbleDialogFrame::onTPButClicked(category, butName, value);
    emit tpButClicked(category, butName, value);
}

void GlanceImagesDialog::onOrientationChanged(bool isHor)
{
    if (m_ButtonsBoxFrame != nullptr)
    {
        m_ButtonsBoxFrame->setIsHor(isHor);
    }
}

void GlanceImagesDialog::resizeEvent(QResizeEvent*)
{
}

GlanceImages::GlanceImages(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::GlanceImages)
    , m_PatientWorkflow(NULL)
    , m_GeneralWorkflowFunction(NULL)
    , m_ProgressBarView(NULL)
    , m_ImageSuffixes(QStringList() << Resource::imgExt << Resource::cineExt)
    , m_DicomTaskManager(NULL)
    , m_selectProperty(GlanceImages::All)
    , m_AviPlayDialog(NULL)
    , m_Parent(NULL)
    , m_Model(ScreenOrientationModel::getInstance())
    , m_StateManager(NULL)
    , m_ColorMapManager(NULL)
{
    ui->setupUi(this);
    m_BaseInputAbleDialogFrame = dynamic_cast<BaseInputAbleDialogFrame*>(parent);
    ui->radioButton2->click();

    initImageSkimManager();

    connect(ui->pushButtonUp, SIGNAL(clicked()), ui->widgetImages, SLOT(prePage()));
    connect(ui->pushButtonDown, SIGNAL(clicked()), ui->widgetImages, SLOT(nextPage()));
    connect(ui->widgetTop, SIGNAL(currentPatientInfoChanged(QString)), this,
            SLOT(onCurrentPatientInfoChanged(QString)));
    connect(this, SIGNAL(tpButClicked(QString, QString, QString)), this,
            SLOT(onTPButClicked(QString, QString, QString)));
    Util::disablePushButtonDefault(this);

    m_ButtonsText << QT_TR_NOOP("") << QT_TR_NOOP("New Exam") << QT_TR_NOOP("Continue Exam") << QT_TR_NOOP("Archive")
                  << QT_TR_NOOP("Exit");
    m_ButtonsName << ""
                  << "newExam"
                  << "continueExam"
                  << "archive"
                  << "exit";

    ui->widgetImages->setEnableCineEasyView(true);
    this->setAttribute(Qt::WA_StyledBackground, true);

    ui->widgetImages->setSupportSlider(false);

    if (AppSetting::isAnimalAndChinese())
    {
        Util::setPatient2Animal(ui->pushButtonInformation, "GlanceImages", m_AnimalString[Animal_Information]);
    }
}

GlanceImages::~GlanceImages()
{
    delete ui;
    Util::SafeDeletePtr(m_ProgressBarView);
}

void GlanceImages::setBufferManager(StressEchoBufferManager* bufferManager)
{
    ui->widgetImages->setBufferManager(bufferManager);
}

void GlanceImages::setBeamFormer(IBeamFormer* beamformer)
{
    ui->widgetImages->setBeamFormer(beamformer);
}

void GlanceImages::load(const QString& fileName)
{
    ui->widgetImages->load(fileName);
}

void GlanceImages::load(const QStringList& list)
{
    ui->widgetImages->load(list);
}

void GlanceImages::setPatientWorkflow(PatientWorkflow* patientWorkflow)
{
    m_PatientWorkflow = patientWorkflow;
}

void GlanceImages::setGeneralWorkflowFunction(GeneralWorkflowFunction* generalWorkflowFunction)
{
    m_GeneralWorkflowFunction = generalWorkflowFunction;
}

QWidget* GlanceImages::imageView() const
{
    return ui->widgetImages;
}

void GlanceImages::setPatientInfoList(const QHash<QString, QString>& idInfo)
{
    m_PatientBaseInfo = idInfo;
    QStringList sortList = idInfo.keys();
    std::sort(sortList.begin(), sortList.end(), std::greater<QString>());
    ui->widgetTop->setPatientInfoList(sortList);
}

void GlanceImages::setDiskPath(const QString& path)
{
    m_DiskPath = path;
}

void GlanceImages::ascertainButtonState(const QString& studyOid)
{
    bool continues = m_GeneralWorkflowFunction->isOrNotContinueStudy(studyOid);
    if (continues)
    {
        m_BaseInputAbleDialogFrame->setButtonEnabled(true, GlanceImagesDialog::button_new);
        m_BaseInputAbleDialogFrame->setButtonEnabled(true, GlanceImagesDialog::button_continue);
        m_BaseInputAbleDialogFrame->setButtonText(QT_TR_NOOP("Continue Exam"), GlanceImagesDialog::button_continue);
        m_ButtonTextIsContinue = true;
    }
    else if (!continues && !studyOid.isEmpty())
    {
        m_BaseInputAbleDialogFrame->setButtonEnabled(true, GlanceImagesDialog::button_new);
        m_BaseInputAbleDialogFrame->setButtonEnabled(!ui->widgetImages->allImageInfoList().isEmpty(),
                                                     GlanceImagesDialog::button_continue);
        m_BaseInputAbleDialogFrame->setButtonText(QT_TR_NOOP("Edit Exam"), GlanceImagesDialog::button_continue);
        m_ButtonTextIsContinue = false;
    }

    if (studyOid.isEmpty())
    {
        m_BaseInputAbleDialogFrame->setButtonEnabled(false, GlanceImagesDialog::button_new);
        m_BaseInputAbleDialogFrame->setButtonEnabled(false, GlanceImagesDialog::button_continue);
    }

    //非本地文件不可editExam,需要修改edit后实现加载非本地的数据库内容,否则会出现加载无数据现象
    if ((m_DiskPath != Resource::hardDiskDir) || studyOid.startsWith(Resource::dicomRetrievePrefix))
    {
        m_BaseInputAbleDialogFrame->setButtonEnabled(false, GlanceImagesDialog::button_continue);
        m_BaseInputAbleDialogFrame->setButtonText(QT_TR_NOOP("Edit Exam"), GlanceImagesDialog::button_continue);
        m_ButtonTextIsContinue = false;
    }
}

void GlanceImages::setDicomTaskManager(DicomTaskManager* manager)
{
    m_DicomTaskManager = manager;
}

void GlanceImages::print()
{
    QStringList images = ui->widgetImages->selectedImagePaths();
    if (!images.isEmpty())
    {
        ImageReport report;
        report.setPatient(GeneralWorkflowFunction::getPatient(m_StudyOid));
        report.setImages(images);
        report.setColorMapManager(m_ColorMapManager);
        report.print();
    }
    else
    {
        MessageBoxFrame::warning(this, QString(), QObject::tr("Please select the images you want to print!"));
    }
}

QStringList GlanceImages::printableImages()
{
    return ui->widgetImages->selectedImagePaths();
}

void GlanceImages::setSelectAllProperty(GlanceImages::SelectAllProperty property)
{
    m_selectProperty = property;
}

QString GlanceImages::diskPath() const
{
    return m_DiskPath;
}

bool GlanceImages::isHor()
{
    return m_Model->isHor();
}

void GlanceImages::retranslateUi()
{
    ui->retranslateUi(this);
    if (AppSetting::isAnimalAndChinese())
    {
        Util::setPatient2Animal(ui->pushButtonInformation, "GlanceImages", m_AnimalString[Animal_Information]);
    }
}

void GlanceImages::orientationChanged(Qt::ScreenOrientation orientation)
{
    QString className(this->metaObject()->className());
    OrientationConfigItem config;
    m_Model->getInfosByClassName(orientation, className, config);
    UiUtil::ScreenHVChangedUpdateUI(this, ui->gridLayout_3, config);
#ifdef USE_SIMULATEORIENTATION
    emit orientationChanged(m_Model->isHor());
#else
    emit orientationChanged(orientation == Qt::LandscapeOrientation);
#endif
}

void GlanceImages::changeRC(const int rc)
{
    ui->widgetImages->setRowAndColumn(rc, rc);
}

void GlanceImages::onButtonClicked(int index)
{
    if (!m_Parent->isEnabled())
        return;
    switch (index)
    {
    case GlanceImagesDialog::button_new:
        if (!m_StudyOid.isEmpty())
        {
            m_GeneralWorkflowFunction->setExamEnd(m_DiskPath);
            emit currentDiskPathChanged(m_DiskPath);
            emit studyOidChanged(m_StudyOid);
            emit closedCurrentWidget();
            emit entryPatientState();
        }
        break;
    case GlanceImagesDialog::button_continue:
    {
        if (m_ButtonTextIsContinue)
        {
            if (!m_StudyOid.isEmpty())
            {
                if (m_PatientWorkflow->patient() != NULL &&
                    m_PatientWorkflow->patient()->firstStudy()->StudyOid() != m_StudyOid)
                {
                    m_PatientWorkflow->setCurrentExamEnd();
                    m_GeneralWorkflowFunction->continueStudy(m_StudyOid);
                }
                else if (m_PatientWorkflow->patient() == NULL)
                {
                    m_GeneralWorkflowFunction->continueStudy(m_StudyOid);
                }
            }
            emit closedCurrentWidget();
            emit entryContinueExamState();
        }
        else
        {
            if (m_PatientWorkflow->patient() != NULL &&
                m_PatientWorkflow->patient()->firstStudy()->StudyOid() != m_StudyOid)
            {
                m_PatientWorkflow->setCurrentExamEnd();
                m_GeneralWorkflowFunction->editStudy(m_StudyOid);
            }
            else if (m_PatientWorkflow->patient() == NULL)
            {
                m_GeneralWorkflowFunction->editStudy(m_StudyOid);
            }
            emit closedCurrentWidget();
            emit entryEditExamState();
        }
        break;
    }
    case GlanceImagesDialog::button_archive:
        if (m_Parent != NULL)
            m_Parent->setDisabled(true);
        setDisabled(true);
        //        emit closedCurrentWidget();
        emit onlyJump();
        emit entryArchiveState();
        break;
    case GlanceImagesDialog::button_null:
        break;
    case GlanceImagesDialog::button_cancel:
        emit closedCurrentWidget();
        break;
    }
}

void GlanceImages::on_pushButtonInformation_clicked()
{
    if (!m_StudyOid.isEmpty())
    {
        m_GeneralWorkflowFunction->showPatientInfo(m_StudyOid);
    }
}

void GlanceImages::on_pushButtonReport_clicked()
{
    QScopedPointer<Patient> patient(m_GeneralWorkflowFunction->getPatient(m_StudyOid));

    if (m_DicomTaskManager != NULL && AppSetting::isDicom())
    {
        ReportFileLoadHelper::loadReport(patient.data(), m_DicomTaskManager);
    }
    else
    {
        ReportFileLoadHelper::loadReport(patient.data());
    }
}

void GlanceImages::on_pushButtonSend_clicked()
{
    if (selectedImagePathsList().isEmpty())
    {
        MessageBoxFrame::warning(this, QString(), QObject::tr("Please select the images you want to send!"));
        return;
    }
    bool pflag = false, sflag = false, uflag = false, nflag = false, bflag = false;

    GeneralWorkflowFunction::hasUsableDevice(this, &pflag, &sflag, &uflag, &nflag, &bflag);

    if (pflag || sflag || uflag || nflag || bflag)
    {
        //        DicomSeverInfoDialog dlg(this);
        QScopedPointer<Patient> patient(GeneralWorkflowFunction::getPatient(m_StudyOid));
        //        dlg.setDefaultDirName(PatientPath::instance().exportPatientDirName(patient.data()));
        //        dlg.setExportFilePaths(selectedImagePathsList());
        //        dlg.setExprotFileNames(selectedImagePathsList());
        //        qDebug() << "selectedImagePathsList()" <<selectedImagePathsList();
        //        dlg.setDicomTaskManager(m_DicomTaskManager);
        //        dlg.initWidegt();
        //        dlg.exec();
        GeneralWorkflowFunction::exportFiles(this, PatientPath::instance().exportPatientDirName(patient.data()),
                                             selectedImagePathsList(), selectedImagePathsList(), m_DicomTaskManager,
                                             m_StateManager, m_ColorMapManager);
    }
    else
    {
        MessageBoxFrame::warning(this, QString(), tr("There is no available device for export!"));
    }
    //    GeneralWorkflowFunction::exportFiles(this, selectedImagePathsList(), m_StudyOid);
}

void GlanceImages::on_pushButtonPrint_clicked()
{
    print();
}

void GlanceImages::on_pushButtonDelete_clicked()
{
    if (selectedImagePathsList().isEmpty())
    {
        MessageBoxFrame::warning(this, QString(), QObject::tr("Please select the images you want to delete!"));
        return;
    }
    if (m_ProgressBarView == NULL)
    {
        m_ProgressBarView = new ProgressBarView(m_StateManager, this);
    }
    QStringList pathList = selectedImagePathsList();
    if (GeneralWorkflowFunction::ifContinueOperate(this))
    {
        ui->widgetImages->clearLabels(pathList);
        GeneralWorkflowFunction::deleteFileList(pathList, &m_ThreadModel, m_ProgressBarView, false);
    }
}

void GlanceImages::on_radioButton4_toggled(bool checked)
{
    if (checked)
    {
        changeRC(4);
    }
}

void GlanceImages::on_radioButton2_toggled(bool checked)
{
    if (checked)
    {
        changeRC(2);
    }
}

void GlanceImages::on_radioButton1_toggled(bool checked)
{
    if (checked)
    {
        changeRC(1);
    }
}

void GlanceImages::onClosed()
{
    m_StudyOid.clear();
    m_DiskPath.clear();
    if (m_AviPlayDialog != NULL && m_AviPlayDialog->isVisible())
    {
        m_AviPlayDialog->hide();
    }
    ui->widgetImages->onClosed();
}

void GlanceImages::onCurrentPatientInfoChanged(const QString& text)
{
    QString id = text.split("(").at(0);
    QString oid = m_PatientBaseInfo.value(text);
    m_StudyOid = oid;
    ui->widgetImages->load(PatientPath::instance().path(id, oid, m_DiskPath));
    ascertainButtonState(m_StudyOid);
}

QStringList GlanceImages::selectedImagePathsList()
{
    return GeneralWorkflowFunction::imageInstanceFiles(ui->widgetImages->selectedImagePaths());
}

void GlanceImages::onTPButClicked(const QString& category, const QString& butName, const QString& value)
{
    qDebug() << "## GlanceImages::onTPButClicked" << category << butName << value;
    QStringList btnames = butName.split(":", Qt::SkipEmptyParts);

    if (category != "EasyView" || btnames.size() < 2 || btnames.at(0) != "EasyView")
    {
        return;
    }
    QString btn = btnames.last();
    if (btn == "Information")
    {
        on_pushButtonInformation_clicked();
    }
    else if (btn == "Report")
    {
        on_pushButtonReport_clicked();
    }
    else if (btn == "SendImages")
    {
        on_pushButtonSend_clicked();
    }
    else if (btn == "PrintImages")
    {
        on_pushButtonPrint_clicked();
    }
    else if (btn == "DeleteImages")
    {
        on_pushButtonDelete_clicked();
    }
    else if (btn == "PrePage")
    {
        ui->widgetImages->prePage();
    }
    else if (btn == "NextPage")
    {
        ui->widgetImages->nextPage();
    }
    else if (btn == "SelectAll")
    {
        on_pushButtonSelectAll_clicked();
    }
    else if (btn == "DeselectAll")
    {
        on_pushButtonDeselectAll_clicked();
    }
}

void GlanceImages::on_pushButtonSelectAll_clicked()
{
    switch (m_selectProperty)
    {
    case All:
        ui->widgetImages->setSelections(ui->widgetImages->allImagePaths());
        break;
    case CurrentPage:
    {
        ui->widgetImages->selectCurPageImage();
        break;
    }
    default:
        ui->widgetImages->setSelections(ui->widgetImages->allImagePaths());
        break;
    }
}

void GlanceImages::on_pushButtonDeselectAll_clicked()
{
    ui->widgetImages->setSelections(QStringList());
}

void GlanceImages::onFourDCallBack(const QString& filepath)
{
    QString fourdAviFileName = Util::getSameBaseNamesFileName(filepath, Resource::aviExt);
    if (QFile::exists(fourdAviFileName))
    {
        if (m_AviPlayDialog == NULL)
        {
            m_AviPlayDialog = new AviPlayDialog(fourdAviFileName, this);
        }
        else
        {
            m_AviPlayDialog->setFileName(fourdAviFileName);
        }
        // Avi框显示之前暂停状态机,为屏蔽顶部菜单按钮
        m_StateManager->setPaused(true);
        m_AviPlayDialog->exec();
        // Avi框恢复之后恢复状态机
        m_StateManager->setPaused(false);
    }
    else
    {
        emit fourdCallBack(filepath);
    }
}

void GlanceImages::initImageSkimManager()
{
    ImageSkimWidget* skimWidget = ui->widgetImages->getSkimWidget();
    skimWidget->setHorizontalSpacing(0);
    skimWidget->setVerticalSpacing(0);
    skimWidget->useQssColor("reviewContents");
    skimWidget->setBorderDraw(true);

    ui->widgetImages->changeToolWidgetLocation(ThumbnailViewUtil::Hidden);
    ui->widgetImages->setSelectType(ThumbnailViewUtil::Multi);
    ui->widgetImages->setEasyViewCallBack(true);

    connect(ui->widgetImages, SIGNAL(fourdCallBack(QString)), this, SLOT(onFourDCallBack(QString)));
}

void GlanceImages::showEvent(QShowEvent* e)
{
    if (m_Parent != NULL)
        m_Parent->setDisabled(false);
    setDisabled(false);
}

int GlanceImages::getRowCol()
{
    return ui->widgetImages->getRow();
}

void GlanceImages::setToolsFacade(IToolsFacade* value)
{
    ui->widgetImages->setToolsFacade(value);
}

void GlanceImages::setStateManager(IStateManager* value)
{
    m_StateManager = value;
}

void GlanceImages::setColorMapManager(IColorMapManager* colorMapManager)
{
    m_ColorMapManager = colorMapManager;
    ui->widgetImages->setColorMapManager(colorMapManager);
}

void GlanceImages::setImageSkimSpacing(int spacing)
{
    ui->widgetImages->getSkimWidget()->setSpacing(spacing);
}
