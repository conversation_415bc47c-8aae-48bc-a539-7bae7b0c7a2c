
build-phoenix-job:
  stage: build
  needs: [pre-build-job]
  tags:
    - Phoenix
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  script:
    - ./configure_unix_x86_64bit.sh -m Phoenix
  cache:
    key: build-Phoenix-cache
    paths:
      - build/Phoenix
    policy: push

test-phoenix-job:
  stage: test   
  needs: [build-phoenix-job]
  tags:
    - Phoenix
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  script:
    - ./ci/shell/runtest.sh -m Phoenix
  cache:
    key: build-Phoenix-cache
    paths:
      - build/Phoenix
    policy: pull

# deploy-phoenix-job:
#   stage: deploy
#   needs: [test-phoenix-job]   
#   tags:
#     - Phoenix_81
#   # rules:
#   #   - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
#   script:
#     - echo "deploy end ..."
#   when: manual
#   cache:
#     key: build-Phoenix-cache
#     paths:
#       - build/Phoenix
#     policy: pull

# release-phoenix-job:
#   stage: release
#   needs: [test-phoenix-job]   
#   tags:
#     - Phoenix
#   rules:
#     - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
#       when: manual
#   script:
#     - ./ci/shell/runrelease.sh -m Phoenix
#   cache:
#     key: build-Phoenix-cache
#     paths:
#       - build/Phoenix
#     policy: pull


