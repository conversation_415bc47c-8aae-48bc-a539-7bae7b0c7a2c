#include "adminconfigmodel.h"
#include "userpresetdatahandler.h"
#include "authorityinfo.h"
#include "adminconfigmodeldata.h"
#include "presetutilitytool.h"
#include "fingerprintinfo.h"
#ifdef USE_FINGERPRINT
#include "fingerprintdev.h"
#endif
#include "fingerprinttask.h"
#include "diskutil.h"
#include <stdio.h>
#include "modelconfig.h"
#include "syncfingerprintoperator.h"
#include "asyncfingerprintoperator.h"
#include "abstractmachine.h"
#include <QDebug>
#include "generalinfo.h"

AdminConfigModel::AdminConfigModel(QObject* parent)
    : QObject(parent)
    , db(UserPresetDataHandler::instance())
    , data(new AdminConfigModelData())
{
    db->getAllAuthorityInfo(data->m_authorities);
    db->getAllFingerprintInfo(data->m_fingerprints);
    updateUsers();

    m_emgencyId = "01_" + PresetUtilityTool::md5("Emergency").leftJustified(6, '.', true);
    m_serviceId = "01_" + PresetUtilityTool::md5("Service").leftJustified(6, '.', true);

    initAdmin();
    initEmergency();
    initService();

    createFPrintOperator();
}

AdminConfigModel* AdminConfigModel::instance()
{
    static AdminConfigModel model;
    return &model;
}

void AdminConfigModel::showLoginDlg()
{
    emit showScreenLoginDlg();
}

void AdminConfigModel::hideLoginDlg()
{
    emit clossLoginDlg();
}

void AdminConfigModel::setMachine(IMachine* value)
{
    if (m_Machine != value)
    {
        if (nullptr != m_Machine)
        {
            AbstractMachine* machine = dynamic_cast<AbstractMachine*>(m_Machine);
            // disconnet
            if (nullptr != machine)
            {
                disconnect(machine, SIGNAL(beginToStandby(bool&)), this, SLOT(onBeginToStandby(bool&)));
            }
        }
        m_Machine = value;
        AbstractMachine* machine = dynamic_cast<AbstractMachine*>(m_Machine);
        // connet
        if (nullptr != machine)
        {
            connect(machine, SIGNAL(beginToStandby(bool&)), this, SLOT(onBeginToStandby(bool&)), Qt::QueuedConnection);
        }
    }
}

const QStringList AdminConfigModel::getAuthorityNames() const
{
    return data->getAllAuthorityNames();
}

const QString AdminConfigModel::getAuthorityName(int authorityId) const
{
    return data->getAuthorityName(authorityId);
}

int AdminConfigModel::getUserAuthorityId(const QString& userName) const
{
    return data->getUserAuthorityId(userName);
}

const QList<UserInfo> AdminConfigModel::getAllUserInfo() const
{
    return data->m_baseUsers.values();
}

const QStringList AdminConfigModel::getAllUserNames() const
{
    return data->getAllUserNames();
}

const QString AdminConfigModel::getUserName(const QString& userId) const
{
    UserInfo user;
    data->getUserInfoById(userId, user);
    return user.userName();
}

bool AdminConfigModel::getUserInfo(const QString& username, UserInfo& user) const
{
    return data->getUserInfoByName(username, user);
}

const UserInfo AdminConfigModel::getUserInfo(const QString& userId) const
{
    UserInfo user;
    data->getUserInfoById(userId, user);

    return user;
}

bool AdminConfigModel::isUserExist(const QString& username) const
{
    UserInfo user;
    return getUserInfo(username, user);
}

bool AdminConfigModel::addUser(const QString& username, const QString& pwd, const QString& quthorityName)
{
    QString id = "00_" + PresetUtilityTool::md5(username).leftJustified(6, '.', true);
    UserInfo muser(id, username, pwd, data->getAuthorityId(quthorityName), false);
    bool ret = db->addUser(muser);
    if (ret)
    {
        updateUsers();
    }
    return ret;
}

bool AdminConfigModel::removeUser(const QString& username)
{
    UserInfo user;
    bool ret = getUserInfo(username, user);
    if (ret)
    {
        QList<FingerprintInfo> fingers = getFingerprintInfo(user.userId());
        foreach (const FingerprintInfo& finger, fingers)
        {
            m_FPrintOperator->remove(finger);
        }
        //删除数据库中对应用户的人脸信息;
    }

    ret = db->removeUser(username);

    if (ret)
    {
        updateUsers();
    }

    return ret;
}

int AdminConfigModel::login(const QString& username, const QString& pwd)
{
    int ret = -1;
    UserInfo user;
    getUserInfo(username, user);
    if (user.password() == pwd)
    {
        ret = 0;
        data->m_curUserId = user.userId();
        if (!user.isChanged())
        {
            ret = 1;
        }
    }
    else if (user.authorityId() == AuthorityInfo::Admin)
    {
        QString SN = GeneralInfo::instance().serialNumber();
        qDebug() << "## SN : " << SN;
        if (!SN.isEmpty() && SN == pwd)
        {
            resetPassword(username);
            ret = 2;
        }
    }

    return ret;
}

int AdminConfigModel::isCorrectPwd(const QString& username, const QString& pwd)
{
    int ret = -1;
    UserInfo user;
    getUserInfo(username, user);
    if (user.password() == pwd)
    {
        ret = 1;
    }

    return ret;
}

int AdminConfigModel::fpLogin(int fpid)
{
    FingerprintInfo fpinfo;
    if (data->getFingerById(fpid, fpinfo))
    {
        data->m_curUserId = fpinfo.userId();
        return 0;
    }
    else
    {
        return -1;
    }
}

int AdminConfigModel::fpLogin(const QString& userId)
{
    foreach (const auto& info, getAllUserInfo())
    {
        if (info.userId() == userId)
        {
            data->m_curUserId = info.userId();
            return 0;
        }
    }
    return -1;
}

bool AdminConfigModel::logOut() const
{
    if (!data->m_curUserId.isEmpty())
        data->m_preUserId = data->m_curUserId;
    data->m_curUserId = QString();

    return true;
}

bool AdminConfigModel::isLogin() const
{
    if (data->getCurUserId().isEmpty() || isEmergency())
    {
        return false;
    }
    UserInfo user;
    if (data->getUserInfoById(data->getCurUserId(), user))
    {
        return true;
    }
    else
    {
        return false;
    }
}

int AdminConfigModel::changePwd(const QString& pwd, const QString& newPwd)
{
    UserInfo user;
    if (!data->getUserInfoById(data->getCurUserId(), user))
    {
        return NO_USER;
    }
    if (user.password() != pwd)
    {
        return CUR_PASSWORD_INCORRECT;
    }
    if (pwd == newPwd)
    {
        return NEW_PASSWORD_SAMEAS_CUR_PASSWORD;
    }

    user.setPassword(newPwd);
    user.setIsChanged(true);
    if (db->updateUser(user))
    {
        updateUsers();
    }

    logOut();

    return SUCCEED;
}

void AdminConfigModel::setIsChanged(bool isChanged)
{
    //只有首次登陆时，需要弹出“修改秘密”弹窗；就算首次登陆修改密码cancel掉，下次登陆无需再弹出“修改秘密”弹窗
    UserInfo user;
    if (!data->getUserInfoById(data->getCurUserId(), user))
    {
        return;
    }

    user.setIsChanged(isChanged);
    if (db->updateUser(user))
    {
        updateUsers();
    }
}

int AdminConfigModel::resetPassword(const QString& username)
{
    int ret = -1;
    UserInfo user;
    if (getUserInfo(username, user))
    {
        if (user.authorityId() == AuthorityInfo::Admin)
        {
            user.setPassword("ADM123456");
        }
        else
        {
            user.setPassword("USER123456");
        }
        user.setIsChanged(false);
        if (db->updateUser(user))
        {
            updateUsers();
        }

        logOut();

        ret = 0;
    }

    return ret;
}

const UserInfo AdminConfigModel::getCurUserInfo() const
{
    UserInfo user;
    data->getUserInfoById(data->getCurUserId(), user);

    return user;
}

const QString AdminConfigModel::getCurUserId() const
{
    return data->getCurUserId();
}

void AdminConfigModel::emergency() const
{
    data->m_curUserId = m_emgencyId;
}

bool AdminConfigModel::isEmergency() const
{
    return (data->m_curUserId == m_emgencyId);
}

const QList<FingerprintInfo> AdminConfigModel::getFingerprintInfo(const QString& userId) const
{
    return data->getFingerprintInfo(userId);
}

const FingerprintInfo AdminConfigModel::getFingerprintInfo(int fingerIndex) const
{
    FingerprintInfo fpInfo;
    data->getFingerByIndex(fingerIndex, fpInfo);

    return fpInfo;
}

bool AdminConfigModel::addFingerprint(const FingerprintInfo& info)
{
    bool ret = db->addFingerprint(info);
    if (ret)
    {
        db->getAllFingerprintInfo(data->m_fingerprints);
    }
    return ret;
}

bool AdminConfigModel::removeFingerprint(int fingerIndex)
{
    bool ret = db->removeFingerprint(fingerIndex);
    if (ret)
    {
        db->getAllFingerprintInfo(data->m_fingerprints);
    }
    return ret;
}

bool AdminConfigModel::changeFingerName(int fingerIndex, const QString& newname)
{
    FingerprintInfo finger;
    bool ret = data->getFingerByIndex(fingerIndex, finger);
    if (ret)
    {
        finger.setFingerprintName(newname);
        ret = db->updateFingerprint(finger);
    }

    if (ret)
    {
        db->getAllFingerprintInfo(data->m_fingerprints);
    }
    return ret;
}

void AdminConfigModel::initAdmin()
{
    QString adminName = "Admin";
    UserInfo admin;
    bool ret = getUserInfo(adminName, admin);

    if (ret && admin.authorityId() == AuthorityInfo::Admin)
    {
        return;
    }

    QString id = "01_" + PresetUtilityTool::md5(adminName).leftJustified(6, '.', true);

    admin.setUserId(id);
    admin.setUserName(adminName);
    admin.setAuthorityId((int)AuthorityInfo::Admin);
#ifdef SYS_APPLE
    adminName = "admin";
    admin.setUserName(adminName);
    admin.setPassword("admin2020");
#else
    admin.setPassword("ADM123456");
#endif
    admin.setIsChanged(false);

    if (ret)
    {
        db->updateUser(admin);
    }
    else
    {
        db->addUser(admin);
    }

    updateUsers();
}

void AdminConfigModel::initEmergency()
{
    UserInfo emergency;
    bool ret = data->getUserInfoById(m_emgencyId, emergency);
    if (emergency.userId() == m_emgencyId && emergency.authorityId() == AuthorityInfo::User)
    {
        return;
    }
    emergency.setUserId(m_emgencyId);
#ifdef SYS_APPLE
    emergency.setUserName("emergency");
    emergency.setPassword("emergency2020");
#else
    emergency.setUserName("");
#endif
    emergency.setAuthorityId((int)AuthorityInfo::User);
    emergency.setIsChanged(true);

    if (ret)
    {
        db->updateUser(emergency);
    }
    else
    {
        db->addUser(emergency);
    }

    updateUsers();
}

void AdminConfigModel::initService()
{
    UserInfo service;
    bool ret = data->getUserInfoById(m_serviceId, service);
    if (service.userId() == m_serviceId && service.authorityId() == AuthorityInfo::Service &&
        service.isChanged() == true)
    {
        return;
    }
    service.setUserId(m_serviceId);
    service.setUserName("Service");
#ifdef SYS_APPLE
    service.setUserName("service");
    service.setPassword("service2020");
#else
    service.setPassword("SER123456");
#endif
    service.setAuthorityId((int)AuthorityInfo::Service);
    // Service密码不许更改，因此新建用户时设置为true
    service.setIsChanged(true);

    if (ret)
    {
        db->updateUser(service);
    }
    else
    {
        db->addUser(service);
    }

    updateUsers();
}

void AdminConfigModel::updateUsers()
{
    QList<UserInfo> users;
    if (db->getAllUserInfo(users))
    {
        data->updateUsers(users);
    }
}

bool AdminConfigModel::isSameUserId()
{
    return data->getCurUserId() == data->getPreUserId();
}

bool AdminConfigModel::isEmptyOfpreUserId()
{
    return data->getPreUserId().isEmpty();
}

void AdminConfigModel::setPreUserId()
{
    if (!data->m_curUserId.isEmpty())
        data->m_preUserId = data->m_curUserId;
}

void AdminConfigModel::createFPrintOperator()
{
    int fpType = ModelConfig::instance().value(ModelConfig::FingerPrintType, 1).toInt();
    qDebug() << PRETTY_FUNCTION << " fpType = " << fpType;
    if (fpType == 0)
    {
        m_FPrintOperator = new SyncFingerPrintOperator(this);
    }
    else if (fpType == 1)
    {
        m_FPrintOperator = new ASyncFingerPrintOperator(this);
    }
    else
    {
        qDebug() << PRETTY_FUNCTION << "error";
    }
}

void AdminConfigModel::onBeginToStandby(bool& accept)
{
    qDebug() << PRETTY_FUNCTION << " accept = " << accept;
    emit clossLoginDlg();
}

IFingerPrintOperator* AdminConfigModel::fPrintOperator() const
{
    return m_FPrintOperator;
}

QString AdminConfigModel::getServiceId()
{
    return m_serviceId;
}

bool AdminConfigModel::isValid(QString s)
{
    QRegExp rxNumber(".*[0-9].*");
    QRegExp rxCapitalLetters(".*[A-Z].*");
    QRegExp rxSmallLetters(".*[a-z].*");
    //密码有大写字母并且有数字，返回true
    if (rxNumber.exactMatch(s) && rxCapitalLetters.exactMatch(s))
    {
        return true;
    }
    //密码有小写字母并且有数字，返回true
    if (rxNumber.exactMatch(s) && rxSmallLetters.exactMatch(s))
    {
        return true;
    }
    //其他返回false
    return false;
}

void AdminConfigModel::updateUserAuthorityInfoToUser(UserInfo& userInfo)
{
    userInfo.setAuthorityId((int)AuthorityInfo::User);
    db->updateUser(userInfo);
}

bool AdminConfigModel::removeUserById(const QString& userId)
{
    return db->removeUserById(userId);
}
