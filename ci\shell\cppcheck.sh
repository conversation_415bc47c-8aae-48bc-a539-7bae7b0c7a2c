#!/bin/bash

# Function to check if cppcheck is installed
check_cppcheck()
{
    if ! command -v cppcheck &> /dev/null; then
        echo "Error: cppcheck is not installed. Please install it and try again."
        exit 1
    fi
}

# Function to run cppcheck and analyze the output
run_cppcheck()
{
    echo "Running cppcheck, please wait..."
    cppcheck --enable=all \
                      --error-exitcode=1 \
                      --inline-suppr \
                      -i build \
                      src > /dev/null 2>.cppcheckerror
                      
    if cat .cppcheckerror | grep -qE "uninitMemberVar|uninitMemberVarPrivate"; then
        echo "Error: Uninitialized member variable(s) detected:"
        cat .cppcheckerror | grep -E "uninitMemberVar|uninitMemberVarPrivate"
        echo "CI pipeline stopped due to uninitialized member variable(s)."
        exit 1
    fi

    echo "No uninitialized variables detected."
}

# Main script execution
check_cppcheck
run_cppcheck
