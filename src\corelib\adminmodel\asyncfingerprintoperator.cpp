#include "asyncfingerprintoperator.h"
#include "adminconfigmodel.h"
#include "userinfo.h"
#include "openfingerprinttask.h"
#include "fingercontroller.h"
#include "resource.h"
#include <QDebug>
#include "logger.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, ASyncFingerPrintOperator)
#define VERIFY_OK 0

static void enrollCBFunc(int finishedStage, int allStages, void* object)
{
    qDebug() << PRETTY_FUNCTION << " finishedStage = " << finishedStage << "; allStages = " << allStages;
    ASyncFingerPrintOperator* ops = reinterpret_cast<ASyncFingerPrintOperator*>(object);
    if (ops != nullptr)
    {
        ops->enrollCallBack(finishedStage, allStages);
    }
}

static void verifyCBFunc(const char* username, bool verified, void* object)
{
    qDebug() << PRETTY_FUNCTION << " username = " << username << "; verified = " << verified;
    ASyncFingerPrintOperator* ops = reinterpret_cast<ASyncFingerPrintOperator*>(object);
    if (ops != nullptr)
    {
        ops->verifyCallFunc(username, verified);
    }
}

ASyncFingerPrintOperator::ASyncFingerPrintOperator(AdminConfigModel* model, QObject* parent)
    : FingerPrintOperatorBase(parent)
    , m_AdminModel(model)
#ifdef USE_OPENFINGERPRINT
#ifdef USE_OPENFINGERPRINT_LIB
    , m_FingerprintDev(new OpenFingerprintDev())
#endif
#endif
    , m_IsRunning(false)
{
#ifdef USE_OPENFINGERPRINT
//加了一个宏USE_OPENFINGERPRINT_LIB，
//宏打开的时候直接调用指纹模块的库，
//宏关闭的时候，指纹模块是一个进程
#ifdef USE_OPENFINGERPRINT_LIB
    m_FingerprintDev->registerEnrollCallback(enrollCBFunc, this);
    m_FingerprintDev->registerVerifyCallback(verifyCBFunc, this);
#else
    log()->info() << PRETTY_FUNCTION << " executePath = " << Resource::fingerprintCmd
                  << "; fingerDeviceId = " << Resource::fingerDeviceId;
    FingerController::instance().setExecutePath(Resource::fingerprintCmd);
    FingerController::instance().setFingerDeviceId(Resource::fingerDeviceId);
    FingerController::instance().registerEnrollCallback(enrollCBFunc, this);
    FingerController::instance().registerVerifyCallback(verifyCBFunc, this);
#endif
#endif
}

ASyncFingerPrintOperator::~ASyncFingerPrintOperator()
{
}

IFingerPrintOperator::FPrintType ASyncFingerPrintOperator::fprintType() const
{
    return Async;
}

int ASyncFingerPrintOperator::maxCount()
{
    return 1;
}

bool ASyncFingerPrintOperator::enroll(const QString& userId)
{
#ifdef USE_OPENFINGERPRINT
#ifdef USE_OPENFINGERPRINT_LIB
    m_FingerprintDev->enroll(userId.toStdString());
#else
    FingerController::instance().enroll(userId);
#endif
#endif
    return true;
}

bool ASyncFingerPrintOperator::verify()
{
    m_IsRunning = true;

    m_VerifiedUserId.clear();
#ifdef USE_OPENFINGERPRINT
#ifdef USE_OPENFINGERPRINT_LIB
    FingerPrint::ErrorCode ret = m_FingerprintDev->verify();
    qDebug() << PRETTY_FUNCTION << " FingerPrint verify return is " << ret;
#else
    FingerController::instance().verify();
#endif
#endif

    m_IsRunning = false;

    return true;
}

void ASyncFingerPrintOperator::stop()
{
#ifdef USE_OPENFINGERPRINT
#ifdef USE_OPENFINGERPRINT_LIB
    m_FingerprintDev->stopVerify();
#else
    FingerController::instance().stop();
#endif
#endif
}

bool ASyncFingerPrintOperator::remove(const FingerprintInfo& fpinfo)
{
    Q_ASSERT(fpinfo.fingerprintId() == -1);
    //删除指纹设备中对应用户的指纹信息;
#ifdef USE_OPENFINGERPRINT
#ifdef USE_OPENFINGERPRINT_LIB
    m_FingerprintDev->deleteFp(fpinfo.userId().toStdString());
#else
    FingerController::instance().deleteFp(fpinfo.userId());
#endif
#endif

    //删除数据库中对应用户的指纹信息;
    if (m_AdminModel->removeFingerprint(fpinfo.fingerIndex()))
    {
        return true;
    }
    return false;
}

const QList<FingerprintInfo> ASyncFingerPrintOperator::getFingerprintInfo(const QString& userId) const
{
    return QList<FingerprintInfo>();
}

bool ASyncFingerPrintOperator::addFingerprint(const QString& fingerName, int fingerId, int status,
                                              const QString& userId)
{
    FingerprintInfo finger(-1, fingerId, fingerName, userId, status == IFingerPrintOperator::EnrollOK ? true : false);
    return m_AdminModel->addFingerprint(finger);
}

void ASyncFingerPrintOperator::enrollCallBack(int finishedStage, int allStages)
{
    qDebug() << PRETTY_FUNCTION << " finishedStage=" << finishedStage << "; allStages=" << allStages;
    if (finishedStage < allStages)
    {
        emit pressFinger(finishedStage);
    }
    else if (finishedStage == allStages)
    {
        emit enrollState(EnrollOK, -1);
    }
    else
    {
        qDebug() << "error finishedStage must less than allStages";
    }
}

void ASyncFingerPrintOperator::verifyCallFunc(const char* username, bool verified)
{
    qDebug() << PRETTY_FUNCTION << " username=" << username << "; verified=" << verified;
    if (verified)
    {
        m_VerifiedUserId = username;
        qDebug() << PRETTY_FUNCTION << m_VerifiedUserId;
        int ret = m_AdminModel->fpLogin(m_VerifiedUserId);
        qDebug() << PRETTY_FUNCTION << " fpLogin ret is " << ret;
        if (ret == 0)
        {
            emit finish(ret);
        }
        else
        {
            if (!m_VerifiedUserId.isEmpty())
            {
                stop();
                verify();
            }
            emit send();
        }
    }
    else
    {
        qDebug() << PRETTY_FUNCTION << "verify again";
        //        m_FPTask->startVerify();
        stop();
        verify();
    }
}

bool ASyncFingerPrintOperator::hasFingerprint()
{
#ifdef USE_OPENFINGERPRINT
    return true;
#else
    return false;
#endif
}
