#include "backupmodel.h"
#include "applogger.h"
#include "confirmdialog.h"
#include "dataaccesslayerhelper.h"
#include "model/patient.h"
#include "model/study.h"
#include "modeldirectorygetter.h"
#include "progressbarview.h"
#include "resource.h"
#include "util.h"
#include <QDir>
#include <QFileInfo>

BackupModel::BackupModel(IStateManager* value, QObject* parent)
    : QObject(parent)
    , m_ProgressBar(NULL)
    , m_ConfirmDialog(NULL)
    , m_StateManager(value)
{
}

BackupModel::~BackupModel()
{
    Util::SafeDeletePtr(m_ProgressBar);
    Util::SafeDeletePtr(m_ConfirmDialog);
}

void BackupModel::setDirNameList(const QStringList& nameList)
{
    m_DirNameList.clear();
    foreach (QString dirName, nameList)
    {
        if (isPatientDir(dirName))
        {
            getStudyDir(dirName);
        }
        else
        {
            if (!m_DirNameList.contains(dirName))
            {
                m_DirNameList.append(dirName);
            }
        }
    }
    getStudyOidList(m_DirNameList);
}

void BackupModel::getStudyOidList(const QStringList& nameList)
{
    m_StudyOid.clear();
    foreach (QString dirName, nameList)
    {
        m_StudyOid.append(QFileInfo(dirName).baseName());
    }
}

void BackupModel::saveStudys(const QString& discName, QWidget* widget, bool backToLocal)
{
    progressBar(widget);
    QList<Patient*> patients;
    foreach (QString oid, m_StudyOid)
    {
        Study* study = DataAccessLayerHelper::getStudy(oid);
        if (study != NULL)
        {
            Patient* patient = DataAccessLayerHelper::getPatient(study->getPatient()->PatientId(), false);
            delete study->getPatient();

            study->setParent(patient);
            study->setPatient(patient);
            patient->Studies().append(study);
            patients.append(patient);
        }
    }

    if (copyFileToDisc(discName)) //如果return false 表示磁盘空间不够，不用进行拷贝数据库的操作
    {
        Util::processEvents(QEventLoop::ExcludeUserInputEvents);
        changeDbFileName(discName);
        Util::processEvents(QEventLoop::ExcludeUserInputEvents);
        copyDataToDisk(discName);
        Util::processEvents(QEventLoop::ExcludeUserInputEvents);
        foreach (Patient* patient, patients)
        {
            DataAccessLayerHelper::saveOrUpdate(*patient);
            Util::processEvents(QEventLoop::ExcludeUserInputEvents);
        }
    }
    //增加这个字段为了防止自动刷新回local在restore的时候
    if (backToLocal)
    {
        changeDbFileName(QString());
    }
    else
    {
        changeDbFileName(m_Disk);
    }
    qDeleteAll(patients);
}

void BackupModel::changeDbFileName(const QString& discName)
{
    if (!discName.isEmpty() && discName != Resource::hardDiskDir)
    {
        QDir dir(discName);
        DataAccessLayerHelper::setDbFileName(dir.filePath(Resource::getDbBaseFileName()));
    }
    else
    {
        DataAccessLayerHelper::setDbFileName();
    }
}

void BackupModel::copyDataToDisk(const QString& disc)
{
    if (disc != Resource::hardDiskDir)
    {
        QString dbFilePath = QDir(disc).filePath(Resource::getDbBaseFileName());
        DataAccessLayerHelper::createDataBaseFile(dbFilePath, Resource::BackupDbBaseFileName);
    }
}

void BackupModel::setDisk(const QString& disk)
{
    m_Disk = disk;
}

bool BackupModel::burnFileToDvd(const QString& disc, QWidget* widget)
{
    QList<Patient*> patients;
    foreach (QString oid, m_StudyOid)
    {
        Study* study = DataAccessLayerHelper::getStudy(oid);
        if (study != NULL)
        {
            Patient* patient = DataAccessLayerHelper::getPatient(study->getPatient()->PatientId(), false);
            delete study->getPatient();

            study->setParent(patient);
            study->setPatient(patient);
            patient->Studies().append(study);
            patients.append(patient);
        }
    }
    if (!m_DirNameList.isEmpty())
    {
        progressBar(widget, false, true);
        connect(m_ProgressBar, SIGNAL(cancelButtonClicked()), &m_ThreadModel, SLOT(terminateBurnThread()),
                Qt::UniqueConnection);

        m_ThreadModel.setBackupModel(this);
        m_ThreadModel.setBackupPatients(patients);
        return m_ThreadModel.burn(m_DirNameList);
    }
    else
    {
        return false;
    }
}

void BackupModel::saveDbfileToTmp(const QString& discName, const QList<Patient*>& patients)
{
    // set db session as in tmp burn dir
    changeDbFileName(discName);

    // reset db file in tmp burn dir as base data in :resouce/files/CommonData.s3db
    copyDataToDisk(discName);

    // update db file data in tmp burn dir
    foreach (Patient* patient, patients)
    {
        DataAccessLayerHelper::saveOrUpdate(*patient);
    }

    changeDbFileName(QString());

    qDeleteAll(patients);
}

void BackupModel::clear()
{
    m_DirNameList.clear();
    m_StudyOid.clear();
}

bool BackupModel::copyFileToDisc(const QString& disc)
{
    if (!m_DirNameList.isEmpty())
    {
        if (disc == Resource::hardDiskDir)
        {
            m_ThreadModel.setIsMkPatientDir(true);
            return m_ThreadModel.copy(m_DirNameList, PatientPath::instance().databasePath());
        }
        else
        {
            m_ThreadModel.setValue(2);
            m_ThreadModel.setIsMkPatientDir(true);
            return m_ThreadModel.copy(m_DirNameList, disc);
        }
    }
    else
    {
        return false;
    }
}

void BackupModel::progressBar(QWidget* widget, bool busyIndicative, bool isCancelButtonVisible)
{
    if (m_ProgressBar == NULL)
    {
        m_ProgressBar = new ProgressBarView(m_StateManager, widget);
        m_ThreadModel.setProgressBar(m_ProgressBar);
    }
    if (m_ProgressBar != NULL)
    {
        m_ProgressBar->setBusyIndicative(busyIndicative);
        m_ProgressBar->setCancelButtonVisible(isCancelButtonVisible);
    }

    if (m_ConfirmDialog == NULL)
    {
        m_ConfirmDialog = new ConfirmDialogFrame(widget);
        m_ThreadModel.setConfirmDialog(m_ConfirmDialog);
    }
}

/*
The generation of path from a specific selection is as below,
pathahead + PatientID + pathSeparator + StudyOid

If the selection is a patient under patient view, the
StudyOid is empty. So the path will be like,
/xx/xx/../PatientID/

If the selection is a study, the path will be like,
/xx/xx/../PatientID/StudyOid

Please refer to
DataBasePatientViewModel::fileInfo() and
DataBaseStudyViewModel::fileInfo()
*/
bool BackupModel::isPatientDir(QString path) const
{
    return path.endsWith('/');
}

void BackupModel::getStudyDir(const QString& path)
{
    QString patientID = path;
    QString studyPath;
    QChar pathSep = '/';
    int pos = -1;

    if (patientID.endsWith("/"))
    {
        patientID.chop(1);
    }
    else
    {
        return;
    }

    // int pos = patientID.indexOf("/", -1);
    for (int i = patientID.size() - 1; i >= 0; i--)
    {
        if (patientID.at(i) == pathSep)
        {
            pos = i;
            break;
        }
    }
    patientID = patientID.mid(pos + 1);

    if (patientID.isEmpty())
    {
        return;
    }

    Patient* patient = DataAccessLayerHelper::getPatient(patientID, true);
    if (patient == NULL)
    {
        return;
    }

    QList<Study*> studies = patient->Studies();
    foreach (Study* study, studies)
    {
        studyPath.clear();
        studyPath = path + study->StudyOid();
        if (!m_DirNameList.contains(studyPath))
        {
            m_DirNameList.append(studyPath);
        }
    }

    delete patient;
}
