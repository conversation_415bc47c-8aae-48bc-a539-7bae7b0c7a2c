#include "patientmiddlewidget.h"
#include "ui_patientmiddlewidget.h"
#include "model/study.h"

PatientMiddleWidget::PatientMiddleWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::PatientMiddleWidget)
    , m_Model(ScreenOrientationModel::getInstance())
{
    ui->setupUi(this);
    //    showWidget(0);
}

PatientMiddleWidget::~PatientMiddleWidget()
{
    delete ui;
}

void PatientMiddleWidget::setCurrentIndex(const int index)
{
    ui->widgetHater->setCurrentIndex(index);
}

void PatientMiddleWidget::setPatient(Patient* patient)
{
}

void PatientMiddleWidget::setStudy(Study* study)
{
    ui->widgetHater->setStudy(study);
    ui->widgetHomogenous->setStudy(study);
}

void PatientMiddleWidget::flawlessStudy(Study* study)
{
    ui->widgetHater->flawlessStudy(study);
    ui->widgetHomogenous->flawlessStudy(study);
}

void PatientMiddleWidget::clearStudyInfo()
{
    ui->widgetHater->clearStudyInfo();
    ui->widgetHomogenous->clearStudyInfo();
}

void PatientMiddleWidget::setCurrentWidgetEnabled(bool enabled)
{
    ui->widgetHater->setCurrentWidgetEnabled(enabled);
    ui->widgetHomogenous->setCurrentWidgetEnabled(enabled);
}

void PatientMiddleWidget::setPhysiciansName(const QString& value)
{
    ui->widgetHomogenous->setPhysiciansName(value);
}

void PatientMiddleWidget::setAccession(const QString& value)
{
    ui->widgetHomogenous->setAccession(value);
}

void PatientMiddleWidget::setAnimalSpeciesIndex(int index)
{
    ui->widgetHater->setAnimalSpeciesIndex(index);
}

bool PatientMiddleWidget::isHor()
{
    return m_Model->isHor();
}

void PatientMiddleWidget::retranslateUi()
{
    ui->retranslateUi(this);
}

void PatientMiddleWidget::orientationChanged(Qt::ScreenOrientation orientation)
{
    QString className(this->metaObject()->className());
    OrientationConfigItem config;
    m_Model->getInfosByClassName(orientation, className, config);
}

//    switch(index)
//    {
//    case 0:
//        ui->stackedWidget->setCurrentIndex(0);
//        showWidget(0);
//        break;
//    case 1:
//        ui->stackedWidget->setCurrentIndex(0);
//        showWidget(0);
//        break;
//    case 2:
//        ui->stackedWidget->setCurrentIndex(1);
//        showWidget(1);
//        break;
//    case 3:
//        ui->stackedWidget->setCurrentIndex(2);
//        showWidget(2);
//        ui->widgetBody->setWidgetVisible(true);
//        break;
//    case 4:
//        ui->stackedWidget->setCurrentIndex(2);
//        showWidget(2);
//        break;
//    case 5:
//        ui->stackedWidget->setCurrentIndex(2);
//        showWidget(2);
//        break;
//    case 6:
//        ui->stackedWidget->setCurrentIndex(2);
//        showWidget(2);
//        break;
//    }
//}

// QString PatientMiddleWidget::lineEditStudyDescription() const
//{
//    return ui->lineEditStudyDescription->text();
//}

// void PatientMiddleWidget::setLineEditStudyDescription(const QString &text)
//{
//    ui->lineEditStudyDescription->setText(text);
//}

// QString PatientMiddleWidget::lineEditPrimary() const
//{
//    return ui->lineEditPrimary->text();
//}

// void PatientMiddleWidget::setLineEditPrimary(const QString &text)
//{
//    ui->lineEditPrimary->setText(text);
//}

// QString PatientMiddleWidget::lineEditSecondary() const
//{
//    return ui->lineEditSecondary->text();
//}

// void PatientMiddleWidget::setLineEditSecondary(const QString &text)
//{
//    ui->lineEditSecondary->setText(text);
//}

// QString PatientMiddleWidget::lineEditCPT4Code() const
//{
//    return ui->lineEditCPT4Code->text();
//}

// void PatientMiddleWidget::setLineEditCPT4Code(const QString &text)
//{
//    ui->lineEditCPT4Code->setText(text);
//}

// QString PatientMiddleWidget::lineEditCPT4Description() const
//{
//    return ui->lineEditCPT4Description->text();
//}

// void PatientMiddleWidget::setLineEditCPT4Description(const QString &text)
//{
//    ui->lineEditCPT4Description->setText(text);
//}

// QString PatientMiddleWidget::comboBoxPhysician() const
//{
//    return ui->comboBoxPhysician->currentText();
//}

// void PatientMiddleWidget::setComboBoxPhysician(const QString &text)
//{
//    ui->comboBoxPhysician->setEditText(text);
//}

// QString PatientMiddleWidget::comboBoxDiagnostician() const
//{
//    return ui->comboBoxDiagnostician->currentText();
//}

// void PatientMiddleWidget::setComboBoxDiagnostician(const QString &text)
//{
//    ui->comboBoxDiagnostician->setEditText(text);
//}

// QString PatientMiddleWidget::comboBoxOperator() const
//{
//    return ui->comboBoxOperator->currentText();
//}

// void PatientMiddleWidget::setComboBoxOperator(const QString &text)
//{
//    ui->comboBoxOperator->setEditText(text);
//}

// QString PatientMiddleWidget::lineEditAccession() const
//{
//    return ui->lineEditAccession->text();
//}

// void PatientMiddleWidget::setLineEditAccession(const QString &text)
//{
//    ui->lineEditAccession->setText(text);
//}

// QString PatientMiddleWidget::textEditComment() const
//{
//    return ui->textEditComment->toPlainText();
//}

// void PatientMiddleWidget::settextEditComment(const QString &text)
//{
//    ui->textEditComment->setText(text);
//}

// void PatientMiddleWidget::setWidgetVisible(bool visable)
//{
//    ui->widgetBody->setVisible(visable);
//    ui->widgetCard->setVisible(visable);
//    ui->widgetOB->setVisible(visable);
//////    ui->page->setVisible(visable);
//////    ui->page_3->setVisible(visable);
//////    ui->page_4->setVisible(visable);
//}

// void PatientMiddleWidget::showWidget(int index)
//{
//    setWidgetVisible(false);
//    switch(index)
//    {
//    case 0:
//        ui->widgetOB->setVisible(true);
//////        ui->page->setVisible(true);
//        break;
//    case 1:
//        ui->widgetCard->setVisible(true);
//////        ui->page_3->setVisible(true);
//        break;
//    case 2:
//        ui->widgetBody->setVisible(true);
//////        ui->page_4->setVisible(true);
//        ui->widgetBody->setWidgetVisible(false);
//        break;
//    }
//}
