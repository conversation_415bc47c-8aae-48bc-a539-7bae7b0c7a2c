#ifndef CONFIGURATIONWIDGET_H
#define CONFIGURATIONWIDGET_H
#include "controls_global.h"

#include "basebuttonsboxframecontent.h"
#include "baseinputabledialogframe.h"
#include "basewidget.h"
#include "generalsettings.h"
#include "isettings.h"
#include "measurementsettingwidget.h"
#include "netconfigurewidget.h"
#include "pushbuttonfor3d.h"
#include "reportsettingswidget.h"
#include "screenorientationmodel.h"
#include "screentypedef.h"
#include "serverlist.h"
#include "systeminfosettings.h"
#include <QGridLayout>
#include <QHBoxLayout>
#include <QHash>
#include <QList>
#include <QPushButton>
#include <QStackedWidget>
#include <QStringList>
#include <QVBoxLayout>
#include <QWidget>

#define TABCOUNT 10

class ISettings;
class GeneralSettings;
class ConfigurationWidget;
class PackagesMeasurement;
class CommentConfigModel;
class BodyMarkConfigModel;
class ExamModeConfigModel;
class AdminConfigModel;

class CommentConfigFrame;
class BodyMarkConfigFrame;
class ExamModeConfigFrame;
class AdminConfigFrame;

class IKeyboard;
class IMachine;
class HotKeyConfig;
class HotKeyContainer;
class DicomTaskManager;
class ImageTile;
class MotorControl;
class UpdateController;
class Patient;
class IStateManager;
class IDiskDevice;
class IColorMapManager;
class IProbeDataSet;

class CONTROLSSHARED_EXPORT ConfigurationDialog : public BaseInputAbleDialogFrame
{
    Q_OBJECT
public:
    explicit ConfigurationDialog(IStateManager* stateManager, QWidget* parent = NULL);
    ~ConfigurationDialog();
    void setPackagesMeasurement(PackagesMeasurement* value);
    void setCommentConfigModel(CommentConfigModel* model);
    void setBodyMarkConfigModel(BodyMarkConfigModel* model);
    void setExamModeConfigModel(ExamModeConfigModel* model);
    void setKeyboard(IKeyboard* value);
    void setMachine(IMachine* value);
    //    void setScreenType(ScreenTypeDef::ScreenType type);
    void setHotKeyModels(HotKeyConfig* config, HotKeyContainer* container);
    void setDicomTaskManager(DicomTaskManager* value);
    void setImageTile(ImageTile* tile);
    void setMotorControl(MotorControl* motorCtrl);
    void runSystemUserImpl();
    bool isDefault() const;
    void saveDefault() const;
    void setLGCVisible(bool isVisible);
    void setUpdateController(UpdateController* updateController);
    void setBeamFormerTool(IBeamFormerTool* beamFormerTool);
    void activeMaintenanceWidget();
    bool isValidMaintenanceKey();
    void setDiskDevice(IDiskDevice* diskDevice);
    void setColorMapManager(IColorMapManager* colorMapManager);
    void setProbeDataSet(IProbeDataSet* value);
public slots:
    void onOrientationChanged(bool isHor);

protected:
    void showEvent(QShowEvent* e);
signals:
    void hwKeyImported();
    void screenControlTimersStoped();
    void screenControlTimersStarted();
    void screenSaverPreviewed(bool, int);
    void lGCEnableChanged(const QVariant&);
    void ntpButClicked(const QString& category, const QString& butName, const QString& value);
    void tpButClicked(const QString& category, const QString& butName, const QString& value);
    void systemDateChanged();
    void signalSetCurVirKeyboardEnglish(bool isEnglish);
    void patientChanged(Patient*);
    void endCurState();

private:
    ConfigurationWidget* m_Settings;
};

class CONTROLSSHARED_EXPORT ConfigurationWidget : public BaseWidget, public BaseButtonsBoxFrameContent
{
    Q_OBJECT
public:
    explicit ConfigurationWidget(IStateManager* stateManager, QWidget* parent = NULL);
    ~ConfigurationWidget();
    void setupUI();
    void initializeWidgets();
    void setPackagesMeasurement(PackagesMeasurement* value);
    void setCommentConfigModel(CommentConfigModel* model);
    void setBodyMarkConfigModel(BodyMarkConfigModel* model);
    void setExamModeConfigModel(ExamModeConfigModel* model);
    void setKeyboard(IKeyboard* value);
    void setMachine(IMachine* value);
    void setScreenType(ScreenTypeDef::ScreenType type);
    void setHotKeyModels(HotKeyConfig* config, HotKeyContainer* container);
    void setDicomTaskManager(DicomTaskManager* value);
    void setImageTile(ImageTile* tile);
    void setMotorControl(MotorControl* motorCtrl);
    void runSystemUserImpl();
    bool isDefault();
    void saveDefault();
    void setLGCVisible(bool isVisible);
    void toggleTabButtons(int index);
    int indexOfTabBt(const QString& btname);
    static void setIsChangePage(bool value);
    void setUpdateController(UpdateController* updateController);
    void setBeamFormerTool(IBeamFormerTool* beamFormerTool);

    void setUpdateRotation(bool isRotation);
    void activeMaintenanceWidget();
    bool isValidMaintenanceKey();
    void setDiskDevice(IDiskDevice* diskDevice);
    void setColorMapManager(IColorMapManager* colorMapManager);
    void setProbeDataSet(IProbeDataSet* value);
signals:
    void orientationChanged(bool isHor);
private slots:
    void save();
    void cancel();
    void setDefault();
    void onTabButtonClicked(int id);
    void onButtonClicked(int id);
    void onDicomStautsChanged(bool value);
    void onLGCEnableChanged(const QVariant& value);
    void onTPButClicked(const QString& category, const QString& butName, const QString& value);
    void onShowRegisterDialog();
signals:
    void updateDialogFrame();
    void accepted();
    void rejected();
    void hwKeyImported();
    void screenControlTimersStoped();
    void screenControlTimersStarted();
    void screenSaverPreviewed(bool, int);
    void networkCfgCleared();
    void buttonClicked(int id);
    void updateContainer();
    void systemDateChanged();
    void signalSetCurVirKeyboardEnglish(bool isEnglish);
    void patientChanged(Patient*);
    void endCurState();

protected:
    Q_INVOKABLE void doTask();
    virtual void retranslateUi();
    void orientationChanged(Qt::ScreenOrientation orientation);
    void showEvent(QShowEvent* e);
    void hideEvent(QHideEvent* e);
    bool event(QEvent* event) override;

private:
    void initLeftButtons();

private:
    SettingsSet m_SettingsSet;
    GeneralSettings* m_GeneralSettings;
    MeasurementSettingWidget* m_MeasurementSettingWidget;
    CommentConfigFrame* m_CommentConfigFrame;
    BodyMarkConfigFrame* m_BodymarkConfigFrame;
    ExamModeConfigFrame* m_ExamModeConfigFrame;
    ReportSettingsWidget* m_ReportSettingsWidget;
    ServerList* m_DicomWidget;
    NetConfigureWidget* m_NetWidget;
    AdminConfigFrame* m_AdminWidget;
    SystemInfoSettings* m_SystemInfoWidget;
    QStackedWidget* m_StackedWidget;
    QHash<QString, QWidget*> m_TabHash;
    PushButtonFor3D* m_TabButtons[TABCOUNT];
    QVBoxLayout* m_LayoutV;
    QHBoxLayout* m_LayoutH;
    QGridLayout* m_LayoutGSet;
    QGridLayout* m_LayoutGTab;
    QGridLayout* m_LayoutGStackedWidget;
    int m_Id;
    QStringList m_TabTextList;
    QWidget* m_LeftWidget;
    int m_ActivateId; // record which tab is activated
    static bool m_IsNeedChange;
    ScreenOrientationModel* m_Model;
    Qt::ScreenOrientation m_Orientation;
    bool m_IsConfigTransposed;
    IStateManager* m_StateManager;
    IDiskDevice* m_DiskDevice;
};

#endif // CONFIGURATIONWIDGET_H
