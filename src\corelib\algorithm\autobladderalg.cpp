#include "autobladderalg.h"

AutoBladderAlg::AutoBladderAlg()
    : VirtualAlg()
    , m_IsLFInvert(0)
    , m_IsUDInvert(0)
{
    m_Lib.setFileName("autobladder");
}

AutoBladderAlg::~AutoBladderAlg()
{
    if (m_Initialized)
    {
        call_remove_bladder_context();
    }
}

int AutoBladderAlg::calcTransverseSection(uint8_t* imgin, int width, int height, QLine& line1, QLine& line2)
{
    if (!m_Initialized)
    {
        call_create_bladder_context();
        if (m_IsError)
        {
            return -1;
        }
        m_Initialized = true;
    }

    int result[8] = {0};

    int ret = call_bladder_auto_measure(imgin, width, height, result, m_IsUDInvert, m_IsLFInvert);
    if (ret > 0)
    {
        line1.setLine(result[0], result[1], result[2], result[3]);
        line2.setLine(result[4], result[5], result[6], result[7]);
    }

    if (m_IsError)
    {
        return -1;
    }
    return ret;
}

int AutoBladderAlg::calcLongitudinalSection(uint8_t* imgin, int width, int height, QLine& line)
{
    if (!m_Initialized)
    {
        call_create_bladder_context();
        if (m_IsError)
        {
            return -1;
        }
        m_Initialized = true;
    }

    int result[4] = {0};

    int ret = call_bladder_auto_measure2(imgin, width, height, result, m_IsUDInvert, m_IsLFInvert);
    if (m_IsError)
    {
        return -1;
    }

    if (ret > 0)
    {
        line.setLine(result[0], result[1], result[2], result[3]);
    }

    return ret;
}

int AutoBladderAlg::calcLongitudinalSectionDiff(uint8_t* imgin, int width, int height, QLine& line)
{
    if (!m_Initialized)
    {
        call_create_bladder_context();
        if (m_IsError)
        {
            return -1;
        }
        m_Initialized = true;
    }

    int result[4] = {0};

    int ret = call_bladder_auto_measure3(imgin, width, height, result, m_IsUDInvert, m_IsLFInvert);
    if (m_IsError)
    {
        return -1;
    }

    if (ret > 0)
    {
        line.setLine(result[0], result[1], result[2], result[3]);
    }

    return ret;
}

void AutoBladderAlg::call_create_bladder_context(void)
{
    typedef void (*create_bladder_context_Prototype)(const char*);
    create_bladder_context_Prototype create_bladder_context =
        (create_bladder_context_Prototype)m_Lib.resolve("create_bladder_context");
    if (!create_bladder_context)
    {
        m_IsError = true;
        return;
    }

    QString s = Resource::mnnDir() + "/autobladder.mnn";
    create_bladder_context(s.toLocal8Bit().constData());
}

void AutoBladderAlg::call_remove_bladder_context(void)
{
    typedef void (*remove_bladder_context_Prototype)(void);
    remove_bladder_context_Prototype remove_bladder_context =
        (remove_bladder_context_Prototype)m_Lib.resolve("remove_bladder_context");
    if (!remove_bladder_context)
    {
        m_IsError = true;
        return;
    }

    remove_bladder_context();
}

int AutoBladderAlg::call_bladder_auto_measure(uint8_t* imgin, int width, int height, int* measure_result, int flipul,
                                              int fliplr)
{
    /**
    *imgin:输入的整帧图片指针
    *width：输入的图片宽度
    *height：输入的图片高度
    *flipul: 是否上下翻转
    *fliplr: 是否左右翻转
  *measure_result 8个元素的int数组，其中第0个元素是膀胱当前切面水平向径像素左边坐标x，1膀胱当前切面水平向径像素左边坐标y
                                           第2个元素是膀胱当前切面水平向径像素右边坐标x，3膀胱当前切面水平向径像素右边坐标y
                                           4个元素是膀胱当前切面垂直向径像素上端坐标x，5膀胱当前切面垂直向径像素上端坐标y
                                           第6个元素是膀胱当前切面垂直向径像素下端坐标x，7膀胱当前切面垂直向径像素下端坐标y
    返回值0表示当前模型没有检测到膀胱，非零表示有膀胱*/

    typedef int (*bladder_auto_measure_Prototype)(uint8_t*, int, int, int*, int, int);
    bladder_auto_measure_Prototype bladder_auto_measure =
        (bladder_auto_measure_Prototype)m_Lib.resolve("bladder_auto_measure");
    if (!bladder_auto_measure || imgin == NULL)
    {
        m_IsError = true;
        return -1;
    }

    return bladder_auto_measure(imgin, width, height, measure_result, flipul, fliplr);
}

int AutoBladderAlg::call_bladder_auto_measure2(uint8_t* imgin, int width, int height, int* measure_result, int flipul,
                                               int fliplr)
{
    typedef int (*bladder_auto_measure2_Prototype)(uint8_t*, int, int, int*, int, int);
    bladder_auto_measure2_Prototype bladder_auto_measure2 =
        (bladder_auto_measure2_Prototype)m_Lib.resolve("bladder_auto_measure2");

    if (!bladder_auto_measure2)
    {
        m_IsError = true;
        return -1;
    }

    return bladder_auto_measure2(imgin, width, height, measure_result, flipul, fliplr);
}

int AutoBladderAlg::call_bladder_auto_measure3(uint8_t* imgin, int width, int height, int* measure_result, int flipul,
                                               int fliplr)
{
    typedef int (*bladder_auto_measure3_Prototype)(uint8_t*, int, int, int*, int, int);
    bladder_auto_measure3_Prototype bladder_auto_measure3 =
        (bladder_auto_measure3_Prototype)m_Lib.resolve("bladder_auto_measure3");

    if (!bladder_auto_measure3)
    {
        m_IsError = true;
        return -1;
    }

    return bladder_auto_measure3(imgin, width, height, measure_result, flipul, fliplr);
}

void AutoBladderAlg::setIsUDInvert(int IsUDInvert)
{
    m_IsUDInvert = IsUDInvert;
}

void AutoBladderAlg::setIsLFInvert(int IsLFInvert)
{
    m_IsLFInvert = IsLFInvert;
}
