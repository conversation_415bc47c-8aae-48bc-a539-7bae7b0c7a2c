<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ObInfoWidget</class>
 <widget class="QWidget" name="ObInfoWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>830</width>
    <height>189</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="sizeConstraint">
    <enum>QLayout::SetDefaultConstraint</enum>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QLabel" name="label">
     <property name="minimumSize">
      <size>
       <width>147</width>
       <height>0</height>
      </size>
     </property>
     <property name="text">
      <string>LMP</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="DateTimeWidget" name="lineEditLmp"/>
   </item>
   <item row="0" column="3">
    <widget class="QLabel" name="label_10">
     <property name="text">
      <string>Gestations</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QLabel" name="label_2">
     <property name="text">
      <string>GA</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="1" column="1">
    <widget class="QLineEdit" name="lineEditGa">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item row="1" column="3">
    <widget class="QLabel" name="label_6">
     <property name="text">
      <string>Day Of Cycle</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="1" column="4">
    <widget class="QLineEdit" name="lineEditCycle">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="text">
      <string>0</string>
     </property>
    </widget>
   </item>
   <item row="1" column="6">
    <widget class="QLabel" name="label_11">
     <property name="text">
      <string>Weight</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="2" column="0">
    <widget class="QLabel" name="label_3">
     <property name="text">
      <string>EDD</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="2" column="3">
    <widget class="QLabel" name="label_7">
     <property name="text">
      <string>Ectopic</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="2" column="4">
    <widget class="QLineEdit" name="lineEditEctopic">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="text">
      <string>0</string>
     </property>
    </widget>
   </item>
   <item row="2" column="6">
    <widget class="QLabel" name="label_12">
     <property name="text">
      <string>BSA</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="2" column="7">
    <widget class="QLineEdit" name="lineEditBsa">
     <property name="text">
      <string>0</string>
     </property>
    </widget>
   </item>
   <item row="2" column="8">
    <widget class="QLabel" name="label_19">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>46</width>
       <height>0</height>
      </size>
     </property>
     <property name="text">
      <string>m²</string>
     </property>
    </widget>
   </item>
   <item row="3" column="0">
    <widget class="QLabel" name="label_4">
     <property name="text">
      <string>Estab. DueDate</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="3" column="1">
    <widget class="DateTimeWidget" name="lineEditEstab"/>
   </item>
   <item row="3" column="3">
    <widget class="QLabel" name="label_9">
     <property name="text">
      <string>Gravida</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="3" column="4">
    <widget class="QLineEdit" name="lineEditGravide">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="text">
      <string>0</string>
     </property>
    </widget>
   </item>
   <item row="3" column="6">
    <widget class="QLabel" name="label_14">
     <property name="text">
      <string>HR</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="3" column="7">
    <widget class="QLineEdit" name="lineEditHr">
     <property name="text">
      <string>0</string>
     </property>
    </widget>
   </item>
   <item row="3" column="8">
    <widget class="QLabel" name="label_20">
     <property name="text">
      <string>bpm</string>
     </property>
    </widget>
   </item>
   <item row="4" column="0">
    <widget class="QLabel" name="label_5">
     <property name="text">
      <string>Ovul. Date</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="4" column="1">
    <widget class="DateTimeWidget" name="lineEditOvil"/>
   </item>
   <item row="4" column="3">
    <widget class="QLabel" name="label_8">
     <property name="text">
      <string>Para</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="4" column="4">
    <widget class="QLineEdit" name="lineEditPare">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="text">
      <string>0</string>
     </property>
    </widget>
   </item>
   <item row="4" column="6">
    <widget class="QLabel" name="label_13">
     <property name="text">
      <string>FHR</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="4" column="7">
    <widget class="QLineEdit" name="lineEditPhr">
     <property name="text">
      <string>0</string>
     </property>
    </widget>
   </item>
   <item row="4" column="8">
    <widget class="QLabel" name="label_21">
     <property name="text">
      <string>bpm</string>
     </property>
    </widget>
   </item>
   <item row="5" column="3">
    <widget class="QLabel" name="label_16">
     <property name="text">
      <string>Abort</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="5" column="4">
    <widget class="QLineEdit" name="lineEditAbort">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="text">
      <string>0</string>
     </property>
    </widget>
   </item>
   <item row="0" column="2">
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="0" column="5">
    <spacer name="horizontalSpacer_2">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="2" column="1">
    <widget class="DateTimeWidget" name="lineEditEdd">
     <property name="enabled">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item row="0" column="4">
    <widget class="BaseComboBox" name="comboBoxGestations">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
    </widget>
   </item>
   <item row="0" column="6">
    <widget class="QLabel" name="label_15">
     <property name="text">
      <string>Height</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="0" column="7" colspan="2">
    <widget class="PatientInfoInputWidget" name="wPatientHeight" native="true"/>
   </item>
   <item row="1" column="7" colspan="2">
    <widget class="PatientInfoInputWidget" name="wPatientWeight" native="true"/>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BaseComboBox</class>
   <extends>QComboBox</extends>
   <header>basecombobox.h</header>
  </customwidget>
  <customwidget>
   <class>DateTimeWidget</class>
   <extends>QLineEdit</extends>
   <header>datetimewidget.h</header>
  </customwidget>
  <customwidget>
   <class>PatientInfoInputWidget</class>
   <extends>QWidget</extends>
   <header>patientinfoinputwidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
