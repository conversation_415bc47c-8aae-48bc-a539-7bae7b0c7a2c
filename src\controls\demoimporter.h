#ifndef DEMOIMPORTER_H
#define DEMOIMPORTER_H
#include "controls_global.h"
#include "iimporter.h"
#include <QFileInfoList>

class CONTROLSSHARED_EXPORT DemoImporter : public IImporter
{
    Q_OBJECT
public:
    enum ResultType
    {
        Success,
        Failed
    };
    static const char* importResultText[];
    static const char* checkResultText[];

    DemoImporter(bool isAnotherSpecie, QObject* parent = 0);
    int externalFilesCount();
    void import();
    void check();
    QString importInfo() const;
    QString checkInfo() const;
    ResultType importResult() const;

private:
    QFileInfoList getExternalFiles() const;
    QString getVersion() const;
    ResultType importFiles(const QFileInfoList& externalFiles);
    ResultType checkFiles();
signals:
    void progressValueChanged();

private:
    QString m_SpeciesName;
    QString m_ExternalPath;
    QString m_InternalPath;
    QFileInfoList m_ExternalFiles;
    ResultType m_ImportResult;
    ResultType m_CheckResult;
};

#endif // DEMOIMPORTER_H
