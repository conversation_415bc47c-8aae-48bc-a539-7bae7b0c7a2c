#ifndef ALGINSTANCE_H
#define ALGINSTANCE_H

#include "algorithm_global.h"
#include "autobladderalg.h"
#include "jsrgalg.h"
#include "rtimtalg.h"
#include "sonocalcalg.h"
#include "sonocardiacalg.h"
#include "sonocarotidguidealg.h"
#include "sonodiaphalg.h"
#include "sonomskalg.h"
#include "sononervealg.h"
#include "sonoobalg.h"
#include "sonothyroidalg.h"
#include "autoefalg.h"
#include "sonoavalg.h"

class ALGORITHMSHARED_EXPORT AlgInstance
{
public:
    static AlgInstance& algInstance();
    AutoBladderAlg* getAutoBladder();
    JsrgAlg* getJsrgAlg();
    RtimtAlg* getRtimtAlg();
    AutoCystisAlg* getAutoCystisAlg();
    SonoCardiacAlg* getSonoCardiacAlg();
    SonoCarotidGuideAlg* getSonoCarotidGuideAlg();
    SonoDiaphAlg* getSonoDiaphAlg();
    SonoMskAlg* getSonoMskAlg();
    SonoNerveAlg* getSonoNerveAlg();
    SonoOBAlg* getSonoOBAlg();
    SonoThyroidAlg* getSonoThyroidAlg();
    AutoEfAlg* getAutoEfAlg();
    SonoAVAlg* getSonoAVAlg();

private:
    AlgInstance();

private:
    AutoBladderAlg m_AutoBladder;
    JsrgAlg m_JsrgAlg;
    RtimtAlg m_RtimtAlg;
    AutoCystisAlg m_AutoCystisAlg;
    SonoCardiacAlg m_SonoCardiacAlg;
    SonoCarotidGuideAlg m_SonoCarotidGuideAlg;
    SonoDiaphAlg m_SonoDiaphAlg;
    SonoMskAlg m_SonoMskAlg;
    SonoNerveAlg m_SonoNerveAlg;
    SonoOBAlg m_SonoOBAlg;
    SonoThyroidAlg m_SonoThyroidAlg;
    AutoEfAlg m_AutoEfAlg;
    SonoAVAlg m_SonoAvAlg;
};

#endif // ALGINSTANCE_H
