#ifndef FINGERPRINTTASK_H
#define FINGERPRINTTASK_H
#include <QObject>
#include "adminmodel_global.h"

#define PRIME_OK 0
#define PRIME_STATUS_FULL 0x08
#define PRIME_STATUS_TIMEOUT 0x12
#define PRIME_ERROR_COMM 0x81

#define PRIME_STATUS_MATCH 0x00
#define PRIME_STATUS_NOT_MATCH 0x01

#define PRIME_STATUS_ABORT 0x02
#define PRIME_STATUS_NO_FINGER 0x03
#define PRIME_STATUS_VERIFY_FAILED 0x07

#define PRIME_DELAY_COMMON 50000 // 50ms
#define PRIME_STATUS_WAIT 0x25
#define PRIME_STATUS_IDLE 0x3f
#define PRIME_STATUS_GOOD 0x21

class ADMINMODELSHARED_EXPORT FingerprintTask : public QObject
{
    Q_OBJECT
public:
    FingerprintTask(QObject* parent = NULL);
    ~FingerprintTask();

    void startEnroll();
    void startVerify();
    void startCalibration();
    int getId() const;
    void stopTask();
    bool delFp(uint id);
signals:
    void pressFinger(int timer);
    void enrollState(uint state);
    void verifyState(uint state);

protected:
    friend class FingerprintEnrollTask;
    friend class FingerprintVerifyTask;
    void enroll();
    void verify();

private:
    int m_id;
    bool m_isRunning;
};

#endif // FINGERPRINTTASK_H
