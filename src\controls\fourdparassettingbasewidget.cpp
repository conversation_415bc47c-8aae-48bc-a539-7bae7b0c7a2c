#include "fourdparassettingbasewidget.h"
#include "ui_fourdparassettingbasewidget.h"
#include "fourdparascontainer.h"
#include "inputdialogframe.h"
#include "messageboxframe.h"

FourDParasSettingBaseWidget::FourDParasSettingBaseWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::FourDParasSettingBaseWidget)
    , m_Warning(QString(""))
{
    ui->setupUi(this);
    ui->addButton->setEnabled(false);
    ui->deleteButton->setEnabled(false);
    ui->renameButton->setEnabled(false);
}

FourDParasSettingBaseWidget::~FourDParasSettingBaseWidget()
{
    delete ui;
}

void FourDParasSettingBaseWidget::currenIndexChanged(const QString& arg1)
{
    Q_UNUSED(arg1);
}

template <class T> bool FourDParasSettingBaseWidget::isExistNameT(const QString& name)
{
    return FourDParasContainer::get<T>(m_ParaType).isExistName(name);
}

bool FourDParasSettingBaseWidget::contains(const QString& name)
{
    Q_UNUSED(name);
    return false;
}

template <class T> void FourDParasSettingBaseWidget::addOneParaT(const QString& name, const T& para)
{
    FourDParasContainer::get<T>(m_ParaType).addOnePara(name, para);
}

template <class T> void FourDParasSettingBaseWidget::saveOneParaT()
{
    FourDParasManager::instance().saveParas(m_ParaType);
}

template <class T> void FourDParasSettingBaseWidget::deleteOneParaT(const QString& name)
{
    FourDParasContainer::get<T>(m_ParaType).deleteOnePara(name);
}

template <class T> void FourDParasSettingBaseWidget::renameT(const QString& oldName, const QString& newName)
{
    FourDParasContainer::get<T>(m_ParaType).rename(oldName, newName);
}

template <class T> QStringList FourDParasSettingBaseWidget::namesT()
{
    return FourDParasContainer::get<T>(m_ParaType).names();
}

template <class T> T FourDParasSettingBaseWidget::oneParasT(const QString& name) const
{
    return FourDParasContainer::get<T>(m_ParaType).oneParas(name);
}

void FourDParasSettingBaseWidget::retranslateUi()
{
    ui->setupUi(this);
}

void FourDParasSettingBaseWidget::addWidget(QWidget* widget)
{
    ui->verticalLayout->addWidget(widget);
}

void FourDParasSettingBaseWidget::initalize()
{
    ui->comboBox->clear();
    ui->comboBox->addItems(names());
    currenIndexChanged(ui->comboBox->currentText());
}

void FourDParasSettingBaseWidget::on_comboBox_currentIndexChanged(const QString& arg1)
{
    if (contains(arg1))
    {
        currenIndexChanged(arg1);
    }
}

void FourDParasSettingBaseWidget::on_addButton_clicked()
{
    bool ok = false;
    QString str = InputDialogFrame::getText(this, m_SettingName, tr("Please input the name:"), QLineEdit::Normal,
                                            QString("User%1").arg(ui->comboBox->count() + 1), &ok);
    if (ok)
    {
        if (!contains(str))
        {
            ui->comboBox->addItem(str);
            add(str);
            ui->comboBox->setCurrentIndex(ui->comboBox->count() - 1);
        }
    }
}

void FourDParasSettingBaseWidget::on_renameButton_clicked()
{
    if (ui->comboBox->currentIndex() >= 1)
    {
        bool ok = false;
        QString str =
            InputDialogFrame::getText(this, "OpacityTransferFunction",
                                      tr(QString("%1Please input the name:").arg(m_Warning).toStdString().c_str()),
                                      QLineEdit::Normal, ui->comboBox->currentText(), &ok);
        m_Warning.clear();
        if (ok)
        {
            if (ui->comboBox->currentText() == str)
            {
                return;
            }
            if (rename(ui->comboBox->currentText(), str))
            {
                ui->comboBox->setItemText(ui->comboBox->currentIndex(), str);
            }
            else
            {
                m_Warning = QString("%1 exists, ").arg(str);
                on_renameButton_clicked();
            }
        }
    }
}

void FourDParasSettingBaseWidget::on_deleteButton_clicked()
{
    if (MessageBoxFrame::question(this, tr("Delete"), tr("Are you sure to delete the selected items?"),
                                  QMessageBox::Yes | QMessageBox::No, QMessageBox::No) == QMessageBox::Yes)
    {
        del(ui->comboBox->currentText());
        ui->comboBox->removeItem(ui->comboBox->currentIndex());
    }
}

void FourDParasSettingBaseWidget::on_saveButton_clicked()
{
    bool needSave = true;
    needSave = (MessageBoxFrame::question(
                    this, QString(), QString("Are you sure to save this %1 setting?").arg(ui->comboBox->currentText()),
                    QMessageBox::Yes | QMessageBox::No) == QMessageBox::Yes);
    if (needSave)
    {
        save(ui->comboBox->currentText());
        MessageBoxFrame::tipInformation(QString("%1 has been saved successfully!").arg(m_SettingName));
    }
}

#define FUNCTION_TEPLATE(CLASS)                                                                                        \
    template void FourDParasSettingBaseWidget::addOneParaT<CLASS>(const QString& name, const CLASS& id);               \
    template bool FourDParasSettingBaseWidget::isExistNameT<CLASS>(const QString& name);                               \
    template void FourDParasSettingBaseWidget::saveOneParaT<CLASS>();                                                  \
    template void FourDParasSettingBaseWidget::deleteOneParaT<CLASS>(const QString& name);                             \
    template void FourDParasSettingBaseWidget::renameT<CLASS>(const QString& newName, const QString& oldName);         \
    template QStringList FourDParasSettingBaseWidget::namesT<CLASS>();                                                 \
    template CLASS FourDParasSettingBaseWidget::oneParasT<CLASS>(const QString& name) const;

FUNCTION_TEPLATE(FourDParasInfo::FourDQualityPara)
FUNCTION_TEPLATE(FourDParasInfo::FourDLightAnglePara)
FUNCTION_TEPLATE(FourDParasInfo::FourDPresetPara)
FUNCTION_TEPLATE(FourDParasInfo::FourDRenderModePara)
