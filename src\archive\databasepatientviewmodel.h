#ifndef DATABASEPATIENTVIEWMODEL_H
#define DATABASEPATIENTVIEWMODEL_H
#include "archive_global.h"

#include "databaseviewmodel.h"
/**
 * @brief 数据库信息以patient模式显示
 */
class TreeItem;
class ARCHIVESHARED_EXPORT DataBasePatientViewModel : public DataBaseViewModel
{
    Q_OBJECT
public:
    DataBasePatientViewModel(QObject* parent = 0);
    ~DataBasePatientViewModel();
    /**
     * @brief data 病人的检查信息
     * @param index
     * @param role
     * @return
     */
    virtual QVariant data(const QModelIndex& index, int role) const;
    /**
     * @brief index 检查信息的modelindex格式
     * @param row
     * @param column
     * @param parent
     * @return
     */
    virtual QModelIndex index(int row, int column, const QModelIndex& parent) const;
    /**
     * @brief parent 父节点
     * @param child
     * @return
     */
    virtual QModelIndex parent(const QModelIndex& child) const;

    virtual QModelIndex sibling(int row, int column, const QModelIndex& idx) const;

    virtual Qt::ItemFlags flags(const QModelIndex& index) const;

    int rowCount(const QModelIndex& parent = QModelIndex()) const;
    int columnCount(const QModelIndex& parent = QModelIndex()) const;
    virtual QFileInfo fileInfo(const QModelIndex& index);
    virtual QString filePath(const QModelIndex& index);
    virtual QString patientId(const QModelIndex& index);
    virtual QString studyOid(const QModelIndex& index);

protected:
    void setupModelData(QSqlQuery& query, TreeItem* parent);
protected slots:
    void patientViewSetup();

private:
    bool hasChildren(const QModelIndex& parent) const;

private:
    TreeItem* rootItem;
};

#endif // DATABASEPATIENTVIEWMODEL_H
