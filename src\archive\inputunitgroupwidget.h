#ifndef INPUTUNITGROUPWIDGET_H
#define INPUTUNITGROUPWIDGET_H

#include <QWidget>
#include "archive_global.h"

namespace Ui
{
class InputUnitGroupWidget;
}

class ARCHIVESHARED_EXPORT InputUnitGroupWidget : public QWidget
{
    Q_OBJECT

public:
    explicit InputUnitGroupWidget(QWidget* parent = 0);
    ~InputUnitGroupWidget();

    void setEditEnabled(const bool enabled);
    void setEditText(const QString& textValue);
    QString curEditText();
    double curValue();
    int curUnit()
    {
        return m_Unit;
    }
    void setUnit(int unit)
    {
        m_Unit = unit;
    }
    void setUnitName(QString unitName);
    void setValue(QVariant value, const double maxLimit);
    void setUnitMinWidth(const int width);

signals:
    void textEdited();

private slots:
    void on_lineEditValue_editingFinished();
    void on_lineEditValue_textEdited(const QString& arg1);

private:
    Ui::InputUnitGroupWidget* ui;
    int m_Unit;
};

#endif // INPUTUNITGROUPWIDGET_H
