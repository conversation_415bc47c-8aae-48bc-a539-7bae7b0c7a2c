<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FourDQualityWidget</class>
 <widget class="QWidget" name="FourDQualityWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>290</width>
    <height>221</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>FourD Quality</string>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="labelQuality">
        <property name="text">
         <string>Quality Value</string>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="BaseComboBox" name="densityComboBox">
        <item>
         <property name="text">
          <string>DensityLow</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>DensityHigh</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="labelDensity">
        <property name="text">
         <string>Density Value</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="qualityLineEdit"/>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="labelLinespae">
        <property name="text">
         <string>LineSpace Value</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QLineEdit" name="lineSpaceLineEdit"/>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>81</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BaseComboBox</class>
   <extends>QComboBox</extends>
   <header>basecombobox.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
