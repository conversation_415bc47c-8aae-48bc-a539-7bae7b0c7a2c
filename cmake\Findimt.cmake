#
# The module defines the following variables:
# IMT_FOUND - True if imt found.
# IMT_INCLUDE_DIRS - where to find imt.h, etc.
# IMT_LIBRARIES - List of libraries when using imt.
#
thirdparty_prefix_path(imt)

find_path ( IMT_INCLUDE_DIR
            NAMES
                imt.h
                measure.h
            HINTS
                ${IMT_ROOT}/include
            )
set(IMT_INCLUDE_DIRS ${IMT_INCLUDE_DIR})
message("Found imt headers: ${IMT_INCLUDE_DIRS}")


find_library ( IMT_LIBRARY
            NAMES 
                imt
            HINTS
                ${IMT_ROOT}/lib
             )
set(IMT_LIBRARIES ${IMT_LIBRARY})
message("Found IMT libs: ${IMT_LIBRARY}")

