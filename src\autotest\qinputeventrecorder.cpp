#include "qinputeventrecorder.h"
#include "eventserialization.h"
#include <QContextMenuEvent>
#include <QFile>
#include <QDataStream>
#include <QTimer>
#include <QWidget>
#include <QApplication>
#include <QMetaClassInfo>
#include <QDebug>
#include <QPushButton>
#include <QFileDialog>
#include "tgcdataevent.h"
#include "stateevent.h"
#include "messageevent.h"
#ifdef USE_KEYBOARD
#include "ikeyboard.h"
#include "presskey.h"
#include "keyboardbase.h"
#endif
#include "applogger.h"

static QString objectPath(QObject* obj)
{
    QString res;
    for (; obj; obj = obj->parent())
    {
        if (!res.isEmpty())
            res.prepend("/");
        res.prepend(obj->objectName());
    }
    return res;
}
static bool isChild(QObject* obj, QObject* parent)
{
    while ((obj = obj->parent()))
    {
        if (obj == parent)
            return true;
    }

    return false;
}

QInputEventRecorder::EventDelivery::EventDelivery(int timeOffset, QObject* obj, QEvent* ev)
    : m_TimeOffset(timeOffset)
    , m_ClsName(obj->metaObject()->className())
    , m_ObjName(objectPath(obj))
    , m_Ev(ev)
{
}

void QInputEventRecorder::recordEvent(QObject* obj, QEvent* event)
{
    if (obj != NULL && event != NULL)
    {
        QDateTime curDt(QDateTime::currentDateTime());
        int timeOffset =
            m_RecordingStartTime.daysTo(curDt) * 24 * 3600 * 1000 + m_RecordingStartTime.time().msecsTo(curDt.time());
        m_Recording.push_back(EventDelivery(timeOffset, obj, event));
    }
}
#ifdef USE_KEYBOARD
void QInputEventRecorder::onFunctionCodeReceived(PressKey* key)
{
    QDateTime curDt(QDateTime::currentDateTime());
    int timeOffset =
        m_RecordingStartTime.daysTo(curDt) * 24 * 3600 * 1000 + m_RecordingStartTime.time().msecsTo(curDt.time());

    if (key->functionName().contains("Mode"))
    {
        OperateLogger::Logger()->info("state");
    }
    QEvent* event = new StateEvent(key->functionName(), key->isSinglePress());

    writeLogger(timeOffset, static_cast<QInputEvent*>(event));
}
#endif
void QInputEventRecorder::onTgcDataReceived(unsigned char* tgcData, int tgcLen)
{
    QEvent* event = new TGCDataEvent(QByteArray((const char*)(tgcData), tgcLen));
    recordEvent(sender(), event);
}

//
// QInputEventRecorder
//
QEvent* QInputEventRecorder::cloneEvent(QEvent* ev)
{
    if (dynamic_cast<QContextMenuEvent*>(ev))
        return new QContextMenuEvent(*static_cast<QContextMenuEvent*>(ev));
    else if (dynamic_cast<QKeyEvent*>(ev))
        return new QKeyEvent(*static_cast<QKeyEvent*>(ev));
    else if (dynamic_cast<QMouseEvent*>(ev))
        return new QMouseEvent(*static_cast<QMouseEvent*>(ev));
    else if (dynamic_cast<QTabletEvent*>(ev))
        return new QTabletEvent(*static_cast<QTabletEvent*>(ev));
    else if (dynamic_cast<QTouchEvent*>(ev))
        return new QTouchEvent(*static_cast<QTouchEvent*>(ev));
    else if (dynamic_cast<QWheelEvent*>(ev))
        return new QWheelEvent(*static_cast<QWheelEvent*>(ev));
    else if (dynamic_cast<StateEvent*>(ev))
        return new StateEvent(*static_cast<StateEvent*>(ev));
    else if (dynamic_cast<TGCDataEvent*>(ev))
        return new TGCDataEvent(*static_cast<TGCDataEvent*>(ev));
    else if (dynamic_cast<MessageEvent*>(ev)) //
        return new MessageEvent(*static_cast<MessageEvent*>(ev));
    return 0;
}

QInputEventRecorder::QInputEventRecorder(QObject* obj)
    : m_Obj(obj)
    , m_Timer(new QTimer)
    , m_Keyboard(NULL)
{
    m_Timer->setSingleShot(true);
    QObject::connect(m_Timer, SIGNAL(timeout()), this, SLOT(replay()));
}

QInputEventRecorder::~QInputEventRecorder()
{
    delete m_Timer;
}

void QInputEventRecorder::setKeyboard(IKeyboard* value)
{
    m_Keyboard = value;
}

bool QInputEventRecorder::eventFilter(QObject* obj, QEvent* ev)
{
    int timeOffset = 0;
    static bool isRecode = true;

    if (ev->type() == QEvent::KeyPress && isRecode)
    {
        QKeyEvent* keyEvent = static_cast<QKeyEvent*>(ev);

        if (keyEvent->modifiers() != Qt::AltModifier)
        {
            QDateTime curDt(QDateTime::currentDateTime());
            timeOffset = m_RecordingStartTime.daysTo(curDt) * 24 * 3600 * 1000 +
                         m_RecordingStartTime.time().msecsTo(curDt.time()); // - m_StopTime;
            writeLogger(timeOffset, static_cast<QInputEvent*>(ev));
        }
        isRecode = false;
    }
    else if (ev->type() == QEvent::KeyRelease || ev->type() == QEvent::MouseButtonRelease)
    {
        isRecode = true;
    }
    else if (ev->type() == QEvent::MouseMove)
    {
        QDateTime curDt(QDateTime::currentDateTime());
        timeOffset = m_RecordingStartTime.daysTo(curDt) * 24 * 3600 * 1000 +
                     m_RecordingStartTime.time().msecsTo(curDt.time()); // - m_StopTime;
        writeLogger(timeOffset, static_cast<QInputEvent*>(ev));
    }
    else if (ev->type() == QEvent::MouseButtonPress && isRecode)
    {
        QDateTime curDt(QDateTime::currentDateTime());
        timeOffset = m_RecordingStartTime.daysTo(curDt) * 24 * 3600 * 1000 +
                     m_RecordingStartTime.time().msecsTo(curDt.time()); // - m_StopTime;
        writeLogger(timeOffset, static_cast<QInputEvent*>(ev));
        isRecode = false;
    }
    else
    {
    }
    return QObject::eventFilter(obj, ev);
}

void QInputEventRecorder::save(const QString& fileName)
{
    QFile f(fileName);
    if (!f.open(QFile::WriteOnly))
        return;
    QDataStream ds(&f);
    foreach (EventDelivery ed, m_Recording)
    {
        ds << (qint32)ed.timeOffset(); // << ed.clsName() << ed.objName();
        QEvent* ev(ed.event());
        ds << static_cast<QInputEvent*>(ev);
    }
}

void QInputEventRecorder::load(const QString& fileName)
{
    bool isBeginLoad = false;
    char messageBuf[512];
    QString message;
    QStringList paraList;
    qint32 timeOffset;
    QString clsName, objName;
    QInputEvent* ev = NULL;

    QFile f(fileName);
    if (!f.open(QFile::ReadOnly))
        return;

    m_Recording.clear();

    while (!f.atEnd())
    {
        f.readLine(messageBuf, 512);
        message.append(messageBuf);
        message.remove('\n');
        message = message.section('#', 1);
        if (message.compare("state") == 0)
        {
            isBeginLoad = true;
            continue;
        }
        else
        {
            paraList = message.split(':');
        }

        if (isBeginLoad)
        {
            readLogger(&timeOffset, ev, paraList);
            m_Recording.push_back(EventDelivery(timeOffset, clsName, objName, ev));
        }
    }
}

void QInputEventRecorder::nameAllWidgets(QWidget* w)
{
    static int uniqueId = 0;

    QObjectList children = w->children();
    foreach (QObject* o, children)
    {
        if (dynamic_cast<QWidget*>(o))
        {
            if (o->objectName().isEmpty())
                o->setObjectName(QString("unique_%1").arg(uniqueId++));
            nameAllWidgets(static_cast<QWidget*>(o));
        }
    }
}

void QInputEventRecorder::record()
{
#ifdef USE_KEYBOARD
    m_Recording.clear();
    qApp->installEventFilter(this);
    m_RecordingStartTime = QDateTime::currentDateTime();
    connect(m_Keyboard, SIGNAL(functionCodeReceived(PressKey*)), this, SLOT(onFunctionCodeReceived(PressKey*)));
    connect(m_Keyboard, SIGNAL(tgcDataReceived(unsigned char*, int)), this,
            SLOT(onTgcDataReceived(unsigned char*, int)));
#endif
}

void QInputEventRecorder::stop()
{
#ifdef USE_KEYBOARD
    qApp->removeEventFilter(this);
    m_Timer->stop();
    disconnect(m_Keyboard, SIGNAL(functionCodeReceived(PressKey*)), this, SLOT(onFunctionCodeReceived(PressKey*)));
    disconnect(m_Keyboard, SIGNAL(tgcDataReceived(unsigned char*, int)), this,
               SLOT(onTgcDataReceived(unsigned char*, int)));

    disconnect(this, SIGNAL(functionCodeReceived(PressKey*)), m_Keyboard, SIGNAL(functionCodeReceived(PressKey*)));
    disconnect(this, SIGNAL(tgcDataReceived(unsigned char*, int)), m_Keyboard,
               SIGNAL(tgcDataReceived(unsigned char*, int)));
#endif
}

void QInputEventRecorder::replay(float speedFactor)
{
#ifdef USE_KEYBOARD
    if (m_Recording.size() == 0)
    {
        disconnect(this, SIGNAL(functionCodeReceived(PressKey*)), m_Keyboard, SIGNAL(functionCodeReceived(PressKey*)));
        disconnect(this, SIGNAL(tgcDataReceived(unsigned char*, int)), m_Keyboard,
                   SIGNAL(tgcDataReceived(unsigned char*, int)));
        return;
    }

    qApp->removeEventFilter(this);

    disconnect(m_Keyboard, SIGNAL(functionCodeReceived(PressKey*)), this, SLOT(onFunctionCodeReceived(PressKey*)));
    disconnect(m_Keyboard, SIGNAL(tgcDataReceived(unsigned char*, int)), this,
               SLOT(onTgcDataReceived(unsigned char*, int)));
    connect(this, SIGNAL(functionCodeReceived(PressKey*)), m_Keyboard, SIGNAL(functionCodeReceived(PressKey*)));
    connect(this, SIGNAL(tgcDataReceived(unsigned char*, int)), m_Keyboard,
            SIGNAL(tgcDataReceived(unsigned char*, int)));

    m_ReplayPos = 0;
    m_ReplaySpeedFactor = speedFactor;
    m_Timer->setInterval(0);
    m_Timer->start();
#endif
}

void QInputEventRecorder::replay()
{
    EventDelivery& rec(m_Recording[m_ReplayPos++]);
    QWidget* w = QApplication::widgetAt(QCursor::pos());
    QEvent* e = cloneEvent(rec.event());
    if (e->type() == QEvent::MouseMove)
    {
        QCursor::setPos(static_cast<QMouseEvent*>(e)->globalPos());
        if (w != NULL)
        {
            w->setFocus();
            qApp->postEvent(w, e);
        }
    }
    else if (e->type() == StateEvent::eventType())
    {
#ifdef USE_KEYBOARD
        QString evName = static_cast<StateEvent*>(e)->eventName();
        bool isSinglePress = static_cast<StateEvent*>(e)->isSinglePress();
        PressKey key(0, evName, evName, isSinglePress);
        emit functionCodeReceived(&key);
#endif
    }
    else if (e->type() == TGCDataEvent::eventType())
    {
        QByteArray data = static_cast<TGCDataEvent*>(e)->tgcData();
        emit tgcDataReceived((unsigned char*)data.data(), data.size());
    }
    else
    {
        if (w != NULL)
        {
            w->setFocus();
            qApp->postEvent(w, e);
        }
    }

    if (m_ReplayPos >= m_Recording.size())
    {
        m_ReplayPos = 0;
        m_Timer->setInterval(100 * m_ReplaySpeedFactor);
        m_Timer->start();
        return;
    }
    int delta = m_Recording[m_ReplayPos].timeOffset() - m_Recording[m_ReplayPos - 1].timeOffset();
    m_Timer->setInterval(delta > 0 ? delta * m_ReplaySpeedFactor : 0);
    m_Timer->start();
}
