add_definitions(-DCONTROLS_LIBRARY)

target_files(${USE_MOTOR} motor)
set(MOTOR_FILES ${TARGET_FILE})

if(${USE_ULTRAREMOTE})
    set(ULTRAREMOTEMODEL ultraremotemodel)
endif()


include_depends(imagerender presetmodel systeminfo licensetool archive obtableview presetsettingview elementtestview settingview systemstatusview thumbnailview
        reportview measurementview keyboard autotest hardware commentview bodymarkview exammodeview
        sonoguideview registerview buffer usapi settingview ${MOTOR_FILES} ${FH3D_INCLUDE_DIR_NAME} adminview ${ULTRAREMOTEMODEL} usf.common.ui.controls)

target_files(${USE_4D}
		fourdlightanglesettingwidget.cpp 
		fourdlightanglewidget.cpp
		fourdparasetbasewidget.cpp
		fourdparasettingwidget.cpp
		fourdparassettingbasewidget.cpp
		fourdpresetsettingwidget.cpp
		fourdpresetwidget.cpp
		fourdqualitysettingwidget.cpp
		fourdqualitywidget.cpp
		fourdrendermodesettingwidget.cpp
		fourdrendermodewidget.cpp
		fourdiimageparaswidget.cpp
		fourdfreezebarwidget.cpp
		fourdtransformwidget.cpp	
		fourdrightwidget.cpp
		fourdlivemodel.cpp
		abstractfourdlivewidget.cpp
		chisonfourdlivewidget.cpp
		chisonfourdresourcefactory.cpp
		ifourdlivewidget.cpp
		ifourdresourcefactory.cpp
		odsfourdlivewidget.cpp
		odsfourdresourcefactory.cpp
		chisonfourdcolorbarwidget.cpp
        ifourdlicenseprocessor.cpp
        abstractfourdlicenseprocessor.cpp
        chisonfourdlicenseprocessor.cpp
        odsfourdliceneseprocessor.cpp
		)
set(FOURD_RELATED_CPPS ${TARGET_FILE})

target_files(${USE_FREEHAND3D}  freehand3droicontrol.cpp freehand3dwidget.cpp freehand3dmodel.cpp)
set(FH3D_RELATED_CPPS ${TARGET_FILE})

if(WIN32)
    set(systemusagedef systemusagewin.cpp)
elseif(UNIX)
    set(systemusagedef systemusagelinux.cpp)
endif()

add_library_qt(controls 
    configurationwidget.cpp
    systemhintwidget.cpp
    systemhintdefaultwidget.cpp
	workstatuswidget.cpp
	multifuncoperationareaview.cpp
    systemtbstatuswidget.cpp
    bfhwinfomodel.cpp
    vmonitor.cpp
    vmonitorselect.cpp
    autoswitchprobewidget.cpp
    burnintestwidget.cpp
    fpsinfowidget.cpp
    parametersetupwidget.cpp
    demoimporter.cpp
    demoimporters.cpp
    screensaverdialog.cpp
    seniorscreensaverdialog.cpp
    storeprogressmoviewidget.cpp
    modechangewidget.cpp
    motoroperationwidget.cpp
    probeselftestwidget.cpp
    seniormediawidget.cpp
    mediaplayercontroller.cpp
    autotestform.cpp
    isystemusage.cpp
    ${systemusagedef}
    ${FOURD_RELATED_CPPS}
    ${FH3D_RELATED_CPPS}
    )

target_link_libraries(controls imagerender presetmodel systeminfo licensetool archive obtableview settingview systemstatusview thumbnailview
        reportview measurementview keyboard autotest hardware commentview bodymarkview exammodeview presetsettingview elementtestview
        sonoguideview registerview buffer usapi settingview adminview ${FH3D_LIB_NAME} ${FFMPEG_LIBRARIES}  ${MNN_LIBRARIES} ${ULTRAREMOTEMODEL} usf.common.ui.controls)

if(${USE_MOTOR})
    target_include_directories(controls PRIVATE motor ${FFMPEG_INCLUDE_DIRS})
    target_link_libraries(controls motor) 
endif(${USE_MOTOR})

if(${USE_FFMPEG})
    add_custom_command(TARGET controls
        POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/ffmpeglib/
        COMMAND ${CMAKE_COMMAND} -E copy ${FFMPEG_BIN_DIR}/* ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/ffmpeglib/
        )
endif(${USE_FFMPEG})


