#include "dicomserverwidget.h"
#include "ui_dicomserverwidget.h"
#include "util.h"
#include "dicommodel.h"
#include "messageboxframe.h"
#include "systeminfo.h"
#include "netinfo.h"
#include "dicomutility.h"
#include "deletefilescontroller.h"

DicomServerWidget::DicomServerWidget(DicomChison::ServiceIndex index, QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::DicomServerWidget)
    , m_Model(NULL)
    , m_ServiceType(index)
    , m_WarningInfo(tr("All images do not support anonymous export or no image selection!"))
    , m_DeleteFilesController(NULL)
{
    ui->setupUi(this);
    ui->listWidget->setVisible(false);
    QStringList allKeys;
    m_DefaultName = DicomChison::getDefaultServiceName(index, allKeys);
    ui->comboBox->addItems(allKeys);

    if (allKeys.size() <= 1)
    {
        ui->comboBox->setEnabled(false);
    }
    if (m_DefaultName.isEmpty())
    {
        ui->comboBox->setCurrentIndex(0);
    }
    else
    {
        ui->comboBox->setCurrentIndex(allKeys.indexOf(m_DefaultName));
    }
}

DicomServerWidget::~DicomServerWidget()
{
    delete ui;
    // dicom的几个发送不需要判断文件个数用来删除exam,所以这里在widget析构时直接
    //调用dodelete删除记录的study和指针
    if (m_DeleteFilesController != NULL && !isHidden())
    {
        m_DeleteFilesController->doDelete(true);
    }
}

void DicomServerWidget::setModel(DicomModel* model)
{
    m_Model = model;
}

void DicomServerWidget::modifyWarningInfo(const QString& value)
{
    m_WarningInfo = value;
}

void DicomServerWidget::setDeleteFilesController(DeleteFilesController* controller)
{
    m_DeleteFilesController = controller;
}

void DicomServerWidget::retranslateUi()
{
    ui->retranslateUi(this);
}

void DicomServerWidget::on_pushButtonExport_clicked()
{
    if (ui->comboBox->currentText() != m_DefaultName)
    {
        m_Model->setSpecialServiceGroup(ui->comboBox->currentText());
    }
    m_Model->setParentWidget(this);
    m_Model->setIsGDPR(ui->checkBoxGDPR->isChecked());
    if (!SystemInfo::instance().netInfo()->linked())
    {
        DicomUtility::showNotLinkedOtherWarning();
    }
    else
    {
        int successNum = m_Model->doDicomWork(m_ServiceType);
        if (successNum > 0)
        {
            MessageBoxFrame::tipInformation(
                tr("%n DICOM %1 Task(s) has been appended to the DICOM Task Queue!", "", successNum)
                    .arg(DicomChison::getServiceNameOfIndex(m_ServiceType)));
        }
        else
        {
            MessageBoxFrame::warning(this, QString(), m_WarningInfo);
        }
    }

    emit closed();
}

void DicomServerWidget::on_pushButtonClose_clicked()
{
    emit closed();
}

void DicomServerWidget::on_pushButtonDetail_clicked()
{
    if (ui->listWidget->isVisible())
    {
        ui->listWidget->setVisible(false);
        ui->pushButtonDetail->setText(tr("Detail").append(" >>"));
    }
    else
    {
        ui->listWidget->setVisible(true);
        updateServerDetailInfo();
        ui->pushButtonDetail->setText(tr("Hide").append("..."));
    }
}

void DicomServerWidget::on_comboBox_currentIndexChanged(int index)
{
    updateServerDetailInfo();
}

void DicomServerWidget::updateServerDetailInfo()
{
    if (ui->listWidget->isVisible())
    {
        ui->listWidget->clear();
        QStringList keys;
        keys << DicomChison::netKeyOfAetitleName() << DicomChison::netKeyOfIPName() << DicomChison::netKeyOfPortName()
             << DicomChison::netKeyOfTimeoutName();
        QMap<QString, QString> res =
            DicomChison::getSpecifiedKeysValues(m_ServiceType, ui->comboBox->currentText(), keys);

        foreach (const QString& str, keys)
        {
            ui->listWidget->addItem(Util::translate("DicomChison", str) + "   " + res.value(str));
        }
    }
}
