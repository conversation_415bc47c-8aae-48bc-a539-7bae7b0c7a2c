#ifndef AUTOEFALG_H
#define AUTOEFALG_H

#include "algorithm_global.h"
#include <QLibrary>
#ifdef USE_AUTOEF
#include "autoef.hpp"
#else
typedef struct LV_STRUCT
{
    int pnts[4000];
    int pntsNum;
    float area;
    float volume;
} LVSEG;

enum LVTYPE
{
    A4C = 0,
    A2C,
    ALAX,
    PSAXB,
    PSAXM,
    PSAXA,
};
#endif
#include "virtualalg.h"

class ALGORITHMSHARED_EXPORT AutoEfAlg : public VirtualAlg
{
    friend class AlgInstance;

private:
    AutoEfAlg();

public:
    inline bool isError(void)
    {
        return m_IsError;
    }

    typedef int (*LVInitDetect)(const char* path, int code, float thresh, const char* clcache_path);
    typedef int (*LVDetect)(uint8_t* imgdata, int width, int height, int& num, int* pnts, float& area, float& volume);
    typedef int (*LVIQA)(uint8_t* imgdata, int width, int height, float& score);
    typedef int (*LVRelease)();

    int initDetect(const char* path, int code, float thresh, const char* clcache_path);
    int detect(uint8_t* imgdata, int width, int height, int& num, int* pnts, float& area, float& volume);
    int iQA(uint8_t* imgdata, int width, int height, float& score);
    int release();

private:
    LVDetect m_LVDetect;
};

#endif // AUTOEFALG_H
