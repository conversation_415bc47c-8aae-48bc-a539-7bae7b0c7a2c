#ifndef QINPUTEVENTRECORDER_H
#define QINPUTEVENTRECORDER_H

#include "autotest_global.h"
#include <QDateTime>
#include <QEvent>
#include <QMetaClassInfo>
#include <QSharedPointer>
#include <QStringList>
#include <QVector>
class QTimer;
class IKeyboard;
#ifdef USE_KEYBOARD
class PressKey;
#endif
class StateEvent;

class AUTOTESTSHARED_EXPORT QInputEventRecorder : public QObject
{
    Q_OBJECT

public:
    class AUTOTESTSHARED_EXPORT EventDelivery
    {
    public:
        EventDelivery()
            : m_TimeOffset(0)
            , m_Ev(nullptr)
        {
        }

        EventDelivery(int timeOffset, QObject* obj, QEvent* ev);

        EventDelivery(int timeOffset, const QString& clsName, const QString& objName, QEvent* ev)
            : m_TimeOffset(timeOffset)
            , m_ClsName(clsName)
            , m_ObjName(objName)
            , m_Ev(ev)
        {
        }
        int timeOffset() const
        {
            return m_TimeOffset;
        }
        const QString& clsName() const
        {
            return m_ClsName;
        }
        const QString& objName() const
        {
            return m_ObjName;
        }
        QEvent* event() const
        {
            return m_Ev.data();
        }

    private:
        int m_TimeOffset;
        QString m_ClsName;
        QString m_ObjName;
        QSharedPointer<QEvent> m_Ev;
    };

public:
    QInputEventRecorder(QObject* obj = 0);
    ~QInputEventRecorder();
    void setKeyboard(IKeyboard* value);

    virtual bool eventFilter(QObject* obj, QEvent* ev);
    void setObj(QObject* obj)
    {
        m_Obj = obj;
    }
    void save(const QString& fileName);
    void load(const QString& fileName);

    static void nameAllWidgets(QWidget*);

public slots:
    void record();
    void replay(float speedFactor);
    void stop();

private slots:
    void replay();
#ifdef USE_KEYBOARD
    void onFunctionCodeReceived(PressKey* key);
#endif
    void onTgcDataReceived(unsigned char* tgcData, int tgcLen);

signals:
#ifdef USE_KEYBOARD
    void functionCodeReceived(PressKey* key);
#endif
    void tgcDataReceived(unsigned char* tgcData, int tgcLen);

private:
    void recordEvent(QObject* obj, QEvent* event);
    QEvent* cloneEvent(QEvent*);

private:
    QObject* m_Obj;
    QVector<EventDelivery> m_Recording;
    int m_ReplayPos;
    QTimer* m_Timer;
    QDateTime m_RecordingStartTime;
    float m_ReplaySpeedFactor;
    IKeyboard* m_Keyboard;
    QStringList m_Messages;
};

#endif // QINPUTEVENTRECORDER_H
