@echo off
set num=0
for %%a in (%*) do set /a num+=1
if /I "%1"=="-h" (
    call:usage
    pause
    goto:end
)

setlocal EnableDelayedExpansion
set index=0
for %%a in (%*) do (
    set a[!index!]=%%a
    set /a index+=1
)
set /a range=!index!-1
for /l %%n in (0,2,!range!) do (
            if "!a[%%n]!" == "-r" (
                set /a RESOURCEBRANCHIndex=%%n+1
            ) else if "!a[%%n]!" == "-e" (
                set /a GITLABPATHIndex=%%n+1 
            ) else if "!a[%%n]!" == "-u" (
                set /a USAPIEXAMPLEBRANCHIndex=%%n+1 
            ) 
)

if defined RESOURCEBRANCHIndex (
	set RESOURCEBRANCH=!a[%RESOURCEBRANCHIndex%]!
)
if defined GITLABPATHIndex (
    set GITLABPATH=!a[%GITLABPATHIndex%]!
)
if not defined RESOURCEBRANCH (
	set RESOURCEBRANCH=pangu
)

if defined USAPIEXAMPLEBRANCHIndex (
	set USAPIEXAMPLEBRANCH=!a[%USAPIEXAMPLEBRANCHIndex%]!
)

if not defined USAPIEXAMPLEBRANCH (
	set USAPIEXAMPLEBRANCH=master
)

setlocal DisableDelayedExpansion

if not exist %GITLABPATH%\code (
    mkdir %GITLABPATH%\code
)

call:deal_res
call:deal_usapi
goto:end

@echo off
:deal_res
    cd /d %GITLABPATH%\code
    if not exist resource-xunit (
        git clone git@192.168.20.38:usplatform/resource-xunit.git
    )
    cd /d %GITLABPATH%\code\resource-xunit
    git fetch
    git reset --hard origin/%RESOURCEBRANCH%
    if %errorlevel% equ 128 (
        echo ===========Parameter error, please re-enter!===========
        exit /b 128
    )
goto:end

@echo off
:deal_usapi
    cd /d %GITLABPATH%\code
    if not exist usapiexample (
        git clone git@192.168.20.38:usplatform_thirdparty/usapiexample.git
    )
    cd /d %GITLABPATH%\code\usapiexample
    git fetch
    git reset --hard origin/%USAPIEXAMPLEBRANCH%
goto:end


:end



