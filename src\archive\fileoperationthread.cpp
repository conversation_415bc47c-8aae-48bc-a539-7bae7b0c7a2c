#include "fileoperationthread.h"
#include <QDir>

FileOperationThread::FileOperationThread(QObject* parent)
    : QThread(parent)
{
}

void FileOperationThread::run()
{
}

void FileOperationThread::setCurTaskName(const QString& str)
{
    m_CurTaskName = str;
}

QString FileOperationThread::curTaskName()
{
    return m_CurTaskName;
}

qint64 FileOperationThread::calcSrcSize(const QString& path)
{
    QFileInfo info(path);
    if (info.isFile())
    {
        return info.size();
    }
    else
    {
        QFileInfoList list = QDir(path).entryInfoList(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);

        checkIfNeedToChangeFileList(list);

        qint64 dirSize = 0;
        for (int i = 0; i < list.count(); i++)
        {
            if (list.at(i).isFile())
            {
                dirSize += list.at(i).size();
            }
            else
            {
                //                dirSize += list.at(i).size();
                dirSize += calcSrcSize(list.at(i).filePath());
            }
        }
        return dirSize;
    }
}

qint64 FileOperationThread::calcSrcSizes(const QStringList& list)
{
    qint64 size = 0;
    foreach (QString str, list)
    {
        size += calcSrcSize(str);
    }
    return size;
}

qint64 FileOperationThread::calcSrcSizes(const QFileInfoList& list)
{
    qint64 size = 0;
    int count = 0;
    foreach (QFileInfo info, list)
    {
        size += calcSrcSize(info.filePath());
        emit curValue((++count) * 100 / list.count());
    }
    return size;
}
