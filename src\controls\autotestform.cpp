#include "autotestform.h"
#include "ui_autotestform.h"
#include "util.h"
#include <QDebug>

#ifdef Q_OS_LINUX
#define RECORD_PROGRAM "./record"
#define REPLAY_PROGRAM "./replay"
#define KILL_PROGRAM "killall %1"
#elif defined(Q_OS_WIN)
#define RECORD_PROGRAM "record.exe"
#define REPLAY_PROGRAM "replay.exe"
#define KILL_PROGRAM "taskkill /IM %1 /F /T"
#else
#define RECORD_PROGRAM "TODO"
#define REPLAY_PROGRAM "TODO"
#define KILL_PROGRAM "TODO"
#endif

AutoTestDialog::AutoTestDialog(QWidget* parent)
    : BaseDialogFrame(parent)
    , m_<PERSON>(nullptr)
{
    m_Child = new AutoTestForm(this);
    this->setContent(m_Child);
    setFrameTitle(tr("Auto Test"));
    connect(m_<PERSON>, &AutoTestForm::quit, this, [this] { this->close(); });
}

void AutoTestDialog::retranslateUi()
{
    setFrameTitle(tr("Auto Test"));
}

void AutoTestForm::retranslateUi()
{
    ui->retranslateUi(this);
}

AutoTestForm::AutoTestForm(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::AutoTestForm)
    , m_CurTestStatus(Init)
{
    ui->setupUi(this);
    setTestStatus(Waiting);
    connect(&m_Process, &QProcess::errorOccurred, this, [](QProcess::ProcessError error) { qCritical() << error; });
    connect(&m_Process, &QProcess::readyReadStandardError, this,
            [this] { qCritical() << "standard error" << m_Process.readAllStandardError(); });
    //    connect(&m_Process, &QProcess::readyReadStandardOutput, this, [this]{
    //        qDebug()<<"standard output"<<m_Process.readAllStandardOutput();
    //    });
    connect(&m_Process, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished), this,
            [this](int code, QProcess::ExitStatus exitStatus) {
                Q_UNUSED(code)
                Q_UNUSED(exitStatus)
                setTestStatus(Waiting);
            });
}

AutoTestForm::~AutoTestForm()
{
    delete ui;
}

void AutoTestForm::setTestStatus(const AutoTestForm::TestStatus status)
{
    if (m_CurTestStatus == status)
        return;
    switch (status)
    {
    case Init:
    case Waiting:
        ui->btnRecord->setText(tr("Start Record"));
        ui->btnRecord->setEnabled(true);
        ui->btnReplay->setText(tr("Start Replay"));
        ui->btnReplay->setEnabled(true);
        break;
    case Recording:
        ui->btnRecord->setText(tr("Stop Record"));
        ui->btnRecord->setEnabled(true);
        ui->btnReplay->setEnabled(false);
        break;
    case Replaying:
        ui->btnRecord->setEnabled(false);
        ui->btnReplay->setText(tr("Stop Replay"));
        ui->btnReplay->setEnabled(true);
        break;
    }
    m_CurTestStatus = status;
}

void AutoTestForm::on_btnRecord_clicked()
{
    if (m_CurTestStatus == Recording)
    {
        setTestStatus(Waiting);
        stopRecord();
    }
    else
    {
        setTestStatus(Recording);
        startRecord();
    }
}

void AutoTestForm::on_btnReplay_clicked()
{
    if (m_CurTestStatus == Replaying)
    {
        setTestStatus(Waiting);
        stopReplay();
    }
    else
    {
        setTestStatus(Replaying);
        startReplay();
    }
}

void AutoTestForm::startRecord()
{
    m_Process.start(RECORD_PROGRAM, QStringList(), QIODevice::ReadWrite);
    emit quit();
}

void AutoTestForm::startReplay()
{
    m_Process.start(REPLAY_PROGRAM, QStringList(), QIODevice::ReadWrite);
    emit quit();
}

void AutoTestForm::stopRecord()
{
    Util::System(QString(KILL_PROGRAM).arg(RECORD_PROGRAM));
    m_Process.close();
}

void AutoTestForm::stopReplay()
{
    Util::System(QString(KILL_PROGRAM).arg(REPLAY_PROGRAM));
    m_Process.close();
}
