#ifndef FOURDRENDERMODEWIDGET_H
#define FOURDRENDERMODEWIDGET_H

#include "controls_global.h"
#include "fourdparasetbasewidget.h"
#include "fourdparasinfo.h"

namespace Ui
{
class FourDRenderModeWidget;
}

class CONTROLSSHARED_EXPORT FourDRenderModeWidget : public FourDParaSetBaseWidget
{
    Q_OBJECT

public:
    explicit FourDRenderModeWidget(QWidget* parent = 0);
    ~FourDRenderModeWidget();
    virtual void initalize();
    void setFourDRenderModePara(const FourDParasInfo::FourDRenderModePara& para);
    FourDParasInfo::FourDRenderModePara fourDRenderModePara();

protected:
    void retranslateUi();

private:
    Ui::FourDRenderModeWidget* ui;
    FourDParasInfo::FourDRenderModePara m_FourDRenderModePara;
};

#endif // FOURDRENDERMODEWIDGET_H
