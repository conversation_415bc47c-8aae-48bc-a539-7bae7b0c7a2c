#ifndef BLUETOOTHSTORAGEFILEWIDGET_H
#define BLUETOOTHSTORAGEFILEWIDGET_H

#include "archive_global.h"

#include "dicomtaskmanager.h"
#include "basewidget.h"
#include "imageprocessedcopy.h"

#include <QWidget>
#include <QMap>
#include <QBluetoothAddress>

class ImageProcessedCopy;
class IColorMapManager;

namespace Ui
{
class BluetoothStorageFilleWidget;
}

class ARCHIVESHARED_EXPORT BluetoothStorageFileWidget : public BaseWidget
{
    Q_OBJECT
public:
    explicit BluetoothStorageFileWidget(IColorMapManager* colorMapManager, QWidget* parent = 0);
    ~BluetoothStorageFileWidget();
    void initBluetoothNames();
    void setExportFileListPaths(const QStringList& filePaths);
    void setDicomTaskManager(DicomTaskManager* dicomTaskManager);

    static bool isFileExists(const QString& fileName, const QString& path);

private:
    void setCopyOptions();
    void readyForFile();

    QFileInfoList getAllInfoList(const QDir& dir);
    void setFileFilter(QStringList suffixFilters);

protected:
    void retranslateUi();

private slots:
    void on_pushButtonClose_clicked();

    void on_pushButtonExport_clicked();

    void on_comboBoxBluetoothDevices_currentIndexChanged(const QString& bluetoothdevicename);

    void on_radioButtonPNG_toggled(bool checked);

    void on_radioButtonJPG_toggled(bool checked);

    void on_radioButtonBMP_toggled(bool checked);

    void on_checkBoxGDPR_toggled(bool checked);

signals:
    void closed();

private:
    Ui::BluetoothStorageFilleWidget* ui;
    QStringList m_fileNameList;
    QString m_bluetoothDeviceName;
    QBluetoothAddress m_bluetoothAddr;
    DicomTaskManager* m_DicomTaskManager;

    QStringList m_tmpFilePathes;
    QString m_tmpFilePath;
    ImageProcessedCopy* m_copy;
    bool m_isGDPR;
};

#endif // BLUETOOTHSTORAGEFILEWIDGET_H
