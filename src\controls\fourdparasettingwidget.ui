<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FourDParaSettingWidget</class>
 <widget class="QWidget" name="FourDParaSettingWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="leftMargin">
    <number>1</number>
   </property>
   <property name="topMargin">
    <number>1</number>
   </property>
   <property name="rightMargin">
    <number>1</number>
   </property>
   <property name="bottomMargin">
    <number>1</number>
   </property>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <widget class="BaseComboBox" name="comboBox">
       <item>
        <property name="text">
         <string>FourDQuality</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>FourDLightAngle</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>FourDPreset</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>RenderModeMapping</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QStackedWidget" name="stackedWidget_fourd">
       <property name="currentIndex">
        <number>3</number>
       </property>
       <widget class="FourDQualitySettingWidget" name="fourdQualityWidget"/>
       <widget class="FourDLightAngleSettingWidget" name="fourdLightAngleWidget"/>
       <widget class="FourDPresetSettingWidget" name="fourdPresetWidget"/>
       <widget class="FourDRenderModeSettingWidget" name="fourdRenderModeWidget"/>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>FourDQualitySettingWidget</class>
   <extends>QWidget</extends>
   <header>fourdqualitysettingwidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>FourDLightAngleSettingWidget</class>
   <extends>QWidget</extends>
   <header>fourdlightanglesettingwidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>FourDPresetSettingWidget</class>
   <extends>QWidget</extends>
   <header>fourdpresetsettingwidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>FourDRenderModeSettingWidget</class>
   <extends>QWidget</extends>
   <header>fourdrendermodesettingwidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>BaseComboBox</class>
   <extends>QComboBox</extends>
   <header>basecombobox.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
