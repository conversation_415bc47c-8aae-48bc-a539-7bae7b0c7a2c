#include "patienthomogeneouswidget.h"
#include "ui_patienthomogeneouswidget.h"
#include "model/patient.h"
#include "model/study.h"
#include "stringqueue.h"
#include "appsetting.h"
#include <QRegExpValidator>
#include "PersonName.h"
#include "deleteitemdelegate.h"

PatientHomogeneousWidget::PatientHomogeneousWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::PatientHomogeneousWidget)
{
    ui->setupUi(this);

    QRegExp rxName("[^/:*?<>|\"\\\\^=`]{1,64}$"); //根据DICOM PersonName禁用以下字符/:*?<>|"\^=.
    QValidator* validatorName = new QRegExpValidator(rxName, this);
    ui->comboBoxDiagnostician->setValidator(validatorName);
    ui->comboBoxPhysician->setValidator(validatorName);
    ui->comboBoxSonographer->setValidator(validatorName);

    DeleteItemDelegate* diagnosticiandelegate = new DeleteItemDelegate(this);
    ui->comboBoxDiagnostician->setItemDelegate(diagnosticiandelegate);
    ui->comboBoxDiagnostician->setShowDeleteAll(true);
    connect(diagnosticiandelegate, SIGNAL(deleteItem(int)), this, SLOT(deleteDiagnostician(int)));
    connect(ui->comboBoxDiagnostician, SIGNAL(deleteAllItem()), this, SLOT(deleteDiagnosticianAllItem()));

    DeleteItemDelegate* physiciandelegate = new DeleteItemDelegate(this);
    ui->comboBoxPhysician->setItemDelegate(physiciandelegate);
    ui->comboBoxPhysician->setShowDeleteAll(true);
    connect(physiciandelegate, SIGNAL(deleteItem(int)), this, SLOT(deletePhysician(int)));
    connect(ui->comboBoxPhysician, SIGNAL(deleteAllItem()), this, SLOT(deletePhysicianAllItem()));

    DeleteItemDelegate* sonographer = new DeleteItemDelegate(this);
    ui->comboBoxSonographer->setItemDelegate(sonographer);
    ui->comboBoxSonographer->setShowDeleteAll(true);
    connect(sonographer, SIGNAL(deleteItem(int)), this, SLOT(deleteSonographer(int)));
    connect(ui->comboBoxSonographer, SIGNAL(deleteAllItem()), this, SLOT(deleteSonographerAllItem()));

    ui->comboBoxDiagnostician->clear();
    ui->comboBoxDiagnostician->addItems(StringQueues::instance().diagnosticPhysician().items());
    ui->comboBoxPhysician->clear();
    ui->comboBoxPhysician->addItems(StringQueues::instance().refPhysician().items());
    ui->comboBoxSonographer->clear();
    ui->comboBoxSonographer->addItems(StringQueues::instance().sonographer().items());
}

PatientHomogeneousWidget::~PatientHomogeneousWidget()
{
    delete ui;
}

void PatientHomogeneousWidget::setPatient(Patient* patient)
{
}

void PatientHomogeneousWidget::setStudy(Study* study)
{
    if (study != NULL)
    {
        // NameComponent name;
        ui->lineEditStudyDescription->setText(study->StudyDescription());
        ui->lineEditPrimary->setText(study->PrimaryIndications());
        ui->lineEditSecondary->setText(study->SecondaryIndications());

        // name.setInternalPersonName(study->ReferringPhysiciansName());
        ui->comboBoxPhysician->setEditText(study->ReferringPhysiciansName());
        ui->lineEditAccession->setText(study->AccessionNumber());
        ui->lineEditCPT4Code->setText(study->CPT4Code());
        ui->lineEditCPT4Description->setText(study->CPT4Description());

        // name.setInternalPersonName(study->DiagnosticPhysiciansName());
        ui->comboBoxDiagnostician->setEditText(study->DiagnosticPhysiciansName());

        // name.setInternalPersonName(study->Sonographer());
        ui->comboBoxSonographer->setEditText(study->Sonographer());
        ui->textEditComment->setText(study->Comment());
    }
}

void PatientHomogeneousWidget::flawlessStudy(Study*& study)
{
    if (study != NULL)
    {
        study->setStudyDescription(ui->lineEditStudyDescription->text().trimmed());
        study->setPrimaryIndications(ui->lineEditPrimary->text().trimmed());
        study->setSecondaryIndications(ui->lineEditSecondary->text().trimmed());

        //        if(AppSetting::isHuman())
        {
            study->setReferringPhysiciansName(ui->comboBoxPhysician->currentText().trimmed());
        }

        study->setAccessionNumber(ui->lineEditAccession->text().trimmed());
        study->setCPT4Code(ui->lineEditCPT4Code->text().trimmed());
        study->setCPT4Description(ui->lineEditCPT4Description->text().trimmed());
        study->setDiagnosticPhysiciansName(ui->comboBoxDiagnostician->currentText().trimmed());
        study->setSonographer(ui->comboBoxSonographer->currentText().trimmed());
        study->setComment(ui->textEditComment->toPlainText().trimmed());

        //        if(AppSetting::isHuman())
        {
            if (StringQueues::instance().diagnosticPhysician().add(ui->comboBoxDiagnostician->currentText().trimmed()))
            {
                ui->comboBoxDiagnostician->clear();
                ui->comboBoxDiagnostician->addItems(StringQueues::instance().diagnosticPhysician().items());
            }

            if (StringQueues::instance().refPhysician().add(ui->comboBoxPhysician->currentText().trimmed()))
            {
                ui->comboBoxPhysician->clear();
                ui->comboBoxPhysician->addItems(StringQueues::instance().refPhysician().items());
            }

            if (StringQueues::instance().sonographer().add(ui->comboBoxSonographer->currentText().trimmed()))
            {
                ui->comboBoxSonographer->clear();
                ui->comboBoxSonographer->addItems(StringQueues::instance().sonographer().items());
            }
        }
    }
}

void PatientHomogeneousWidget::clearStudyInfo()
{
    ui->lineEditStudyDescription->clear();
    ui->lineEditPrimary->clear();
    ui->lineEditSecondary->clear();
    ui->comboBoxPhysician->setEditText("");
    ui->lineEditAccession->clear();
    ui->lineEditCPT4Code->clear();
    ui->lineEditCPT4Description->clear();
    ui->comboBoxDiagnostician->setEditText("");
    ui->comboBoxSonographer->setEditText("");
    ui->textEditComment->clear();
}

void PatientHomogeneousWidget::setCurrentWidgetEnabled(bool enabled)
{
    //    this->setEnabled(enabled);
    ui->lineEditStudyDescription->setEnabled(enabled);
    ui->lineEditPrimary->setEnabled(enabled);
    ui->lineEditSecondary->setEnabled(enabled);
    ui->comboBoxPhysician->setEnabled(enabled);
    ui->lineEditAccession->setEnabled(enabled);
    ui->lineEditCPT4Code->setEnabled(enabled);
    ui->lineEditCPT4Description->setEnabled(enabled);
    ui->comboBoxDiagnostician->setEnabled(enabled);
    ui->comboBoxSonographer->setEnabled(enabled);
    ui->textEditComment->setEnabled(enabled);
}

void PatientHomogeneousWidget::setPhysiciansName(const QString& str)
{
    ui->comboBoxDiagnostician->setEditText(str);
}

void PatientHomogeneousWidget::setAccession(const QString& str)
{
    ui->lineEditAccession->setText(str);
}

void PatientHomogeneousWidget::retranslateUi()
{
    ui->retranslateUi(this);
}

void PatientHomogeneousWidget::deleteDiagnostician(const int& index)
{
    if (index < ui->comboBoxDiagnostician->count() &&
        StringQueues::instance().diagnosticPhysician().remove(ui->comboBoxDiagnostician->itemText(index).trimmed()))
    {
        ui->comboBoxDiagnostician->removeItem(index);
    }
}

void PatientHomogeneousWidget::deletePhysician(const int& index)
{
    if (index < ui->comboBoxPhysician->count() &&
        StringQueues::instance().refPhysician().remove(ui->comboBoxPhysician->itemText(index).trimmed()))
    {
        ui->comboBoxPhysician->removeItem(index);
    }
}
void PatientHomogeneousWidget::deleteSonographer(const int& index)
{
    if (index < ui->comboBoxSonographer->count() &&
        StringQueues::instance().sonographer().remove(ui->comboBoxSonographer->itemText(index).trimmed()))
    {
        ui->comboBoxSonographer->removeItem(index);
    }
}

void PatientHomogeneousWidget::deleteDiagnosticianAllItem()
{
    StringQueues::instance().diagnosticPhysician().clear();
}
void PatientHomogeneousWidget::deletePhysicianAllItem()
{
    StringQueues::instance().refPhysician().clear();
}
void PatientHomogeneousWidget::deleteSonographerAllItem()
{
    StringQueues::instance().sonographer().clear();
}
