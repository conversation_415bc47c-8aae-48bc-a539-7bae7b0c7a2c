#ifndef EVENTSERIALIZATION_H
#define EVENTSERIALIZATION_H

#include <QDataStream>
#include <QContextMenuEvent>
#include "autotest_global.h"
class StateEvent;
class TGCDataEvent;
class MessageEvent;

struct EventClass
{
    enum
    {
        ContextMenuEvent = 1,
        KeyEvent,
        MouseEvent,
        TabletEvent,
        TouchEvent,
        WheelEvent,
        StateEvent,
        TGCDataEvent,
        MessageEvent,
    };
};

void readLogger(qint32* timeoffset, QInputEvent*& ev, const QStringList& paraList);

void writeLogger(int timeOffset, const QInputEvent* ev);

QDataStream& operator<<(QDataStream& ds, const QContextMenuEvent& ev);
QDataStream& operator<<(QDataStream& ds, const QKeyEvent& ev);
QDataStream& operator<<(QDataStream& ds, const QMouseEvent& ev);
QDataStream& operator<<(QDataStream& ds, const QTabletEvent& ev);
QDataStream& operator<<(QDataStream& ds, const QTouchEvent& ev);
QDataStream& operator<<(QDataStream& ds, const QWheelEvent& ev);
QDataStream& operator<<(QDataStream& ds, const StateEvent& ev);
QDataStream& operator<<(QDataStream& ds, const TGCDataEvent& ev);
QDataStream& operator<<(QDataStream& ds, const MessageEvent& ev);
QDataStream& operator<<(QDataStream& ds, const QInputEvent* ev);

QDataStream& operator>>(QDataStream& ds, QContextMenuEvent*& ev);
QDataStream& operator>>(QDataStream& ds, QKeyEvent*& ev);
QDataStream& operator>>(QDataStream& ds, QMouseEvent*& ev);
QDataStream& operator>>(QDataStream& ds, QTabletEvent*& ev);
QDataStream& operator>>(QDataStream& ds, QTouchEvent*& ev);
QDataStream& operator>>(QDataStream& ds, QWheelEvent*& ev);
QDataStream& operator>>(QDataStream& ds, StateEvent*& ev);
QDataStream& operator>>(QDataStream& ds, TGCDataEvent*& ev);
QDataStream& operator>>(QDataStream& ds, MessageEvent*& ev);
QDataStream& operator>>(QDataStream& ds, QInputEvent*& ev);

#endif // EVENTSERIALIZATION_H
