#
# The module defines the following variables:
# ULTRAREMOTE_FOUND - True if ULTRAREMOTE found.
# ULTRAREMOTE_INCLUDE_DIRS - where to find, etc.
# ULTRAREMOTE_LIBRARIES - List of libraries when using UltraRemote.
#

thirdparty_prefix_path(ultraremote)

set(ULTRAREMOTE_INCLUDE_DIRS ${ULTRAREMOTE_ROOT}include  ${ULTRAREMOTE_ROOT}include/ZoomSDK ${ULTRAREMOTE_ROOT}include/qrencode ${ULTRAREMOTE_ROOT}include/openssl ${ULTRAREMOTE_ROOT}include/libyuv)
message("Found UltraRemote headers: ${ULTRAREMOTE_INCLUDE_DIRS}")


find_library ( FDKAA_LIBRARY
            NAMES 
                fdkaac2
            HINTS
                ${ULTRAREMOTE_ROOT}/lib
             )
             
find_library ( MPG123_LIBRARY
            NAMES 
                mpg123
            HINTS
                ${ULTRAREMOTE_ROOT}/lib
             )
             
find_library ( TURBOJPEG_LIBRARY
            NAMES 
                turbojpeg
            HINTS
                ${ULTRAREMOTE_ROOT}/lib
             )
             
find_library ( ZOOMSDK_LIBRARY
            NAMES 
                videosdk
            HINTS
                ${ULTRAREMOTE_ROOT}/lib
             )
             
find_library ( XCBXTEST_LIBRARY
            NAMES 
                xcb-xtest
            HINTS
                ${ULTRAREMOTE_ROOT}/lib
             )
            
find_library ( QRENCODE_LIBRARY
            NAMES 
                qrencode
            HINTS
                ${ULTRAREMOTE_ROOT}/lib
             )
             
find_library ( SSL_LIBRARY
            NAMES 
                ssl
            HINTS
                ${ULTRAREMOTE_ROOT}/lib
             )
             
find_library ( CRYPTO_LIBRARY
            NAMES 
                crypto
            HINTS
                ${ULTRAREMOTE_ROOT}/lib
             )

find_library ( YUV_LIBRARY
             NAMES
                 yuv
             HINTS
                 ${ULTRAREMOTE_ROOT}/lib
            )

find_library ( JPEG_LIBRARY
             NAMES
                 jpeg
             HINTS
                 ${ULTRAREMOTE_ROOT}/lib
            )

find_library ( UDEV_LIBRARY
              NAMES
                  udev
              HINTS
                  ${ULTRAREMOTE_ROOT}/lib
             )
             
             
             
set(ULTRAREMOTE_LIBRARIES ${FDKAA_LIBRARY} ${MPG123_LIBRARY} ${TURBOJPEG_LIBRARY} ${ZOOMSDK_LIBRARY} ${XCBXTEST_LIBRARY} ${QRENCODE_LIBRARY} ${SSL_LIBRARY} ${CRYPTO_LIBRARY} ${YUV_LIBRARY} ${JPEG_LIBRARY} ${UDEV_LIBRARY})
message("Found UltraRemote libs: ${ULTRAREMOTE_LIBRARIES}")




