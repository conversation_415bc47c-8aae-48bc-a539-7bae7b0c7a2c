<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>BodyInfoWidget</class>
 <widget class="QWidget" name="BodyInfoWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>845</width>
    <height>226</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="1" column="6">
    <widget class="QLineEdit" name="lineEditPsa">
     <property name="text">
      <string notr="true">0.00</string>
     </property>
     <property name="placeholderText">
      <string notr="true"/>
     </property>
    </widget>
   </item>
   <item row="0" column="2" colspan="2">
    <widget class="PatientInfoInputWidget" name="wPatientHeight" native="true"/>
   </item>
   <item row="0" column="8">
    <spacer name="horizontalSpacer_2">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="0" column="4">
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="0" column="9">
    <widget class="QLabel" name="label_3">
     <property name="text">
      <string>BSA</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="1" column="2">
    <widget class="QLineEdit" name="lineEditHr">
     <property name="text">
      <string>0</string>
     </property>
     <property name="placeholderText">
      <string notr="true"/>
     </property>
    </widget>
   </item>
   <item row="1" column="7">
    <widget class="QLabel" name="labelng">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>66</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>30</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="text">
      <string>ng/ml</string>
     </property>
    </widget>
   </item>
   <item row="0" column="0" colspan="2">
    <widget class="QLabel" name="label">
     <property name="minimumSize">
      <size>
       <width>147</width>
       <height>0</height>
      </size>
     </property>
     <property name="text">
      <string>Height</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="0" column="10">
    <widget class="QLineEdit" name="lineEditBsa">
     <property name="text">
      <string notr="true">0.00</string>
     </property>
     <property name="placeholderText">
      <string notr="true"/>
     </property>
    </widget>
   </item>
   <item row="2" column="1" colspan="3">
    <widget class="QCheckBox" name="checkBoxIMT">
     <property name="text">
      <string>IMT Information</string>
     </property>
    </widget>
   </item>
   <item row="0" column="6" colspan="2">
    <widget class="PatientInfoInputWidget" name="wPatientWeight" native="true"/>
   </item>
   <item row="1" column="3">
    <widget class="QLabel" name="labelbpm">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>46</width>
       <height>0</height>
      </size>
     </property>
     <property name="text">
      <string>bpm</string>
     </property>
    </widget>
   </item>
   <item row="0" column="5">
    <widget class="QLabel" name="label_2">
     <property name="text">
      <string>Weight</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="0" column="11">
    <widget class="QLabel" name="label_6">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>46</width>
       <height>0</height>
      </size>
     </property>
     <property name="text">
      <string>m²</string>
     </property>
    </widget>
   </item>
   <item row="3" column="1" colspan="10">
    <widget class="SonoIMTInfoWidget" name="IMTInfoWidget" native="true"/>
   </item>
   <item row="1" column="5">
    <widget class="QLabel" name="labelPSA">
     <property name="text">
      <string>Serum PSA</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="1" column="0" colspan="2">
    <widget class="QLabel" name="labelHR">
     <property name="text">
      <string>HR</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>PatientInfoInputWidget</class>
   <extends>QWidget</extends>
   <header>patientinfoinputwidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>SonoIMTInfoWidget</class>
   <extends>QWidget</extends>
   <header>sonoimtinfowidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
