#ifdef WIN32

#include <windows.h>
#include <malloc.h>
#include <stdio.h>
#include <tchar.h>
#include <windef.h>
#include <mmdeviceapi.h>
#include <endpointvolume.h>
#include <audioclient.h>
#include <QFile>

#include "systemusagewin.h"
#include "logger.h"
#include "resource.h"

#pragma comment(lib, "winmm.lib")

LOG4QT_DECLARE_STATIC_LOGGER(log, SystemUsageWin)

typedef BOOL(WINAPI* _InitializeOls)();
typedef VOID(WINAPI* _DeinitializeOls)();
typedef INT(WINAPI* _Rdmsr)(unsigned int index, unsigned int& eax, unsigned int& edx);
typedef BOOL(WINAPI* LPFN_GLPI)(PSYSTEM_LOGICAL_PROCESSOR_INFORMATION, PDWORD);

const char* cWingRingLibName = "WinRing0x64.dll";

#define DIV (1024 * 1024)

SystemUsageWin::SystemUsageWin()
    : m_nCpuCoreCount(0)
    , m_nCpuThreadCount(0)
{
    m_nProcessorsNum = getProcessorNumber();
    m_hCurProcess = GetCurrentProcess();
    m_hDevice = INVALID_HANDLE_VALUE;
    initCpuInformation();
    initWinRing0();
}

SystemUsageWin::~SystemUsageWin()
{
    FreeLibrary(m_hWinRing0);
    _DeinitializeOls DeinitializeOls;
    DeinitializeOls = (_DeinitializeOls)GetProcAddress(m_hWinRing0, "DeinitializeOls");
    DeinitializeOls();
}

unsigned long SystemUsageWin::getCpuCoresCount()
{
    return m_nCpuCoreCount;
}

unsigned long SystemUsageWin::getCpuThreadCount()
{
    return m_nCpuThreadCount;
}

void SystemUsageWin::initWinRing0()
{
    m_hWinRing0 = LoadLibraryA(cWingRingLibName);
    if (NULL == m_hWinRing0)
    {
        log()->error("LoadLibrary WinRing0x64.dll failure");
        return;
    }

    _InitializeOls InitializeOls;
    InitializeOls = (_InitializeOls)GetProcAddress(m_hWinRing0, "InitializeOls");
    if (false == InitializeOls())
    {
        log()->error("InitializeOls failure");
    }
}

void SystemUsageWin::initCpuInformation()
{
    LPFN_GLPI glpi;
    BOOL done = FALSE;
    PSYSTEM_LOGICAL_PROCESSOR_INFORMATION buffer = NULL;
    PSYSTEM_LOGICAL_PROCESSOR_INFORMATION ptr = NULL;
    DWORD returnLength = 0;
    DWORD byteOffset = 0;
    m_nCpuCoreCount = 0;
    m_nCpuThreadCount = 0;

    glpi = (LPFN_GLPI)GetProcAddress(GetModuleHandle(TEXT("kernel32")), "GetLogicalProcessorInformation");
    if (NULL == glpi)
    {
        log()->error("Error: GetLogicalProcessorInformation is not supported");
        return;
    }

    while (!done)
    {
        DWORD rc = glpi(buffer, &returnLength);

        if (FALSE == rc)
        {
            if (GetLastError() == ERROR_INSUFFICIENT_BUFFER)
            {
                if (buffer)
                    free(buffer);

                buffer = (PSYSTEM_LOGICAL_PROCESSOR_INFORMATION)malloc(returnLength);

                if (NULL == buffer)
                {
                    log()->error("Error: Allocation failure");
                    return;
                }
            }
            else
            {
                log()->error("Error:%d", GetLastError());
                return;
            }
        }
        else
        {
            done = TRUE;
        }
    }

    ptr = buffer;

    while (byteOffset + sizeof(SYSTEM_LOGICAL_PROCESSOR_INFORMATION) <= returnLength)
    {
        switch (ptr->Relationship)
        {
        case RelationProcessorCore:
            m_nCpuCoreCount++;
            m_nCpuThreadCount += countSetBits(ptr->ProcessorMask);
            break;

        default:
            break;
        }
        byteOffset += sizeof(SYSTEM_LOGICAL_PROCESSOR_INFORMATION);
        ptr++;
    }
}

int SystemUsageWin::getCpuTemperature()
{
    int cpuTemp = 0;
    if (m_hWinRing0 != NULL)
    {
        unsigned int eax, edx;
        eax = edx = 0;
        _Rdmsr Rdmsr;
        Rdmsr = (_Rdmsr)GetProcAddress(m_hWinRing0, "Rdmsr");
        Rdmsr(0x1A2, eax, edx);
        unsigned int TjMax = eax;
        TjMax &= 0xFF0000;
        TjMax = TjMax >> 16;
        Rdmsr(0x19C, eax, edx);
        unsigned int IAcore = eax;
        IAcore &= 0xFF0000;
        IAcore = IAcore >> 16;
        cpuTemp = (int)(TjMax - IAcore);
    }

    return cpuTemp;
}

int SystemUsageWin::getCpuUsage()
{
    static FILETIME preidleTime;
    static FILETIME prekernelTime;
    static FILETIME preuserTime;

    FILETIME idleTime;
    FILETIME kernelTime;
    FILETIME userTime;

    GetSystemTimes(&idleTime, &kernelTime, &userTime);

    quint64 x, y;
    int idle, kernel, user;

    x = (preidleTime.dwHighDateTime << 31) | preidleTime.dwLowDateTime;
    y = (idleTime.dwHighDateTime << 31) | idleTime.dwLowDateTime;
    idle = y - x;

    x = (prekernelTime.dwHighDateTime << 31) | prekernelTime.dwLowDateTime;
    y = (kernelTime.dwHighDateTime << 31) | kernelTime.dwLowDateTime;
    kernel = y - x;

    x = (preuserTime.dwHighDateTime << 31) | preuserTime.dwLowDateTime;
    y = (userTime.dwHighDateTime << 31) | userTime.dwLowDateTime;
    user = y - x;

    int cpuPercent = (kernel + user - idle) * 100 / (kernel + user);

    preidleTime = idleTime;
    prekernelTime = kernelTime;
    preuserTime = userTime;

    return cpuPercent;
}

void SystemUsageWin::getMemoryUsage(unsigned int& nMemTotal, unsigned int& nMemFree)
{
    MEMORYSTATUSEX statex;
    statex.dwLength = sizeof(statex);
    GlobalMemoryStatusEx(&statex);

    nMemTotal = statex.ullTotalPhys / DIV; // MB
    nMemFree = statex.ullAvailPhys / DIV;  // MB
}

int SystemUsageWin::getProcessorNumber()
{
    SYSTEM_INFO info;
    GetSystemInfo(&info);
    return (int)info.dwNumberOfProcessors;
}

DWORD SystemUsageWin::countSetBits(ULONG_PTR bitMask)
{
    DWORD LSHIFT = sizeof(ULONG_PTR) * 8 - 1;
    DWORD bitSetCount = 0;
    ULONG_PTR bitTest = (ULONG_PTR)1 << LSHIFT;
    DWORD i;

    for (i = 0; i <= LSHIFT; ++i)
    {
        bitSetCount += ((bitMask & bitTest) ? 1 : 0);
        bitTest /= 2;
    }

    return bitSetCount;
}

int SystemUsageWin::getCurVolumeVal()
{
    IAudioEndpointVolume* pAudioEndpointVolume = getAudioEndpointVolume();
    if (NULL == pAudioEndpointVolume)
        return false;

    float currentVolume = 0.0f;
    HRESULT hr = pAudioEndpointVolume->GetMasterVolumeLevelScalar(&currentVolume);
    if (FAILED(hr))
        return -1;

    return int(currentVolume * 100); //转换百分比
}

bool SystemUsageWin::setCurVolumeVal(int nVolumeVal)
{
    IAudioEndpointVolume* pAudioEndpointVolume = getAudioEndpointVolume();
    if (NULL == pAudioEndpointVolume)
        return false;

    float fVolumeVal = nVolumeVal / 100.0f; //转换百分比
    HRESULT hr = pAudioEndpointVolume->SetMasterVolumeLevelScalar(fVolumeVal, NULL);
    if (FAILED(hr))
        return false;

    return true;
}

bool SystemUsageWin::playVideo(const QString& fileName)
{
    return PlaySound((LPCSTR)fileName.toLocal8Bit(), NULL, SND_ASYNC);
}

IAudioEndpointVolume* SystemUsageWin::getAudioEndpointVolume()
{
    IMMDevice* device = getDefaultAudioDevice();
    if (NULL == device)
    {
        log()->error("Unable to get IMMDevice");
        return NULL;
    }

    IAudioEndpointVolume* endpointVolume = NULL;
    HRESULT hr = device->Activate(__uuidof(IAudioEndpointVolume), CLSCTX_INPROC_SERVER, NULL, (LPVOID*)&endpointVolume);
    if (FAILED(hr))
    {
        log()->error("Unable to get IAudioEndpointVolume");
        return NULL;
    }
    return endpointVolume;
}

IMMDevice* SystemUsageWin::getDefaultAudioDevice()
{
    IMMDeviceEnumerator* deviceEnumerator = NULL;
    HRESULT hr = CoCreateInstance(__uuidof(MMDeviceEnumerator), NULL, CLSCTX_INPROC_SERVER,
                                  __uuidof(IMMDeviceEnumerator), (LPVOID*)&deviceEnumerator);
    if (FAILED(hr))
    {
        return NULL;
    }
    IMMDevice* defaultDevice = NULL;
    CoInitializeEx(NULL, COINIT_APARTMENTTHREADED);
    hr = deviceEnumerator->GetDefaultAudioEndpoint(eRender, eConsole, &defaultDevice);
    deviceEnumerator->Release();
    if (FAILED(hr))
    {
        return NULL;
    }
    return defaultDevice;
}

#endif
