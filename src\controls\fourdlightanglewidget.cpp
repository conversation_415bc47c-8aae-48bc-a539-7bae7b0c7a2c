#include "fourdlightanglewidget.h"
#include "ui_fourdlightanglewidget.h"
#include "fourdparascontainer.h"

FourDLightAngleWidget::FourDLightAngleWidget(QWidget* parent)
    : FourDParaSetBaseWidget(parent)
    , ui(new Ui::FourDLightAngleWidget)
{
    ui->setupUi(this);
    m_ParaType = FourDParasInfo::FourDLightAngle;
}

FourDLightAngleWidget::~FourDLightAngleWidget()
{
    delete ui;
}

void FourDLightAngleWidget::initalize()
{
}

void FourDLightAngleWidget::setFourDLightAnglePara(const FourDLightAnglePara& para)
{
    m_FourDLightAnglePara = para;
    ui->lineEditHorDegree->setText(QString::number(para.m_HorDegree));
    ui->lineEditVerDegree->setText(QString::number(para.m_VerDegree));
}

FourDParasInfo::FourDLightAnglePara FourDLightAngleWidget::fourDLightAnglePara()
{
    FourDParasInfo::FourDLightAnglePara para = m_FourDLightAnglePara;
    para.m_HorDegree = ui->lineEditHorDegree->text().toFloat();
    para.m_VerDegree = ui->lineEditVerDegree->text().toFloat();
    return para;
}

void FourDLightAngleWidget::retranslateUi()
{
    ui->retranslateUi(this);
}
