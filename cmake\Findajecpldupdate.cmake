
thirdparty_prefix_path(ajecpldupdate)

find_path ( AJECPLDUPDATE_INCLUDE_DIR
            NAMES
                ajecpldupdate.h
            HINTS
                ${AJECPLDUPDATE_ROOT}/include
            )
set(AJECPLDUPDATE_INCLUDE_DIRS ${AJEC<PERSON>D<PERSON>DATE_INCLUDE_DIR})
message("Found ajecpldupdate headers: ${AJECPLDUPDATE_INCLUDE_DIRS}")

find_library ( AJECPLDUPDATE_LIBRARY
            NAMES 
                ajecpldupdate ajecpldupdate_${ANDROID_ABI}
            HINTS
                ${AJECPLDUPDATE_LIBPATH}
             )
set(AJECPLDUPDATE_LIBRARIES ${AJECPLDUPDATE_LIBRARY})
message("Found ajecpldupdate libs: ${AJECPLDUPDATE_LIBRARIES}")

