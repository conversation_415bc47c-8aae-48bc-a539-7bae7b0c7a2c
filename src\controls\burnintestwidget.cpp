/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#include "burnintestwidget.h"
#include "ui_burnintestwidget.h"
#include "progressbarview.h"
#include "confirmdialog.h"
#include "imageprocessedcopy.h"
#include "diskselectionwidget.h"
#include "threadmodel.h"
#include "modeldirectorygetter.h"
#include "patientworkflow.h"
#include "imageskimmanager.h"
#include "util.h"
#include "generalworkflowfunction.h"
#include "messageboxframe.h"
#include "generalinfo.h"
#include "stringutil.h"
#include "appsetting.h"
#include "abstractstate.h"
#include <QDir>
#include "setting.h"
#include "baseprobeselectionwidget.h"
#include "istatemanager.h"
BurnInTestDialog::BurnInTestDialog(IStateManager* stateManager, IColorMapManager* colorMapManager, QWidget* parent)
    : BaseDialogFrame(parent)
    , m_child(NULL)
{
    m_child = new BurnInTestWidget(stateManager, colorMapManager, this);
    connect(m_child, SIGNAL(hide()), this, SLOT(onHide()));
    this->setContent(m_child);
    this->setFrameTitle("Burn-In Test");
}

void BurnInTestDialog::setProbeFrame(BaseProbeSelectionChild* value)
{
    m_child->setProbeFrame(value);
}

void BurnInTestDialog::setPatientWorkFlow(PatientWorkflow* value)
{
    m_child->setPatientWorkFlow(value);
}

void BurnInTestDialog::setImageSkimManager(ImageSkimManager* value)
{
    m_child->setImageSkimManager(value);
}

void BurnInTestDialog::onHide()
{
    hide();
}

BurnInTestWidget::BurnInTestWidget(IStateManager* stateManager, IColorMapManager* colorMapManager, QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::BurnInTestWidget)
    , m_progressBar(NULL)
    , m_copy(NULL)
    , m_confirmDialog(NULL)
    , m_patientWorkflow(NULL)
    , m_imageSkimManager(NULL)
    , m_currentPicIndex(0)
    , m_StateManager(stateManager)
    , m_ColorMapManager(colorMapManager)
{
    ui->setupUi(this);

    initThreadModel();

    ui->pushButtonStopTest->setEnabled(false);

    m_burnInTest.setStateManager(stateManager);

    connect(&m_burnInTest, SIGNAL(sendCurrentFiles(QString)), this, SLOT(onSendCurrentFiles(QString)));
    connect(&m_burnInTest, SIGNAL(emitResetFlags()), this, SLOT(resetFlags()));
}

BurnInTestWidget::~BurnInTestWidget()
{
    delete ui;
}

void BurnInTestWidget::setProbeFrame(BaseProbeSelectionChild* value)
{
    m_burnInTest.setProbeFrame(value);
}

void BurnInTestWidget::setPatientWorkFlow(PatientWorkflow* value)
{
    m_patientWorkflow = value;
}

void BurnInTestWidget::setImageSkimManager(ImageSkimManager* value)
{
    m_imageSkimManager = value;
}

void BurnInTestWidget::on_pushButtonStartTest_clicked()
{
    m_burnInTest.updateIniFiles();

    if (m_imageSkimManager->allImageInfoList().count() > 0)
    {
        int ret;

        ret =
            MessageBoxFrame::question(0, tr("Delete Local Files"),
                                      tr("To start test you must remove current local files, do you want to continue?"),
                                      QMessageBox::Yes | QMessageBox::No);

        if (ret == QMessageBox::Yes)
        {
            connect(&m_threadModel, SIGNAL(deleteThreadFinished()), this, SLOT(onStart()));

            onDeleteLocalFiles();
        }
        else if (ret == QMessageBox::No)
        {
            return;
        }
    }
    else
    {
        onStart();
    }
}

void BurnInTestWidget::on_pushButtonStopTest_clicked()
{
    m_burnInTest.stopTest();

    emit hide();
}

void BurnInTestWidget::onSendCurrentFiles(const QString& inf)
{
    if (m_imageSkimManager->allImageInfoList().count() == 0)
    {
        return;
    }

    int ret = MessageBoxFrame::question(0, tr("Send to UDisk"), inf, QMessageBox::Yes | QMessageBox::No);

    if (ret == QMessageBox::Yes)
    {
        QString sendBaseDir = DiskSelectionDialog::getFileUDiskPath("");

        QString sendDir = PatientPath::instance().exportPatientPath(m_patientWorkflow->patient(), sendBaseDir);

        sendDir = sendDir + Resource::pathSeparator + generateDirWithMachineIDAndSerialNumber();
        m_currentDir = sendDir;

        if (!sendBaseDir.isEmpty())
        {
            Util::Mkdir(sendDir);

            QStringList filePaths;
            generateSendFilePaths(filePaths);

            bool isGoingToCopy = m_threadModel.copy(filePaths, sendDir);

            if (isGoingToCopy)
            {
                QEventLoop loop;
                connect(&m_threadModel, SIGNAL(threadFinished(bool)), &loop, SLOT(quit()));
                loop.exec(QEventLoop::ExcludeUserInputEvents);
            }
        }
    }
}

void BurnInTestWidget::onThreadFinished()
{
    renameScreenshotsFiles();

    deleteLocalFiles();
}

void BurnInTestWidget::deleteLocalFiles()
{
    int ret = MessageBoxFrame::question(0, tr("Delete Local Files"), tr("Do you want to remove local files?"),
                                        QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes)
    {
        onDeleteLocalFiles();
    }
}

QString BurnInTestWidget::getCurrentModeName() const
{
    QString name = m_StateManager->currentState()->name();

    return name;
}

void BurnInTestWidget::renameScreenshotsFiles()
{
    QFileInfoList list = QDir(m_currentDir).entryInfoList(Resource::screenImgFilters);

    QFileInfoList filterList;

    foreach (QFileInfo info, list)
    {
        if (!info.fileName().contains("Mode"))
        {
            filterList << info;
        }
    }

    foreach (QFileInfo info, filterList)
    {
        renameWithModeName(info);
    }
}

void BurnInTestWidget::renameWithModeName(const QFileInfo& info)
{
    QString baseFileName = info.fileName();
    QString newBaseName = generateModeNameWithPicName(baseFileName);
    QString newName = info.absolutePath() + Resource::pathSeparator + newBaseName;

    QFile::remove(newName);
    QFile::rename(info.absoluteFilePath(), newName);
}

QString BurnInTestWidget::generateModeNameWithPicName(const QString& name)
{
    QString currentModeName = m_burnInTest.modeNames().at(m_currentPicIndex++);

    QString newName = currentModeName + "_" + name;

    return newName;
}

void BurnInTestWidget::initThreadModel()
{
    m_progressBar = new ProgressBarView(m_StateManager, this);
    m_confirmDialog = new ConfirmDialogFrame(this);

    m_threadModel.setProgressBar(m_progressBar);
    m_threadModel.setConfirmDialog(m_confirmDialog);
    m_copy = new ImageProcessedCopy(m_ColorMapManager, &m_threadModel);
    m_threadModel.setICopy(m_copy);

    connect(&m_threadModel, SIGNAL(threadFinished(bool)), this, SLOT(onThreadFinished()));
}

void BurnInTestWidget::onDeleteLocalFiles()
{
    QStringList deleteFilePathList = m_imageSkimManager->allImagePaths();

    m_imageSkimManager->clear();

    GeneralWorkflowFunction::deleteFileList(GeneralWorkflowFunction::imageInstanceFiles(deleteFilePathList),
                                            &m_threadModel, m_progressBar, false);
}

void BurnInTestWidget::generateSendFilePaths(QStringList& stringList)
{
    stringList = m_imageSkimManager->allImagePaths();
}

void BurnInTestWidget::setPushButtonWithRunningState(bool value)
{
    ui->pushButtonStartTest->setEnabled(!value);
    ui->pushButtonStopTest->setEnabled(value);
}

QString BurnInTestWidget::generateDirWithMachineIDAndSerialNumber() const
{
    QString sn = GeneralInfo::instance().serialNumber();
    sn = StringUtil::replaceInvalidFileNamesCharacter(sn);

    return AppSetting::model() + "_" + sn;
}

void BurnInTestWidget::resetFlags()
{
    setPushButtonWithRunningState(false);
    m_currentPicIndex = 0;
    Setting::instance().defaults().setIsShowImageHead(false);
}

void BurnInTestWidget::onStart()
{
    bool isStarted = m_burnInTest.startTest();
    emit hide();

    if (isStarted)
    {
        Setting::instance().defaults().setIsShowImageHead(true);
        setPushButtonWithRunningState(true);
    }
}
