#include "filesystemmodel.h"
#include "modeluiconfig.h"

FileSystemModel::FileSystemModel(QObject* parent)
    : QFileSystemModel(parent)
    , m_FolderIcon(ModelUiConfig::instance().value(ModelUiConfig::FolderSmallIcon).toString())
    , m_fileIcon(":/images/report.png")
{
}

QVariant FileSystemModel::data(const QModelIndex& index, int role) const
{
    if (!index.isValid() || index.model() != this)
        return QVariant();
    QFileInfo info = this->fileInfo(index);
    switch (role)
    {
    case Qt::DecorationRole:
        if (index.column() == 0 && info.isDir())
        {
            return m_FolderIcon;
        }
        else if (index.column() == 0)
        {
            return m_fileIcon;
        }
        break;
    default:
        return QFileSystemModel::data(index, role);
        break;
    }
    return QVariant();
}
