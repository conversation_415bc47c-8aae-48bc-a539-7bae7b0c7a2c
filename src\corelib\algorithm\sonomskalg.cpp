#include "sonomskalg.h"

SonoMskAlg::SonoMskAlg()
    : VirtualAlg()
{
    m_CreateContext = NULL;
    m_RemoveContext = NULL;
    m_MskSeg = NULL;
    m_Lib.setFileName("mskAlg");
}
//算法必须按照createViewontext -> mskSeg -> removeViewontext顺序执行
//为了避免这些接口多线程重入导致算法崩溃，在函数中添加锁
//保证算法调用以及m_Initialized这个关键变量是线程安全的
void SonoMskAlg::createViewontext(const char* seg_modelpath, mskPlaneType inType, bool opencl)
{
    m_Mutex.lock();
    if (m_CreateContext == NULL)
    {
        m_CreateContext = (CreateViewontext)m_Lib.resolve("createViewontext");
        if (m_CreateContext == NULL)
        {
            m_Initialized = false;
            m_IsError = true;
            m_Mutex.unlock();
            return;
        }
    }
    m_CreateContext(seg_modelpath, inType, opencl);
    m_Initialized = true;
    m_Mutex.unlock();
}

void SonoMskAlg::removeViewontext()
{
    m_Mutex.lock();
    if (m_RemoveContext == NULL)
    {
        m_RemoveContext = (RemoveViewontext)m_Lib.resolve("removeViewontext");
    }
    if (m_RemoveContext == NULL || !m_Initialized)
    {
        m_IsError = true;
        m_Mutex.unlock();
        return;
    }
    m_Initialized = false;
    m_RemoveContext();
    m_Mutex.unlock();
}

void SonoMskAlg::mskSeg(std::tuple<std::vector<std::string>, std::vector<std::vector<cv::Point>>>& contours,
                        unsigned char* imgin, int width, int height)
{

    m_Mutex.lock();
    if (m_MskSeg == NULL)
    {
        m_MskSeg = (MskSeg)m_Lib.resolve("MskSeg");
    }
    if (m_MskSeg == NULL || !m_Initialized)
    {
        m_IsError = true;
        m_Mutex.unlock();
        return;
    }
    m_MskSeg(contours, imgin, width, height);
    m_Mutex.unlock();
}
