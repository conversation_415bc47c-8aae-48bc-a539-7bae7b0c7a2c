#
# The module defines the following variables:
# FREEM_FOUND - True if FREEM found.
# FREEM_INCLUDE_DIRS - where to find FREEM.h, etc.
# FREEM_LIBRARIES - List of libraries when using FREEM.
#

thirdparty_prefix_path(freem)

find_path ( FREEM_INCLUDE_DIR
            NAMES
                freem.h
            HINTS
                ${FREEM_ROOT}/include
            )
set(FREEM_INCLUDE_DIRS ${FREEM_INCLUDE_DIR})
message("Found FREEM headers: ${FREEM_INCLUDE_DIRS}")


find_library ( FREEM_LIBRARY
            NAMES 
                freem
            HINTS
                ${FREEM_ROOT_DIR}/lib
                ${FREEM_ROOT}/lib
             )
set(FREEM_LIBRARIES ${FREEM_LIBRARY})
message("Found FREEM libs: ${FREEM_LIBRARIES}")