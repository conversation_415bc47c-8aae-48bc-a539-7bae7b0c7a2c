#ifndef TESTCONTAINER_H
#define TESTCONTAINER_H
#include "autotest_global.h"

#include <QList>
#include <QHash>

class ITestCommand;

class AUTOTESTSHARED_EXPORT TestContainer
{
public:
    static TestContainer& instance();
    ~TestContainer();
    void addTestCommand(const QString& name, ITestCommand* testCommand);
    ITestCommand* testCommand(const QString& name) const;
    QHash<QString, ITestCommand*>& testCommands();

private:
    TestContainer();
    QHash<QString, ITestCommand*> m_testCommands;
};

#endif // TESTCONTAINER_H
