#ifndef GENERALWORKFLOWFUNCTION_H
#define GENERALWORKFLOWFUNCTION_H
#include "archive_global.h"
#include "QObject"
#include <QStringList>
#include <QFileInfoList>
#include "threadmodel.h"
class DicomTaskManager;
class DeleteFilesController;

/**
 * @brief 为archive、patientNew和easyView提供多次使用的函数
 */
class PatientWorkflow;
class Patient;
class PatientEditModel;
class PatientWorkflowModel;
class ProgressBarView;
class IStateManager;
class IDiskDevice;
class IColorMapManager;

class ARCHIVESHARED_EXPORT GeneralWorkflowFunction : public QObject
{
    Q_OBJECT
public:
    GeneralWorkflowFunction(QObject* parent = 0);
    ~GeneralWorkflowFunction();
    void setPatientWorkflow(PatientWorkflow* patientWorkflow);
    void setPatientWorkflowModel(PatientWorkflowModel* patientWorkflowModel);
    /**
     * @brief isOrNotContinueStudy 根据检查结束的时间是否大于3天
     *        来确定接着检查
     * @param studyOid
     *
     * @return bool
     */
    bool isOrNotContinueStudy(const QString& studyOid);
    /**
     * @brief continueStudy 将已经结束的检查重新激活
     * @param oid
     */
    void continueStudy(const QString& oid);
    /**
     * @brief editStudy 将已经结束且不满足重新激活条件的检查进行编辑
     * @param oid
     */
    void editStudy(const QString& oid);
    /**
     * @brief showPatientInfo 显示病人的基本信息，只能看不能编辑
     *
     * @param studyOid
     */
    void showPatientInfo(const QString& studyOid);
    /**
     * @brief setExamEnd 结束检查
     *
     * @param path
     */
    void setExamEnd(const QString& path);
    /**
     * @brief getPatient 根据studyOid获取含有唯一study的patient
     *
     * @param studyOid
     *
     * @return
     */
    static Patient* getPatient(const QString& studyOid);
    /**
     * @brief ifContinueOperate 给提示信息
     *
     * @param widget
     *
     * @return
     */
    static bool ifContinueOperate(QWidget* widget);
    /**
     * @brief deleteFileList 删除文件
     *
     * @param pathList
     * @param model
     * @param barWiew
     * @param isDeleteDir
     */
    static void deleteFileList(const QStringList& pathList, ThreadModel* model, ProgressBarView* barWiew,
                               bool isDeleteDir = true);
    /**
     * @brief changeStringToFileInfo 将路径转换成Qfileinfo
     *
     * @param pathList
     *
     * @return
     */
    static QFileInfoList changeStringToFileInfo(const QStringList& pathList);
    /**
     * @brief changeDbFilepath 改变数据库路径
     *
     * @param path
     */
    static void changeDbFilepath(const QString& path = QString());
    /**
     * @brief exportFiles 导出选中的图片文件，或者是study的dirs
     *
     * @param parent
     * @param fileList 图片的全路径的list或者study的全路径目录的list
     * @param studyOid
     */
    static void exportFiles(QWidget* parent, IStateManager* stateManager, IColorMapManager* colorMapManager,
                            const QStringList& fileList, const QString& studyOid);

    /**
     * @brief imageInstanceFiles 获取某个图片文件相关的所以文件包含.cin/.img/.bmp/.jpg
     *
     * @param imageFileNames 图片文件的全路径的list
     *
     * @return
     */
    static QStringList imageInstanceFiles(const QStringList& imageFileNames);

    /**
     * @brief exportFiles
     * @param parent
     * @param defaultDir 的到处u盘的默认文件名
     * @param fileList 图片的全路径的list
     * @param filePathes study的全路径目录的list
     * @param manager
     */
    static void exportFiles(QWidget* parent, const QString& defaultDir, const QStringList& fileList,
                            const QStringList& filePathes, DicomTaskManager* manager, IStateManager* value,
                            IColorMapManager* colorMapManager, DeleteFilesController* deleteController = NULL,
                            bool isCurrentMultiChoice = false, bool deleteAfterSendExam = true, bool sendSR = false);

    /**
     * @brief 是否支持u盘或dicom,network,bluetooth
     * @param pflag dicomprint
     * @param sflag dicomstorage
     * @param uflag udisk
     * @param nflag network storage
     * @param bflag bluetooth storage
     * @param srflag dicom sr，默认为NULL,只有Archive中的send才需要使用
     */
    static void hasUsableDevice(QWidget* parent, bool* pflag, bool* sflag, bool* uflag, bool* nflag, bool* bflag,
                                bool* srflag = NULL);
    static void setDiskDevice(IDiskDevice* diskDevice);

private:
    PatientWorkflow* m_PatientWorkflow;
    PatientWorkflowModel* m_PatientWorkflowModel;
    static IDiskDevice* m_DiskDevice;
};

#endif // GENERALWORKFLOWFUNCTION_H
