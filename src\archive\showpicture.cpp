#include "showpicture.h"
#include "ui_showpicture.h"
#include <QPixmap>
#include <QFileInfo>
//#include <QDebug>
#include <QDir>
#include <QApplication>
#include <QDesktopWidget>
#include "resource.h"
#include <QPainter>
#include "formula.h"
#include "setting.h"
#include "util.h"
#include "modeluiconfig.h"
#include "qscreen.h"

ShowPicture::ShowPicture(QWidget* parent)
    : QDialog(parent)
    , ui(new Ui::ShowPicture)
    , m_PicFilter(Resource::screenImgFilters)
    , m_CurPicCount(0)
{
    ui->setupUi(this);
    connect(ui->picLabel, SIGNAL(hinAction(float)), this, SLOT(onLabelHInAction(float)));

    setFixedSize(QGuiApplication::primaryScreen()->geometry().size());

    ui->picLabel->setGeometry(0, 0, width(), height());
    int edge = 10;
    ui->prePicButton->move(edge, (height() - ui->prePicButton->height()) / 2);
    ui->nextPicButton->move((width() - ui->nextPicButton->width() - edge),
                            (height() - ui->nextPicButton->height()) / 2);
}

ShowPicture::~ShowPicture()
{
    delete ui;
}

void ShowPicture::on_prePicButton_clicked()
{
    m_CurPicCount--;
    if (m_CurPicCount < 0)
    {
        m_CurPicCount = m_PicFileNames.count() - 1;
    }
    loadPicture(m_PicFileNames.at(m_CurPicCount));
}

void ShowPicture::on_nextPicButton_clicked()
{
    m_CurPicCount++;
    if (m_CurPicCount > (m_PicFileNames.count() - 1))
    {
        m_CurPicCount = 0;
    }
    loadPicture(m_PicFileNames.at(m_CurPicCount));
}

void ShowPicture::loadPicture(const QString& path)
{
    //    qDebug() << path;
    QPixmap picture(path);
    if (picture.isNull())
    {
        ui->picLabel->clear();
        ui->picLabel->setText(tr("Invalid Picture"));
    }
    else
    {
        QPainter painter(&picture);
        painter.setPen(QColor(ModelUiConfig::instance().value(ModelUiConfig::TitleRenderColor).toUInt()));
        if (checkIsCineFile(path))
        {
            QBrush brush(QColor(96, 161, 217));
            QPixmap icon(ModelUiConfig::instance().value(ModelUiConfig::MovieIcon).toString());
            QRect rect(picture.rect().width() - icon.width() - 5, picture.rect().height() - icon.height() - 5,
                       icon.width(), icon.height());

            painter.fillRect(rect, brush);
            painter.drawPixmap(rect, icon);
        }
        ui->picLabel->setPixmap(picture);
    }
    calCurPicCount(path);
}

void ShowPicture::calCurPicCount(const QString& path)
{
    //    if (m_DirPath != QFileInfo(path).absolutePath())
    //    {
    //        m_DirPath = QFileInfo(path).absolutePath();

    //        QDir dir(m_DirPath);
    //        m_PicFileNames = dir.entryList(m_PicFilter);

    //        QMutableStringListIterator i(m_PicFileNames);
    //        while (i.hasNext())
    //        {
    //            i.next();
    //            i.setValue(dir.filePath(i.value()));
    //        }

    //        ui->totalCount->setNum(m_PicFileNames.count());
    //    }
    if (!getDir(path).isEmpty() && m_DirPath != getDir(path))
    {
        m_DirPath = getDir(path);
        QFileInfoList infoList = Util::getFileList(m_DirPath, m_PicFilter);
        m_PicFileNames.clear();
        foreach (QFileInfo info, infoList)
        {
            m_PicFileNames.append(info.filePath());
        }
        ui->totalCount->setNum(m_PicFileNames.count());
    }

    if (m_PicFileNames.contains(path))
    {
        m_CurPicCount = m_PicFileNames.indexOf(path);
        ui->curCount->setNum(m_CurPicCount + 1);
    }
}

int ShowPicture::curPicCount() const
{
    return m_CurPicCount;
}

QString ShowPicture::curPicPath() const
{
    if (m_CurPicCount >= 0 && m_CurPicCount <= (m_PicFileNames.count() - 1))
    {
        return m_PicFileNames.at(m_CurPicCount);
    }
    else
    {
        return QString();
    }
}

bool ShowPicture::checkIsCineFile(const QString& path)
{
    if (QFile(QFileInfo(path).absolutePath() + '/' + QFileInfo(path).baseName() + Resource::cineExt).exists())
    {
        return true;
    }
    else if (QFile(QFileInfo(path).absolutePath() + '/' + QFileInfo(path).baseName() + Resource::fourdCineExt).exists())
    {
        return true;
    }
    else
    {
        return false;
    }
}

QString ShowPicture::getDir(const QString& filePath)
{
    if (!QFileInfo(filePath).exists())
    {
        return QString();
    }
    QString dirPath = Resource::dataStoreDir;
    QString path = filePath;
    int count = 0;

    while (QFileInfo(path).dir().dirName() != dirPath)
    {
        count++;
        path = QFileInfo(path).absolutePath();
    }
    if (count > 2)
    {
        return QString();
    }
    return path;
}

void ShowPicture::showEvent(QShowEvent* e)
{
    move(Formula::middleLeftTop(QApplication::desktop()->size(), size()));

    QDialog::showEvent(e);
}

void ShowPicture::onLabelHInAction(float power)
{
    if (power >= 0)
    {
        on_nextPicButton_clicked();
    }
    else
    {
        on_prePicButton_clicked();
    }
}

void ShowPicture::on_closeButton_clicked()
{
    close();
}
