#include "bfhwinfomodel.h"
#include "beamformerbase.h"
#include "bfpnames.h"
#include "messageboxframe.h"
#include "parameter.h"
#include "probetemperaturewidget.h"
#include "setting.h"
#include "sonoparameters.h"
#include "stateeventnames.h"
#include "vmonitor.h"
#include "vmonitorselect.h"
#ifdef USE_MOTOR
#include "motorcontrol.h"
#endif
#include "chisonultrasoundcontext.h"
#include "patient.h"
#include "patientworkflow.h"
#include "study.h"
#include "teeprobecalculator.h"

BFHWInfoModel::BFHWInfoModel(QObject* parent)
    : QObject(parent)
    , m_ViewParent(NULL)
    , m_VMonitorDialog(NULL)
    , m_beamFormer(NULL)
    , m_testCommand(NULL)
    , m_selectDialog(NULL)
    , m_WarningMsgOpened(false)
    , m_patientWorkflow(NULL)
    , m_imageSkimManager(NULL)
    , m_MotorControl(NULL)
    , m_ProbeTemperatureDialog(NULL)
    , m_ChisonUltrasoundContext(NULL)
    , m_DoesUserDiscard(false)
    , m_HasShowWarning(false)
    , m_StateManager(NULL)
    , m_ColorMapManager(NULL)
    , m_ProbeDataSet(NULL)
    , m_ProbeTempOverLimtTimes(0)
{
}

void BFHWInfoModel::setViewParent(QWidget* value)
{
    m_ViewParent = value;
}

void BFHWInfoModel::setBeamFormer(IBeamFormer* beamFormer)
{
    m_beamFormer = beamFormer;
    connect(m_beamFormer->sonoParameters()->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(resetProbeTemperature()));
    connect(m_beamFormer->sonoParameters()->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(resetProbeTemperature()));
}

void BFHWInfoModel::setTestCommand(ITestCommand* value)
{
    m_testCommand = value;
}

void BFHWInfoModel::setPatientWorkFlow(PatientWorkflow* value)
{
    m_patientWorkflow = value;
}

void BFHWInfoModel::setImageSkimManager(ImageSkimManager* value)
{
    Q_ASSERT(value != NULL);

    m_imageSkimManager = value;
}

void BFHWInfoModel::setMotorControl(MotorControl* value)
{
    m_MotorControl = value;
}

void BFHWInfoModel::setChisonUltrasoundContext(ChisonUltrasoundContext* value)
{
    m_ChisonUltrasoundContext = value;
}

void BFHWInfoModel::showView()
{
    initHWInfoDlg();
    if (Setting::instance().defaults().isEngineeringMode())
    {
        if (m_VMonitorDialog == NULL)
        {
            m_VMonitorDialog = new VMonitorDialog(m_ViewParent);
        }

        if (m_ProbeTemperatureDialog == NULL)
        {
            m_ProbeTemperatureDialog = new ProbeTemperatureDialog(m_beamFormer, m_ProbeDataSet, m_ViewParent);
        }

        //        m_VMonitorDialog->show();
        if (m_selectDialog == NULL)
        {
            m_selectDialog = new VMonitorSelectDialog(m_beamFormer);
            m_selectDialog->setVMonitor(m_VMonitorDialog);
            m_selectDialog->setProbeTemperature(m_ProbeTemperatureDialog);
            m_selectDialog->setTestCommand(m_testCommand);
            m_selectDialog->setPatientWorkFlow(m_patientWorkflow);
            m_selectDialog->setImageSkimManager(m_imageSkimManager);
            m_selectDialog->setMotorControl(m_MotorControl);
            m_selectDialog->setProbeSelfTest(m_ChisonUltrasoundContext);
            m_selectDialog->setStateManager(m_StateManager);
            m_selectDialog->setColorMapManager(m_ColorMapManager);

            connect(m_selectDialog, SIGNAL(sendParameter()), this, SIGNAL(sendParameter()));
        }
        m_ChisonUltrasoundContext->setProbeSelfTestControl(m_beamFormer->probeSelfTest());
        m_selectDialog->show();
        m_selectDialog->adjustSize();
    }
}

void BFHWInfoModel::onHighTemperatureDetected()
{
}

void BFHWInfoModel::onVoltageInfoChanged(const QVector<VoltageData>& datas, const QVector<unsigned short>& states)
{
    if (m_VMonitorDialog != NULL && m_VMonitorDialog->isVisible())
    {
        m_VMonitorDialog->fillData(datas, states);
    }
}

void BFHWInfoModel::onHeadDataUpdated(uchar* data, int len)
{
    if (m_VMonitorDialog != NULL && m_VMonitorDialog->isVisible())
    {
        m_VMonitorDialog->fillHeadData(data, len);
    }
}

void BFHWInfoModel::onWarningMsgDestroyed()
{
    m_WarningMsgOpened = false;
}

VMonitorDialog* BFHWInfoModel::vmDialog() const
{
    return m_VMonitorDialog;
}

void BFHWInfoModel::onProbeTemperatureWarning(int temp)
{
    if (m_ProbeTemperatureDialog != NULL && m_ProbeTemperatureDialog->isVisible())
    {
        m_ProbeTemperatureDialog->setProbeTemperature(temp);
    }
}

void BFHWInfoModel::resetProbeTemperature()
{
    m_ProbeTempOverLimtTimes = 0;
}

void BFHWInfoModel::onTeeProbeTemperatureWarning(int temp, int angle)
{
    if (m_beamFormer == NULL || !m_beamFormer->curProbe().isTeeProbe)
    {
        return;
    }

    double dtmp = TeeProbeCalculator::instace().getTemperature(temp);
    QString tempstr = TeeProbeCalculator::instace().getTemperature(dtmp);

    if (m_patientWorkflow != NULL && m_patientWorkflow->patient() != NULL)
    {
        double ptemp = m_beamFormer->pDV(BFPNames::PatientTemperatureStr);
        if (dtmp < ptemp)
        {
            tempstr = "< " + QString::number(ptemp) + "℃";
        }
    }
    m_beamFormer->setPV(BFPNames::TeeProbeTemperatureStr, tempstr);
    m_beamFormer->setPV(BFPNames::TeeProbeAngleStr, TeeProbeCalculator::getTeeAngle(angle));

    checkTeeProbeTemperature(dtmp);
}

void BFHWInfoModel::checkTeeProbeTemperature(double temp)
{
    if (m_beamFormer == NULL || !m_beamFormer->curProbe().isTeeProbe)
    {
        return;
    }
    TeeProbeCalculator teePC = TeeProbeCalculator::instace();

    if (temp >= teePC.getTeeTempWarn())
    {
        m_beamFormer->setPV(BFPNames::TeeProbeTemperatureWarningStr, true);
    }
    else
    {
        m_beamFormer->setPV(BFPNames::TeeProbeTemperatureWarningStr, false);
    }

    if (temp < teePC.getTeeTempMin() || temp >= teePC.getTeeTempMax())
    {
        m_beamFormer->freeze(true);
        m_DoesUserDiscard = false;
    }
    else if (temp >= teePC.getTeeTempMin() && temp <= teePC.getTeeTempWarn())
    {
        if (m_DoesUserDiscard && temp < teePC.getTeeTempWarn() - 1)
        {
            m_DoesUserDiscard = false;
        }
        return;
    }
    else if (temp > teePC.getTeeTempWarn() && temp <= teePC.getTeeTempMax())
    {
        if (m_DoesUserDiscard)
        {
            return;
        }
        if (m_HasShowWarning)
        {
            return;
        }
        if (!m_beamFormer->isFrozen())
        {
            m_HasShowWarning = true;
            QMessageBox::StandardButton ret = MessageBoxFrame::question(
                NULL, tr("Warning"), tr("Do you wish to continue scanning up to a higher temperature limit?"),
                QMessageBox::Cancel | QMessageBox::Ok);
            if (ret == QMessageBox::Ok)
            {
                m_DoesUserDiscard = true && !m_beamFormer->isFrozen();
            }
            else
            {
                m_DoesUserDiscard = false;
                m_beamFormer->freeze(true);
            }
            m_HasShowWarning = false;
        }
    }
    else
    {
        m_DoesUserDiscard = false;
        m_beamFormer->freeze(true);
        MessageBoxFrame::warning(NULL, tr("Warning"), tr("WARNING: Temperature is over limit!"));
    }
}

void BFHWInfoModel::showVMonitor()
{
    initHWInfoDlg();
    // bug：没进工程师模式，点击CHISON必崩
    //原因：m_selectDialog指针在initHWInfoDlg函数中，只有工程师模式才会被初始化，而如果没有进工程师模式，m_selectDialog就是空指针，直接使用会崩溃
    //方案：这里使用的时候，进行判空
    if (nullptr != m_selectDialog)
    {
        m_selectDialog->showVMonitor();
    }
}

void BFHWInfoModel::setStateManager(IStateManager* stateManager)
{
    m_StateManager = stateManager;
}

void BFHWInfoModel::setColorMapManager(IColorMapManager* colorMapManager)
{
    m_ColorMapManager = colorMapManager;
}

void BFHWInfoModel::setProbeDataSet(IProbeDataSet* probeDataSet)
{
    m_ProbeDataSet = probeDataSet;
}

void BFHWInfoModel::initHWInfoDlg()
{
    if (Setting::instance().defaults().isEngineeringMode())
    {
        if (m_VMonitorDialog == NULL)
        {
            m_VMonitorDialog = new VMonitorDialog(m_ViewParent);
        }

        if (m_ProbeTemperatureDialog == NULL)
        {
            m_ProbeTemperatureDialog = new ProbeTemperatureDialog(m_beamFormer, m_ProbeDataSet, m_ViewParent);
        }

        //        m_VMonitorDialog->show();
        if (m_selectDialog == NULL)
        {
            m_selectDialog = new VMonitorSelectDialog(m_beamFormer);
            m_selectDialog->setVMonitor(m_VMonitorDialog);
            m_selectDialog->setProbeTemperature(m_ProbeTemperatureDialog);
            m_selectDialog->setTestCommand(m_testCommand);
            m_selectDialog->setPatientWorkFlow(m_patientWorkflow);
            m_selectDialog->setImageSkimManager(m_imageSkimManager);
            m_selectDialog->setMotorControl(m_MotorControl);
            m_selectDialog->setProbeSelfTest(m_ChisonUltrasoundContext);
            m_selectDialog->setStateManager(m_StateManager);
            m_selectDialog->setColorMapManager(m_ColorMapManager);

            connect(m_selectDialog, SIGNAL(sendParameter()), this, SIGNAL(sendParameter()));
        }
        m_ChisonUltrasoundContext->setProbeSelfTestControl(m_beamFormer->probeSelfTest());
    }
}
