#
# The module defines the following variables:
# TLD_FOUND - True if TLD found.
# TLD_INCLUDE_DIRS - where to find tldbase.h, etc.
# TLD_LIBRARIES - List of libraries when using TLD.
#

set(TLD_ROOT ${THIRDPARTYLIB_PATH}/TLD/)

find_path ( TLD_INCLUDE_DIR
            NAMES
                tldbase.h
            HINTS
                ${TLD_ROOT}/include
            )
set(TLD_INCLUDE_DIRS ${TLD_INCLUDE_DIR})
message("Found TLD headers: ${TLD_INCLUDE_DIRS}")


find_library ( TLD_LIBRARY
            NAMES 
                tld
            HINTS
                ${TLD_ROOT_DIR}/lib
                ${TLD_ROOT}/lib
             )
set(TLD_LIBRARIES ${TLD_LIBRARY})
message("Found TLD libs: ${TLD_LIBRARIES}")