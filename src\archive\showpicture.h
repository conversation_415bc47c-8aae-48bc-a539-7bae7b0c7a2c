#ifndef SHOWPICTURE_H
#define SHOWPICTURE_H
#include "archive_global.h"

#include <QDialog>
#include <QPushButton>
#include <QFileInfoList>

/**
 * @brief no use
 */
namespace Ui
{
class ShowPicture;
}

class ARCHIVESHARED_EXPORT ShowPicture : public QDialog
{
    Q_OBJECT

public:
    explicit ShowPicture(QWidget* parent = 0);
    ~ShowPicture();
    void loadPicture(const QString& path);
    void calCurPicCount(const QString& path);
    int curPicCount() const;
    QString curPicPath() const;
    bool checkIsCineFile(const QString& path);
    QString getDir(const QString& filePath);

protected:
    void showEvent(QShowEvent* e);
private slots:
    void on_prePicButton_clicked();
    void on_nextPicButton_clicked();
    void onLabelHInAction(float power);
    void on_closeButton_clicked();

private:
    Ui::ShowPicture* ui;
    QStringList m_PicFilter;
    //    QFileInfoList m_PicInfoList;
    QStringList m_PicFileNames;
    int m_CurPicCount;
    QString m_DirPath;
};

#endif // SHOWPICTURE_H
