#include "patientinfoinputwidget.h"
#include "measurementdef.h"
#include "measurementtext.h"
#include "measureunitconverter.h"
#include "setting.h"
#include "ui_patientinfoinputwidget.h"
#include <QDebug>
#include <qmath.h>

// PatientInfoInputModel
PatientInfoInputModel::PatientInfoInputModel()
    : m_UnitType(0)
    , m_CurUnitIndex(0)
{
}

void PatientInfoInputModel::setUnitType(const int value)
{
    m_UnitType = value;
}

void PatientInfoInputModel::setCurUnitIndex(const int index)
{
    m_CurUnitIndex = index;
}

int PatientInfoInputModel::curUnitIndex() const
{
    return m_CurUnitIndex;
}

void PatientInfoInputModel::setUnits(const QList<int>& units)
{
    m_Units = units;
}

const QList<int>& PatientInfoInputModel::units() const
{
    return m_Units;
}

int PatientInfoInputModel::curUnit()
{
    if ((m_CurUnitIndex < 0) || (m_CurUnitIndex >= m_Units.count()))
    {
        return Measurement::Unit_None;
    }
    return m_Units.at(m_CurUnitIndex);
}

int PatientInfoInputModel::curUnitCount()
{
    return MeasurementText::instance().unitText(curUnit()).count();
}

void PatientInfoInputModel::setValue(const double value, const int srcUnit)
{
    m_Values.clear();
    MeasureUnitConverter::instance().unitConvert(srcUnit, value, curUnit(), m_Values);
}

QList<QVariant> PatientInfoInputModel::curValue()
{
    return m_Values;
}

// PatientInfoInputWidget
PatientInfoInputWidget::PatientInfoInputWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::PatientInfoInputWidget)
{
    ui->setupUi(this);
}

PatientInfoInputWidget::~PatientInfoInputWidget()
{
    delete ui;
}

void PatientInfoInputWidget::setModel(const PatientInfoInputModel& model)
{
    m_Model = model;
    updateView();
}

void PatientInfoInputWidget::setEnabled(const bool enabled)
{
    for (int i = 0; i < m_InputWidgets.count(); ++i)
    {
        m_InputWidgets.at(i)->setEditEnabled(enabled);
    }
}

double PatientInfoInputWidget::curValue(const int tarUnit)
{
    double result = 0.0f;
    foreach (InputUnitGroupWidget* pInputWidget, m_InputWidgets)
    {
        result +=
            MeasureUnitConverter::instance().unitConvert(pInputWidget->curUnit(), pInputWidget->curValue(), tarUnit);
    }
    return result;
}

void PatientInfoInputWidget::setValue(const double value, const int srcUnit)
{
    m_Model.setValue(value, srcUnit);

    for (int i = 0; i < m_Model.curValue().count(); i++)
    {
        QVariant value = m_Model.curValue()[i];
        switch (value.type())
        {
        case QVariant::Int:
            m_InputWidgets[i]->setEditText(QString::number(value.toInt()));
            break;
        case QVariant::Double:
            m_InputWidgets[i]->setEditText(QString::number(value.toFloat(), 'f', 2));
            break;
        default:
            break;
        }
    }
}

QStringList PatientInfoInputWidget::curTextValue()
{
    QStringList textValue;
    foreach (InputUnitGroupWidget* pInputWidget, m_InputWidgets)
    {
        textValue.append(pInputWidget->curEditText());
    }
    return textValue;
}

void PatientInfoInputWidget::setTextValue(const QStringList& textValues)
{
    for (int index = 0; index < textValues.count(); index++)
    {
        m_InputWidgets[index]->setEditText(textValues[index]);
    }
}

void PatientInfoInputWidget::updateView()
{
    if (m_InputWidgets.count() > 0)
    {
        foreach (InputUnitGroupWidget* pInputWidget, m_InputWidgets)
        {
            delete pInputWidget;
        }
        m_InputWidgets.clear();
    }

    //添加输入框
    InputUnitGroupWidget* pInputWidget = NULL;
    QStringList list = MeasurementText::instance().unitText(m_Model.units().at(m_Model.curUnitIndex()));

    int unitValueMax = 0;
    if (list.count() > 1)
    {
        int unitM = MeasureUnitConverter::instance().getCalculateUnit(m_Model.curUnit(), 0);
        int unitL = MeasureUnitConverter::instance().getCalculateUnit(m_Model.curUnit(), 1);
        double value = MeasureUnitConverter::instance().unitConvert(unitM, 1.0f, unitL);
        unitValueMax = floor(value) - 1;
    }

    for (int index = 0; index < list.count(); ++index)
    {
        pInputWidget = new InputUnitGroupWidget(this);
        connect(pInputWidget, SIGNAL(textEdited()), this, SIGNAL(textEdited()));
        pInputWidget->setUnit(MeasureUnitConverter::instance().getCalculateUnit(m_Model.curUnit(), index));
        pInputWidget->setUnitName(list[index]);
        if (index > 0)
        {
            pInputWidget->setValue(m_Model.curValue()[index], unitValueMax);
        }
        else
        {
            pInputWidget->setValue(m_Model.curValue()[index], 999999);
        }

        m_InputWidgets.append(pInputWidget);
        ui->hLayout->addWidget(pInputWidget);
    }

    update();
}

void PatientInfoInputWidget::setUnitMinWidth(const int width)
{
    foreach (InputUnitGroupWidget* pInputWidget, m_InputWidgets)
    {
        pInputWidget->setUnitMinWidth(width);
    }
}

void PatientInfoInputWidget::retranslateUi()
{
}
