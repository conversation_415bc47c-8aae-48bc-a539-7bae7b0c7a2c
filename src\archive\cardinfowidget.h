#ifndef CARDINFOWIDGET_H
#define CARDINFOWIDGET_H
#include "archive_global.h"
#include "basewidget.h"
#include "patientinfoinputwidget.h"

class Study;
namespace Ui
{
class CardInfoWidget;
}

/**
 * @brief 新建病人的界面中，对病人基本信息的补充
 */
class ARCHIVESHARED_EXPORT CardInfoWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit CardInfoWidget(QWidget* parent = 0);
    ~CardInfoWidget();
    /**
     * @brief setStudy 设置需要添加基本信息的study
     *
     * @param study
     */
    void setStudy(Study* study);
    /**
     * @brief flawlessStudy 根据输入框中的信息，给study赋值
     *
     * @param study
     */
    void flawlessStudy(Study*& study);
    /**
     * @brief clearStudyInfo 清空检查信息
     */
    void clearStudyInfo();
    /**
     * @brief setCurrentWidgetEnabled 控制控件的状态
     *
     * @param enabled
     */
    void setCurrentWidgetEnabled(bool enabled);
    void initSet();
    void setAnimalSpeciesIndex(int index);

public slots:
    void setInfo(const QMap<QString, QStringList>& info);
    void onPatientInfoUnitChanged(const QString& key, const int unitIndex);

signals:
    void baseInfo(const QMap<QString, QStringList>& info);

protected:
    const QMap<QString, QStringList> infoList();
    void calBsa();
    void retranslateUi();

private slots:
    //    void on_lineEditHeight_editingFinished();
    //    void on_lineEditWeight_editingFinished();
    void on_lineEditBsa_editingFinished();
    void on_lineEditHr_editingFinished();

    //    void on_lineEditHeight_textEdited(const QString &arg1);

    //    void on_lineEditWeight_textEdited(const QString &arg1);

    void on_lineEditHr_textEdited(const QString& arg1);

    void patientInfo_textEdited();

private:
    Ui::CardInfoWidget* ui;
    PatientInfoInputModel m_PatientHeightModel;
    PatientInfoInputModel m_PatientWeightModel;
    int m_AnimalSpeciesIndex;
};

#endif // CARDINFOWIDGET_H
