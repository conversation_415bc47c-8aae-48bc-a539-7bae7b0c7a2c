#ifndef FOURDPRESETWIDGET_H
#define FOURDPRESETWIDGET_H

#include "controls_global.h"
#include "fourdparasetbasewidget.h"
#include "fourdparasinfo.h"

namespace Ui
{
class FourDPresetWidget;
}

class CONTROLSSHARED_EXPORT FourDPresetWidget : public FourDParaSetBaseWidget
{
    Q_OBJECT
public:
    explicit FourDPresetWidget(QWidget* parent = 0);
    ~FourDPresetWidget();
    virtual void initalize();
    void setFourDPresetPara(const FourDParasInfo::FourDPresetPara& para);
    FourDParasInfo::FourDPresetPara fourDPresetPara();
    void removeComboxItem(const int& index);
    void addComboxItem(const QString& name);
    void renameComboxItemText(const int& index, const QString& name);

protected:
    void retranslateUi();
    virtual void onSetSonoParameters();
private slots:
    void on_copyButton_clicked();
signals:
    void fourDPresetParasChanged();

private:
    Ui::FourDPresetWidget* ui;
    FourDParasInfo::FourDPresetPara m_FourDPresetPara;
};

#endif // FOURDPRESETWIDGET_H
