<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PatientMiddleWidget</class>
 <widget class="QWidget" name="PatientMiddleWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>472</width>
    <height>712</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="PatientHaterInfoWidget" name="widgetHater" native="true">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>300</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>300</height>
      </size>
     </property>
    </widget>
   </item>
   <item>
    <widget class="PatientHomogeneousWidget" name="widgetHomogenous" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>400</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>PatientHaterInfoWidget</class>
   <extends>QWidget</extends>
   <header>patienthaterinfowidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>PatientHomogeneousWidget</class>
   <extends>QWidget</extends>
   <header>patienthomogeneouswidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
