#include "syncfingerprintoperator.h"
#include "fingerprinttask.h"
#include "adminconfigmodel.h"
#include "fingerprintinfo.h"
#include <QDebug>

SyncFingerPrintOperator::SyncFingerPrintOperator(AdminConfigModel* model, QObject* parent)
    : FingerPrintOperatorBase(parent)
    , m_AdminModel(model)
    , m_fpTask(new FingerprintTask)
{
    m_fpTask->startCalibration();

    connect(m_fpTask.get(), &FingerprintTask::verifyState, this, &SyncFingerPrintOperator::onFpVerify);
    connect(m_fpTask.get(), &FingerprintTask::pressFinger, this, &SyncFingerPrintOperator::onPressFinger);
    connect(m_fpTask.get(), &FingerprintTask::enrollState, this, &SyncFingerPrintOperator::onEnrollStatus);
}

SyncFingerPrintOperator::~SyncFingerPrintOperator()
{
    m_fpTask->stopTask();
}

IFingerPrintOperator::FPrintType SyncFingerPrintOperator::fprintType() const
{
    return Sync;
}

int SyncFingerPrintOperator::maxCount()
{
    return 5; // 最大录入数
}

bool SyncFingerPrintOperator::enroll(const QString& userId)
{
    m_fpTask->startEnroll();
    return true;
}

bool SyncFingerPrintOperator::verify()
{
    m_fpTask->stopTask();
    m_fpTask->startVerify();
    return true;
}

void SyncFingerPrintOperator::stop()
{
    m_fpTask->stopTask();
}

bool SyncFingerPrintOperator::remove(const FingerprintInfo& fpinfo)
{
    if (fpinfo.fingerprintId() > -1)
    {
        //删除指纹设备中对应用户的指纹信息;
        if (!m_fpTask->delFp(fpinfo.fingerprintId()))
        {
            return false;
        }
    }
    //删除数据库中对应用户的指纹信息;
    if (m_AdminModel->removeFingerprint(fpinfo.fingerIndex()))
    {
        return true;
    }
    return false;
}

const QList<FingerprintInfo> SyncFingerPrintOperator::getFingerprintInfo(const QString& userId) const
{
    return m_AdminModel->getFingerprintInfo(userId);
}

bool SyncFingerPrintOperator::addFingerprint(const QString& fingerName, int fingerId, int status, const QString& userId)
{
    FingerprintInfo finger(-1, fingerId, fingerName, userId, fingerId > -1 ? true : false);
    return m_AdminModel->addFingerprint(finger);
}

void SyncFingerPrintOperator::onFpVerify(uint state)
{
    if (state == PRIME_STATUS_MATCH && m_fpTask->getId() != -1)
    {
        int ret = m_AdminModel->fpLogin(m_fpTask->getId());
        if (ret == 0)
        {
            emit finish(ret);
        }
    }
    else
    {
        m_fpTask->startVerify();
    }
    //    else if(state == PRIME_STATUS_NOT_MATCH)
    //    {
    //         m_fpTask->startVerify();
    //    }
    //    else if (state == PRIME_STATUS_VERIFY_FAILED)
    //    {
    //        m_fpTask->startVerify();
    //    }
}

void SyncFingerPrintOperator::onEnrollStatus(uint stats)
{
    if (stats == PRIME_STATUS_FULL)
    {
        emit enrollState(NoneSpace, -1);
    }
    else if (stats == PRIME_OK)
    {
        int id = -1;
        id = m_fpTask->getId();

        emit enrollState(stats, id);
        qDebug() << "## onEnrollStatus # id:" << id << " state:" << stats;
    }
}

void SyncFingerPrintOperator::onPressFinger(int timer)
{
    emit pressFinger(timer);
}

bool SyncFingerPrintOperator::hasFingerprint()
{
#ifdef USE_FINGERPRINT
    return true;
#else
    return false;
#endif
}
