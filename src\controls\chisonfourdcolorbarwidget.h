#ifndef CHISONFOURDCOLORBARWIDGET_H
#define CHISONFOURDCOLORBARWIDGET_H

#include <QtOpenGL/QGLWidget>
#include "controls_global.h"
#include "fourddatatypedef.h"

class CONTROLSSHARED_EXPORT ChisonFourDColorBarWidget : public QGLWidget
{
    Q_OBJECT
public:
    explicit ChisonFourDColorBarWidget(QWidget* parent = 0);
    ~ChisonFourDColorBarWidget();

    void setOrientation(Qt::Orientation value);
    void setMarginX(double marginX);
    void setBackgroundColor(const QColor& backgroundColor);
    void setBorderColor(const QColor& borderColor);

    void setScalarRange(double minVal, double maxVal);
    void refreshColorBar(const QList<ChisonFourDCTFNode>& nodeList);

protected:
    void initializeGL();
    void resizeGL(int w, int h);
    void paintGL();
    void drawScene();

private:
    void swap(double& first, double& second);

private:
    QColor m_BackgroundColor;
    QColor m_BorderColor;
    Qt::Orientation m_Orientation;
    double m_ScalarMin;
    double m_ScalarMax;
    double m_MarginX;
    double m_MarginY;
    QList<ChisonFourDCTFNode> m_CTFNodeList;
};

#endif // CHISONFOURDCOLORBARWIDGET_H
