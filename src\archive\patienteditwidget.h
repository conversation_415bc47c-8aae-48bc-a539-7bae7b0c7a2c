#ifndef PATIENTEDITWIDGET_H
#define PATIENTEDITWIDGET_H
#include "archive_global.h"

#include "basewidget.h"
#include "baseinputabledialogframe.h"
#include "basebuttonsboxframecontent.h"
#include "screenorientationmodel.h"
class PatientEditWidget;
class PatientWorkflowModel;
class PatientWorkflow;
class Patient;
class Study;
class IStateManager;
/**
 * @brief 新建病人的界面
 */
class ARCHIVESHARED_EXPORT PatientEditDialog : public BaseInputAbleDialogFrame
{
    Q_OBJECT
public:
    enum BUTTON
    {
        button_patient,
        button_study,
        button_end,
        button_ok,
        button_cancel
    };
    explicit PatientEditDialog(IStateManager* stateManager, QWidget* parent = 0, bool isHuman = true);
    /**
     * @brief setPatient 将传递来的病人信息在界面显示
     *
     * @param patient
     *
     * @return
     */
    void setPatient(Patient* patient);
    /**
     * @brief createPatient 创建一个新的病人
     *
     * @param patient
     *
     * @return
     */
    void createPatient(Patient* patient);
    /**
     * @brief createStudy 新建一个检查
     *
     * @param study
     */
    void createStudy(Study* study);
    void setPatientWorkflow(PatientWorkflow* patientWorkflow);
    /**
     * @brief lineEditPatientId 获取病人的id
     *
     * @return
     */
    QString lineEditPatientId();
    void setPatietId(const QString& patientId);
    void setCurrentWidgetEnabled(bool enabled);
    bool isNameLengthValid() const;
    QWidget* parentPtr() const;
    void showWorklist();
    // BaseDialogFrame interface
    QString mppsDcmFileName() const;
    void clearMPPSDcmFileName();

public slots:
    void onTPButClicked(const QString& category, const QString& butName, const QString& value);
    void onOrientationChanged(bool isHor);

protected:
    void showEvent(QShowEvent* e);

signals:
    void entryArchiveState();
    void clearPatientInfo();
    void clearStudyInfo();
    void isNewPatientId(bool isNew);
    void onlyJump();

private:
    PatientEditWidget* m_Child;
};

namespace Ui
{
class PatientEditWidget;
}

class ARCHIVESHARED_EXPORT PatientEditWidget : public BaseWidget, public BaseButtonsBoxFrameContent
{
    Q_OBJECT
    Q_PROPERTY(bool isHor READ isHor)
public:
    explicit PatientEditWidget(IStateManager* stateManager, QWidget* parent = 0);
    ~PatientEditWidget();
    void setPatient(Patient* patient);
    void createPatient(Patient* patient);
    void createStudy(Study* study);
    void setPatientWorkflow(PatientWorkflow* patientWorkflow);
    QString lineEditPatientId();
    void setCurrentWidgetEnabled(bool enabled);
    void setPatietId(const QString& patientId);
    bool isNameLengthValid() const;
    void showWorklist();
    QString mppsDcmFileName() const;
    void clearMPPSDcmFileName();
    bool isHor();
public slots:
    void onClearPatientInfo();
    void onClearStudyInfo();
    void onAnimalSpeciesIndexChanged(int index);
signals:
    void entryArchiveState();
    void closeCurrentWidget();
    void isNewPatientId(bool isNew);
    void onlyJump();
    void orientationChanged(bool isHor);

protected:
    void retranslateUi();
    void orientationChanged(Qt::ScreenOrientation orientation);
protected slots:
    void archiveButtonClicked();
    void on_tabWidget_currentChanged(int index);
    void onPhysiciasNameSetted(const QString& value);
    void onAccessionSetted(const QString& value);
    void onTPButClicked(const QString& category, const QString& butName, const QString& value);

private:
    Ui::PatientEditWidget* ui;
    PatientWorkflow* m_PatientWorkflow;
    BaseInputAbleDialogFrame* m_BaseInputAbleDialogFrame;
    static const char* const m_AnimalString[];
    enum ANIMALSTRING
    {
        Animal_Information
    };
    ScreenOrientationModel* m_Model;
};

#endif // PATIENTEDITWIDGET_H
