#include "freehand3droiwidget.h"
#include <QPen>
#include <QPainter>
#include <QPaintEvent>
#include <QMouseEvent>
#include "setting.h"
#include "modeluiconfig.h"
#include "toolsfactory.h"
#include "toolnames.h"
#include "icommand.h"
#include "imagewidget.h"
#include "imagetile.h"
#include "sonoparameters.h"
#include "parameter.h"
#include "bfpnames.h"

FreeHand3DRoiWidget::FreeHand3DRoiWidget(QWidget* parent)
    : BaseWidget(parent)
    , m_ImageWidget(qobject_cast<ImageWidget*>(parent))
    , m_SonoParameters(m_ImageWidget->imageTile()->sonoParameters())
    , m_ShowFreeHand3DRoiWidget(false)
    , m_UpdateFreeHand3DRoi(false)
    , m_SolidLine(true)
    , m_ImageSize(m_ImageWidget->imageTile()->imageSize())
    , m_RoiMinSize(Setting::instance().defaults().freeHand3DRoiMinSize())
    , m_RoiSize(Setting::instance().defaults().freeHand3DRoiSize())
    , m_RoiPos(Setting::instance().defaults().freeHand3DRoiPos())
    , m_OldMoveBitSet(1)
{

    QRect rect = QRect(parent->mapToGlobal(QPoint(0, 0)), parent->size());
    this->setGeometry(rect);

    m_ImageWidget->imageTile()->setFreeHand3DRoiMinSize(m_RoiMinSize);
    // 无边框，置顶
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint);
    connect(m_ImageWidget, SIGNAL(rightButtonReleased()), this, SLOT(onRightButtonReleased()));
    connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onFreezeChanged(QVariant)));
    m_ImageWidget->imageTile()->setFreeHand3DROIGeometry(QRect(m_RoiPos, m_RoiSize));
}

FreeHand3DRoiWidget::~FreeHand3DRoiWidget()
{
}

void FreeHand3DRoiWidget::retranslateUi()
{
}

void FreeHand3DRoiWidget::paintEvent(QPaintEvent* event)
{
    QPainter painter(this);
    if (m_ShowFreeHand3DRoiWidget)
    {
        QPen pen;
        pen.setWidth(1);
        pen.setStyle(m_SolidLine ? Qt::SolidLine : Qt::DashLine);

        pen.setColor(m_UpdateFreeHand3DRoi
                         ? QColor(ModelUiConfig::instance().value(ModelUiConfig::RoiActiveColor).toInt())
                         : QColor(ModelUiConfig::instance().value(ModelUiConfig::RoiFreezeColor).toInt()));
        painter.setPen(pen);
        painter.drawRect(QRect(m_RoiPos, m_RoiSize));
    }
    BaseWidget::paintEvent(event);
}

void FreeHand3DRoiWidget::onFreeHand3DRoiOnChanged(const QVariant& value)
{
    m_ShowFreeHand3DRoiWidget = value.toBool();
    m_UpdateFreeHand3DRoi = value.toBool();
    m_SolidLine = false;
    m_ImageWidget->setShowFreeHand3DRoi(m_ShowFreeHand3DRoiWidget);
    this->setVisible(m_ShowFreeHand3DRoiWidget);
    m_RoiPos = Setting::instance().defaults().freeHand3DRoiPos();
    m_RoiSize = Setting::instance().defaults().freeHand3DRoiSize();
    m_ImageWidget->imageTile()->setFreeHand3DROIGeometry(QRect(m_RoiPos, m_RoiSize));
    changeMouseAction(value.toBool());
    update();
}

void FreeHand3DRoiWidget::onBeforecurSonoParametersChanged()
{
    disconnect(m_SonoParameters->parameter(BFPNames::FreeHand3DRoiOnStr), SIGNAL(valueChanged(QVariant)), this,
               SLOT(onFreeHand3DRoiOnChanged(QVariant)));
}

void FreeHand3DRoiWidget::onCurSonoParametersChanged()
{
    m_SonoParameters = m_ImageWidget->imageTile()->sonoParameters();
    connect(m_SonoParameters->parameter(BFPNames::FreeHand3DRoiOnStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onFreeHand3DRoiOnChanged(QVariant)));
    onFreeHand3DRoiOnChanged(m_SonoParameters->pV(BFPNames::FreeHand3DRoiOnStr));
}

void FreeHand3DRoiWidget::onFreezeChanged(const QVariant& value)
{
    if (!value.toBool())
    {
        m_SonoParameters->setPV(BFPNames::FreeHand3DRoiOnStr, false);
    }
}

void FreeHand3DRoiWidget::onRightButtonReleased()
{
    m_UpdateFreeHand3DRoi = !m_UpdateFreeHand3DRoi;
    changeMouseAction(m_UpdateFreeHand3DRoi);
    if (m_ShowFreeHand3DRoiWidget)
    {
        update();
    }
}

void FreeHand3DRoiWidget::leftButtonPressed(const QPoint& pos)
{
    if (m_UpdateFreeHand3DRoi)
    {
        m_SolidLine = !m_SolidLine;
    }
    update();
}

void FreeHand3DRoiWidget::movedAction(const QPoint& offset, const QPoint& pos)
{
    Q_UNUSED(pos)
    if (m_ShowFreeHand3DRoiWidget && m_UpdateFreeHand3DRoi)
    {
        if (m_SolidLine)
        {
            if (m_RoiPos.x() + offset.x() < 0)
            {
                m_RoiPos.setX(0);
            }
            else if (m_RoiPos.x() + m_RoiSize.width() + offset.x() > m_ImageSize.width() - 1)
            {
                m_RoiPos.setX(m_ImageSize.width() - m_RoiSize.width() - 1);
            }
            else
            {
                m_RoiPos.setX(m_RoiPos.x() + offset.x());
            }

            if (m_RoiPos.y() + offset.y() < 0)
            {
                m_RoiPos.setY(0);
            }
            else if (m_RoiPos.y() + m_RoiSize.height() + offset.y() > m_ImageSize.height() - 1)
            {
                m_RoiPos.setY(m_ImageSize.height() - m_RoiSize.height() - 1);
            }
            else
            {
                m_RoiPos.setY(m_RoiPos.y() + offset.y());
            }
        }
        else
        {
            if (offset.x() > 0)
            {
                if (m_RoiPos.x() > offset.x())
                {
                    m_RoiPos.setX(m_RoiPos.x() - offset.x());
                }
                if (m_RoiSize.width() + m_RoiPos.x() + 2 * offset.x() < m_ImageSize.width())
                {
                    m_RoiSize.setWidth(m_RoiSize.width() + 2 * offset.x());
                }
                else
                {
                    if ((m_RoiSize.width() + m_RoiPos.x() + 2) < m_ImageSize.width())
                    {
                        m_RoiSize.setWidth(m_RoiSize.width() + 2);
                    }
                }
            }
            else
            {
                if (m_RoiSize.width() + 2 * offset.x() >= m_RoiMinSize.width())
                {
                    m_RoiPos.setX(m_RoiPos.x() - offset.x());
                    m_RoiSize.setWidth(m_RoiSize.width() + 2 * offset.x());
                }
            }

            if (offset.y() > 0)
            {
                if (m_RoiPos.y() > offset.y())
                {
                    m_RoiPos.setY(m_RoiPos.y() - offset.y());
                }
                if (m_RoiSize.height() + m_RoiPos.y() + 2 * offset.y() < m_ImageSize.height())
                {
                    m_RoiSize.setHeight(m_RoiSize.height() + 2 * offset.y());
                }
                else
                {
                    if ((m_RoiSize.height() + m_RoiPos.y() + 2) < m_ImageSize.height())
                    {
                        m_RoiSize.setHeight(m_RoiSize.height() + 2);
                    }
                }
            }
            else
            {
                if (m_RoiSize.height() + 2 * offset.y() >= m_RoiMinSize.height())
                {
                    m_RoiPos.setY(m_RoiPos.y() - offset.y());
                    m_RoiSize.setHeight(m_RoiSize.height() + 2 * offset.y());
                }
            }
        }
        m_ImageWidget->imageTile()->setFreeHand3DROIGeometry(QRect(m_RoiPos, m_RoiSize));
        update();
    }
}

void FreeHand3DRoiWidget::changeMouseAction(bool changeRoi)
{
    ICommand* cinePlayMouseTool = ToolsFactory::instance().command(ToolNames::CinePlayMouseStr);
    ICommand* freeHand3DMouseTool = ToolsFactory::instance().command(ToolNames::FreeHand3DMouseStr);
    if (cinePlayMouseTool != NULL && freeHand3DMouseTool != NULL && m_ShowFreeHand3DRoiWidget)
    {
        if (changeRoi)
        {
            ToolsFactory::instance().command(ToolNames::CinePlayMouseStr)->stop();
            ToolsFactory::instance().command(ToolNames::FreeHand3DMouseStr)->run();
        }
        else
        {
            ToolsFactory::instance().command(ToolNames::FreeHand3DMouseStr)->stop();
            ToolsFactory::instance().command(ToolNames::CinePlayMouseStr)->run();
        }
    }
}
