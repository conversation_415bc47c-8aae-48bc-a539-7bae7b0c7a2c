#include "makedcmfilesoptionwidget.h"
#include "ui_makedcmfilesoptionwidget.h"
#include "util.h"
#include "globaldef.h"
#include <QSettings>

MakeDcmFilesOptionWidget::MakeDcmFilesOptionWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::MakeDcmFilesOptionWidget)
{
    ui->setupUi(this);

    fillInCompressionComboxes();

    initCompressionComboxesIndex();
}

MakeDcmFilesOptionWidget::~MakeDcmFilesOptionWidget()
{
    delete ui;
}

void MakeDcmFilesOptionWidget::save()
{
    QSettings settings(DicomChison::dicomSettingFileName(), QSettings::IniFormat);

    settings.beginGroup(DicomChison::makeDcmOptionGroupName());

    settings.setValue(DicomChison::compressRatio(), DicomChison::compressRatios()[compressRatiosIndex[0]]);
    settings.setValue(DicomChison::compressRatioYbr(), DicomChison::compressRatiosYbr()[compressRatiosIndex[1]]);
    settings.setValue(DicomChison::compressMode(),
                      DicomChison::compressModes()[ui->comboBoxCompressionMode->currentIndex()]);
    settings.setValue(DicomChison::maxFramerate(),
                      DicomChison::maxFramerates()[ui->comboBoxMaxFramerate->currentIndex()]);

    settings.endGroup();
}

void MakeDcmFilesOptionWidget::retranslateUi()
{
    ui->retranslateUi(this);
}

void MakeDcmFilesOptionWidget::fillInCompressionComboxes()
{
    QStringList tred;
    ui->comboBoxCompressionMode->clear();
    foreach (const QString& str, DicomChison::compressModes())
    {
        tred.append(Util::translate("DicomChison", str));
    }
    ui->comboBoxCompressionMode->insertItems(0, tred);

    tred.clear();
    ui->comboBoxCompressionRatio->clear();
    foreach (const QString& str, DicomChison::compressRatios())
    {
        tred.append(Util::translate("DicomChison", str));
    }
    ui->comboBoxCompressionRatio->insertItems(0, tred);

    tred.clear();
    ui->comboBoxMaxFramerate->clear();
    foreach (const QString& str, DicomChison::maxFramerates())
    {
        tred.append(Util::translate("DicomChison", str));
    }
    ui->comboBoxMaxFramerate->insertItems(0, tred);
}

void MakeDcmFilesOptionWidget::initCompressionComboxesIndex()
{
    QMap<QString, QString> temp = DicomChison::getMakeDcmOptionKeysValues();

    int index = DicomChison::compressModes().indexOf(temp.value(DicomChison::compressMode()));
    ui->comboBoxCompressionMode->setCurrentIndex(index == -1 ? 0 : index);

    int ratioindex = DicomChison::compressRatios().indexOf(temp.value(DicomChison::compressRatio()));
    compressRatiosIndex[0] = ratioindex < 0 ? 1 : ratioindex; //初始化为low
    ratioindex = DicomChison::compressRatiosYbr().indexOf(temp.value(DicomChison::compressRatioYbr()));
    compressRatiosIndex[1] = ratioindex < 0 ? 0 : ratioindex; //初始化为low

    if (index == 1)
    {
        // RGB, JPEG Compression
        index = compressRatiosIndex[0];
    }
    else if (index == 2)
    {
        // YBR, JPEG Compression
        index = compressRatiosIndex[1];
    }
    else
    {
        // Uncompression
        index = 0;
    }
    ui->comboBoxCompressionRatio->setCurrentIndex(index == -1 ? 0 : index);

    index = DicomChison::maxFramerates().indexOf(temp.value(DicomChison::maxFramerate()));
    ui->comboBoxMaxFramerate->setCurrentIndex(index == -1 ? 1 : index);
}

void MakeDcmFilesOptionWidget::on_comboBoxCompressionRatio_currentIndexChanged(int index)
{
    int modeindex = ui->comboBoxCompressionMode->currentIndex();
    if (modeindex == 1)
    {
        // RGB, JPEG Compression
        compressRatiosIndex[0] = index;
    }
    else if (modeindex == 2)
    {
        // YBR, JPEG Compression
        compressRatiosIndex[1] = index;
    }
}

void MakeDcmFilesOptionWidget::on_comboBoxCompressionMode_currentIndexChanged(int index)
{
    if (index != -1)
    {
        if (index == 0)
        {
            ui->comboBoxCompressionRatio->setEnabled(false);
        }
        else
        {
            QStringList tred;
            int ratioindex = 0;

            if (index == 1)
            {
                // RGB, JPEG Compression
                foreach (const QString& str, DicomChison::compressRatios())
                {
                    tred.append(Util::translate("DicomChison", str));
                }
                ratioindex = compressRatiosIndex[0];
            }
            else if (index == 2)
            {
                // YBR, JPEG Compression
                foreach (const QString& str, DicomChison::compressRatiosYbr())
                {
                    tred.append(Util::translate("DicomChison", str));
                }
                ratioindex = compressRatiosIndex[1];
            }

            ui->comboBoxCompressionRatio->clear();
            ui->comboBoxCompressionRatio->insertItems(0, tred);
            ui->comboBoxCompressionRatio->setCurrentIndex(ratioindex);
            ui->comboBoxCompressionRatio->setEnabled(true);
        }
    }
}
