#ifndef IFOURDLIVEWIDGET_H
#define IFOURDLIVEWIDGET_H

#include "controls_global.h"
#include "ibasewidget.h"

class IFourDRenderWidget;
class FourDTransformWidget;
class GrayCurveWidget;
class FourDLeftWidget;
class FourDRightWidget;
class ImageTile;
class ILineBufferManager;
class FourDLiveModel;
class FourDVolumeLineImageArgs;
class ICommand;
class CONTROLSSHARED_EXPORT IFourDLiveWidget : public IBaseWidget
{
    Q_OBJECT
public:
    virtual ~IFourDLiveWidget()
    {
    }
    virtual void setContext(ImageTile* context, ILineBufferManager* bufferManager) = 0;
    virtual void init() = 0;
    virtual void reset() = 0;
    virtual void resetToPresetDefaultValue() = 0;
    virtual void prepareFourDParameters(bool resetParameters) = 0;
    virtual void clearFourDData() = 0;
    virtual void saveCurGroupParas() = 0;
    virtual void saveROIInfo() = 0;
    virtual IFourDRenderWidget* fourDRenderWidget() const = 0;
    virtual FourDTransformWidget* transformWidget() const = 0;
    virtual GrayCurveWidget* fourdGrayCurveWidget() const = 0;
    virtual void onAviPlay(const QString& aviFileName) = 0;
    virtual ICommand* currentMouseCommand() const = 0;
    virtual void setFreezeBarAllIndex() = 0;
public slots:
    virtual void onFourDZoom(bool value) = 0;
    virtual void onCurvedLineCtrlPointMoved(const QString& direction) = 0;
    virtual void onBeforecurSonoParametersChanged() = 0;
    virtual void onCurSonoParametersChanged() = 0;
    virtual void sendVolumeData(const FourDVolumeLineImageArgs&) = 0;
    virtual void onFourDTransformAxisChanged(QString value) = 0;
    virtual void onFourDDirectionChanged(bool increase) = 0;

protected:
    explicit IFourDLiveWidget(QWidget* parent = 0);
};

#endif // IFOURDLIVEWIDGET_H
