/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#ifndef HTMLEXPORTER_H
#define HTMLEXPORTER_H
#include "autotest_global.h"

#include <QString>
#include <QStringList>

class AUTOTESTSHARED_EXPORT HTMLExporter
{
public:
    typedef QList<QList<QString>> ValueList;
    HTMLExporter(int row, int col);
    HTMLExporter()
    {
    }
    QString create();
    void setHTMLTitle(const QString& title);
    void setTitleList(const QStringList& value);
    void setTitle(const QString& val);
    void addValueList(const QList<QString>& value);
    void setRow(int value);
    void setCol(int value);
    void setHead(const QString& value);
    QString head() const;
    void clearValue();
    void setFront(const QStringList& value);
    void setEnd(const QStringList& value);

private:
    int m_rowCount;
    int m_colCount;
    QString m_head;
    QString m_title;
    QStringList m_titleList;
    ValueList m_valueList;
    QStringList m_frontList;
    QStringList m_endList;
};

#endif // HTMLEXPORTER_H
