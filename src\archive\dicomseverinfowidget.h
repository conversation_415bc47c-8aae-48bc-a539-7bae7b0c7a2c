#ifndef DICOMSEVERINFOWIDGET_H
#define DICOMSEVERINFOWIDGET_H
#include "archive_global.h"

#include <QWidget>
#include <QHash>
#include "baseinputabledialogframe.h"
class DicomModel;

class DicomSeverInfoWidget;
class Patient;
class DicomTaskManager;
class ExportFileWidget;
class DeleteFilesController;
class NetworkStorageFileWidget;
class BluetoothStorageFileWidget;
class IStateManager;
class IColorMapManager;
class IDiskDevice;

////.h
class ARCHIVESHARED_EXPORT DicomSeverInfoDialog : public BaseInputAbleDialogFrame
{
    Q_OBJECT
public:
    explicit DicomSeverInfoDialog(IStateManager* value, IColorMapManager* colorMapManager, IDiskDevice* diskDevice,
                                  QWidget* parent = 0);
    void setDefaultDirName(const QString& name);
    void setExportFilePaths(const QStringList& list);
    void setIsCurrentMultiChoice(bool value);
    void setExprotFileNames(const QStringList& list);
    void setDicomTaskManager(DicomTaskManager* manager);
    void setDeleteFilesController(DeleteFilesController* controller);
    void setDeleteAfterSendExam(bool flag);
    void setIsSendSR(bool value);
    //    void setPatient(Patient *p);
    void initWidegt();
    //    void setHash(const QHash<QString, Patient*>& value);
public slots:
signals:
    void deleteCurrentSelections();
private slots:
    virtual void reject();

private:
    DicomSeverInfoWidget* m_Child;
};

namespace Ui
{
class DicomSeverInfoWidget;
}

class ARCHIVESHARED_EXPORT DicomSeverInfoWidget : public QWidget
{
    Q_OBJECT

public:
    explicit DicomSeverInfoWidget(IStateManager* value, IColorMapManager* colorMapManager, IDiskDevice* diskDevice,
                                  QWidget* parent = 0);
    void setDefaultDirName(const QString& name);
    void setExportFilePaths(const QStringList& list);
    void setExprotFileNames(const QStringList& list);
    void setDicomTaskManager(DicomTaskManager* manager);
    void setDeleteFilesController(DeleteFilesController* controller);
    void setIsCurrentMultiChoice(bool value);
    void setDeleteAfterSendExam(bool flag);
    void setIsSendSR(bool value);
    //    void setPatient(Patient* p);

    ~DicomSeverInfoWidget();
    void resizeEvent(QResizeEvent* e);
    void initWidegt();
    //    void setHash(const QHash<QString, Patient*>& value);
    bool isWorking();
signals:
    void deleteCurrentSelections();
    void accept();

private slots:
    void on_listWidget_currentRowChanged(int currentRow);

private:
    Ui::DicomSeverInfoWidget* ui;
    QString m_DefaultDirName;
    QStringList m_ExportFilePathes;
    QStringList m_FileNames;
    DicomTaskManager* m_DicomTaskManager;
    DeleteFilesController* m_DeleteFilesController;
    DicomModel* m_DicomModel;
    NetworkStorageFileWidget* m_NetworkStorageWidget;
    BluetoothStorageFileWidget* m_BluetoothStorageWidget;
    ExportFileWidget* m_ExportWidget;
    bool m_isCurrentMultiChoice;
    bool m_deleteAfterSendExam;
    bool m_IsSendSR;
    //    Patient *m_Patient;
    //    QHash<QString, Patient*> m_Hash;
    IStateManager* m_StateManager;
    IColorMapManager* m_ColorMapManager;
    IDiskDevice* m_DiskDevice;

protected:
    // 添加翻译 by jinyuqi
    void retranslateUi();
};

#endif // DICOMSEVERINFOWIDGET_H
