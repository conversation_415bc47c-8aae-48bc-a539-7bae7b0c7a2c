#ifndef SONODIAPHALG_H
#define SONODIAPHALG_H

#include "algorithm_global.h"
#include <QLibrary>
#ifdef USE_AUTODIAPH
#include "dainterface.h"
#else
typedef struct DAResultData
{
#ifdef <PERSON>oeye
#ifdef Sonoair
    int points[2000 * 3];
    int length;
#else
    int points[2000 * 3];
    int length;
    int Idx_v;
    int Idx_p;
#endif
#else
    int points[1000 * 3];
    int length;
#endif
} DARetData;
#endif
#include "virtualalg.h"

class ALGORITHMSHARED_EXPORT SonoDiaphAlg : public VirtualAlg
{
    friend class AlgInstance;

private:
    SonoDiaphAlg();

public:
    typedef int (*CreateDiapher)(void*& handle);
    typedef int (*ReleaseDiapher)(void* handle);
    typedef int (*DiaphragmAnalysis)(void* handle, unsigned char* inImage, int width, int height, int rect[],
                                     DARetData* retData);

    int createDiapher(void*&);
    int releaseDiapher(void*);
    int diaphragmAnalysis(void* handle, unsigned char* inImage, int width, int height, int rect[], DARetData* retData);
};

#endif // SONODIAPHALG_H
