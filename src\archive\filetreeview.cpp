#include "filetreeview.h"
#include "ui_filetreeview.h"

#include <QDebug>

FileTreeView::FileTreeView(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::FileTreeView)
{
    ui->setupUi(this);
    ui->treeView->setHeader<PERSON><PERSON><PERSON>(true);
}

FileTreeView::~FileTreeView()
{
    delete ui;
}

void FileTreeView::setModel(QAbstractItemModel* model)
{
    ui->treeView->setModel(model);
    int count = ui->treeView->model()->columnCount();
    while (count > 1)
    {
        count--;
        ui->treeView->setColumnHidden(count, true);
    }
}

void FileTreeView::setRootIndex(const QModelIndex& index)
{
    ui->treeView->setRootIndex(index);
    ui->treeView->setInitFileInfo(index);
}

void FileTreeView::setAnimated(bool enable)
{
    ui->treeView->setAnimated(enable);
}

void FileTreeView::setIndentation(int i)
{
    ui->treeView->setIndentation(i);
}

void FileTreeView::setSortingEnabled(bool enable)
{
    ui->treeView->setSortingEnabled(enable);
}

void FileTreeView::setRootIsDecorated(bool show)
{
    ui->treeView->setRootIsDecorated(show);
}

void FileTreeView::on_pushButtonBack_clicked()
{
    ui->treeView->back();
}

void FileTreeView::on_pushButtonMakeDir_clicked()
{
    ui->treeView->makeDir();
}

void FileTreeView::setItemsExpandable(bool enable)
{
    ui->treeView->setItemsExpandable(enable);
}

QFileInfo FileTreeView::currentFileInfo()
{
    return ui->treeView->currentFileInfo();
}

void FileTreeView::on_pushButtonDelete_clicked()
{
    QFileInfoList infoList = ui->treeView->deleteFileList();
    if (!infoList.isEmpty())
    {
        emit fileInfoList(infoList);
    }
}

void FileTreeView::retranslateUi()
{
    ui->retranslateUi(this);
}
