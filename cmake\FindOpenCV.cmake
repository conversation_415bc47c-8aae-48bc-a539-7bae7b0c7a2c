#
# The module defines the following variables:
# OPENCV_FOUND - True if opencv found.
# OPENCV_INCLUDE_DIRS - where to find opencv.h, etc.
# OPENCV_LIBRARIES - List of libraries when using opencv.
#

thirdparty_prefix_path(opencv)

find_path ( OPENCV_INCLUDE_DIR
            NAMES
                opencv opencv2
            HINTS
                ${OPENCV_ROOT}/include
            )
set(OPENCV_INCLUDE_DIRS ${OPENCV_INCLUDE_DIR})
message("Found OpenCV headers: ${OPENCV_INCLUDE_DIRS}")

find_library ( OPENCV_LIBRARY
            NAMES 
                opencv_world opencv_world410 opencv_world410d
            HINTS
                ${OPENCV_LIBPATH}
             )
set(OPENCV_LIBRARIES ${OPENCV_LIBRARY})
message("Found OpenCV libs: ${OPENCV_LIBRARIES}")