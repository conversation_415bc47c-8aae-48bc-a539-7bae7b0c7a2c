#ifndef AUTOTESTFORM_H
#define AUTOTESTFORM_H
#include "controls_global.h"

#include <QWidget>
#include "basedialogframe.h"
#include <QProcess>

namespace Ui
{
class AutoTestForm;
}
class AutoTestForm;

class CONTROLSSHARED_EXPORT AutoTestDialog : public BaseDialogFrame
{
    Q_OBJECT
public:
    explicit AutoTestDialog(QWidget* parent = 0);

protected:
    virtual void retranslateUi();

private:
    AutoTestForm* m_Child;
};
class AutoTestForm : public BaseWidget
{
    Q_OBJECT

public:
    enum TestStatus
    {
        Init = -1,
        Waiting = 1,
        Recording,
        Replaying
    };

protected:
    virtual void retranslateUi();

public:
    explicit AutoTestForm(QWidget* parent = nullptr);
    ~AutoTestForm();
    void setTestStatus(const TestStatus status);
private slots:
    void on_btnRecord_clicked();
    void on_btnReplay_clicked();

signals:
    void quit();

private:
    void startRecord();
    void startReplay();
    void stopRecord();
    void stopReplay();

private:
    Ui::AutoTestForm* ui;
    TestStatus m_CurTestStatus;
    QProcess m_Process;
};

#endif // AUTOTESTFORM_H
