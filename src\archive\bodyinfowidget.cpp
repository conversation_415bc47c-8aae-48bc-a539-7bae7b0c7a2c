#include "bodyinfowidget.h"
#include "ui_bodyinfowidget.h"
#include "model/study.h"
#include "formula.h"
#include "doublevalidator.h"
#include "util.h"
#include "realcompare.h"
#include "measurementdef.h"
#include "patientinfounit.h"
#include "appsetting.h"

BodyInfoWidget::BodyInfoWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::BodyInfoWidget)
    , m_AnimalSpeciesIndex(Formula::Canine)
{
    ui->setupUi(this);
    setWidgetVisible(false);
    setIMTWidgetVisible(false);
    initSet();
    connect(ui->wPatientHeight, SIGNAL(textEdited()), this, SLOT(patientInfo_textEdited()));
    connect(ui->wPatientWeight, SIGNAL(textEdited()), this, SLOT(patientInfo_textEdited()));
    connect(ui->checkBoxIMT, SIGNAL(stateChanged(int)), this, SLOT(patientInfo_stateChanged(int)));
}

BodyInfoWidget::~BodyInfoWidget()
{
    delete ui;
}

void BodyInfoWidget::initSet()
{
    //    ui->lineEditHeight->setValidator(new DoubleValidator(0, 999, 2, this));
    //    ui->lineEditWeight->setValidator(new DoubleValidator(0, 999, 2, this));
    QList<PatientInfoUnitGroup>& groups = PatientInfoUnit::instance().patientInfoUnitGroups();
    for (int index = 0; index < groups.count(); index++)
    {
        switch (groups[index].unitType())
        {
        case Measurement::PatientInfoType_Height:
            m_PatientHeightModel.setUnitType(groups[index].unitType());
            m_PatientHeightModel.setCurUnitIndex(groups[index].curIndex());
            m_PatientHeightModel.setUnits(groups[index].unitName());
            break;
        case Measurement::PatientInfoType_Weight:
            m_PatientWeightModel.setUnitType(groups[index].unitType());
            m_PatientWeightModel.setCurUnitIndex(groups[index].curIndex());
            m_PatientWeightModel.setUnits(groups[index].unitName());
            break;
        default:
            break;
        }
    }

    m_PatientHeightModel.setValue(0.0, Measurement::Unit_cm);
    ui->wPatientHeight->setModel(m_PatientHeightModel);
    m_PatientWeightModel.setValue(0.0, Measurement::Unit_kg);
    ui->wPatientWeight->setModel(m_PatientWeightModel);

    ui->lineEditHr->setValidator(new QIntValidator(0, 999, this));
    ui->lineEditPsa->setValidator(new DoubleValidator(0, 9999, 2, this));
    ui->lineEditBsa->setValidator(new DoubleValidator(0, 999, 2, this));
    ui->lineEditBsa->setReadOnly(true);
    ui->checkBoxIMT->setChecked(false);
}

void BodyInfoWidget::setAnimalSpeciesIndex(int index)
{
    m_AnimalSpeciesIndex = index;
    calBsa();
}

void BodyInfoWidget::setWidgetVisible(bool visible)
{
    ui->labelHR->setVisible(visible);
    ui->lineEditHr->setVisible(visible);
    ui->labelbpm->setVisible(visible);

    ui->labelPSA->setVisible(visible);
    ui->lineEditPsa->setVisible(visible);
    ui->labelng->setVisible(visible);
}

void BodyInfoWidget::setIMTVisible(bool visible)
{
    ui->checkBoxIMT->setVisible(visible);

    ui->IMTInfoWidget->setVisible(visible);

    if (ui->checkBoxIMT->isChecked() == false)
    {
        ui->IMTInfoWidget->setVisible(false);
    }
}

void BodyInfoWidget::setIMTWidgetVisible(bool visible)
{
    ui->IMTInfoWidget->initSet();
    ui->IMTInfoWidget->setVisible(visible);
}

void BodyInfoWidget::setStudy(Study* study)
{
    if (study != NULL)
    {
        //        ui->lineEditHeight->setText(QString::number(study->Height(), 'f', 2));
        //        ui->lineEditWeight->setText(QString::number(study->Weight(), 'f', 2));
        ui->wPatientHeight->setValue(study->Height(), Measurement::Unit_cm);
        ui->wPatientWeight->setValue(study->Weight(), Measurement::Unit_kg);
        if (study->BSA() == 0.0f)
            calBsa();
        else
            ui->lineEditBsa->setText(QString::number(study->BSA(), 'f', 2));
        ui->lineEditHr->setText(QString::number(study->HR()));
        ui->lineEditPsa->setText(QString::number(study->SPSA(), 'f', 2));
        if (study->IMT() == 1)
        {
            ui->checkBoxIMT->setCheckState(Qt::CheckState::Checked);
        }
        else
        {
            ui->checkBoxIMT->setCheckState(Qt::CheckState::Unchecked);
        }
        ui->IMTInfoWidget->setStudy(study);
    }
}

void BodyInfoWidget::flawlessStudy(Study* study)
{
    if (study != NULL)
    {
        //        study->setHeight(ui->lineEditHeight->text().toFloat());
        //        study->setWeight(ui->lineEditWeight->text().toFloat());
        study->setHeight(ui->wPatientHeight->curValue(Measurement::Unit_cm));
        study->setWeight(ui->wPatientWeight->curValue(Measurement::Unit_kg));
        study->setBSA(ui->lineEditBsa->text().toFloat());
        study->setHR(ui->lineEditHr->text().toInt());
        study->setSPSA(ui->lineEditPsa->text().toFloat());
        study->setIMT(ui->checkBoxIMT->checkState() == Qt::CheckState::Checked ? 1 : 0);
        if (ui->checkBoxIMT->isChecked())
        {
            ui->IMTInfoWidget->flawlessStudy(study);
        }
    }
}

void BodyInfoWidget::clearStudyInfo()
{
    //    ui->lineEditHeight->setText("0.00");
    //    ui->lineEditWeight->setText("0.00");
    ui->wPatientHeight->setValue(0.0, Measurement::Unit_cm);
    ui->wPatientWeight->setValue(0.0, Measurement::Unit_kg);
    ui->lineEditBsa->setText("0.00");
    ui->lineEditHr->setText("0");
    ui->lineEditPsa->setText("0.00");
    ui->IMTInfoWidget->clearStudyInfo();
    ui->checkBoxIMT->setChecked(false);
}

void BodyInfoWidget::setCurrentWidgetEnabled(bool enabled)
{
    //    ui->lineEditHeight->setEnabled(enabled);
    //    ui->lineEditWeight->setEnabled(enabled);
    ui->wPatientHeight->setEnabled(enabled);
    ui->wPatientWeight->setEnabled(enabled);
    ui->lineEditBsa->setEnabled(enabled);
    ui->lineEditHr->setEnabled(enabled);
    ui->lineEditPsa->setEnabled(enabled);
    ui->checkBoxIMT->setEnabled(enabled);
    ui->IMTInfoWidget->setCurrentWidgetEnabled(enabled);
}

void BodyInfoWidget::setInfo(const QMap<QString, QStringList>& info)
{
    //    ui->lineEditHeight->setText(info.value("height"));
    //    ui->lineEditWeight->setText(info.value("weight"));
    ui->wPatientHeight->setTextValue(info.value("height"));
    ui->wPatientWeight->setTextValue(info.value("weight"));
    ui->lineEditBsa->setText(info.value("bsa")[0]);
    ui->lineEditHr->setText(info.value("hr")[0]);
}

void BodyInfoWidget::onPatientInfoUnitChanged(const QString& key, const int unitIndex)
{
    if (key == "height")
    {
        double height = ui->wPatientHeight->curValue(Measurement::Unit_cm);
        m_PatientHeightModel.setCurUnitIndex(unitIndex);
        m_PatientHeightModel.setValue(height, Measurement::Unit_cm);
        ui->wPatientHeight->setModel(m_PatientHeightModel);
    }
    else if (key == "weight")
    {
        double weight = ui->wPatientWeight->curValue(Measurement::Unit_kg);
        m_PatientWeightModel.setCurUnitIndex(unitIndex);
        m_PatientWeightModel.setValue(weight, Measurement::Unit_kg);
        ui->wPatientWeight->setModel(m_PatientWeightModel);
    }
}

const QMap<QString, QStringList> BodyInfoWidget::infoList()
{
    QMap<QString, QStringList> info;
    //    info.insert("height", ui->lineEditHeight->text());
    //    info.insert("weight", ui->lineEditWeight->text());
    info.insert("height", ui->wPatientHeight->curTextValue());
    info.insert("weight", ui->wPatientWeight->curTextValue());
    info.insert("bsa", QStringList() << ui->lineEditBsa->text());
    info.insert("hr", QStringList() << ui->lineEditHr->text());
    return info;
}

void BodyInfoWidget::calBsa()
{
    if (AppSetting::isAnimal())
    {
        if (Formula::Canine == m_AnimalSpeciesIndex || Formula::Feline == m_AnimalSpeciesIndex)
        {
            ui->lineEditBsa->setReadOnly(true);
            double weight = ui->wPatientWeight->curValue(Measurement::Unit_kg);

            ui->lineEditBsa->setText(QString::number(Formula::calAnimalBsa(m_AnimalSpeciesIndex, weight), 'f', 2));
        }
        else
        {
            ui->lineEditBsa->clear();
            ui->lineEditBsa->setText(QString::number(0.00, 'f', 2));
            ui->lineEditBsa->setReadOnly(false);
        }
    }
    else
    {
        //    if(RealCompare::AreEqual(ui->lineEditHeight->text().toFloat(), 0)
        //            || RealCompare::AreEqual(ui->lineEditWeight->text().toFloat(), 0))
        double height = ui->wPatientHeight->curValue(Measurement::Unit_cm);
        double weight = ui->wPatientWeight->curValue(Measurement::Unit_kg);

        ui->lineEditBsa->setText(QString::number(Formula::calBsa(height, weight), 'f', 2));
    }
}

void BodyInfoWidget::retranslateUi()
{
    ui->retranslateUi(this);
}

// void BodyInfoWidget::on_lineEditHeight_editingFinished()
//{
//    calBsa();
//    emit baseInfo(infoList());
//}

// void BodyInfoWidget::on_lineEditWeight_editingFinished()
//{
//    calBsa();
//    emit baseInfo(infoList());
//}

void BodyInfoWidget::on_lineEditBsa_editingFinished()
{
    emit baseInfo(infoList());
}

void BodyInfoWidget::on_lineEditHr_editingFinished()
{
    emit baseInfo(infoList());
}

// added by jyq to make weight and height with bsa
// void BodyInfoWidget::on_lineEditHeight_textEdited(const QString &arg1)
//{
//    // 保存当前光标所在位置
//    int pos = ui->lineEditHeight->cursorPosition();
//    calBsa();
//    emit baseInfo(infoList());
//    ui->lineEditHeight->setCursorPosition(pos);

//}

// void BodyInfoWidget::on_lineEditWeight_textEdited(const QString &arg1)
//{
//    // 保存当前光标所在位置
//    int pos = ui->lineEditWeight->cursorPosition();
//    calBsa();
//    emit baseInfo(infoList());
//    ui->lineEditWeight->setCursorPosition(pos);

//}

// added by jyq
void BodyInfoWidget::on_lineEditBsa_textEdited(const QString& arg1)
{
    emit baseInfo(infoList());
}

void BodyInfoWidget::on_lineEditHr_textEdited(const QString& arg1)
{
    emit baseInfo(infoList());
}

void BodyInfoWidget::patientInfo_textEdited()
{
    calBsa();
    emit baseInfo(infoList());
}

void BodyInfoWidget::patientInfo_stateChanged(int state)
{
    if (state == Qt::Checked)
    {
        setIMTWidgetVisible(true);
    }
    else
    {
        setIMTWidgetVisible(false);
    }
}
