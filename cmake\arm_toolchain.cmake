set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR arm)
set(CMAKE_CROSSCOMPILING TRUE)
set(CMAKE_SYSROOT /opt/rootfs)
set(SYSROOTPREFIX /opt/rootfs)

set(CMAKE_CXX_COMPILER /usr/bin/aarch64-linux-gnu-g++)
set(CMAKE_C_COMPILER /usr/bin/aarch64-linux-gnu-gcc)

set(Qt5_DIR ${SYSROOTPREFIX}/opt/Qt/5.15.2/aarch64/lib/cmake/Qt5)
set(Qt5_DIRCore ${SYSROOTPREFIX}/opt/Qt/5.15.2/aarch64/lib/cmake/Qt5Core)
set(QT_QMAKE_EXECUTABLE ${SYSROOTPREFIX}/opt/Qt/5.15.2/aarch64/bin/qmake)
set(CMAKE_PREFIX_PATH ${SYSROOTPREFIX}/opt/Qt/5.15.2/aarch64)
set(Boost_INCLUDE_DIR ${SYSROOTPREFIX}/opt/boost_1_75_0)

list(APPEND CMAKE_MODULE_PATH ${SYSROOTPREFIX}/opt/Qt/5.15.2/aarch64/lib/cmake)

list(APPEND CMAKE_FIND_ROOT_PATH ${XUNITDIR}/src/thirdparty/unix_aarch_64bit)

set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)