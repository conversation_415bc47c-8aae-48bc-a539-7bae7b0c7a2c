#ifndef STOREPROGRESSMOVIEWIDGET_H
#define STOREPROGRESSMOVIEWIDGET_H
#include "controls_global.h"

#include "basewidget.h"

class QMovie;

namespace Ui
{
class StoreProgressMovieWidget;
}

class CONTROLSSHARED_EXPORT StoreProgressMovieWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit StoreProgressMovieWidget(QWidget* parent = 0);
    ~StoreProgressMovieWidget();

    void setPositionWidgets(QWidget* FreezeBarW, QWidget* SonoParasW);
    void showSavingMovie(bool inFourDWidget = false);
    void hideProgessMovie();
    void setFourDProgressPos(QWidget* fourdRightWidget);

protected:
    void retranslateUi();

private:
    void setPosAndSize(bool inFourDWidget = false);
    void setup();

signals:
    void isShow();
    void isHide();

private:
    Ui::StoreProgressMovieWidget* ui;
    QMovie* m_gif;
    QSize m_MovieSize;
    QWidget* m_FreezeBarWidget;
    QWidget* m_SonoParasWidget;
    QPoint m_FourDProgressPos;
};

#endif // STOREPROGRESSMOVIEWIDGET_H
