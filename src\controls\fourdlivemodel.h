#ifndef FOURDLIVEMODEL_H
#define FOURDLIVEMODEL_H

#include "controls_global.h"
#include <QObject>
#include "dataarg.h"

class FourDDataProcess;
class IFourDDataSender;
class ImageTile;
class SonoParameters;
class ILineBufferManager;
class FourDVolumeLineImageArgs;

#ifdef USE_IIMAGE
class IImageProcessHelper;
#endif

class CONTROLSSHARED_EXPORT FourDLiveModel : public QObject
{
    Q_OBJECT
public:
    explicit FourDLiveModel(IFourDDataSender* dataSender, QObject* parent = 0);
    ~FourDLiveModel();
    void setContext(ImageTile* context);
    void prepareFourDParameters(bool resetParameters);
    void clearFourDData();
    void setBufferManager(ILineBufferManager* bufferManager);
    void setSonoParameters(SonoParameters* sonoParameters);
    void updateCurrentIndex();
    void sendVolumeData(const FourDVolumeLineImageArgs& arg);
    void zoom(bool value);
    void moveCurvedLineCtrlPoint(bool isVHDon, const QString& direction);

private:
    void connectSignals();
    void disconnectSignals();

private slots:
    void onTimeOut();

private:
    SonoParameters* m_SonoParameters;
    FourDDataProcess* m_FourDDataProcess;
    IFourDDataSender* m_FourDDataSender;
    quint32 m_ROIWidth;
    quint32 m_ROIHeight;
    ILineBufferManager* m_LineBufferManager;
    QTimer* m_timer;
#ifdef USE_IIMAGE
    IImageProcessHelper* m_IImageProcessHelper;
#endif
};

#endif // FOURDLIVEMODEL_H
