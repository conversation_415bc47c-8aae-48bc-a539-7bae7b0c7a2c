#ifndef USERINFO_H
#define USERINFO_H
#include <QString>
#include "adminmodel_global.h"

class ADMINMODELSHARED_EXPORT UserInfo
{
public:
    UserInfo();
    UserInfo(const QString& userid, const QString& username, const QString& pwd, const int authorityid, bool ischanged);

    //    UserInfo &operator =(const UserInfo &user);

    QString userId() const;
    void setUserId(const QString& userId);

    QString userName() const;
    void setUserName(const QString& userName);

    QString password() const;
    void setPassword(const QString& password);

    int authorityId() const;
    void setAuthorityId(int authorityId);

    bool isChanged() const;
    void setIsChanged(bool isChanged);

private:
    QString m_userId;
    QString m_userName;
    QString m_password;
    int m_authorityId;
    bool m_isChanged;
};

#endif // USERINFO_H
