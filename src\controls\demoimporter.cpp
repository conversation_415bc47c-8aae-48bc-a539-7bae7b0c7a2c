#include "demoimporter.h"
#include "util.h"
#include "resource.h"
#include "diskselectionwidget.h"
#include <QDir>
#include <QFileInfo>
#include <QStringList>
#include "crypticfile.h"

const char* DemoImporter::importResultText[] = {QT_TRANSLATE_NOOP("DemoImporter", "Import demo successfully!"),
                                                QT_TRANSLATE_NOOP("DemoImporter", "Import demo failed!")};
const char* DemoImporter::checkResultText[] = {QT_TRANSLATE_NOOP("DemoImporter", "Check demo successfully!"),
                                               QT_TRANSLATE_NOOP("DemoImporter", "Check demo failed!")};

DemoImporter::DemoImporter(bool isAnotherSpecie, QObject* parent)
    : IImporter(parent)
    , m_SpeciesName(Resource::getSpeciesName(isAnotherSpecie))
    , m_ExternalPath(Resource::getExternalSlideShowPath(isAnotherSpecie))
    , m_InternalPath(Resource::getInternalSlideShowPath(isAnotherSpecie))
{
    m_ExternalFiles = getExternalFiles();
    //当前机型的外部路径找不到demo文件,尝试寻找旧方式的路径,兼容旧升级方式
    if (!isAnotherSpecie)
    {
        if (m_ExternalFiles.isEmpty())
        {
            m_ExternalPath = Resource::externalSlideShowDir;
            m_ExternalFiles = getExternalFiles();
        }
    }
}

int DemoImporter::externalFilesCount()
{
    return m_ExternalFiles.count();
}

void DemoImporter::import()
{
    m_ImportResult = importFiles(m_ExternalFiles);
}

void DemoImporter::check()
{
    m_CheckResult = checkFiles();
}

QString DemoImporter::importInfo() const
{
    return m_SpeciesName + ":" + Util::translate("DemoImporter", importResultText[m_ImportResult]);
}

QString DemoImporter::checkInfo() const
{
    return getVersion() + " " + Util::translate("DemoImporter", checkResultText[m_CheckResult]);
}

DemoImporter::ResultType DemoImporter::importResult() const
{
    return m_ImportResult;
}

QFileInfoList DemoImporter::getExternalFiles() const
{
    // load slideshows from usb first
    QString externalPath = DiskSelectionDialog::getUDiskFilePath(m_ExternalPath, false);

    if (!externalPath.isEmpty())
    {
        // copy external usb disk slideshow files to internal path
        // file type filter bmp/jpg/jpeg/png/txt
        static QStringList filter = Util::pictureFilters();
        QStringList externalDemoFilter = filter;
        externalDemoFilter.append("*.txt");

        QDir dir(externalPath);
        QFileInfoList externalFiles = dir.entryInfoList(externalDemoFilter, QDir::Files);
        return externalFiles;
    }
    return QFileInfoList();
}

QString DemoImporter::getVersion() const
{
    QString version;
    QString versionFileName = Resource::getFileFullName("version.txt", m_InternalPath);
    QFile versionFile(versionFileName);
    if (versionFile.open(QFile::ReadOnly))
    {
        QTextStream text(&versionFile);
        if (!text.atEnd())
        {
            version = text.readLine().trimmed();
        }
        versionFile.close();
    }
    return m_SpeciesName + ":" + version;
}

DemoImporter::ResultType DemoImporter::importFiles(const QFileInfoList& externalFiles)
{
    QDir internalPath(m_InternalPath);
    if (!internalPath.exists())
    {
        internalPath.mkpath(".");
    }

    if (!externalFiles.isEmpty())
    {
        // clear internal dir
        QStringList internalFiles = internalPath.entryList();
        foreach (const QString& name, internalFiles)
        {
            internalPath.remove(name);
        }

        foreach (const QFileInfo& toCopy, externalFiles)
        {
            QString newName = internalPath.absolutePath() + QDir::separator() + toCopy.fileName();
            if (!QFile::copy(toCopy.absoluteFilePath(), newName))
            {
                return Failed;
            }
            emit progressValueChanged();
            Util::processEvents(QEventLoop::ExcludeUserInputEvents);
        }
        return Success;
    }
    else
    {
        return Failed;
    }
}

DemoImporter::ResultType DemoImporter::checkFiles()
{
    // create md5 hash from file
    QHash<QString, QString> md5Hash;
    QString md5FileName = Resource::getFileFullName("md5.txt", m_InternalPath);
    QFile md5File(md5FileName);
    if (md5File.open(QFile::ReadOnly))
    {
        QTextStream text(&md5File);
        while (!text.atEnd())
        {
            QStringList lineList = text.readLine().trimmed().split(" ", Qt::SkipEmptyParts);
            if (lineList.count() == 2)
            {
                QString key = lineList.first().trimmed();
                QString value = lineList.last().trimmed();
                if (!key.isEmpty())
                {
                    if (!md5Hash.values().contains(value))
                    {
                        md5Hash.insert(key, value);
                    }
                }
            }
        }
        md5File.close();
    }
    if (md5Hash.isEmpty())
    {
        return Failed;
    }

    // generate and verify md5
    foreach (const QString& name, md5Hash.values())
    {
        QString fullName = m_InternalPath + Resource::pathSeparator + name;
        QString md5 = QString(CrypticFile::generateMd5(fullName).toHex());
        if (md5Hash.value(md5) != name)
        {
            return Failed;
        }
    }

    return Success;
}
