#ifndef TREEVIEWWIDGET_H
#define TREEVIEWWIDGET_H
#include "archive_global.h"

//#include <QWidget>
#include "basewidget.h"
#include <QAbstractItemView>
#include <QScroller>
class TreeViewButton;
namespace Ui
{
class TreeViewWidget;
}

class ARCHIVESHARED_EXPORT TreeViewWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit TreeViewWidget(QWidget* parent = 0);
    ~TreeViewWidget();
    void setModel(QAbstractItemModel* model);
    void setSelectionMode(QAbstractItemView::SelectionMode mode);
    void clearSelection();
    QItemSelectionModel* selectionModel() const;
    QAbstractItemView::SelectionMode selectionMode() const;
    void setColumnWidth(int column, int width);
    void hideColumn(int column);
    void setRootIsDecorated(bool show);
    bool isExpanded(const QModelIndex& index) const;
    void setExpanded(const QModelIndex& index, bool expand);
    /**
     * @brief setButtonVisble 将TreeView上的按钮隐藏
     * @param visible
     * @return
     */
    void setButtonVisible(bool visible);
    TreeViewButton* buttonWidget() const;
public slots:
    void expandAll();
    void collapseAll();
    void selectAll();

protected:
    void retranslateUi();
signals:
    void clicked(const QModelIndex& index);
    void doubleClicked(const QModelIndex& index);

private:
    Ui::TreeViewWidget* ui;
    QScroller* m_scroller;
};

#endif // TREEVIEWWIDGET_H
