#ifndef MODECHANGEWIDGET_H
#define MODECHANGEWIDGET_H

#include "controls_global.h"
#include "modechangemodel.h"
#include <QFrame>
#include <QToolButton>

class ModeChangeModel;
class IStateManager;

namespace Ui
{
class ModeChangeWidget;
}

class CONTROLSSHARED_EXPORT ModeChangeWidget : public QFrame
{
    Q_OBJECT

public:
    explicit ModeChangeWidget(QWidget* parent = 0);
    ~ModeChangeWidget();
    void setModel(ModeChangeModel* value);
    void setStateManager(IStateManager* value);
    void setUpAndDownArrowState(bool UpState, bool DownState);
private slots:
    void onModeShowChanged();
    void onFlashArrow(int dir);
    void highLightToolButton();
    void lowLightToolButton();
    void onSetLRArrowState();
    void onSetUDArrowState();
    void onSetUpArrowState();
    void onSetDownArrowState();

    void on_toolBtn_Up_clicked();
    void on_toolBtn_Down_clicked();
    void on_toolBtn_Left_clicked();
    void on_toolBtn_Right_clicked();

private:
    void flashArrow(QToolButton* button);
    void arrowStateIcon(QIcon& icon, int dir, bool isEnabled);

private:
    Ui::ModeChangeWidget* ui;
    ModeChangeModel* m_model;
    QToolButton* m_flashButton;
    bool m_isUpArrowEnabled;
    bool m_isDownArrowEnabled;
    bool m_isLRArrowEnabled;
    bool m_isFlashUpArrow;
    bool m_isFlashDownArrow;
    bool m_isFlashLRArrow;
    QIcon m_pressIcon;
    IStateManager* m_StateManager;
};

#endif // MODECHANGEWIDGET_H
