#ifndef AUTOBLADDERALG_H
#define AUTOBLADDERALG_H

#include "algorithm_global.h"
#include "resource.h"
#include "virtualalg.h"
#include <QLibrary>
#include <QLine>
#include <QList>
#include <QString>
#include <QVariant>
#include <array>

class ALGORITHMSHARED_EXPORT AutoBladderAlg : public VirtualAlg
{
    friend class AlgInstance;

public:
    ~AutoBladderAlg();

    inline bool isError(void)
    {
        return m_IsError;
    }
    int calcTransverseSection(uint8_t* imgin, int width, int height, QLine& line1, QLine& line2);
    int calcLongitudinalSection(uint8_t* imgin, int width, int height, QLine& line);
    int calcLongitudinalSectionDiff(uint8_t* imgin, int width, int height, QLine& line);
    void setIsLFInvert(int IsLFInvert);
    void setIsUDInvert(int IsUDInvert);

protected:
    void call_create_bladder_context(void);
    void call_remove_bladder_context(void);
    int call_bladder_auto_measure(uint8_t* imgin, int width, int height, int* measure_result, int flipul, int fliplr);
    int call_bladder_auto_measure2(uint8_t* imgin, int width, int height, int* measure_result, int flipul, int fliplr);
    int call_bladder_auto_measure3(uint8_t* imgin, int width, int height, int* measure_result, int flipul, int fliplr);

private:
    AutoBladderAlg();

public:
    int m_IsLFInvert;
    int m_IsUDInvert;
};
#endif // AUTOBLADDERALG_H
