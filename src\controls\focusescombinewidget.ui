<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FocusesCombineWidget</class>
 <widget class="QWidget" name="FocusesCombineWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>287</width>
    <height>458</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_3">
   <item row="0" column="0" colspan="3">
    <layout class="QGridLayout" name="gridLayout_2">
     <item row="1" column="0">
      <widget class="QLabel" name="label_4">
       <property name="text">
        <string>Focus Pos</string>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="label">
       <property name="text">
        <string>Combine Start</string>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QSpinBox" name="spinBoxCombineStart">
       <property name="suffix">
        <string>%</string>
       </property>
       <property name="maximum">
        <number>100</number>
       </property>
       <property name="value">
        <number>20</number>
       </property>
      </widget>
     </item>
     <item row="3" column="0">
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>Combine Len</string>
       </property>
      </widget>
     </item>
     <item row="3" column="1">
      <widget class="QSpinBox" name="spinBoxCombineLen">
       <property name="suffix">
        <string>%</string>
       </property>
       <property name="maximum">
        <number>100</number>
       </property>
       <property name="value">
        <number>20</number>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QLabel" name="labelFocusPos">
       <property name="text">
        <string>0</string>
       </property>
      </widget>
     </item>
     <item row="0" column="0">
      <widget class="QLabel" name="label_3">
       <property name="text">
        <string>Focus Num</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QLabel" name="labelFocusNum">
       <property name="text">
        <string>1</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="0" colspan="3">
    <layout class="QGridLayout" name="gridLayout"/>
   </item>
   <item row="2" column="1">
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="3" column="0">
    <widget class="QPushButton" name="pushButtonDefault">
     <property name="text">
      <string>Default</string>
     </property>
    </widget>
   </item>
   <item row="3" column="1">
    <widget class="QPushButton" name="pushButtonReload">
     <property name="text">
      <string>Reload</string>
     </property>
    </widget>
   </item>
   <item row="3" column="2">
    <widget class="QPushButton" name="pushButtonSave">
     <property name="text">
      <string>Save</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
