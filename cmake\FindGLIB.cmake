
if(WIN32)
    # Win32 Glib文件放置在源代码的目录下的第三方库目录

    set(GSTREAMER_ROOT ${THIRDPARTYLIB_PATH}/gstreamer/)
    set(GLIB_INCLUDE_DIRS ${GSTREAMER_ROOT}/include/
                        ${GSTREAMER_ROOT}/lib/glib-2.0/include
                        ${GSTREAMER_ROOT}/include/glib-2.0/ CACHE STRING "glib include")
    set(GLIB_LIBRARIES ${GSTREAMER_ROOT}/lib/glib-2.0.lib  CACHE STRING "glin libraries")
    set(GLIB_GOBJECT_LIBRARIES ${GSTREAMER_ROOT}/lib/gobject-2.0.lib CACHE STRING "glib object libraries")
else()
    find_package(PkgConfig)
    pkg_check_modules(GLIB2 glib-2.0)
    find_library(GLIB_LIBRARIES
            NAMES glib-2.0
            HINTS ${GLIB2_LIBDIR}
            ${GLIB2_LIBRARY_DIRS}
            )
    # Files in glib's main include path may include glibconfig.h, which,
    # for some odd reason, is normally in $LIBDIR/glib-2.0/include.
    get_filename_component(_GLIB_LIBRARY_DIR ${GLIB_LIBRARIES} PATH)
    find_path(GLIBCONFIG_INCLUDE_DIR
            NAMES glibconfig.h
            HINTS ${PC_LIBDIR} ${PC_LIBRARY_DIRS} ${_GLIB_LIBRARY_DIR}
            PATH_SUFFIXES glib-2.0/include
            )
    find_path(GLIB_INCLUDE_DIR
            NAMES glib.h
            HINTS ${GLIB2_INCLUDEDIR}
            ${GLIB2_INCLUDE_DIRS}
            PATH_SUFFIXES glib-2.0
            )
    set(GLIB_INCLUDE_DIRS ${GLIB_INCLUDE_DIR} ${GLIBCONFIG_INCLUDE_DIR})
    # Version detection
    file(READ "${GLIBCONFIG_INCLUDE_DIR}/glibconfig.h" GLIBCONFIG_H_CONTENTS)
    string(REGEX MATCH "#define GLIB_MAJOR_VERSION ([0-9]+)" _dummy "${GLIBCONFIG_H_CONTENTS}")
    set(GLIB_VERSION_MAJOR "${CMAKE_MATCH_1}")
    string(REGEX MATCH "#define GLIB_MINOR_VERSION ([0-9]+)" _dummy "${GLIBCONFIG_H_CONTENTS}")
    set(GLIB_VERSION_MINOR "${CMAKE_MATCH_1}")
    string(REGEX MATCH "#define GLIB_MICRO_VERSION ([0-9]+)" _dummy "${GLIBCONFIG_H_CONTENTS}")
    set(GLIB_VERSION_MICRO "${CMAKE_MATCH_1}")
    set(GLIB_VERSION "${GLIB_VERSION_MAJOR}.${GLIB_VERSION_MINOR}.${GLIB_VERSION_MICRO}")
    # Additional Glib components. We only look for libraries, as not all of them
    # have corresponding headers and all headers are installed alongside the main
    # glib ones.
    set(GLIB_FIND_COMPONENTS gio gobject gmodule gthread)
    foreach (_component ${GLIB_FIND_COMPONENTS})
        if (${_component} STREQUAL "gio")
            find_library(GLIB_GIO_LIBRARIES NAMES gio-2.0 HINTS ${_GLIB_LIBRARY_DIR})
            set(ADDITIONAL_REQUIRED_VARS ${ADDITIONAL_REQUIRED_VARS} GLIB_GIO_LIBRARIES)
        elseif (${_component} STREQUAL "gobject")
            find_library(GLIB_GOBJECT_LIBRARIES NAMES gobject-2.0 HINTS ${_GLIB_LIBRARY_DIR})
            set(ADDITIONAL_REQUIRED_VARS ${ADDITIONAL_REQUIRED_VARS} GLIB_GOBJECT_LIBRARIES)
        elseif (${_component} STREQUAL "gmodule")
            find_library(GLIB_GMODULE_LIBRARIES NAMES gmodule-2.0 HINTS ${_GLIB_LIBRARY_DIR})
            set(ADDITIONAL_REQUIRED_VARS ${ADDITIONAL_REQUIRED_VARS} GLIB_GMODULE_LIBRARIES)
        elseif (${_component} STREQUAL "gthread")
            find_library(GLIB_GTHREAD_LIBRARIES NAMES gthread-2.0 HINTS ${_GLIB_LIBRARY_DIR})
            set(ADDITIONAL_REQUIRED_VARS ${ADDITIONAL_REQUIRED_VARS} GLIB_GTHREAD_LIBRARIES)
        endif ()
    endforeach ()
    include(FindPackageHandleStandardArgs)
    FIND_PACKAGE_HANDLE_STANDARD_ARGS(GLIB REQUIRED_VARS GLIB_INCLUDE_DIRS GLIB_LIBRARIES ${ADDITIONAL_REQUIRED_VARS}
    VERSION_VAR GLIB_VERSION)
endif(WIN32)

message("Found GLIB_INCLUDE_DIRS headers: ${GLIB_INCLUDE_DIRS}")
message("Found GLIB_LIBRARIES libs: ${GLIB_LIBRARIES}")
message("Found GLIB_GOBJECT_LIBRARIES libs: ${GLIB_GOBJECT_LIBRARIES}")
