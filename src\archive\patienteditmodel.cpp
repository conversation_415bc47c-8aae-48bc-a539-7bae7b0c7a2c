#include "patienteditmodel.h"
#include "patienteditwidget.h"
#include "util.h"
#include "patientworkflow.h"
#include "patientworkflowmodel.h"
#include "patientmanagement.h"
#include "model/patient.h"
#include "model/study.h"
#include "modeldirectorygetter.h"
#include "dataaccesslayerhelper.h"
#include "generalworkflowfunction.h"
#include "appsetting.h"
#include "messageboxframe.h"
#ifdef USE_ADMINVIEW
#include "adminconfigmodel.h"
#endif
#include "mppssendcontrol.h"

/**
 * @brief PatientEditModel newpatient界面开始运行，有两种情况：第一，是通过archive界面、easyview
 *        界面上的patientinfo按钮进入该界面，界面只能查看病人的信息不能修改，但是newstudy、
 *        newpatient、cancel等按钮可用，界面的信息是根据当前的检查从数据库读取，如果选择的新建病人
 *        或新建检查，当前的界面信息可以修改；
 *		  第二，新建检查、新建病人或继续检查进入该界面，界面的内容可以修该，如要新建病人清空所有的
 *        信息，同时id更新；新建检查时只清空与检查有关的内容；
 * @param parent
 */
PatientEditModel::PatientEditModel(IStateManager* stateManager, QWidget* parent)
    : QObject(parent)
    , m_createPatient(true)
    , m_PatientWorkflow(NULL)
    , m_PatientEditDialog(NULL)
    , m_GeneralWorkflowFunction(NULL)
    , m_MppsSendControl(NULL)
    , m_Patient(NULL)
    , m_IsNewPatientId(true)
    , m_bNewStudy(false)
    , m_IsEnded(false)
{
    m_MppsSendControl = new MppsSendControl(this);
    m_PatientEditDialog = new PatientEditDialog(stateManager, parent);
    connect(m_PatientEditDialog, SIGNAL(buttonClicked(int)), this, SLOT(onButtonClicked(int)));
    connect(m_PatientEditDialog, SIGNAL(finished(int)), this, SIGNAL(finished(int)));
    connect(m_PatientEditDialog, SIGNAL(onlyJump()), this, SIGNAL(onlyJump()));
    connect(m_PatientEditDialog, SIGNAL(entryArchiveState()), this, SIGNAL(entryArchiveState()));
    connect(m_PatientEditDialog, SIGNAL(closed()), this, SLOT(onClose()));
    connect(m_PatientEditDialog, SIGNAL(isNewPatientId(bool)), this, SLOT(onIsNewPatientId(bool)));
    connect(this, SIGNAL(clearPatientInfo()), m_PatientEditDialog, SIGNAL(clearPatientInfo()));
    connect(this, SIGNAL(clearStudyInfo()), m_PatientEditDialog, SIGNAL(clearStudyInfo()));
}

PatientEditModel::~PatientEditModel()
{
    //    Util::SafeDeletePtr(m_PatientEditDialog);
    // PatientWorkflow is responsiable for deleting m_Patient. Don't delete it here.
    // Util::SafeDeletePtr(m_Patient);
}

PatientEditDialog* PatientEditModel::patientEditDialog() const
{
    return m_PatientEditDialog;
}

void PatientEditModel::setPatientWorkflow(PatientWorkflow* patientWorkflow)
{
    m_PatientWorkflow = patientWorkflow;
    m_PatientEditDialog->setPatientWorkflow(patientWorkflow);
    if (m_PatientWorkflow != NULL)
    {
        connect(m_PatientWorkflow, SIGNAL(currentExamEnded()), this, SLOT(onCurrentExamEnded()));
    }
}

void PatientEditModel::backstageCreatePatient()
{
    if (m_PatientWorkflow->patient() == NULL)
    {
        Patient* patient = PatientManagement::createPatient();

        //[BUG:56763] 产品序号：293【3D UI】【莫总反馈】Emergency登录，新建病人扫图End后，进Archive无记录
        //原因：主界面保存电影或图片值，后台创建的一个病人的Study没有设置当前系统的UserId，
        //     导致insert到数据库中的Study信息UserId为空，查询数据库的时候，就查询不到。
        //解决办法：后台创建一个病人的时候，设置一下Study的UserId
        //影响范围：后台创建病人插入数据库
#ifdef USE_ADMINVIEW
        QList<Study*> studies = patient->Studies();
        foreach (Study* study, studies)
        {
            study->setUserId(AdminConfigModel::instance()->getCurUserId());
        }
#endif
        PatientManagement::writeMaxPatientIdFile();
        savePatient(patient);
        m_PatientWorkflow->updatePatientInfo();
    }
    else
    {
        //每次都尝试创建Study目录，因为在Review中删除完所有图片，会删除Study目录
        Util::Mkdir(PatientPath::instance().path(*m_PatientWorkflow->patient()));
    }
}

void PatientEditModel::savePatient(Patient* patient)
{
    if (patient != NULL)
    {
        // TODO:
        if (m_PatientWorkflow->patient() != patient)
        {
            m_PatientWorkflow->setPatient(patient);
            Util::Mkdir(PatientPath::instance().path(*patient), false);
            DataAccessLayerHelper::save(*patient);
        }
    }
}

void PatientEditModel::createStudy(Patient* patient)
{
    Study* study = NULL;
    if (patient != NULL)
    {
        study = PatientManagement::createStudy(patient, QDateTime(), true);
        if (study != NULL)
        {
            m_PatientEditDialog->createPatient(patient);
#ifdef USE_ADMINVIEW
            study->setUserId(AdminConfigModel::instance()->getCurUserId());
#endif
            DataAccessLayerHelper::save(*patient->firstStudy());
            DataAccessLayerHelper::update(*patient);
            Util::Mkdir(PatientPath::instance().path(*patient));
        }
    }
}

void PatientEditModel::setPatientDialogButtonEnable()
{
    if (m_PatientWorkflow->patient() != NULL)
    {
        m_PatientEditDialog->setButtonEnabled(true, PatientEditDialog::button_end);
    }
    else
    {
        m_PatientEditDialog->setButtonEnabled(false, PatientEditDialog::button_end);
    }
    m_PatientEditDialog->show();
    m_PatientEditDialog->setCurrentWidgetEnabled(true);
}

void PatientEditModel::exec()
{
    m_IsNewPatientId = true;
    m_createPatient = true;
    m_IsEnded = false;
    if (m_StudyOid.isEmpty())
    {
        m_PatientEditDialog->setPatient(m_PatientWorkflow->patient());
    }
    setPatientDialogButtonEnable();

    m_PatientEditDialog->activateWindow();
    m_PatientEditDialog->setButtonEnabled(true, PatientEditDialog::button_patient);
    m_PatientEditDialog->setButtonEnabled(true, PatientEditDialog::button_study);
    m_PatientEditDialog->setButtonEnabled(true, PatientEditDialog::button_ok);
}

void PatientEditModel::exec(Patient* patient)
{
    m_PatientEditDialog->setPatient(patient);

    m_PatientEditDialog->setButtonEnabled(false, PatientEditDialog::button_patient);
    m_PatientEditDialog->setButtonEnabled(false, PatientEditDialog::button_study);
    m_PatientEditDialog->setButtonEnabled(false, PatientEditDialog::button_end);
    m_PatientEditDialog->setButtonEnabled(false, PatientEditDialog::button_ok);
    m_PatientEditDialog->setCurrentWidgetEnabled(false);

    m_PatientEditDialog->show();
    m_PatientEditDialog->raise();
}

bool PatientEditModel::isPatientExist()
{
    QString patientID = m_PatientEditDialog->lineEditPatientId().trimmed();
    return DataAccessLayerHelper::isPatientExists(patientID);
}

void PatientEditModel::updateStudy()
{
    if (m_PatientWorkflow->patient() == NULL)
    {
        return;
    }

    m_PatientEditDialog->createPatient(m_PatientWorkflow->patient());
    DataAccessLayerHelper::update(*m_PatientWorkflow->patient());
}

void PatientEditModel::createStudyFromWorkflow()
{
    if (m_PatientWorkflow->patient() == NULL)
    {
        return;
    }

    m_IsEnded = true;
    m_PatientWorkflow->endStudy();
    createStudy(m_PatientWorkflow->patient());
    m_PatientWorkflow->onNewStudy();
}

void PatientEditModel::createNewPatient(const QString& patientId)
{
    m_IsEnded = true;
    m_PatientWorkflow->setCurrentExamEnd();
    Patient* patient = PatientManagement::createPatient(patientId);
    if (m_IsNewPatientId)
    {
        PatientManagement::writeMaxPatientIdFile();
        m_IsNewPatientId = false;
        Study* study = patient->firstStudy();
        if (study != NULL)
        {
#ifdef USE_ADMINVIEW
            study->setUserId(AdminConfigModel::instance()->getCurUserId());
#endif
        }
    }
    m_PatientEditDialog->createPatient(patient);
    savePatient(patient);
}

void PatientEditModel::createStudyFromPatientID()
{
    QString patientId = m_PatientEditDialog->lineEditPatientId().trimmed();

    m_IsEnded = true;
    m_PatientWorkflow->setCurrentExamEnd();
    // PatientWorkflow is responsiable for deleting m_Patient. Don't delete it here.
    m_Patient = DataAccessLayerHelper::getPatient(patientId, false);
    createStudy(m_Patient);
    m_PatientWorkflow->setPatient(m_Patient);
}

void PatientEditModel::checkEndedState()
{
    if (m_IsEnded)
    {
        emit entryContinueExamState();
        m_IsEnded = false;
    }
}

void PatientEditModel::sendMPPSCmd(bool ncreate)
{
    if (!m_PatientEditDialog->mppsDcmFileName().isEmpty())
    {
        if (ncreate) // N-CREATE Cmd
        {
            m_MppsSendControl->doMppsNCreateTask(m_PatientEditDialog->mppsDcmFileName());
        }
        else // N-SET Cmd
        {
            m_MppsSendControl->doMppsNSetTask();
        }
    }
}

/*
 * Notes for PatientEditModel::create()

 * 1. m_bNewStudy:
 * This flag is set to true if "New Study" on PatientEditWidget has been clicked.
 * It will be reset to default(false) when window closed & "End Study" clicked.

 * 2. An existing patient can be loaded to the widget via below methods,
 * (1). Modify the patient ID;
 * (2). DICOM worklist;
 * (3). Click "New Exam" from Archive widget or EasyView.
 *
 * Note the method (3) will end any current exam before PatientEditWidget pops up.
 * So the method (3) belongs to the condition of no exam in progress.
 */
void PatientEditModel::create()
{
    QString patientId = m_PatientEditDialog->lineEditPatientId().trimmed();

    // If patient does not exist. Todo: Create patient with study.
    if (!isPatientExist())
    {
        if (!patientId.isEmpty())
        {
            createNewPatient(patientId);
        }
        m_PatientWorkflow->updatePatientInfo();
        sendMPPSCmd();
        return;
    }

    // If patient exists. Todo: Create or update study.
    if (m_PatientWorkflow->patient() != NULL)
    {
        // An exam is in progress.
        if (m_PatientWorkflow->patient()->PatientId() == patientId)
        {
            // Current patient.
            if (m_bNewStudy)
            {
                //"New Study" button clicked.
                createStudyFromWorkflow();
            }
            else
            {
                //"New Study" button not clicked.
                updateStudy();
            }
        }
        else
        {
            // An existing different patient has been loaded to the widget.
            createStudyFromPatientID();
        }
    }
    else
    {
        // No exam is in progress.
        // An existing patient has been loaded to the widget.
        createStudyFromPatientID();
    }
    m_PatientWorkflow->updatePatientInfo();
    sendMPPSCmd();
    return;
}

void PatientEditModel::onButtonClicked(int index)
{
    switch (index)
    {
    case PatientEditDialog::button_patient:
        m_createPatient = true;
        m_IsNewPatientId = true;
        clearStudyOid();
        m_PatientEditDialog->setCurrentWidgetEnabled(true);
        emit clearPatientInfo();
        break;
    case PatientEditDialog::button_study:
        m_createPatient = false;
        m_bNewStudy = true;
        m_PatientEditDialog->setCurrentWidgetEnabled(true);
        emit clearStudyInfo();
        break;
    case PatientEditDialog::button_end:
        m_createPatient = true;
        m_IsNewPatientId = true;
        m_bNewStudy = false;
        m_IsEnded = true;
        emit clearPatientInfo();
        m_PatientWorkflow->setCurrentExamEnd();
        m_PatientEditDialog->setButtonEnabled(false, PatientEditDialog::button_end);
        break;
    case PatientEditDialog::button_ok:
    {
        DataAccessLayerHelper::setDbFileName();
        if (m_PatientEditDialog->lineEditPatientId().trimmed().isEmpty())
        {
            int ret = QMessageBox::Cancel;
            if (AppSetting::isAnimalAndChinese())
            {
                ret = MessageBoxFrame::warning(tr("Please input animal ID!"));
            }
            else
            {
                ret = MessageBoxFrame::warning(tr("Please input patient ID!"));
            }

            if (ret)
            {
                m_PatientEditDialog->activateWindow();
            }
        }
        else if (!m_PatientEditDialog->isNameLengthValid())
        {
            int ret = QMessageBox::Cancel;
            if (AppSetting::isAnimalAndChinese())
            {
                ret = MessageBoxFrame::question(static_cast<QWidget*>(m_PatientEditDialog),
                                                tr("Length exceeds max value."),
                                                tr("The Length of animal name(including separators) "
                                                   "exceeds 64 characters.\n"
                                                   "Please refer to PersonName in DICOM standard.\n"
                                                   "Click \"Yes\" to truncate the name.\n"
                                                   "Click \"Cancel\" to modify the name"),
                                                QMessageBox::Yes | QMessageBox::Cancel, QMessageBox::Cancel);
            }
            else
            {
                ret = MessageBoxFrame::question(static_cast<QWidget*>(m_PatientEditDialog),
                                                tr("Length exceeds max value."),
                                                tr("The Length of patient name(including separators) "
                                                   "exceeds 64 characters.\n"
                                                   "Please refer to PersonName in DICOM standard.\n"
                                                   "Click \"Yes\" to truncate the name.\n"
                                                   "Click \"Cancel\" to modify the name"),
                                                QMessageBox::Yes | QMessageBox::Cancel, QMessageBox::Cancel);
            }

            switch (ret)
            {
            case QMessageBox::Yes:
                create();
                m_PatientEditDialog->accept();
                emit createNewExam();
                break;
            case QMessageBox::Cancel:
            default:
                break;
            }
        }
        else
        {
            create();
            m_PatientEditDialog->accept();
            emit createNewExam();
        }
    }
    break;
    case PatientEditDialog::button_cancel:
        m_PatientEditDialog->reject();
        break;
    }
}

void PatientEditModel::clearStudyOid()
{
    m_StudyOid.clear();
    m_DiskPath.clear();
    DataAccessLayerHelper::setDbFileName();
}

void PatientEditModel::onClose()
{
    m_StudyOid.clear();
    m_DiskPath.clear();
    // BUGID:11700 只有在archive界面选择u盘目录,点击patientInfo，在patientEdit页面时DBFileName才可能为U盘目录。
    // 关闭archive页面时，DBFileName会恢复为本地目录,所以此处没有必要设置DBFileName为默认值。

    m_bNewStudy = false;

    checkEndedState();

    m_PatientEditDialog->clearFocus();
}

void PatientEditModel::onCurrentExamEnded()
{
    sendMPPSCmd(false);
    m_PatientEditDialog->clearMPPSDcmFileName();
}

void PatientEditModel::temporaryPatient()
{
    GeneralWorkflowFunction::changeDbFilepath(m_DiskPath);
    Study* study = DataAccessLayerHelper::getStudy(m_StudyOid);

    if (study != NULL)
    {
        Patient* patient = DataAccessLayerHelper::getPatient(study->getPatient()->PatientId(), false);
        GeneralWorkflowFunction::changeDbFilepath();
        {
            /* 不保留病人的HR和FHR的参数值 */
            patient->setStudies(QList<Study*>() << study);
            patient->firstStudy()->setHR(0);
            patient->firstStudy()->setFHR(0);
        }
        m_PatientEditDialog->setPatient(patient);
        delete patient;
        delete study;
    }
}

void PatientEditModel::onSetStudyOid(const QString& id)
{
    m_StudyOid = id;
    temporaryPatient();
}

void PatientEditModel::onCurrentDiskPathChanged(const QString& diskPath)
{
    m_DiskPath = diskPath;
}

void PatientEditModel::onIsNewPatientId(bool isNew)
{
    m_IsNewPatientId = isNew;

    // BUG:18730 当该病人正在检查中，进入Patient Information修改Patient ID，之前病人部分输入信息未被清除或者归零
    //当病人ID发生变化时，确认新的病人Id和正在检查的病人ID是否为同一Id，如果不是，清空检查信息，如果是，将当前检查信息显示到界面中
    QString patientId = m_PatientEditDialog->lineEditPatientId().trimmed();
    if (m_PatientWorkflow != NULL && m_PatientWorkflow->patient() != NULL &&
        m_PatientWorkflow->patient()->PatientId() == patientId)
    {
        m_PatientEditDialog->setPatient(m_PatientWorkflow->patient());
    }
    else
    {
        emit clearStudyInfo();
    }
}

GeneralWorkflowFunction* PatientEditModel::generalWorkflowFunction() const
{
    return m_GeneralWorkflowFunction;
}

void PatientEditModel::setGeneralWorkflowFunction(GeneralWorkflowFunction* generalWorkflowFunction)
{
    m_GeneralWorkflowFunction = generalWorkflowFunction;
}
