#include "fourdqualitywidget.h"
#include "ui_fourdqualitywidget.h"
#include "fourdparascontainer.h"
#include "bfpnames.h"

FourDQualityWidget::FourDQualityWidget(QWidget* parent)
    : FourDParaSetBaseWidget(parent)
    , ui(new Ui::FourDQualityWidget)
{
    ui->setupUi(this);
    m_ParaType = FourDParasInfo::FourDQuality;
    m_RelateParasName = BFPNames::FourDQualityIndexStr;
}

FourDQualityWidget::~FourDQualityWidget()
{
    delete ui;
}

void FourDQualityWidget::initalize()
{
}

void FourDQualityWidget::setFourDQualityPara(const FourDParasInfo::FourDQualityPara& para)
{
    m_FourDQualityPara = para;
    ui->qualityLineEdit->setText(QString::number(para.m_QualityValue));
    ui->densityComboBox->setCurrentIndex(para.m_DensityIndex);
    ui->lineSpaceLineEdit->setText(QString::number(para.m_LineSpaceValue));
}

FourDQualityPara FourDQualityWidget::fourDQualityPara()
{
    FourDQualityPara para = m_FourDQualityPara;
    para.m_QualityValue = ui->qualityLineEdit->text().toFloat();
    para.m_DensityIndex = ui->densityComboBox->currentIndex();
    para.m_LineSpaceValue = ui->lineSpaceLineEdit->text().toFloat();
    return para;
}

void FourDQualityWidget::retranslateUi()
{
    ui->retranslateUi(this);
}
