#include "fingerprintoperatorbase.h"

FingerPrintOperatorBase::FingerPrintOperatorBase(QObject* parent)
    : IFingerPrintOperator(parent)
{
}

int FingerPrintOperatorBase::maxCount()
{
    return -1;
}

bool FingerPrintOperatorBase::enroll(const QString& userId)
{
    return false;
}

bool FingerPrintOperatorBase::verify()
{
    return false;
}

void FingerPrintOperatorBase::stop()
{
}

bool FingerPrintOperatorBase::remove(const FingerprintInfo& fpinfo)
{
    return true;
}

const QList<FingerprintInfo> FingerPrintOperatorBase::getFingerprintInfo(const QString& userId) const
{
    return QList<FingerprintInfo>();
}

bool FingerPrintOperatorBase::addFingerprint(const QString& fingerName, int fingerId, int status, const QString& userId)
{
    return true;
}

bool FingerPrintOperatorBase::hasFingerprint()
{
    return true;
}
