#ifndef MPPSSENDCONTROL_H
#define MPPSSENDCONTROL_H

#include <QObject>
#include "archive_global.h"
#include <QRunnable>
#include <QString>

class ARCHIVESHARED_EXPORT MppsSendControl : public QObject
{
    Q_OBJECT
    friend class MppsNCreateTask;
    friend class MppsNSetTask;

public:
    explicit MppsSendControl(QObject* parent = 0);
    ~MppsSendControl();
    void doMppsNCreateTask(const QString& mppDcmFileName);
    void doMppsNSetTask();

private:
    void sendMppsCmd(bool ncreate = true);
signals:
    void mppsFailed();
private slots:
    void onMppsFailed();

private:
    QString m_DcmAttributesFileName;
    QString m_SopInstanceUID;
};

class ARCHIVESHARED_EXPORT MppsNCreateTask : public QRunnable
{
public:
    MppsNCreateTask(MppsSendControl* mppsControl)
        : m_MppsSendControl(mppsControl)
    {
    }

private:
    void run()
    {
        m_MppsSendControl->sendMppsCmd();
    }

    MppsSendControl* m_MppsSendControl;
};

class ARCHIVESHARED_EXPORT MppsNSetTask : public QRunnable
{
public:
    MppsNSetTask(MppsSendControl* mppsControl)
        : m_MppsSendControl(mppsControl)
    {
    }

private:
    void run()
    {
        m_MppsSendControl->sendMppsCmd(false);
    }

    MppsSendControl* m_MppsSendControl;
};

#endif // MPPSSENDCONTROL_H
