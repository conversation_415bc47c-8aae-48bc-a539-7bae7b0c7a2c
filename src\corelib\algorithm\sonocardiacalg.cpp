#include "sonocardiacalg.h"

SonoCardiacAlg::SonoCardiacAlg()
    : VirtualAlg()
{
    m_Lib.setFileName("SonoCardiac64");
    m_CreateContext = NULL;
    m_RemoveContext = NULL;
    m_Process = NULL;
}

int SonoCardiacAlg::CreateSonoCardiacHandle(SonoCardiacHandle* pSonoCardiacHandle, InitInfo64* pInitInfo64)
{
    if (m_CreateContext == NULL)
    {
        m_CreateContext = (createSonoCardiacHandle)m_Lib.resolve("CreateSonoCardiacHandle");
        if (m_CreateContext == NULL)
        {
            m_Initialized = false;
            m_IsError = true;
            return ERROR_CODE_UNKNOWN;
        }
    }

    if (m_Initialized)
    {
        return ERROR_CODE_OK;
    }

    if (m_CreateContext(pSonoCardiacHandle, pInitInfo64) == ERROR_CODE_OK)
    {
        m_Initialized = true;
    }
    else
    {
        m_Initialized = false;
    }
    return m_Initialized ? ERROR_CODE_OK : ERROR_CODE_UNKNOWN;
}

int SonoCardiacAlg::ProcessSonoCardiac(SonoCardiacHandle hSonoCardiacHandle, UserInfo* pUserInfo, int nImageWidth,
                                       int nImageHeight, int nImageDataSize, unsigned char* pImageData,
                                       SonoCardiacResult* pSonoCardiacResult)
{
    if (m_Process == NULL)
    {
        m_Process = (processSonoCardiac)m_Lib.resolve("ProcessSonoCardiac");
    }
    if (m_Process == NULL || !m_Initialized)
    {
        m_IsError = true;
        return ERROR_CODE_UNKNOWN;
    }
    m_Process(hSonoCardiacHandle, pUserInfo, nImageWidth, nImageHeight, nImageDataSize, pImageData, pSonoCardiacResult);
    return ERROR_CODE_OK;
}

int SonoCardiacAlg::ReleaseSonoCardiacHandle(SonoCardiacHandle* pSonoCardiacHandle)
{
    if (m_RemoveContext == NULL)
    {
        m_RemoveContext = (releaseSonoCardiacHandle)m_Lib.resolve("ReleaseSonoCardiacHandle");
    }
    if (m_RemoveContext == NULL)
    {
        m_IsError = true;
        return ERROR_CODE_UNKNOWN;
    }
    m_Initialized = false;
    m_RemoveContext(pSonoCardiacHandle);
    return ERROR_CODE_OK;
}
