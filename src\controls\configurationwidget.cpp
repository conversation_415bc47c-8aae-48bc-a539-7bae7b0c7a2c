#include "configurationwidget.h"
#include "appsetting.h"
#include "bodymarkconfigframe.h"
#include "buttonsboxframe.h"
#include "commentconfigframe.h"
#include "ecoresmanager/ecoresmanager.h"
#include "exammodeconfigframe.h"
#include "isettings.h"
#include "messageboxframe.h"
#include "modeluiconfig.h"
#include "netsettings.h"
#include "netstoragewidget.h"
#include "setting.h"
#include "uiutil.h"
#include "util.h"
#ifdef USE_ADMINVIEW
#include "adminconfigframe.h"
#endif
#include "registerwidget.h"
#include "updatecontroller.h"
#include <QDebug>
#include <QEvent>
#include <QGuiApplication>
#include <QKeyEvent>
#include <QScreen>
#include <QSignalMapper>
ConfigurationDialog::ConfigurationDialog(IStateManager* stateManager, QWidget* parent)
    : BaseInputAbleDialogFrame(parent, true)
    , m_Settings(NULL)
{
    setCategory("Setup");
    setShowTopWidget(true);
    setAutoZOrder(false);
    m_Settings = new ConfigurationWidget(stateManager, this);
    connect(this, SIGNAL(buttonClicked(int)), m_Settings, SLOT(onButtonClicked(int)));

    connect(m_Settings, SIGNAL(orientationChanged(bool)), this, SLOT(onOrientationChanged(bool)));
    connect(m_Settings, SIGNAL(updateDialogFrame()), this, SLOT(update()));
    connect(m_Settings, SIGNAL(accepted()), this, SLOT(accept()));
    connect(m_Settings, SIGNAL(rejected()), this, SLOT(reject()));
    connect(m_Settings, SIGNAL(hwKeyImported()), this, SIGNAL(hwKeyImported()));
    connect(m_Settings, SIGNAL(screenControlTimersStoped()), this, SIGNAL(screenControlTimersStoped()));
    connect(m_Settings, SIGNAL(screenControlTimersStarted()), this, SIGNAL(screenControlTimersStarted()));
    connect(m_Settings, SIGNAL(screenSaverPreviewed(bool, int)), this, SIGNAL(screenSaverPreviewed(bool, int)));
    connect(m_Settings, SIGNAL(systemDateChanged()), this, SIGNAL(systemDateChanged()));
    connect(m_Settings, SIGNAL(signalSetCurVirKeyboardEnglish(bool)), this,
            SIGNAL(signalSetCurVirKeyboardEnglish(bool)));
    connect(this, SIGNAL(lGCEnableChanged(const QVariant&)), m_Settings, SLOT(onLGCEnableChanged(const QVariant&)));
    connect(m_Settings, SIGNAL(endCurState()), this, SIGNAL(endCurState()));

    connect(m_Settings, &ConfigurationWidget::updateContainer, this, [=]() { updateContainerForm(); });

    connect(m_Settings, SIGNAL(ntpButClicked(QString, QString, QString)), this,
            SIGNAL(ntpButClicked(QString, QString, QString)));
    connect(this, SIGNAL(tpButClicked(QString, QString, QString)), m_Settings,
            SIGNAL(tpButClicked(QString, QString, QString)));
    connect(this, SIGNAL(tpButClicked(QString, QString, QString)), this,
            SLOT(onTPButClicked(QString, QString, QString)));
    connect(this, SIGNAL(patientChanged(Patient*)), m_Settings, SIGNAL(patientChanged(Patient*)));

    // this->setContent(m_Settings);
    titleColorUseQss("settingMenuTitleBar");
    containerColorUseQss("settingChildContainer");

    if (m_FullScreenShow)
    {
        this->setFixedSize(QGuiApplication::primaryScreen()->geometry().width(),
                           QGuiApplication::primaryScreen()->geometry().height());
    }
}

ConfigurationDialog::~ConfigurationDialog()
{
}

void ConfigurationDialog::setPackagesMeasurement(PackagesMeasurement* value)
{
    Q_ASSERT(m_Settings != NULL);
    m_Settings->setPackagesMeasurement(value);
}

void ConfigurationDialog::setCommentConfigModel(CommentConfigModel* model)
{
    m_Settings->setCommentConfigModel(model);
}

void ConfigurationDialog::setBodyMarkConfigModel(BodyMarkConfigModel* model)
{
    m_Settings->setBodyMarkConfigModel(model);
}

void ConfigurationDialog::setExamModeConfigModel(ExamModeConfigModel* model)
{
    m_Settings->setExamModeConfigModel(model);
}

void ConfigurationDialog::setKeyboard(IKeyboard* value)
{
    m_Settings->setKeyboard(value);
}

void ConfigurationDialog::setMachine(IMachine* value)
{
    m_Settings->setMachine(value);
}

// void ConfigurationDialog::setScreenType(ScreenTypeDef::ScreenType type)
//{
//    m_Settings->setScreenType(type);
//}

void ConfigurationDialog::setHotKeyModels(HotKeyConfig* config, HotKeyContainer* container)
{
    m_Settings->setHotKeyModels(config, container);
}

void ConfigurationDialog::setDicomTaskManager(DicomTaskManager* value)
{
    m_Settings->setDicomTaskManager(value);
}

void ConfigurationDialog::setImageTile(ImageTile* tile)
{
    m_Settings->setImageTile(tile);
}

void ConfigurationDialog::setMotorControl(MotorControl* motorCtrl)
{
    m_Settings->setMotorControl(motorCtrl);
}

void ConfigurationDialog::runSystemUserImpl()
{
    m_Settings->runSystemUserImpl();
}

bool ConfigurationDialog::isDefault() const
{
    return m_Settings->isDefault();
}

void ConfigurationDialog::saveDefault() const
{
    m_Settings->saveDefault();
}

void ConfigurationDialog::setLGCVisible(bool isVisible)
{
    m_Settings->setLGCVisible(isVisible);
}

void ConfigurationDialog::setUpdateController(UpdateController* updateController)
{
    m_Settings->setUpdateController(updateController);
}

void ConfigurationDialog::setBeamFormerTool(IBeamFormerTool* beamFormerTool)
{
    m_Settings->setBeamFormerTool(beamFormerTool);
}

void ConfigurationDialog::activeMaintenanceWidget()
{
    m_Settings->activeMaintenanceWidget();
}

bool ConfigurationDialog::isValidMaintenanceKey()
{
    return m_Settings->isValidMaintenanceKey();
}

void ConfigurationDialog::setDiskDevice(IDiskDevice* diskDevice)
{
    m_Settings->setDiskDevice(diskDevice);
}

void ConfigurationDialog::setColorMapManager(IColorMapManager* colorMapManager)
{
    m_Settings->setColorMapManager(colorMapManager);
}

void ConfigurationDialog::setProbeDataSet(IProbeDataSet* value)
{
    m_Settings->setProbeDataSet(value);
}

void ConfigurationDialog::onOrientationChanged(bool isHor)
{
    if (m_ButtonsBoxFrame != nullptr)
    {
        m_ButtonsBoxFrame->setIsHor(isHor);
    }
}

void ConfigurationDialog::showEvent(QShowEvent* e)
{
    static bool first = true;
    if (m_Settings != NULL && first)
    {
        first = false;
        this->setContent(m_Settings);
    }
    BaseInputAbleDialogFrame::showEvent(e);
}

// ConfigurationWidget
bool ConfigurationWidget::m_IsNeedChange = false;
ConfigurationWidget::ConfigurationWidget(IStateManager* stateManager, QWidget* parent)
    : BaseWidget(parent)
    , m_GeneralSettings(NULL)
    , m_MeasurementSettingWidget(NULL)
    , m_CommentConfigFrame(NULL)
    , m_BodymarkConfigFrame(NULL)
    , m_ExamModeConfigFrame(NULL)
    , m_ReportSettingsWidget(NULL)
    , m_DicomWidget(NULL)
    , m_NetWidget(NULL)
    , m_AdminWidget(NULL)
    , m_SystemInfoWidget(NULL)
    , m_StackedWidget(NULL)
    , m_LayoutV(NULL)
    , m_LayoutH(NULL)
    , m_LayoutGSet(NULL)
    , m_LayoutGTab(NULL)
    , m_LayoutGStackedWidget(NULL)
    , m_Id(0)
    , m_LeftWidget(NULL)
    , m_ActivateId(0)
    , m_Model(ScreenOrientationModel::getInstance())
    , m_Orientation(QGuiApplication::primaryScreen()->orientation())
    , m_IsConfigTransposed(false)
    , m_StateManager(stateManager)
    , m_DiskDevice(NULL)
{
    for (int i = 0; i < TABCOUNT; i++)
    {
        m_TabButtons[i] = NULL;
    }

    m_StackedWidget = new QStackedWidget(this);
    m_IsConfigTransposed = ModelUiConfig::instance().value(ModelUiConfig::IsFullScreenDlgTransposed).toBool();

    initializeWidgets();

    setupUI();

    QSignalMapper* mapper = new QSignalMapper(this);

    for (int i = 0; i < TABCOUNT; i++)
    {
        if (m_TabButtons[i] == NULL)
        {
            continue;
        }
        connect(m_TabButtons[i], SIGNAL(clicked()), mapper, SLOT(map()));
        mapper->setMapping(m_TabButtons[i], i);
    }

    connect(mapper, SIGNAL(mapped(int)), this, SIGNAL(buttonClicked(int)));

    connect(this, SIGNAL(buttonClicked(int)), this, SLOT(onTabButtonClicked(int)));

    connect(AppSetting::eventObject(), SIGNAL(dicomStatusChanged(bool)), this, SLOT(onDicomStautsChanged(bool)));
    connect(m_MeasurementSettingWidget, SIGNAL(packagesMeasurementModified(const PackagesMeasurement*)),
            m_ReportSettingsWidget, SLOT(updatePackagesMeasurement(const PackagesMeasurement*)));
    connect(this, SIGNAL(tpButClicked(QString, QString, QString)), this,
            SLOT(onTPButClicked(QString, QString, QString)));

    onDicomStautsChanged(AppSetting::isDicom());

    setWindowTitle(tr("Setup"));

    // Modification: Change the main interface button from Cancle to Exit
    m_ButtonsText << ""
                  << "" << QT_TR_NOOP("Default") << QT_TR_NOOP("OK") << QT_TR_NOOP("Exit");
    m_ButtonsName << ""
                  << ""
                  << "Default"
                  << "OK"
                  << "Exit";

    if (ModelUiConfig::instance().value(ModelUiConfig::IsSupportRotation).toBool())
    {
        orientationChanged(QGuiApplication::primaryScreen()->orientation());
    }
}

ConfigurationWidget::~ConfigurationWidget()
{
}

void ConfigurationWidget::setupUI()
{
    QString layoutFormat = EcoResMgr::getValue("LayoutFormat", "format", "horizontal").toString();
    m_LeftWidget = new QWidget(this);
    m_LeftWidget->setObjectName("SetupLeftMenu");
    m_StackedWidget->setObjectName("SetupStackedConfig");

    if (layoutFormat == "vertical")
    {
        m_LayoutV = new QVBoxLayout;
        m_LayoutH = new QHBoxLayout;
        for (int tab = 0; tab < TABCOUNT; tab++)
        {
            if (m_TabButtons[tab] == NULL)
            {
                continue;
            }
            m_LayoutH->addWidget(m_TabButtons[tab]);
        }
        m_LeftWidget->setLayout(m_LayoutH);
        m_LayoutV->addWidget(m_LeftWidget);
        m_LayoutV->addWidget(m_StackedWidget);
        setLayout(m_LayoutV);
    }
    else if (layoutFormat == "horizontal")
    {
        setUpdateRotation(ModelUiConfig::instance().value(ModelUiConfig::IsSupportRotation).toBool());
    }
}

void ConfigurationWidget::initializeWidgets()
{
    QString panel;
    int index = 0, tabIndex = 0;
    do
    {
        panel = EcoResMgr::getMultiValue("Setting", "tabs", index++, "").toString();
        if (!panel.isEmpty())
        {
            m_TabTextList.push_back(panel);
        }
    } while (!panel.isEmpty());

    foreach (const QString tabtext, m_TabTextList)
    {
        m_TabButtons[tabIndex] = new PushButtonFor3D(this);
        m_TabButtons[tabIndex]->setCheckable(true);
        m_TabButtons[tabIndex]->setText(Util::translate("SetUp", tabtext));
        m_TabButtons[tabIndex]->setObjectName(tabtext);
        if (tabtext == "General")
        {
            m_GeneralSettings = new GeneralSettings(this);
            connect(m_GeneralSettings, SIGNAL(screenSaverPreviewed(bool, int)), this,
                    SIGNAL(screenSaverPreviewed(bool, int)));
            connect(m_GeneralSettings, SIGNAL(systemDateChanged()), this, SIGNAL(systemDateChanged()));
            m_StackedWidget->addWidget(m_GeneralSettings);
            m_TabHash.insert(tabtext, m_GeneralSettings);
            m_SettingsSet.add(m_GeneralSettings);
        }
        else if (tabtext == "Measurement")
        {
            m_MeasurementSettingWidget = new MeasurementSettingWidget(this);
            m_StackedWidget->addWidget(m_MeasurementSettingWidget);
            m_TabHash.insert(tabtext, m_MeasurementSettingWidget);
            m_SettingsSet.add(m_MeasurementSettingWidget);
        }
        else if (tabtext == "Comment")
        {
            m_CommentConfigFrame = new CommentConfigFrame(this);
            m_StackedWidget->addWidget(m_CommentConfigFrame);
            m_TabHash.insert(tabtext, m_CommentConfigFrame);
            m_SettingsSet.add(m_CommentConfigFrame);
        }
        else if (tabtext == "BodyMark")
        {
            m_BodymarkConfigFrame = new BodyMarkConfigFrame(this);
            m_StackedWidget->addWidget(m_BodymarkConfigFrame);
            m_TabHash.insert(tabtext, m_BodymarkConfigFrame);
            m_SettingsSet.add(m_BodymarkConfigFrame);
        }
        else if (tabtext == "ExamMode")
        {
            m_ExamModeConfigFrame = new ExamModeConfigFrame(this);
            m_StackedWidget->addWidget(m_ExamModeConfigFrame);
            m_TabHash.insert(tabtext, m_ExamModeConfigFrame);
            m_SettingsSet.add(m_ExamModeConfigFrame);
        }
        else if (tabtext == "Report")
        {
            m_ReportSettingsWidget = new ReportSettingsWidget(this);
            m_StackedWidget->addWidget(m_ReportSettingsWidget);
            m_TabHash.insert(tabtext, m_ReportSettingsWidget);
            m_SettingsSet.add(m_ReportSettingsWidget);
        }
        else if (tabtext == "DICOM")
        {
            m_DicomWidget = new ServerList(this);
            m_StackedWidget->addWidget(m_DicomWidget);
            m_TabHash.insert(tabtext, m_DicomWidget);
            m_SettingsSet.add(m_DicomWidget);
        }
        else if (tabtext == "Net")
        {
            m_NetWidget = new NetConfigureWidget(this);
            connect(m_NetWidget, SIGNAL(networkCfgCleared()), m_GeneralSettings, SIGNAL(networkCfgCleared()));
            m_StackedWidget->addWidget(m_NetWidget);
            m_TabHash.insert(tabtext, m_NetWidget);
            //            m_SettingsSet.add(m_NetWidget->netSettings());
            //            m_SettingsSet.add(m_NetWidget->netStorage());
            m_SettingsSet.add(m_NetWidget);
        }
        else if (tabtext == "Admin")
        {
#ifdef USE_ADMINVIEW
            m_AdminWidget = new AdminConfigFrame(this);
            m_AdminWidget->setStateManager(m_StateManager);
            connect(m_AdminWidget, SIGNAL(signalSetCurVirKeyboardEnglish(bool)), this,
                    SIGNAL(signalSetCurVirKeyboardEnglish(bool)));
            connect(m_AdminWidget, SIGNAL(endCurState()), this, SIGNAL(endCurState()));
            connect(this, SIGNAL(patientChanged(Patient*)), m_AdminWidget, SLOT(setPatient(Patient*)));
            m_StackedWidget->addWidget(m_AdminWidget);
            m_TabHash.insert(tabtext, m_AdminWidget);
            m_SettingsSet.add(m_AdminWidget);
#endif
        }
        else if (tabtext == "System")
        {
            m_SystemInfoWidget = new SystemInfoSettings(this);
            // 2024-10-11 Write by AlexWang [BUG:76321] 解决SystemInfoSettings中StateManager为空指针引发的异常崩溃
            m_SystemInfoWidget->setStateManager(m_StateManager);

            connect(m_SystemInfoWidget, SIGNAL(showRegisterDialog()), this, SLOT(onShowRegisterDialog()));
            connect(m_SystemInfoWidget, SIGNAL(hwKeyImported()), this, SIGNAL(hwKeyImported()));
            connect(m_SystemInfoWidget, SIGNAL(screenControlTimersStoped()), this, SIGNAL(screenControlTimersStoped()));
            connect(m_SystemInfoWidget, SIGNAL(screenControlTimersStarted()), this,
                    SIGNAL(screenControlTimersStarted()));
            connect(m_SystemInfoWidget, SIGNAL(signalSetCurVirKeyboardEnglish(bool)), this,
                    SIGNAL(signalSetCurVirKeyboardEnglish(bool)));
            m_StackedWidget->addWidget(m_SystemInfoWidget);
            m_TabHash.insert(tabtext, m_SystemInfoWidget);
            m_SettingsSet.add(m_SystemInfoWidget);
        }
        BaseWidget* widget = qobject_cast<BaseWidget*>(m_TabHash.value(tabtext, NULL));
        if (widget != NULL)
        {
            connect(this, SIGNAL(tpButClicked(QString, QString, QString)), widget,
                    SIGNAL(tpButClicked(QString, QString, QString)));
            connect(widget, SIGNAL(ntpButClicked(QString, QString, QString)), this,
                    SIGNAL(ntpButClicked(QString, QString, QString)));
        }
        tabIndex++;
    }
    if (m_ExamModeConfigFrame != nullptr && m_BodymarkConfigFrame != nullptr)
    {
        connect(m_BodymarkConfigFrame, SIGNAL(updateExamModeConfig()), m_ExamModeConfigFrame,
                SLOT(onUpdateExamModeConfig()));
    }
    if (m_ExamModeConfigFrame != nullptr && m_CommentConfigFrame != nullptr)
    {
        connect(m_CommentConfigFrame, SIGNAL(updateExamModeConfig()), m_ExamModeConfigFrame,
                SLOT(onUpdateExamModeConfig()));
    }
}

void ConfigurationWidget::onShowRegisterDialog()
{
    RegisterDialog dlg(this);
    dlg.showRegisterDialog();
}

void ConfigurationWidget::doTask()
{
    if (m_IsNeedChange)
    {
        m_IsNeedChange = false;
        onTabButtonClicked(0);
    }
}

void ConfigurationWidget::setPackagesMeasurement(PackagesMeasurement* value)
{
    Q_ASSERT(m_MeasurementSettingWidget != NULL);
    m_MeasurementSettingWidget->setPackagesMeasurement(value);
    m_ReportSettingsWidget->setPackagesMeasurement(value);
}

void ConfigurationWidget::setCommentConfigModel(CommentConfigModel* model)
{
    m_CommentConfigFrame->setModel(model);
}

void ConfigurationWidget::setBodyMarkConfigModel(BodyMarkConfigModel* model)
{
    m_BodymarkConfigFrame->setModel(model);
}

void ConfigurationWidget::setExamModeConfigModel(ExamModeConfigModel* model)
{
    m_ExamModeConfigFrame->setModel(model);
}

void ConfigurationWidget::setKeyboard(IKeyboard* value)
{
    m_SystemInfoWidget->setKeyboard(value);
}

void ConfigurationWidget::setMachine(IMachine* value)
{
    m_SystemInfoWidget->setMachine(value);
}

// void ConfigurationWidget::setScreenType(ScreenTypeDef::ScreenType type)
//{
////    m_GeneralSettings->imageSettingWidget()->setScreenTypeButton(type);
//}

void ConfigurationWidget::setHotKeyModels(HotKeyConfig* config, HotKeyContainer* container)
{
    // m_GeneralSettings->keyboardSettingWidget()->setModels(config, container);
    m_GeneralSettings->setHotKeyModels(config, container);
}

void ConfigurationWidget::setDicomTaskManager(DicomTaskManager* value)
{
    m_SystemInfoWidget->setDicomTaskManager(value);
}

void ConfigurationWidget::setImageTile(ImageTile* tile)
{
    m_SystemInfoWidget->setImageTile(tile);
}

void ConfigurationWidget::setMotorControl(MotorControl* motorCtrl)
{
    m_SystemInfoWidget->setMotorControl(motorCtrl);
}

void ConfigurationWidget::runSystemUserImpl()
{
    m_SystemInfoWidget->runSystemUserImpl();
}

bool ConfigurationWidget::isDefault()
{
    if (2 == m_Id)
    {
        m_Id = 0;
        return true;
    }
    return false;
}

void ConfigurationWidget::saveDefault()
{
    save();
}

void ConfigurationWidget::setLGCVisible(bool isVisible)
{
    if (m_GeneralSettings != NULL)
    {
        // m_GeneralSettings->imageSettingWidget()->setLGCVisible(isVisible);
    }
}

void ConfigurationWidget::toggleTabButtons(int index)
{
    for (int id = 0; id < TABCOUNT; id++)
    {
        QPushButton* but = m_TabButtons[id];
        if (but == NULL)
        {
            continue;
        }
        if (id != index)
        {
            but->setChecked(false);
        }
        else
        {
            but->setChecked(true);
        }
    }
}

int ConfigurationWidget::indexOfTabBt(const QString& btname)
{
    for (int id = 0; id < TABCOUNT; id++)
    {
        QPushButton* but = m_TabButtons[id];
        if (but == NULL)
        {
            continue;
        }
        if (but->objectName() == btname)
        {
            return id;
        }
    }
    return -1;
}

void ConfigurationWidget::setIsChangePage(bool value)
{
    m_IsNeedChange = value;
}

void ConfigurationWidget::setUpdateController(UpdateController* updateController)
{
    m_SystemInfoWidget->setUpdateController(updateController);
}

void ConfigurationWidget::setBeamFormerTool(IBeamFormerTool* beamFormerTool)
{
    m_SystemInfoWidget->setBeamFormerTool(beamFormerTool);
}

void ConfigurationWidget::setUpdateRotation(bool isRotation)
{
    if (isRotation || m_IsConfigTransposed)
    {
        m_LayoutGSet = new QGridLayout;
        m_LayoutGSet->setObjectName("gridLayout");
        m_LayoutGTab = new QGridLayout;
        initLeftButtons();
        if (isRotation)
        {
            m_LayoutGTab->setContentsMargins(10, 10, 10, 10);
        }
        else
        {
            m_LayoutGTab->setContentsMargins(0, 0, 0, 0);
            m_LayoutGTab->setAlignment(Qt::AlignLeft);
        }
        m_LayoutGTab->setSpacing(0);
        m_LeftWidget->setLayout(m_LayoutGTab);
        m_LayoutGSet->setContentsMargins(0, 0, 0, 0);
        m_LayoutGSet->setSpacing(0);
        m_LayoutGSet->addWidget(m_LeftWidget);
        m_LayoutGSet->addWidget(m_StackedWidget);
        setLayout(m_LayoutGSet);
    }

    else
    {
        m_LayoutH = new QHBoxLayout;
        m_LayoutV = new QVBoxLayout;
        for (int tab = 0; tab < TABCOUNT; tab++)
        {
            if (m_TabButtons[tab] == NULL)
            {
                continue;
            }
            UiUtil::setShadow(m_TabButtons[tab], 1, 1, 5, "#131517");
            m_LayoutV->addWidget(m_TabButtons[tab]);
        }
        m_LayoutV->addStretch(1);
        m_LayoutV->setContentsMargins(10, 10, 10, 10);
        m_LayoutV->setSpacing(0);
        m_LeftWidget->setLayout(m_LayoutV);
        m_LayoutH->setContentsMargins(0, 0, 0, 0);
        m_LayoutH->setSpacing(0);
        m_LayoutH->addWidget(m_LeftWidget);
        m_LayoutH->addWidget(m_StackedWidget);
        setLayout(m_LayoutH);
    }
}

void ConfigurationWidget::activeMaintenanceWidget()
{
    int index = m_StackedWidget->indexOf(m_SystemInfoWidget);
    onTabButtonClicked(index);
    m_SystemInfoWidget->activeMaintenanceWidget();
}

bool ConfigurationWidget::isValidMaintenanceKey()
{
    return m_SystemInfoWidget->isValidMaintenanceKey();
}

void ConfigurationWidget::setDiskDevice(IDiskDevice* diskDevice)
{
    m_MeasurementSettingWidget->setDiskDevice(diskDevice);
    m_CommentConfigFrame->setDiskDevice(diskDevice);
    m_ExamModeConfigFrame->setDiskDevice(diskDevice);
    m_SystemInfoWidget->setDiskDevice(diskDevice);
}

void ConfigurationWidget::setColorMapManager(IColorMapManager* colorMapManager)
{
    m_GeneralSettings->setColorMapManager(colorMapManager);
}

void ConfigurationWidget::setProbeDataSet(IProbeDataSet* value)
{
    m_ExamModeConfigFrame->setProbeDataSet(value);
    m_SystemInfoWidget->setProbeDataSet(value);
}

void ConfigurationWidget::save()
{
    m_SettingsSet.save();
    Setting::instance().defaults().writeIni();
}

// Cancle in the code corresponds to the Exit button on the generation UI interface
void ConfigurationWidget::cancel()
{
    m_SettingsSet.cancel();
}

void ConfigurationWidget::setDefault()
{
    m_SettingsSet.setDefault();
}

void ConfigurationWidget::onTabButtonClicked(int id)
{
    if (id >= 0 && id < TABCOUNT)
    {
        QPushButton* but = m_TabButtons[id];
        if (but == NULL)
        {
            return;
        }
        toggleTabButtons(id);
        m_StackedWidget->setCurrentWidget(m_TabHash.value(but->objectName()));
        m_ActivateId = id;
        QWidget* pwidget = m_TabHash.value(but->objectName());
        if (pwidget != NULL)
        {
            pwidget->activateWindow();
            BaseWidget* basewidget = static_cast<BaseWidget*>(pwidget);
            if (basewidget != NULL)
            {
                if (!basewidget->isCreatedWidget())
                {
                    basewidget->hide();
                    basewidget->createWidget();
                    basewidget->show();
                    // AdminConfigFrame国内版本做特殊处理，隐藏复选框
                    basewidget->changeByCMD();
                    emit updateContainer(); // BUG[51321]网络设置页面IP地址输入框被弹出的软键盘遮盖
                }
                else
                {
                    // AdminConfigFrame国内版本做特殊处理，隐藏复选框
                    basewidget->changeByCMD();
                }
            }
            emit ntpButClicked("Setup", "Configuration:" + but->objectName(), "");
            m_StackedWidget->setCurrentWidget(pwidget);
        }
    }
    if ((m_NetWidget != NULL) && (m_NetWidget->netSettings() != NULL))
    {
        m_NetWidget->netSettings()->windowHideExit();
    }
}

void ConfigurationWidget::onButtonClicked(int id)
{
    m_Id = id;
    switch (id)
    {
    case 0:
        break;
    case 1:
        break;
    case 2:
        setDefault();
        break;
    case 3:
        //这边先保存在触发accepted信号，可以在退出Settings窗口前，完成保存工作
        save();
        emit accepted();
        break;
    case 4:
        cancel();        //此处直接calcel
        emit rejected(); //用于关闭DialogFrame
        break;
    }
}

void ConfigurationWidget::onDicomStautsChanged(bool value)
{
    for (int tab = 0; tab < TABCOUNT; tab++)
    {
        QPushButton* but = m_TabButtons[tab];
        if (but != NULL && but->objectName() == "DICOM")
        {
            but->setEnabled(value);
        }
    }
}

void ConfigurationWidget::onLGCEnableChanged(const QVariant& value)
{
    if (m_GeneralSettings != NULL)
    {
        // m_GeneralSettings->imageSettingWidget()->setLGCEnable(value.toBool());
        m_GeneralSettings->setLGCEnable(value.toBool());
    }
}

void ConfigurationWidget::onTPButClicked(const QString& category, const QString& butName, const QString& value)
{
    qDebug() << "## ConfigurationWidget::onTPButClicked #" << category << butName << value;
    if (category != "Setup" || value.startsWith("_BtnState_"))
    {
        return;
    }
    // static QStringList DialogBtnNams = QStringList() << "" << "" << "Default" << "Ok" << "Cancel";
    QStringList btnames = butName.split(":", Qt::SkipEmptyParts);
    if (btnames.count() == 2 && btnames.at(0) == "SubSetting")
    {
        int id = indexOfTabBt(btnames.at(1));
        onTabButtonClicked(id);
    }
}

void ConfigurationWidget::retranslateUi()
{
    for (int i = 0; i < TABCOUNT; i++)
    {
        if (m_TabButtons[i] == NULL)
        {
            continue;
        }
        m_TabButtons[i]->setText(Util::translate("SetUp", m_TabButtons[i]->objectName()));
    }
    setWindowTitle(tr("Setup"));
    m_GeneralSettings->retranslateUiExteral();
    m_MeasurementSettingWidget->retranslateUiExteral();
    m_CommentConfigFrame->retranslateUiExteral();
    m_BodymarkConfigFrame->retranslateUiExteral();
    m_ExamModeConfigFrame->retranslateUiExteral();
    m_ReportSettingsWidget->retranslateUiExteral();
    m_DicomWidget->retranslateUiExteral();
    m_NetWidget->retranslateUiExteral();
    m_SystemInfoWidget->retranslateUiExteral();
    //    m_AdminWidget->retranslateUiExteral();
}

void ConfigurationWidget::orientationChanged(Qt::ScreenOrientation orientation)
{
    QString className(this->metaObject()->className());
    OrientationConfigItem config;
    m_Model->getInfosByClassName(orientation, className, config);
    UiUtil::ScreenHVChangedUpdateUI(this, m_LayoutGSet, config);
    m_Orientation = orientation;
    initLeftButtons();
}

void ConfigurationWidget::showEvent(QShowEvent* e)
{
    BaseWidget::showEvent(e);
    //    m_StackedWidget->setCurrentIndex(m_ActivateId);
    static bool first = true;
    if (first)
    {
        first = false;
        m_ActivateId = 0;
    }
    onTabButtonClicked(m_ActivateId);
    metaObject()->invokeMethod(this, "doTask", Qt::QueuedConnection);
    //    if(m_ActivateId != 2)
    //    {
    //      m_CommentConfigFrame->setVisible(false);
    //    }
    //    else
    //    {
    //      m_CommentConfigFrame->setVisible(true);
    //    }
    //    m_StackedWidget->setCurrentIndex(0);
    //    m_TabButtons[0]->setChecked(true);
    //    toggleTabButtons(0);
    // m_NetWidget->netSettings()->onShowEvent();
    // m_GeneralSettings->normalSettingWidget()->onFrameShowEvent();
}

void ConfigurationWidget::hideEvent(QHideEvent* e)
{
    //    m_GeneralSettings->normalSettingWidget()->stopTimer();
    //    m_GeneralSettings->normalSettingWidget()->hideChildren();
    BaseWidget::hideEvent(e);
}

// TODO:需要排查造成“BUG[51325]【系统设置】在设置IP地址时，点击虚拟键盘的Enter键，页面会直接返回到General界面。”的根本原因
//      暂时先将收到的enter事件进行过滤
bool ConfigurationWidget::event(QEvent* event)
{
    QEvent::Type t = event->type();
    if (t == QEvent::KeyPress || t == QEvent::KeyRelease)
    {
        int k = ((QKeyEvent*)event)->key();
        if (k == Qt::Key_Enter || k == Qt::Key_Return)
        {
            return true;
        }
    }
    return QWidget::event(event);
}

void ConfigurationWidget::initLeftButtons()
{
    if (m_LayoutGTab != nullptr)
    {
        for (int tab = 0; tab < TABCOUNT; tab++)
        {
            if (m_TabButtons[tab] == NULL)
            {
                continue;
            }
            UiUtil::setShadow(m_TabButtons[tab], 1, 1, 5, "#131517");

#ifdef USE_SIMULATEORIENTATION
            if (m_Model->isHor() && !m_IsConfigTransposed)
            {
                m_LayoutGTab->addWidget(m_TabButtons[tab], tab, 0, 1, 1);
            }
            else
            {
                m_LayoutGTab->addWidget(m_TabButtons[tab], 0, tab, 1, 1);
            }
            emit orientationChanged(m_Model->isHor());
#else
            if (m_Orientation != Qt::LandscapeOrientation && !m_isConfigTransposed)
            {
                m_LayoutGTab->addWidget(m_TabButtons[tab], 0, tab, 1, 1);
            }
            else
            {
                m_LayoutGTab->addWidget(m_TabButtons[tab], tab, 0, 1, 1);
            }
            emit orientationChanged(m_Orientation == Qt::LandscapeOrientation);
#endif
        }
    }
}
