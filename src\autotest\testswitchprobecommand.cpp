/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#include "testswitchprobecommand.h"
#include "probeselectionframe.h"
#include "istatemanager.h"
#include "stateeventnames.h"
#include <QTimer>
#include "testtimecommand.h"
#include "appsetting.h"

#define TEST_TIME 2

TestSwitchProbeCommand::TestSwitchProbeCommand(BaseProbeSelectionChild* probeFrame, IStateManager* stateManager)
    : m_probeFrame(probeFrame)
    , m_StateManager(stateManager)
    , m_timer(new QTimer(this))
    , m_time(3)
    , m_currentFunctionIndex(0)
    , m_currentProbeIndex(0)
{
    organizeFunctionListAndCommand();

    m_timer->setSingleShot(true);
    connect(m_timer, SIGNAL(timeout()), this, SLOT(onTimeOut()));
}

void TestSwitchProbeCommand::organizeFunctionListAndCommand()
{
    int socketNumber = AppSetting::socketCount();

    for (int i = 0; i < socketNumber; i++)
    {
        m_functionList.push_back(&TestSwitchProbeCommand::postProbeEvent);
        m_functionList.push_back(&TestSwitchProbeCommand::testProbeFunction);

        // customize test function's times
        m_testTimeCommandList.push_back(new TestTimeCommand(TEST_TIME, m_timer));
        m_testTimeCommandList.push_back(new TestTimeCommand(&m_time, m_timer));
    }
}

TestSwitchProbeCommand::~TestSwitchProbeCommand()
{
    qDeleteAll(m_testTimeCommandList);
}

void TestSwitchProbeCommand::execute()
{
    m_timer->start(1000 * m_time);
}

void TestSwitchProbeCommand::undo()
{
    m_timer->stop();
}

void TestSwitchProbeCommand::setTestTime(int sec)
{
    m_time = sec;
}

BaseProbeSelectionChild* TestSwitchProbeCommand::probeFrame() const
{
    return m_probeFrame;
}

void TestSwitchProbeCommand::onTimeOut()
{
    doTest(m_currentFunctionIndex++);
}

void TestSwitchProbeCommand::doTest(int index)
{
    index = index % m_functionList.count();
    (this->*m_functionList.at(index))();
    m_testTimeCommandList.at(index)->execute();
}

void TestSwitchProbeCommand::testProbe(int currentIndex)
{
    int index = currentIndex % AppSetting::socketCount();

    m_probeFrame->setActiveSocket(index);
    m_probeFrame->setPresetItemIndex(0);
    m_probeFrame->confirmed();
}

void TestSwitchProbeCommand::testProbeFunction()
{
    testProbe(m_currentProbeIndex++);
    emit finishTest();
}

void TestSwitchProbeCommand::postProbeEvent()
{
    if (m_StateManager)
    {
        m_StateManager->postEvent(StateEventNames::Probe);
    }
}
