#ifndef SONONERVEALG_H
#define SONONERVEALG_H

#include "algorithm_global.h"
#ifdef USE_SONONERVE
#include "neural.h"
#else
namespace Nerver
{
typedef struct _BPoint
{
    int x;
    int y;
    _BPoint(int _x, int _y)
    {
        x = _x;
        y = _y;
    }
} BPoint;
} // namespace Nerver
#endif
#include "virtualalg.h"
#include <QLibrary>

class ALGORITHMSHARED_EXPORT SonoNerveAlg : public VirtualAlg
{
    friend class AlgInstance;

private:
    SonoNerveAlg();

public:
    inline bool isError(void)
    {
        return m_IsError;
    }

    typedef void (*NeuralsgCreatectx)(const char* modelpath, int type, int infertype);
    typedef void (*NeuralsgRelease)();
    typedef int (*NeuralsgProcess)(unsigned char* imgptr, int width, int height, bool fliplr, bool flipul,
                                   std::vector<Nerver::BPoint>& points);

public:
    void neuralsgCreatectx(const char* modelpath, int type, int infertype);
    void neuralsgRelease();
    void neuralsgProcess(unsigned char* imgptr, int width, int height, bool fliplr, bool flipul,
                         std::vector<Nerver::BPoint>& points);

private:
    NeuralsgCreatectx m_CreateContext;
    NeuralsgRelease m_RemoveContext;
    NeuralsgProcess m_NeuralsgProcess;
};

#endif // SONONERVEALG_H
