#include "databasestudyviewmodel.h"
#include <QSqlRecord>
#include <QSqlQuery>
#include <QDebug>
#include <QFileInfo>
#include <QDir>
#include "modeldirectorygetter.h"

DataBaseStudyViewModel::DataBaseStudyViewModel(QObject* parent)
    : DataBaseViewModel(parent)
{
    //    QSqlRecord record = getQuery().record();
    //    qDebug() << record.count();
}

DataBaseStudyViewModel::~DataBaseStudyViewModel()
{
}

QFileInfo DataBaseStudyViewModel::fileInfo(const QModelIndex& index)
{
    QSqlRecord record = this->record(index.row());
    return PatientPath::instance().fileInfo(record.value("PatientID").toString(), record.value("StudyOid").toString(),
                                            m_Disk);
}

QString DataBaseStudyViewModel::filePath(const QModelIndex& index)
{
    QSqlRecord record = this->record(index.row());
    return PatientPath::instance().path(record.value("PatientID").toString(), record.value("StudyOid").toString(),
                                        m_Disk);
}

QString DataBaseStudyViewModel::patientId(const QModelIndex& index)
{
    QSqlRecord record = this->record(index.row());
    return record.value("PatientID").toString();
}

QString DataBaseStudyViewModel::studyOid(const QModelIndex& index)
{
    QSqlRecord record = this->record(index.row());
    return record.value("StudyOid").toString();
}
