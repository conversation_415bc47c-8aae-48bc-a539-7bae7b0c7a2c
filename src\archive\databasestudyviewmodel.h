#ifndef BASEFILESYSTEMMODEL_H
#define BASEFILESYSTEMMODEL_H
#include "archive_global.h"

#include "databaseviewmodel.h"
#include <QObject>

/**
 * @brief 数据库信息的检查模式
 */
class ARCHIVESHARED_EXPORT DataBaseStudyViewModel : public DataBaseViewModel
{
    Q_OBJECT
public:
    DataBaseStudyViewModel(QObject* parent = 0);
    ~DataBaseStudyViewModel();
    virtual QFileInfo fileInfo(const QModelIndex& index);
    virtual QString filePath(const QModelIndex& index);
    virtual QString patientId(const QModelIndex& index);
    virtual QString studyOid(const QModelIndex& index);

protected:
signals:

private:
};

#endif // BASEFILESYSTEMMODEL_H
