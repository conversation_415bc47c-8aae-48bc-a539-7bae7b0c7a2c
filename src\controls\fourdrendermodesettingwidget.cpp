#include "fourdrendermodesettingwidget.h"
#include "fourdparascontainer.h"
#include "ui_fourdparassettingbasewidget.h"

FourDRenderModeSettingWidget::FourDRenderModeSettingWidget(QWidget* parent)
    : FourDParasSettingBaseWidget(parent)
    , m_<PERSON>(new FourDRenderModeWidget(this))
{
    addWidget(m_Child);
    m_SettingName = QString("RenderModeMap");
    m_ParaType = FourDParasInfo::RenderModeMap;
}

void FourDRenderModeSettingWidget::initalize()
{
    m_Child->initalize();
    ui->addButton->setEnabled(true);
    FourDParasSettingBaseWidget::initalize();
}

void FourDRenderModeSettingWidget::setSonoParameters(SonoParameters* sonoParameters)
{
    m_Child->setSonoParameters(sonoParameters);
}

void FourDRenderModeSettingWidget::currenIndexChanged(const QString& arg1)
{
    if (ui->comboBox->currentIndex() == 0)
    {
        ui->renameButton->setEnabled(false);
        ui->deleteButton->setEnabled(false);
    }
    else if (ui->comboBox->currentIndex() > 0)
    {
        ui->renameButton->setEnabled(true);
        ui->deleteButton->setEnabled(true);
    }
    m_Child->setFourDRenderModePara(oneParasT<FourDParasInfo::FourDRenderModePara>(arg1));
    m_Child->setParametersMax(ui->comboBox->count() - 1);
    m_Child->setParametersIndex(ui->comboBox->currentIndex());
}

bool FourDRenderModeSettingWidget::contains(const QString& name)
{
    return isExistNameT<FourDParasInfo::FourDRenderModePara>(name);
}

void FourDRenderModeSettingWidget::add(const QString& name)
{
    addOneParaT<FourDParasInfo::FourDRenderModePara>(name, m_Child->fourDRenderModePara());
    FourDParasManager::instance().saveFourDRenderModeParas();
}

void FourDRenderModeSettingWidget::save(const QString& name)
{
    add(name);
    m_Child->updateParameters();
}

void FourDRenderModeSettingWidget::del(const QString& name)
{
    deleteOneParaT<FourDParasInfo::FourDRenderModePara>(name);
    FourDParasManager::instance().saveFourDRenderModeParas();
}

bool FourDRenderModeSettingWidget::rename(const QString& oldName, const QString& newName)
{
    if (!isExistNameT<FourDParasInfo::FourDRenderModePara>(newName))
    {
        renameT<FourDParasInfo::FourDRenderModePara>(oldName, newName);
        return true;
    }
    else
    {
        return false;
    }
}

QStringList FourDRenderModeSettingWidget::names()
{
    return namesT<FourDParasInfo::FourDRenderModePara>();
}

void FourDRenderModeSettingWidget::onFourDPresetParasChanged()
{
    m_Child->initalize();
    currenIndexChanged(ui->comboBox->currentText());
    m_Child->updateParameters();
}

void FourDRenderModeSettingWidget::onFourDPresetParaDeleted(int index)
{
    bool needSave = false;
    foreach (QString name, names())
    {
        bool changed = false;
        FourDRenderModePara para = oneParasT<FourDParasInfo::FourDRenderModePara>(name);
        int* ptr = (int*)&para;
        int size = sizeof(FourDRenderModePara) / sizeof(int);
        for (int i = 0; i < size; i++)
        {
            if (*ptr == index)
            {
                *ptr = 0;
                changed = true;
                needSave = true;
            }
            else if (*ptr > index)
            {
                (*ptr)--;
                changed = true;
                needSave = true;
            }
            ptr++;
        }
        if (changed)
        {
            addOneParaT<FourDParasInfo::FourDRenderModePara>(name, para);
        }
    }
    m_Child->initalize();
    currenIndexChanged(ui->comboBox->currentText());

    if (needSave)
    {
        FourDParasManager::instance().saveFourDRenderModeParas();
        m_Child->updateParameters();
    }
}
