#ifndef PROBESELFTESTWIDGET_H
#define PROBESELFTESTWIDGET_H

#include "controls_global.h"

#include "basewidget.h"
#include "baseinputabledialogframe.h"
#include <QAbstractButton>

namespace Ui
{
class ProbeSelfTestWidget;
}

class ProbeSelfTestWidget;
class ChisonUltrasoundContext;

class CONTROLSSHARED_EXPORT ProbeSelfTestDialog : public BaseInputAbleDialogFrame
{
    Q_OBJECT
public:
    explicit ProbeSelfTestDialog(QWidget* parent = 0);

    void setChisonUltrasoundContext(ChisonUltrasoundContext* value);

    void show();

private:
    ProbeSelfTestWidget* m_child;
};

class ProbeSelfTestWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit ProbeSelfTestWidget(QWidget* parent = nullptr);
    ~ProbeSelfTestWidget();

    void setChisonUltrasoundContext(ChisonUltrasoundContext* value);

    bool openTest();

    bool closeTest();

protected:
    void retranslateUi()
    {
    }

private slots:

    void on_pushBtnStart_clicked();

    void on_radioBtn_clicked(QAbstractButton* button);

    void on_elementTesting(int elementIndex);

    void on_elementSlider_valueChanged(int value);

private:
    void startAutoTest();

    void stopAutoTest();

    void singleElementTest();

    int getMaxElementIndex();

private:
    Ui::ProbeSelfTestWidget* ui;
    ChisonUltrasoundContext* m_ChisonUltrasoundContext;
    bool m_IsAutoTest;
    bool m_IsTestRunning;
    int m_TestType;
    int m_ElementIndex;
    int m_ValidElementCounts;
};

#endif // PROBESELFTESTWIDGET_H
