#include "databasepatientviewmodel.h"
#include <QSqlQuery>
#include <QVariant>
#include "treeitem.h"
#include "util.h"
#include <QSqlRecord>
#include <QModelIndex>
#include "modeldirectorygetter.h"
#ifdef USE_ADMINVIEW
#include "adminconfigmodel.h"
#include "userinfo.h"
#endif

DataBasePatientViewModel::DataBasePatientViewModel(QObject* parent)
    : DataBaseViewModel(parent)
    , rootItem(NULL)
{
    patientViewSetup();
    connect(this, SIGNAL(sortChanged()), this, SLOT(patientViewSetup()));
    connect(this, SIGNAL(update()), this, SLOT(patientViewSetup()));
}

DataBasePatientViewModel::~DataBasePatientViewModel()
{
    Util::SafeDeletePtr(rootItem);
}

QVariant DataBasePatientViewModel::data(const QModelIndex& index, int role) const
{
    if (!index.isValid())
        return QVariant();

    if (role != Qt::DisplayRole)
        return QVariant();
    TreeItem* item = static_cast<TreeItem*>(index.internalPointer());

    if (index.column() == 1) // Convert Patient name from DICOM format to readable format
    {
        return NameComponent(item->data(index.column()).toString()).formattedName();
    }
    else if (index.column() == 8)
    {
#ifdef USE_ADMINVIEW
        return AdminConfigModel::instance()->getUserName(item->data(index.column()).toString());
#else
        return QVariant();
#endif
    }
    else
    {
        return item->data(index.column());
    }
}

QModelIndex DataBasePatientViewModel::index(int row, int column, const QModelIndex& parent) const
{
    if (!hasIndex(row, column, parent))
    {
        return QModelIndex();
    }
    TreeItem* parentItem;
    if (!parent.isValid())
    {
        parentItem = rootItem;
    }
    else
    {
        parentItem = static_cast<TreeItem*>(parent.internalPointer());
    }

    TreeItem* childItem = parentItem->child(row);
    if (childItem)
    {
        return createIndex(row, column, childItem);
    }
    else
    {
        return QModelIndex();
    }
}

QModelIndex DataBasePatientViewModel::parent(const QModelIndex& child) const
{
    if (!child.isValid())
    {
        return QModelIndex();
    }
    TreeItem* childItem = static_cast<TreeItem*>(child.internalPointer());
    TreeItem* parentItem = childItem->parent();
    if (parentItem == rootItem)
    {
        return QModelIndex();
    }
    return createIndex(parentItem->row(), 0, parentItem);
}

QModelIndex DataBasePatientViewModel::sibling(int row, int column, const QModelIndex& idx) const
{
    return QAbstractItemModel::sibling(row, column, idx);
}

Qt::ItemFlags DataBasePatientViewModel::flags(const QModelIndex& index) const
{
    return QAbstractItemModel::flags(index);
}

int DataBasePatientViewModel::rowCount(const QModelIndex& parent) const
{
    TreeItem* parentItem;
    if (parent.column() > 0)
    {
        return 0;
    }

    if (!parent.isValid())
    {
        parentItem = rootItem;
    }
    else
    {
        parentItem = static_cast<TreeItem*>(parent.internalPointer());
    }

    return parentItem->childCount();
}

int DataBasePatientViewModel::columnCount(const QModelIndex& parent) const
{
    if (parent.isValid())
    {
        return static_cast<TreeItem*>(parent.internalPointer())->columnCount();
    }
    else
    {
        return rootItem->columnCount();
    }
}

QFileInfo DataBasePatientViewModel::fileInfo(const QModelIndex& index)
{
    TreeItem* item = static_cast<TreeItem*>(index.internalPointer());
    return PatientPath::instance().fileInfo(item->data(0).toString(), item->data(7).toString(), m_Disk);
}

QString DataBasePatientViewModel::filePath(const QModelIndex& index)
{
    TreeItem* item = static_cast<TreeItem*>(index.internalPointer());
    return PatientPath::instance().path(item->data(0).toString(), item->data(7).toString(), m_Disk);
}

QString DataBasePatientViewModel::patientId(const QModelIndex& index)
{
    TreeItem* item = static_cast<TreeItem*>(index.internalPointer());
    return item->data(0).toString();
}

QString DataBasePatientViewModel::studyOid(const QModelIndex& index)
{
    TreeItem* item = static_cast<TreeItem*>(index.internalPointer());
    return item->data(7).toString();
}

/**
 * @brief setupModelData
 *		 完成后的结果
 *		 root
 *		 	patient1
 *		 		study1
 *		 		study2
 *		 	patient2
 *		 		study1
 *		 		study2
 *		 	patient3
 *		 		study
 * @param query
 * @param parent
 */
void DataBasePatientViewModel::setupModelData(QSqlQuery& query, TreeItem* parent)
{
    QList<TreeItem*> parents;
    QList<QVariant> idList;
    parents << parent;
    QSqlRecord record = query.record();
    int count = record.count();
    bool isPatient = true;
    while (query.next())
    {
        if (idList.isEmpty() || idList.first() != query.value(0))
        {
            idList.clear();
            idList << query.value(0);
            isPatient = true;
        }
        else if (idList.first() == query.value(0))
        {
            isPatient = false;
        }

        QList<QVariant> columnData;
        for (int index = 0; index < count; ++index)
        {
            columnData << query.value(index);
        }

        if (isPatient)
        { // parents列表中最多有两个item，一个为root,另一个为正在添加studyn的patientn
          //进入这个if语句，表明一个patint添加study完毕
            if (parents.count() > 1)
            { //从patients列表中弹出已经添加study完毕的patient
                parents.pop_back();
            }

            if (parents.count() == 1)
            {
                // root添加一个patient
                QList<QVariant> patientColumn = columnData;
                patientColumn.replace(7, QString());
                parents.last()->appendChild(new TreeItem(patientColumn, parents.last()));
                // patient添加一个study，这个study为当前的patient自己
                parents << parents.last()->child(parents.last()->childCount() - 1);
            }
        }
        // patient添加一个study
        parents.last()->appendChild(new TreeItem(columnData, parents.last()));
    }
}

void DataBasePatientViewModel::patientViewSetup()
{
    QSqlQuery query = getQuery();
    QSqlRecord rec = query.record();
    QList<QVariant> rootData;
    for (int i = 0; i < rec.count(); i++)
    {
        rootData << headerData(i, Qt::Horizontal);
    }
    if (rootItem != NULL)
    {
        Util::SafeDeletePtr(rootItem);
    }
    rootItem = new TreeItem(rootData);
    setupModelData(query, rootItem);
}

bool DataBasePatientViewModel::hasChildren(const QModelIndex& parent) const
{
    if (parent.model() == this || !parent.isValid())
    {
        return rowCount(parent) > 0 && columnCount(parent) > 0;
    }
    return false;
}
