#include "archiveimagelabel.h"
#include <QPixmap>
#include <QPainter>
#include <QDebug>
#include <QSizePolicy>
#include <QMovie>
#include <QApplication>
#include "imagelabelinfo.h"
#include "util.h"
#include "setting.h"
#include "modeluiconfig.h"
// no use
ArchiveImageLabel::ArchiveImageLabel(QWidget* parent)
    : QLabel(parent)
    , m_Info(NULL)
    , m_IsSelected(false)
    , m_Index(0)
{
    setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
}

void ArchiveImageLabel::setIsSelected(bool value)
{
    m_IsSelected = value;
    setInfoPixmap();
}

int ArchiveImageLabel::imageWidth() const
{
    if (m_Info != NULL)
    {
        return m_Info->pixmap().width();
    }
    return 0;
}

int ArchiveImageLabel::imageHeight() const
{
    if (m_Info != NULL)
    {
        return m_Info->pixmap().height();
    }
    return 0;
}

void ArchiveImageLabel::setIndex(const int index)
{
    m_Index = index;
}

int ArchiveImageLabel::getIndex() const
{
    return m_Index;
}

void ArchiveImageLabel::mousePressEvent(QMouseEvent*)
{
    // when clicked, emit the singleclick or doubleclick signal.
    if (m_Info != NULL)
    {
        if (!m_Info->isSelect())
        {
            emit clicked(m_Info->imagePath());
        }
        else
        {
            emit doubleClicked(m_Info->imagePath());
        }
    }
}

void ArchiveImageLabel::setInfoPixmap()
{
    if (m_Info != NULL)
    {
        QMovie* gif = NULL;
        if (!m_Info->isPixmapLoaded())
        {
            gif = new QMovie(":/images/loading.gif", QByteArray(), this);
            setMovie(gif);
            gif->start();
        }

        QPixmap pix(m_Info->pixmap());

        if (gif != NULL)
        {
            Util::processEvents(QEventLoop::ExcludeUserInputEvents);
        }

        QPainter painter(&pix);
        painter.setPen(QColor(ModelUiConfig::instance().value(ModelUiConfig::TitleRenderColor).toUInt()));
        if (m_Info->isSelect())
        {
            painter.drawRect(pix.rect().adjusted(0, 0, -1, -1));
        }

        if (gif != NULL)
        {
            Util::processEvents(QEventLoop::ExcludeUserInputEvents);
        }

        if (m_Info->isCine())
        {
            //            painter.drawText(pix.rect(), "CinFile");
            QBrush brush(QColor(96, 161, 217));
            QPixmap icon(ModelUiConfig::instance().value(ModelUiConfig::MovieIcon).toString());

            QRect rect(pix.rect().width() - icon.width() - 5, pix.rect().height() - icon.height() - 5, icon.width(),
                       icon.height());
            painter.fillRect(rect, brush);
            painter.drawPixmap(rect, icon);
        }
        else if (m_Info->isReport())
        {
            //            painter.drawText(pix.rect(), "CinFile");
            QBrush brush(QColor(96, 161, 217));
            QPixmap icon(":/images/reportfile.png");

            QRect rect(pix.rect().width() - icon.width() - 5, pix.rect().height() - icon.height() - 5, icon.width(),
                       icon.height());
            painter.fillRect(rect, brush);
            painter.drawPixmap(rect, icon);
        }
        else if (m_Index != 0 && !m_Info->isReport() && !m_Info->isCine())
        {
            QBrush brush(QColor(96, 161, 217));
            QRect rect(pix.rect().width() - 27, pix.rect().height() - 27, 22, 22);
            painter.fillRect(rect, brush);
            painter.setPen(QColor(255, 255, 255));
            painter.drawText(rect, Qt::AlignCenter, tr("%1").arg(m_Index));
            // painter.setPen(Qt::gray);
        }

        if (gif != NULL)
        {
            setMovie(NULL);
            delete gif;
            gif = NULL;
        }
        setPixmap(pix);
    }
}

void ArchiveImageLabel::setImageInfo(ImageLabelInfo* info)
{
    m_Info = info;
    if (m_Info != NULL)
    {
        setInfoPixmap();
    }
}

ImageLabelInfo* ArchiveImageLabel::imageInfo() const
{
    return m_Info;
}

bool ArchiveImageLabel::isSelected()
{
    return m_IsSelected;
}
