#include "treeviewbutton.h"
#include "ui_treeviewbutton.h"
#include "appsetting.h"
#include "util.h"

const char* const TreeViewButton::m_AnimalString[] = {QT_TRANSLATE_NOOP("TreeViewButton", "Animal View")};

TreeViewButton::TreeViewButton(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::TreeViewButton)
{
    ui->setupUi(this);
    connect(ui->pushButtonPatient, SIGNAL(clicked()), this, SIGNAL(buttonPatientClicked()));
    connect(ui->pushButtonStudy, SIGNAL(clicked()), this, SIGNA<PERSON>(buttonStudyClicked()));
    connect(ui->pushButtonExpand, SIGNAL(clicked()), this, SIGNAL(buttonExpandClicked()));
    connect(ui->pushButtonCollapse, SIGNAL(clicked()), this, SIG<PERSON><PERSON>(buttonCollapseClicked()));
    connect(ui->pushButtonSelect, SIGNAL(clicked()), this, SIG<PERSON>L(buttonSelectClicked()));

    studyView();
    if (AppSetting::isAnimalAndChinese())
    {
        Util::setPatient2Animal(ui->pushButtonPatient, "TreeViewButton", m_AnimalString[Animal_View]);
    }
}

TreeViewButton::~TreeViewButton()
{
    delete ui;
}

void TreeViewButton::patientView()
{
    setButtonsEnable(false);
}

void TreeViewButton::studyView()
{
    setButtonsEnable(true);
}

void TreeViewButton::setSelectAllButtonEnabled(bool value)
{
    ui->pushButtonSelect->setEnabled(value);
}

void TreeViewButton::on_pushButtonPatient_clicked()
{
    patientView();
}

void TreeViewButton::on_pushButtonStudy_clicked()
{
    studyView();
}

void TreeViewButton::setButtonsEnable(bool enable)
{
    ui->pushButtonPatient->setEnabled(enable);
    ui->pushButtonStudy->setEnabled(!enable);
    ui->pushButtonExpand->setEnabled(!enable);
    ui->pushButtonCollapse->setEnabled(!enable);
}

void TreeViewButton::retranslateUi()
{
    ui->retranslateUi(this);
    if (AppSetting::isAnimalAndChinese())
    {
        Util::setPatient2Animal(ui->pushButtonPatient, "TreeViewButton", m_AnimalString[Animal_View]);
    }
}
