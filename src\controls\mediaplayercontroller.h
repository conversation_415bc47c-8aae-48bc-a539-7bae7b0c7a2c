﻿/*
 * =====================================================================================
 *
 *    Filename:  mediaplayercontroller.h
 *
 *    Description:
 *      Version:  1.0
 *      Created:  2023年08月22日 13时28分18秒
 *      Function: 基于FFMPEG实现多媒体播放控制，支持的格式有mp4、avi、png、bmp、jpg
 *
 *    Author:  wangshang, <EMAIL>
 *
 * =====================================================================================
 */

#ifndef MEDIAPLAYERCONTROLLER_H
#define MEDIAPLAYERCONTROLLER_H
#include "controls_global.h"

#include <QObject>
#include <QProcess>

class QTimer;

class CONTROLSSHARED_EXPORT MediaPlayerController : public QObject
{
    Q_OBJECT

public:
    explicit MediaPlayerController(QObject* parent = nullptr);

    ~MediaPlayerController();

    /**
     * @brief setFilePaths
     * @param FilePaths 设置播放地址
     */
    void setFilePaths(const QStringList& FilePaths);

    /**
     * @brief play
     * @param filePath 当filePath不为空时，只播放该地址的多媒体，反之播放轮询播放m_FilePaths的内容。默认为空
     * @param isLoop   此参数只有当filePath不为空时才生效，旨在控制单媒体播放是否开启循环播放模式。默认开启
     */
    void play(const QString& filePath = "", const bool isLoop = true);

    /**
     * @brief stop 结束播放动作
     */
    void stop();

    /**
     * @brief setImageShowTime 设置图片展示时长
     * @param imageShowTime
     */
    void setImageShowTime(int imageShowTime);

    /**
     * @brief setMute
     * @param isMute 设置多媒体是否支持静音
     */
    void setMute(bool isMute);

signals:
    void ffplayIsRuning();
    void ffplayIsEnded();
    void ffplayExited();

private slots:
    void onPlayControlTimeout();
    void playSingleFileFinished(int code, QProcess::ExitStatus status);

private:
    void playSingleFile(const QString& filePath);
    bool isSupport(const QString& filePath, bool& isImage);
    void createFileNames();
    int getMediaTime(const QString& filePath);

private:
    QProcess* m_FFPlayProcess;
    QString m_FileListPath;
    QStringList m_FilePaths;
    int m_ImageShowTime;
    bool m_IsMute;
    QTimer* m_PlayControlTimer;
    int m_CurrentPlayIndex;
    bool m_LoopShowMedia;
    QString m_LoopMediaFileName;

    int m_Width;
    int m_Height;
};

#endif // MEDIAPLAYERCONTROLLER_H
