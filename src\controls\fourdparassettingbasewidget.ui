<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FourDParasSettingBaseWidget</class>
 <widget class="QWidget" name="FourDParasSettingBaseWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>344</width>
    <height>57</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>8</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>1</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>1</number>
   </property>
   <item>
    <layout class="QGridLayout" name="gridLayout">
     <property name="bottomMargin">
      <number>2</number>
     </property>
     <property name="verticalSpacing">
      <number>2</number>
     </property>
     <item row="0" column="0" colspan="2">
      <widget class="BaseComboBox" name="comboBox"/>
     </item>
     <item row="0" column="2">
      <widget class="QPushButton" name="addButton">
       <property name="text">
        <string>Add</string>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QPushButton" name="renameButton">
       <property name="text">
        <string>Rename</string>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QPushButton" name="deleteButton">
       <property name="text">
        <string>Del</string>
       </property>
      </widget>
     </item>
     <item row="1" column="2">
      <widget class="QPushButton" name="saveButton">
       <property name="text">
        <string>Save</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BaseComboBox</class>
   <extends>QComboBox</extends>
   <header>basecombobox.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
