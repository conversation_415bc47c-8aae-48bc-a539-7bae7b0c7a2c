#ifndef BACKUPMODEL_H
#define BACKUPMODEL_H
#include "archive_global.h"

#include <QObject>
#include <QStringList>
#include "threadmodel.h"

/**
 * @brief 备份
 */
class ProgressBarView;
class ConfirmDialogFrame;
class Patient;
class IStateManager;
class ARCHIVESHARED_EXPORT BackupModel : public QObject
{
    Q_OBJECT
public:
    explicit BackupModel(IStateManager* value, QObject* parent = 0);
    ~BackupModel();
    /**
     * @brief setDirNameList 可以提供空间的磁盘列表
     *
     * @param nameList
     */
    void setDirNameList(const QStringList& nameList);
    /**
     * @brief saveStudys 开始备份
     *
     * @param discName
     * @param widget
     */
    void saveStudys(const QString& discName, QWidget* widget,
                    bool backToLocal = true); // 增加backtolocal为了防止restore时，自动刷新回local
                                              /**
                                               * @brief copyDataToDisk 如果磁盘中不存在数据库，copy一个空的数据库
                                               *
                                               * @param disc
                                               */
    void copyDataToDisk(const QString& disc);
    /**
     * @brief setDisk 设置磁盘
     *
     * @param disk
     */
    void setDisk(const QString& disk);
    bool burnFileToDvd(const QString& disc, QWidget* widget);
    void saveDbfileToTmp(const QString& discName, const QList<Patient*>& patients);

protected:
    /**
     * @brief getStudyOidList 所有检查的studyoid列表
     *
     * @param nameList
     */
    void getStudyOidList(const QStringList& nameList);
    /**
     * @brief changeDbFileName 数据库的全路径名
     *
     * @param discName
     */
    void changeDbFileName(const QString& discName);
    /**
     * @brief clear 将成员变量置空
     */
    void clear();
    /**
     * @brief copyFileToDisc 将检查的信息拷贝到磁盘，路径与本地的路径相同
     *
     * @param disc
     */
    bool copyFileToDisc(const QString& disc);
    void progressBar(QWidget* widget, bool busyIndicative = false, bool isCancelButtonVisible = false);
signals:
    void threadStarted(CopyThread* copyThread);
public slots:

private:
    bool isPatientDir(QString path) const;
    void getStudyDir(const QString& path);

private:
    QStringList m_DirNameList; // 磁盘列表       exam list of selected to backup or restore!
    QStringList m_StudyOid;    //检查列表           study  list of selected to backup or restore! baseName
    ProgressBarView* m_ProgressBar;
    ConfirmDialogFrame* m_ConfirmDialog;
    ThreadModel m_ThreadModel;
    QString m_Disk;
    IStateManager* m_StateManager;
};

#endif // BACKUPMODEL_H
