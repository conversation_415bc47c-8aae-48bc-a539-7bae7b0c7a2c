<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>WorkListSearchWidget</class>
 <widget class="QWidget" name="WorkListSearchWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1477</width>
    <height>840</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>WorkList</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <widget class="QLabel" name="searchKeyLabel">
     <property name="minimumSize">
      <size>
       <width>70</width>
       <height>0</height>
      </size>
     </property>
     <property name="text">
      <string>ID</string>
     </property>
    </widget>
   </item>
   <item row="0" column="1">
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QLineEdit" name="searchkeyLineEdit">
       <property name="minimumSize">
        <size>
         <width>165</width>
         <height>0</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_17">
       <property name="text">
        <string>Search By</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="BaseComboBox" name="searchByComboBox">
       <property name="minimumSize">
        <size>
         <width>360</width>
         <height>0</height>
        </size>
       </property>
       <item>
        <property name="text">
         <string>ID</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>Name</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_4">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>192</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item row="1" column="0" colspan="2">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QTreeView" name="treeView">
       <property name="editTriggers">
        <set>QAbstractItemView::NoEditTriggers</set>
       </property>
       <property name="sortingEnabled">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QPushButton" name="searchButton">
         <property name="minimumSize">
          <size>
           <width>100</width>
           <height>0</height>
          </size>
         </property>
         <property name="focusPolicy">
          <enum>Qt::NoFocus</enum>
         </property>
         <property name="text">
          <string>Update</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="appButton">
         <property name="enabled">
          <bool>false</bool>
         </property>
         <property name="minimumSize">
          <size>
           <width>100</width>
           <height>0</height>
          </size>
         </property>
         <property name="focusPolicy">
          <enum>Qt::NoFocus</enum>
         </property>
         <property name="text">
          <string>Apply</string>
         </property>
         <property name="checkable">
          <bool>false</bool>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
         <property name="default">
          <bool>false</bool>
         </property>
         <property name="flat">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="clearButton">
         <property name="enabled">
          <bool>false</bool>
         </property>
         <property name="minimumSize">
          <size>
           <width>100</width>
           <height>0</height>
          </size>
         </property>
         <property name="text">
          <string>Clear</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BaseComboBox</class>
   <extends>QComboBox</extends>
   <header>basecombobox.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
