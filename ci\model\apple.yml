pre-build-job:
  stage: prebuild
  tags:
    - Apple
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  before_script:
    - ./ci/shell/getknown_hosts.sh
    - ./ci/shell/pullrelatedrep.sh -m Apple
  script:
    - ./ci/shell/comparerootcmakelists.sh
    - ./ci/shell/check_ts.sh -r
    - ./ci/shell/check_autoupgrade_config.sh -m apple

cppcheck-job:
  stage: cppcheck
  needs: [pre-build-job]
  tags:
    - Apple
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  script:
    - ./ci/shell/cppcheck.sh

code-format-job:
  stage: code-format
  needs: [cppcheck-job]
  tags:
    - Apple
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  script:
    - ./ci/shell/clangformat.sh $CI_MERGE_REQUEST_TARGET_BRANCH_NAME $CI_COMMIT_REF_NAME $CI_COMMIT_REF_NAME

build-linux-apple-job:
  stage: build
  needs: [code-format-job]
  tags:
    - Apple
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  script:
    - ./configure_unix_x86_64bit.sh -m Apple
  cache:
    key: build-Apple-cache
    paths:
      - build/Apple
    policy: push

# test-apple-job:
#   stage: test
#   needs: [build-linux-apple-job]   
#   tags:
#     - Apple
#   rules:
#     - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
#   script:
#     - ./ci/shell/runtest.sh -m Apple
#     # - ./ci/shell/runcoverage.sh -s ${CI_PROJECT_DIR} -m Apple
#     # - ./ci/shell/runcoveragegate.sh $CodeCoverageThreshold
#   # allow_failure: true
#   # coverage: /^\s*lines:\s*\d+.\d+\%/
#   # artifacts:
#   #   name: ${CI_JOB_NAME}-${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA}
#   #   expire_in: 2 days
#   #   reports:
#   #     cobertura: build/coverage.xml
#   cache:
#     key: build-Apple-cache
#     paths:
#       - build/Apple
#     policy: pull

deploy-apple-job:
  stage: deploy
  tags:
    - Apple
  variables:
    GIT_STRATEGY: fetch
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule" && $model == "Apple"
  before_script:
    - ./ci/shell/getknown_hosts.sh
    - ./ci/shell/pullrelatedrep.sh -m Apple -r $ResourceBranch -t $CI_COMMIT_BRANCH -v $PreVersion
    - git reset --hard origin/$CI_COMMIT_BRANCH
  script:
    - ./configure_unix_x86_64bit.sh -m Apple
    - ./ci/shell/deployandrun_sonoair.sh $Deploy_IP

release-apple-job:
  stage: release
  tags:
    - Apple
  rules:
    - if: $CI_PIPELINE_SOURCE != "schedule" && $model == "Apple"
  before_script:
    - ./ci/shell/getknown_hosts.sh
    - ./ci/shell/pullrelatedrep.sh -m Apple -r $ResourceBranch -t $CI_COMMIT_BRANCH -v $PreVersion
  script:
    - ./ci/shell/runrelease.sh -m Apple -v $MainVersion -a $VersionAdd


# coverage-report-apple-job:
#   stage: report
#   tags:
#     - Apple
#   rules:
#     - if: ($CI_PIPELINE_SOURCE == "schedule" && $model == "Apple")
#   script:
#     - ./configure_unix_x86_64bit.sh -m $model  -g ON
#     - ./ci/shell/runtest.sh -m $model
#     - ./ci/shell/runcoverage_html.sh -s ${CI_PROJECT_DIR} -m $model
#   coverage: /^\s*lines:\s*\d+.\d+\%/
#   artifacts:
#     name: ${CI_PIPELINE_ID}-${CI_JOB_NAME}-$model
#     paths:
#       - build/html/$model

# pages:
#   stage: deploy
#   needs: [coverage-report-apple-job]   
#   dependencies:
#     - coverage-report-apple-job
#   tags:
#     - Apple
#   rules:
#     - if: ($CI_PIPELINE_SOURCE == "schedule" && $model == "Apple")
#   script:
#     - mv build/html public/
#   artifacts:
#     paths:
#       - public
#     expire_in: 7 days

