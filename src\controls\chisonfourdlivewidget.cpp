#include "chisonfourdlivewidget.h"
#include "ui_chisonfourdlivewidget.h"
#include "imagetile.h"
#include "screenshots.h"
#include "sonoparameters.h"
#include "parameter.h"
#include "bfpnames.h"
#include "fourdapihandler.h"
#include "colormaptypedef.h"
#include "chisonfourdparameter.h"
#include "chisonfourddatasender.h"
#include "util.h"
#include "fourdlivemodel.h"
#include "variantutil.h"
#include "toolsfactory.h"
#include "toolnames.h"
#include "fourddatatypedef.h"
#include "icommand.h"
#include "ilinebuffermanager.h"
ChisonFourDLiveWidget* ChisonFourDLiveWidget::m_ChisonFourDLiveWidget = NULL;

ChisonFourDLiveWidget::ChisonFourDLiveWidget(IFourDDataSender* dataSender, QWidget* parent)
    : AbstractFourDLiveWidget(dataSender, parent)
    , ui(new Ui::ChisonFourDLiveWidget)
    , m_IsInited(false)
{
    ui->setupUi(this);
    ui->grayCurveThumbnail->setVisible(false);
    m_ChisonFourDLiveWidget = this;
}

ChisonFourDLiveWidget::~ChisonFourDLiveWidget()
{
    delete ui;
    delete m_fourDParameter;
}
void ChisonFourDLiveWidget::setContext(ImageTile* context, ILineBufferManager* bufferManager)
{
    m_ImageTile = context;
    m_LineBufferManager = bufferManager;
    m_SonoParameters = m_ImageTile->sonoParameters();
    ui->renderWidget->setSonoParameters(m_SonoParameters);

    m_Model->setContext(context);
    m_Model->setBufferManager(m_LineBufferManager);
    //    ui->fourDTransformWidget->setIsRealTime(m_SonoParameters->isRealTime());

    m_fourDParameter = new ChisonFourDParameter(m_SonoParameters);

    connect(m_SonoParameters, SIGNAL(presetChanged(PresetParameters)), this, SLOT(onPresetChanged(PresetParameters)));
    connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onFreezeChanged(QVariant)));
}

void ChisonFourDLiveWidget::init()
{
    ui->renderWidget->createFourD((void*)callbackFrom4D);

    if (!m_IsInited)
    {
        ui->fourDFreezeBarWidget->setBufferManager(m_LineBufferManager);
        FourDAPIHandler::instance().setBufferManager(m_LineBufferManager);
        m_IsInited = true;
        m_fourDParameter->connectSignals();
        connectSignals();
    }
}

void ChisonFourDLiveWidget::reset()
{
    m_fourDParameter->resetParameters();
    ui->colorBarOf3DImage->refreshColorBar(FourDAPIHandler::instance().ctfNodeList());
    ui->fourDFreezeBarWidget->setAllCurrentIndex(m_LineBufferManager->frameCount(),
                                                 m_LineBufferManager->activeLayoutIndex());
    FourDAPIHandler::instance().clearBuffer();
}

void ChisonFourDLiveWidget::saveCurGroupParas()
{
    m_fourDParameter->saveCurGroupParas();
}

void ChisonFourDLiveWidget::saveROIInfo()
{
    if (m_SonoParameters != NULL)
    {
        m_SonoParameters->setPV(BFPNames::ChisonFourDClipCurvePointsStr, FourDAPIHandler::instance().roiInfo());
    }
}

IFourDRenderWidget* ChisonFourDLiveWidget::fourDRenderWidget() const
{
    return ui->renderWidget;
}

FourDTransformWidget* ChisonFourDLiveWidget::transformWidget() const
{
    return ui->fourDTransformWidget;
}

GrayCurveWidget* ChisonFourDLiveWidget::fourdGrayCurveWidget() const
{
    return ui->grayCurveThumbnail;
}

void ChisonFourDLiveWidget::showEvent(QShowEvent* e)
{
    QWidget::showEvent(e);
    setupRects();
}

void ChisonFourDLiveWidget::onBeforecurSonoParametersChanged()
{
    m_fourDParameter->onBeforecurSonoParametersChanged();
    disconnectSignals();
}

void ChisonFourDLiveWidget::onCurSonoParametersChanged()
{
    m_SonoParameters = m_ImageTile->sonoParameters();

    if (!m_SonoParameters->isRealTime())
    {
        resetClipCurvePoints();
    }
    m_fourDParameter->onCurSonoParametersChanged(m_SonoParameters);
    m_Model->setSonoParameters(m_SonoParameters);
    ui->colorBarOf3DImage->refreshColorBar(FourDAPIHandler::instance().ctfNodeList());

    connectSignals();
    //    ui->fourDTransformWidget->setIsRealTime(m_SonoParameters->isRealTime());
    ui->fourDTransformWidget->updateFourDGain(m_SonoParameters->pV(BFPNames::FourDGainStr));
    ui->fourDTransformWidget->onVirtualHDOnChanged(m_SonoParameters->pBV(BFPNames::FourDVirtualHDOnStr));
    updateFourDMouseState(true);
}

void ChisonFourDLiveWidget::onPresetChanged(const PresetParameters& presets)
{
    m_fourDParameter->updatePresetParameters(presets);
    updateFourDGain(m_SonoParameters->pV(BFPNames::FourDGainStr));
}

void ChisonFourDLiveWidget::onFreezeChanged(const QVariant& value)
{
    bool valueBool = value.toBool();
    m_SonoParameters->parameter(BFPNames::FourDFrameRateStr)->setEnabled(!valueBool);
    m_SonoParameters->parameter(BFPNames::FourDDynamicRangeStr)->setEnabled(!valueBool);
    m_SonoParameters->parameter(BFPNames::FourDGainStr)->setEnabled(!valueBool);
    if (m_SonoParameters->pBV(BFPNames::ShowFourDWidgetStr))
    {
        ui->fourDTransformWidget->onFreezeChanged(value);
    }
    if (!valueBool)
    {
        FourDAPIHandler::instance().clearBuffer();
    }
    updateMouseStateRange();
    updateFourDMouseState();
}

void ChisonFourDLiveWidget::updateMouseStateRange()
{
    if (m_SonoParameters->pBV(BFPNames::FourDVirtualHDOnStr))
    {
        m_SonoParameters->parameter(BFPNames::ChisonFourDMouseStateStr)->setMax(FourDMouseStateEnum::VirtualHDLight);
    }
    else
    {
        m_SonoParameters->parameter(BFPNames::ChisonFourDMouseStateStr)->setMax(FourDMouseStateEnum::Panning);
    }
    if (m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        m_SonoParameters->parameter(BFPNames::ChisonFourDMouseStateStr)->setMin(FourDMouseStateEnum::Loop);
    }
    else
    {
        m_SonoParameters->parameter(BFPNames::ChisonFourDMouseStateStr)->setMin(FourDMouseStateEnum::CurvedLine);
    }
}
void ChisonFourDLiveWidget::updateFourDMouseState(bool forceUpdate)
{
    if (m_SonoParameters->pBV(BFPNames::FourDVirtualHDOnStr))
    {
        m_SonoParameters->parameter(BFPNames::ChisonFourDMouseStateStr)
            ->setValue(FourDMouseStateEnum::VirtualHDLight, forceUpdate);
    }
    else if (m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        m_SonoParameters->parameter(BFPNames::ChisonFourDMouseStateStr)
            ->setValue(FourDMouseStateEnum::Loop, forceUpdate);
    }
    else
    {
        m_SonoParameters->parameter(BFPNames::ChisonFourDMouseStateStr)
            ->setValue(FourDMouseStateEnum::CurvedLine, forceUpdate);
    }
}
void ChisonFourDLiveWidget::onFourDGainChanged(const QVariant& val)
{
    updateFourDGain(val);
}

void ChisonFourDLiveWidget::onRenderModeChanged(const QVariant& value)
{
    m_fourDParameter->setRenderMode(value);
    ui->colorBarOf3DImage->refreshColorBar(FourDAPIHandler::instance().ctfNodeList());
}

void ChisonFourDLiveWidget::onVirtualHDOnChanged(const QVariant& value)
{
    m_fourDParameter->setVirtualHDEnabled(value);
    ui->colorBarOf3DImage->refreshColorBar(FourDAPIHandler::instance().ctfNodeList());
    ui->fourDTransformWidget->onVirtualHDOnChanged(value);
    updateMouseStateRange();
    this->updateFourDMouseState();
}

void ChisonFourDLiveWidget::setupRects()
{
    //    QPoint topLeft, rightBottom;
    //    topLeft.rx() = ui->colorBarOf2DImage->mapToGlobal(QPoint(0, 0)).x();
    //    topLeft.ry() = ui->fourDRenderWidget->mapFromGlobal(QPoint(0, 0)).y();

    //    rightBottom.rx() = ui->colorBarOf3DImage->mapToGlobal(ui->colorBarOf3DImage->rect().bottomRight()).x();
    //    rightBottom.ry() = ui->fourDRenderWidget->mapFromGlobal(ui->fourDRenderWidget->rect().bottomRight()).y();

    //    topLeft.rx() -= 4;
    //    rightBottom.rx() += 4;
    //    Screenshots::instance().setFourDImageRect(QRect(topLeft, rightBottom));
    //    QRect infoRect(QPoint(topLeft.x(), 0), rightBottom);
    //    Screenshots::instance().setFourDImageAndInfoRect(infoRect);

    QPoint topLeft, rightBottom;
    topLeft = ui->fourDRenderWidget->mapToGlobal(QPoint(0, 0));

    rightBottom = ui->fourDRenderWidget->mapToGlobal(ui->fourDRenderWidget->rect().bottomRight());
    topLeft.rx() -= 4;
    rightBottom.rx() += 4;
    Screenshots::instance().setFourDImageRect(QRect(topLeft, rightBottom));
    QRect infoRect(QPoint(topLeft.x(), 0), rightBottom);
    Screenshots::instance().setFourDImageAndInfoRect(infoRect);
}

void ChisonFourDLiveWidget::callbackFrom4D()
{
    m_ChisonFourDLiveWidget->updateCurrentIndex();
}
void ChisonFourDLiveWidget::connectSignals()
{
    if (m_SonoParameters != NULL)
    {
        connect(m_SonoParameters->parameter(BFPNames::ChisonFourDRenderStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onRenderModeChanged(QVariant)));
        connect(m_SonoParameters->parameter(BFPNames::FourDVirtualHDOnStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onVirtualHDOnChanged(QVariant)));
        connect(m_SonoParameters->parameter(BFPNames::FourDGainStr), SIGNAL(valueChanging(QVariant)), this,
                SLOT(onFourDGainChanged(QVariant)));
        connect(m_SonoParameters->parameter(BFPNames::TransformationAxisStr), SIGNAL(valueChanging(QVariant)),
                ui->fourDTransformWidget, SLOT(onAxisChanged(QVariant)));
        connect(m_SonoParameters->parameter(BFPNames::ChisonFourDMouseStateStr), SIGNAL(valueChanging(QVariant)), this,
                SLOT(onFourdMouseActionChanged(QVariant)));
        connect(m_SonoParameters->parameter(BFPNames::ChisonFourDPaletteStr), SIGNAL(valueChanged(QVariant)), this,
                SLOT(onPaletteChanged(QVariant)));
    }
}
void ChisonFourDLiveWidget::onFourdMouseActionChanged(const QVariant& value)
{
    int valueInt = value.toInt();
    Q_ASSERT(valueInt <= FourDMouseStateEnum::VirtualHDLight);
    updateMouseCommand((FourDMouseStateEnum::FourDMouseActionMode)valueInt);
    ui->fourDTransformWidget->onFourdMouseActionChanged(value);
}

void ChisonFourDLiveWidget::onPaletteChanged(const QVariant& value)
{
    ui->colorBarOf3DImage->refreshColorBar(FourDAPIHandler::instance().ctfNodeList());
}
void ChisonFourDLiveWidget::disconnectSignals()
{
    if (m_SonoParameters != NULL)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::ChisonFourDRenderStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onRenderModeChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::FourDVirtualHDOnStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onVirtualHDOnChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::FourDGainStr), SIGNAL(valueChanging(QVariant)), this,
                   SLOT(onFourDGainChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::TransformationAxisStr), SIGNAL(valueChanging(QVariant)),
                   ui->fourDTransformWidget, SLOT(onAxisChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::ChisonFourDMouseStateStr), SIGNAL(valueChanging(QVariant)),
                   this, SLOT(onFourdMouseActionChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::ChisonFourDPaletteStr), SIGNAL(valueChanged(QVariant)), this,
                   SLOT(onPaletteChanged(QVariant)));
    }
}
void ChisonFourDLiveWidget::setFreezeBarAllIndex()
{
    ui->fourDFreezeBarWidget->setAllCurrentIndex(m_LineBufferManager->frameCount(),
                                                 m_LineBufferManager->activeLayoutIndex());
}
void ChisonFourDLiveWidget::updateFourDGain(const QVariant& val)
{
    ui->fourDTransformWidget->updateFourDGain(val);
    m_fourDParameter->updateGainValue(val);
}

void ChisonFourDLiveWidget::resetClipCurvePoints()
{
    if (NULL == m_SonoParameters)
        return;

    const QVariant& value = m_SonoParameters->pV(BFPNames::ChisonFourDClipCurvePointsStr);

    Q_ASSERT(value.type() == QVariant::List);

    ClipCurvePoints points = VariantUtil::toPointFList(value);

    if (points.size() == 3)
    {
        ChisonFourDROIInfo roiInfo;
        memset(&roiInfo, 0, sizeof(ChisonFourDROIInfo));
        roiInfo.m_fFirstPointX = points[0].x();
        roiInfo.m_fROIWidth = points[0].y();
        roiInfo.m_fKnotX = points[1].x();
        roiInfo.m_fKnotY = points[1].y();
        roiInfo.m_fLastPointX = points[2].x();
        roiInfo.m_fROIHeight = points[2].y();
        FourDAPIHandler::instance().initRoiBox((void*)&roiInfo);
    }
}

void ChisonFourDLiveWidget::onCurvedLineCtrlPointMoved(const QString& direction)
{
    if (m_Model != NULL)
    {
        m_Model->moveCurvedLineCtrlPoint(m_SonoParameters->pBV(BFPNames::FourDVirtualHDOnStr), direction);
    }
}
