#include "fourdpresetwidget.h"
#include "ui_fourdpresetwidget.h"
#include "fourdparascontainer.h"
#include "messageboxframe.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include "parameter.h"

FourDPresetWidget::FourDPresetWidget(QWidget* parent)
    : FourDParaSetBaseWidget(parent)
    , ui(new Ui::FourDPresetWidget)
{
    ui->setupUi(this);
    m_ParaType = FourDParasInfo::FourDPreset;
    m_RelateParasName = BFPNames::FourDParasSettingIndexStr;
}

FourDPresetWidget::~FourDPresetWidget()
{
    delete ui;
}

void FourDPresetWidget::initalize()
{
    QStringList names = FourDParasContainer::get<FourDPresetPara>(m_ParaType).names();
    if (names.count() > 0)
    {
        ui->comboBoxFrom->addItems(names);
        names.removeFirst();
        ui->comboBoxTo->addItems(names);
        ui->comboBoxFrom->setCurrentIndex(0);
        ui->comboBoxFrom->setCurrentIndex(0);
    }
}

void FourDPresetWidget::setFourDPresetPara(const FourDPresetPara& para)
{
    m_FourDPresetPara = para;
    ui->spinBoxThreshold->setValue(para.m_FourDThreshold);
    ui->spinBoxSmooth->setValue(para.m_FourDSmoothIndex);
    ui->spinBoxPalette->setValue(para.m_FourDPaletteIndex);
    ui->spinBoxDynamic->setValue(para.m_FourDDynamicRange);
    ui->spinBoxGain->setValue(para.m_FourDGain);
}

FourDPresetPara FourDPresetWidget::fourDPresetPara()
{
    FourDPresetPara para = m_FourDPresetPara;
    para.m_FourDThreshold = ui->spinBoxThreshold->text().toInt();
    para.m_FourDSmoothIndex = ui->spinBoxSmooth->text().toInt();
    para.m_FourDPaletteIndex = ui->spinBoxPalette->text().toInt();
    para.m_FourDDynamicRange = ui->spinBoxDynamic->text().toInt();
    para.m_FourDGain = ui->spinBoxGain->text().toInt();
    return para;
}

void FourDPresetWidget::removeComboxItem(const int& index)
{
    ui->comboBoxFrom->removeItem(index);
    ui->comboBoxTo->removeItem(index - 1);
}

void FourDPresetWidget::addComboxItem(const QString& name)
{
    ui->comboBoxFrom->addItem(name);
    ui->comboBoxTo->addItem(name);
}

void FourDPresetWidget::renameComboxItemText(const int& index, const QString& name)
{
    ui->comboBoxFrom->setItemText(index, name);
    ui->comboBoxTo->setItemText(index - 1, name);
}

void FourDPresetWidget::retranslateUi()
{
    ui->retranslateUi(this);
}

void FourDPresetWidget::onSetSonoParameters()
{
    ui->spinBoxThreshold->setMaximum(m_sonoParameters->parameter(BFPNames::FourDThresholdStr)->max());
    ui->spinBoxSmooth->setMaximum(m_sonoParameters->parameter(BFPNames::FourDSmoothStr)->max());
    ui->spinBoxPalette->setMaximum(m_sonoParameters->parameter(BFPNames::FourDPaletteStr)->max());
    ui->spinBoxDynamic->setMaximum(m_sonoParameters->parameter(BFPNames::FourDDynamicRangeStr)->max());
    ui->spinBoxGain->setMaximum(m_sonoParameters->parameter(BFPNames::FourDGainStr)->max());
}

void FourDPresetWidget::on_copyButton_clicked()
{
    if (MessageBoxFrame::question(this, tr("Copy"), tr("Are you sure copy?"), QMessageBox::Yes | QMessageBox::No) ==
        QMessageBox::Yes)
    {
        QString fromParaName = ui->comboBoxFrom->currentText();
        QString toParaName = ui->comboBoxTo->currentText();
        FourDPresetPara fromPara = FourDParasContainer::get<FourDPresetPara>(m_ParaType).oneParas(fromParaName);
        FourDPresetPara toPara = FourDParasContainer::get<FourDPresetPara>(m_ParaType).oneParas(toParaName);
        toPara.m_FourDThreshold = fromPara.m_FourDThreshold;
        toPara.m_FourDSmoothIndex = fromPara.m_FourDSmoothIndex;
        toPara.m_FourDPaletteIndex = fromPara.m_FourDPaletteIndex;
        toPara.m_FourDDynamicRange = fromPara.m_FourDDynamicRange;
        toPara.m_FourDGain = fromPara.m_FourDGain;

        FourDParasContainer::get<FourDPresetPara>(m_ParaType).paraCopy(toParaName, toPara);

        FourDParasManager::instance().saveFourDPresetParas();
        emit fourDPresetParasChanged();

        MessageBoxFrame::tipInformation(QString("paras has been copyed  successfully!"));
    }
}
