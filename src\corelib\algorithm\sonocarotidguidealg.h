#ifndef SONOCAROTIDGUIDE_H
#define SONOCAROTIDGUIDE_H

#include "algorithm_global.h"
#include <QLibrary>
#ifdef USE_SONOCAROTIDGUIDE
#include "sonocarotidguide64.h"
#else
typedef int ERROR_CODE;
#define ERROR_CODE_OK 0                //没有错误
#define ERROR_CODE_UNKNOWN -1          //未知错误
#define ERROR_CODE_INVALID_PATH -2     //无效路径

//颈动脉切面类型
typedef int CAROTID_VIEW_TYPE;
#define CAROTID_VIEW_TYPE_UKNOWN 0     //未知切面类型
#define CAROTID_VIEW_TYPE_HORIZONTAL 1 //颈动脉横切切面
#define CAROTID_VIEW_TYPE_VERTICAL 2   //颈动脉纵切切面

//引导方向
typedef int GUIDE_DIRECTION;
#define GUIDE_DIRECTION_NONE 0         //引导方向：无
#define GUIDE_DIRECTION_LEFT 1         //引导方向：向左
#define GUIDE_DIRECTION_RIGHT 2        //引导方向：向右

//颈动脉切面质量类型
typedef int CAROTID_VIEW_QUALITY;
#define CAROTID_VIEW_QUALITY_BAD 0     //差质量
#define CAROTID_VIEW_QUALITY_MIDDLE 1  //中质量
#define CAROTID_VIEW_QUALITY_GOOD 2    //好质量

typedef struct _InitInfo
{
    int nImageWidth;
    int nImageHeight;
    char chAlgLogFolderPath[256];
    char chModelsFolderPath[256];
    char chReserve[400];
} InitInfo;

typedef struct _ImageRoiRect
{
    int nLeftX;
    int nRightX;
    int nTopY;
    int nBottomY;
} ImageRoiRect;

typedef struct _CarotidGuideResult
{
    CAROTID_VIEW_TYPE nCarotidViewType;
    float fViewQualityScore;
    CAROTID_VIEW_QUALITY nCaroticQualityType;
    GUIDE_DIRECTION nGuideDirection;
    char chReserve[100];
} CarotidGuideResult;

typedef void* SonoCarotidGuideHandle;
#endif
#include "virtualalg.h"

class ALGORITHMSHARED_EXPORT SonoCarotidGuideAlg : public VirtualAlg
{
    friend class AlgInstance;

private:
    SonoCarotidGuideAlg();

public:
    typedef int (*createSonoCarotidGuideHandle)(SonoCarotidGuideHandle* pSonoCarotidGuideHandle, InitInfo* pInitInfo);
    typedef int (*processSonoCarotidGuide)(SonoCarotidGuideHandle hSonoCarotidGuideHandle,
                                           CAROTID_VIEW_TYPE nCarotidViewType, int nImageWidth, int nImageHeight,
                                           int nImageDataSize, unsigned char* pImageData, ImageRoiRect* pImageRoiRect,
                                           CarotidGuideResult* pCarotidGuideResult);

    typedef int (*releaseSonoCarotidGuideHandle)(SonoCarotidGuideHandle* pSonoCarotidGuideHandle);

    int CreateSonoCarotidGuideHandle(SonoCarotidGuideHandle* pSonoCarotidGuideHandle, InitInfo* pInitInfo);
    int ProcessSonoCarotidGuide(SonoCarotidGuideHandle hSonoCarotidGuideHandle, CAROTID_VIEW_TYPE nCarotidViewType,
                                int nImageWidth, int nImageHeight, int nImageDataSize, unsigned char* pImageData,
                                ImageRoiRect* pImageRoiRect, CarotidGuideResult* pCarotidGuideResult);
    int ReleaseSonoCarotidGuideHandle(SonoCarotidGuideHandle* pSonoCarotidGuideHandle);

    SonoCarotidGuideHandle m_SonoCarotidGuideHandle;
    InitInfo m_StInitInfo;

private:
    createSonoCarotidGuideHandle m_CreateContext;
    processSonoCarotidGuide m_Process;
    releaseSonoCarotidGuideHandle m_RemoveContext;
};

#endif // SONOCAROTIDGUIDE_H
