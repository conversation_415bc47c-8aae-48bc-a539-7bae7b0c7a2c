<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>BodyMarkQBitWidget</class>
 <widget class="QWidget" name="BodyMarkQBitWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>518</width>
    <height>151</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <item>
    <widget class="QWidget" name="widget" native="true">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
    </widget>
   </item>
   <item>
    <widget class="ImageSkimManager" name="bodyMarkWidget" native="true"/>
   </item>
   <item>
    <widget class="QWidget" name="widget_2" native="true">
     <property name="minimumSize">
      <size>
       <width>120</width>
       <height>0</height>
      </size>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ImageSkimManager</class>
   <extends>QWidget</extends>
   <header>imageskimmanager.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
