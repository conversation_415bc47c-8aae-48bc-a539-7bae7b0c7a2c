#ifndef PATIENTHATERINFOWIDGET_H
#define PATIENTHATERINFOWIDGET_H
#include "archive_global.h"

//#include <QWidget>
#include "basewidget.h"

class Patient;
class Study;
namespace Ui
{
class PatientHaterInfoWidget;
}

/**
 * @brief 病人除了姓名、性别、年龄等的基本信息
 */
class ARCHIVESHARED_EXPORT PatientHaterInfoWidget : public BaseWidget
{
    Q_OBJECT
    enum STUDYTYPE
    {
        OB,
        CARD,
        URO
    };

public:
    explicit PatientHaterInfoWidget(QWidget* parent = 0);
    ~PatientHaterInfoWidget();
    /**
     * @brief setCurrentIndex 设置科室
     *
     * @param index
     */
    void setCurrentIndex(const int index);
    /**
     * @brief setStudy 设置一个需要添加信息的study
     *
     * @param study
     */
    void setStudy(Study* study);
    /**
     * @brief flawlessStudy 根据界面编辑框的内容，完善study信息
     *
     * @param study
     */
    void flawlessStudy(Study* study);
    /**
     * @brief clearStudyInfo 清空study信息
     */
    void clearStudyInfo();
    void setCurrentWidgetEnabled(bool enabled);
    void setAnimalSpeciesIndex(int index);

protected:
    void setWidgetVisible(bool visable);
    void showWidget(int index);
    void retranslateUi();

signals:
    void patientInfoUnitChanged(const QString& key, const int unitIndex);

protected slots:
    void setInfo(const QMap<QString, QStringList>& info);
    void onPatientInfoUnitChanged(const QString& str);

private:
    Ui::PatientHaterInfoWidget* ui;
    int getRealIndex(const int index);
};

#endif // PATIENTHATERINFOWIDGET_H
