#include "filiationtreeview.h"
#include "filesystemmodel.h"
#include <QFileInfo>
#include "messageboxframe.h"
#include "inputdialogframe.h"
#include "util.h"
#include "filiationtreeviewmodel.h"
#include <QDebug>

FiliationTreeView::FiliationTreeView(QWidget* patient)
    : QTreeView(patient)
    , m_FileInfoList(QFileInfoList())
    , m_Model(NULL)
{
    m_Model = new FiliationTreeViewModel(this);
    connect(this, SIGNAL(doubleClicked(QModelIndex)), SLOT(clickTreeView(QModelIndex)));
}

FiliationTreeView::~FiliationTreeView()
{
    Util::SafeDeletePtr(m_Model);
}

void FiliationTreeView::clickTreeView(const QModelIndex& index)
{
    if (m_FileInfoList.isEmpty())
    {
        return;
    }

    FileSystemModel* fileModel = getModel();
    if (fileModel == NULL)
    {
        return;
    }

    QFileInfo info(fileModel->fileInfo(index));
    if (info.isDir())
    {
        QFileInfo parentDir = QFileInfo(info.path());
        if (parentDir != m_FileInfoList.last() && !m_FileInfoList.contains(parentDir))
        {
            m_FileInfoList.append(parentDir);
        }
        this->setRootIndex(index);
    }
}

void FiliationTreeView::back()
{
    if (m_FileInfoList.isEmpty())
    {
        return;
    }

    FileSystemModel* fileModel = getModel();
    if (fileModel == NULL)
    {
        return;
    }

    setRootIndex(fileModel->setRootPath(m_FileInfoList.back().filePath()));
    if (m_FileInfoList.count() > 1)
    {
        m_FileInfoList.removeLast();
    }

    this->clearSelection();
}

void FiliationTreeView::setInitFileInfo(const QModelIndex& index)
{
    if (!m_FileInfoList.isEmpty())
    {
        m_FileInfoList.clear();
    }

    QFileInfo info = getInfo(index);
    if (info.exists())
    {
        m_FileInfoList.append(info);
    }

    this->clearSelection();
}

FileSystemModel* FiliationTreeView::getModel()
{
    return dynamic_cast<FileSystemModel*>(this->model());
}

QFileInfo FiliationTreeView::getInfo(const QModelIndex& index)
{
    FileSystemModel* fileModel = getModel();
    if (fileModel == NULL)
    {
        return QFileInfo();
    }
    QFileInfo info(fileModel->fileInfo(index));
    return info;
}

void FiliationTreeView::makeDir()
{
    if (!m_FileInfoList.isEmpty())
    {
        m_Model->mkDir(m_FileInfoList, this);
    }
}

QFileInfoList FiliationTreeView::deleteFileList()
{
    QModelIndexList indexList = this->selectedIndexes();
    if (indexList.count() == 1)
    {
        QFileInfo info = getInfo(indexList.first());
        QFileInfoList infoList;
        if (info.exists() && !m_FileInfoList.contains(info))
        {
            infoList.append(info);
            return infoList;
        }
    }

    MessageBoxFrame::warning(tr("Please select the item!"));

    return QFileInfoList();
}

QFileInfo FiliationTreeView::currentFileInfo()
{
    if (!m_FileInfoList.isEmpty())
    {
        return m_FileInfoList.last();
    }
    else
    {
        return QFileInfo();
    }
}
