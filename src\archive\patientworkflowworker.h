#ifndef PATIENTWORKFLOWWORKER_H
#define PATIENTWORKFLOWWORKER_H
#include "archive_global.h"

#include <QObject>

class PackagesMeasurement;
class Patient;
class IBeamFormer;
class DicomTaskManager;

class ARCHIVESHARED_EXPORT PatientWorkflowWorker : public QObject
{
    Q_OBJECT
public:
    explicit PatientWorkflowWorker(QObject* parent = 0);
    void setPackagesMeasurement(PackagesMeasurement* value);
    void setBeamFormer(IBeamFormer* value);
    void setDicomTaskManager(DicomTaskManager* value);
signals:

public slots:
    void doEnd(const Patient& patient, bool appExit);
    void doContinue(const Patient& patient);

private:
    PackagesMeasurement* m_PackagesMeasurement;
    IBeamFormer* m_BeamFormer;
    DicomTaskManager* m_DicomTaskManager;
};

#endif // PATIENTWORKFLOWWORKER_H
