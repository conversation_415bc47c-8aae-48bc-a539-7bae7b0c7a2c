#ifndef JSRGALG_H
#define JSRGALG_H

#include "algorithm_global.h"
#include <QLibrary>
#ifdef USE_AUTOCYSTIC
#include "jrsg.h"
#else
typedef struct _BPoint
{
    int x;
    int y;
    _BPoint(int _x, int _y)
    {
        x = _x;
        y = _y;
    }
} BPoint;
#endif
#include "virtualalg.h"

struct thyroidOut;
struct boxOut;
class ALGORITHMSHARED_EXPORT JsrgAlg : public VirtualAlg
{
    friend class AlgInstance;

private:
    JsrgAlg();

public:
    inline bool isError(void)
    {
        return m_IsError;
    }

    typedef void (*JrsgCreatectx)(const char* modelpath, int type, int radius, int infertype);
    typedef void (*JrsgRelease)();
    typedef void (*JrsgMeasure)(unsigned char* imgptr, int width, int height, std::vector<BPoint>& points, int ptx,
                                int pty, int roiwidth, int roiheight, int& ellipse_x, int& ellipse_y,
                                int& ellipse_width, int& ellipse_height, int& ellipse_angle, bool ellipsefit);
    int jrsgCreatectx(const char* modelpath, int type, int radius, int infertype);
    void jrsgMeasure(unsigned char* imgptr, int width, int height, std::vector<BPoint>& points, int ptx, int pty,
                     int roiwidth, int roiheight, int& ellipse_x, int& ellipse_y, int& ellipse_width,
                     int& ellipse_height, int& ellipse_angle, bool ellipsefit);
    void jrsgRelease();

public:
    JrsgCreatectx m_CreateContext;
    JrsgRelease m_ReleaseContext;
    JrsgMeasure m_JrsgMeasure;
};

#endif // JSRGALG_H
