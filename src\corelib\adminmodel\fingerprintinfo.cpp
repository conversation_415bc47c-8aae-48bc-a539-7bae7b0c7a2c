#include "fingerprintinfo.h"

FingerprintInfo::FingerprintInfo()
    : m_fingerIndex(-1)
    , m_fingerprintId(-1)
    , m_status(false)
{
}

FingerprintInfo::FingerprintInfo(int index, int fingerprintid, const QString& fingerprintname, const QString& userid,
                                 bool status)
    : m_fingerIndex(index)
    , m_fingerprintId(fingerprintid)
    , m_fingerprintName(fingerprintname)
    , m_userId(userid)
    , m_status(status)
{
}

int FingerprintInfo::fingerprintId() const
{
    return m_fingerprintId;
}

void FingerprintInfo::setFingerprintId(int fingerprintId)
{
    m_fingerprintId = fingerprintId;
}
QString FingerprintInfo::fingerprintName() const
{
    return m_fingerprintName;
}

void FingerprintInfo::setFingerprintName(const QString& fingerprintName)
{
    m_fingerprintName = fingerprintName;
}
QString FingerprintInfo::userId() const
{
    return m_userId;
}

void FingerprintInfo::setUserId(const QString& userId)
{
    m_userId = userId;
}
bool FingerprintInfo::status() const
{
    return m_status;
}

void FingerprintInfo::setStatus(bool status)
{
    m_status = status;
}
int FingerprintInfo::fingerIndex() const
{
    return m_fingerIndex;
}

void FingerprintInfo::setFingerIndex(int fingerIndex)
{
    m_fingerIndex = fingerIndex;
}
