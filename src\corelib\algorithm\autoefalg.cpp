#include "autoefalg.h"

AutoEfAlg::AutoEfAlg()
    : VirtualAlg()
{
    m_Lib.setFileName("lvsec");
}

int AutoEfAlg::initDetect(const char* path, int code, float thresh, const char* clcache_path)
{
    LVInitDetect func = (LVInitDetect)m_Lib.resolve("lv_initdetect");
    if (func == NULL)
    {
        m_Initialized = false;
        m_IsError = true;
        return -1;
    }
    int res = func(path, code, thresh, clcache_path);
    m_Initialized = true;
    return res;
}

int AutoEfAlg::detect(uint8_t* imgdata, int width, int height, int& num, int* pnts, float& area, float& volume)
{
    if (m_LVDetect == NULL)
    {
        m_LVDetect = (LVDetect)m_Lib.resolve("lv_detect");
    }
    if (m_LVDetect == NULL || !m_Initialized)
    {
        m_IsError = true;
        return -1;
    }
    return m_LVDetect(imgdata, width, height, num, pnts, area, volume);
}

int AutoEfAlg::iQA(uint8_t* imgdata, int width, int height, float& score)
{
    LVIQA func = (LVIQA)m_Lib.resolve("lv_IQA");
    if (func == NULL || !m_Initialized)
    {
        m_IsError = true;
        return -1;
    }
    return func(imgdata, width, height, score);
}

int AutoEfAlg::release()
{
    LVRelease func = (LVRelease)m_Lib.resolve("lv_release");
    if (func == NULL || !m_Initialized)
    {
        m_IsError = true;
        return -1;
    }
    m_Initialized = false;
    return func();
}
