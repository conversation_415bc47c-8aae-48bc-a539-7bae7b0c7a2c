#ifndef FOURDFREEZEBARWIDGET_H
#define FOURDFREEZEBARWIDGET_H
#include "controls_global.h"
#include "freezebarwidget.h"

class CONTROLSSHARED_EXPORT FourDFreezeBarWidget : public FreezeBarWidget
{
    Q_OBJECT
public:
    explicit FourDFreezeBarWidget(QWidget* parent = 0);
    ~FourDFreezeBarWidget();
    void setAllCurrentIndex(int frameCount, int bufferIndex = -1);

protected:
    void setFreezeBarVisible(bool value, int bufferIndex, int layoutIndex);
protected slots:
    void onBufferStartIndexChanged(int bufferIndex, int layoutIndex);
    void onBufferCurrentIndexChanged(int bufferIndex, int layoutIndex, int frameCount);
    void onBufferEndIndexChanged(int bufferIndex, int layoutIndex);
};

#endif // FOURDFREEZEBARWIDGET_H
