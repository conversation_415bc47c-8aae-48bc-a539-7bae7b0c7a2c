#
# The module defines the following variables:
# GOOGLETEST_FOUND - True if GOOGLETEST found.
# GOOGLETEST_INCLUDE_DIRS - where to find GOOGLETEST.h, etc.
# GOOGLETEST_LIBRARIES - List of libraries when using GOOGLETEST.
#
thirdparty_prefix_path(googletest)

set(GOOGLETEST_INCLUDE_DIRS ${GOOGLETEST_ROOT}include)
message("Found GOOGLETEST_INCLUDE_DIRS headers: ${GOOGLETEST_INCLUDE_DIRS}")

list(APPEND GTEST_LIB ${GOOGLETEST_LIBPATH}/${LIB_REFIX}gtest${LIB_POSTFIX}${LIB_STATIC_EXT})
message("Found GTEST_LIB libs: ${GTEST_LIB}")
list(APPEND GTEST_MAIN_LIB ${GOOGLETEST_LIBPATH}/${LIB_REFIX}gtest_main${LIB_POSTFIX}${LIB_STATIC_EXT})
message("Found GTEST_MAIN_LIB libs: ${GTEST_MAIN_LIB}")

list(APPEND GMOCK_LIB ${GOOGLETEST_LIBPATH}/${LIB_REFIX}gmock${LIB_POSTFIX}${LIB_STATIC_EXT})
message("Found GMOCK_LIB libs: ${GMOCK_LIB}")
list(APPEND GMOCK_MAIN_LIB ${GOOGLETEST_LIBPATH}/${LIB_REFIX}gmock_main${LIB_POSTFIX}${LIB_STATIC_EXT})
message("Found GMOCK_MAIN_LIB libs: ${GMOCK_MAIN_LIB}")

