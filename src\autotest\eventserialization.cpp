#include "eventserialization.h"
#include "stateevent.h"
#include "tgcdataevent.h"
#include "messageevent.h"
#include "applogger.h"
#include <QStringList>
#include <QDebug>
#include <QTabletEvent>
#include <QTouchEvent>

void readLogger(qint32 timeoffset, QTouchEvent::TouchPoint& pt, const QString& ds)
{
    Q_UNUSED(timeoffset);
    Q_UNUSED(ds);
    qint32 id = 0, state = 1;
    QPointF pos, startPos, lastPos;
    QPointF scenePos, startScenePos, lastScenePos;
    QPointF screenPos, startScreenPos, lastScreenPos;
    QPointF normalizedPos, startNormalizedPos, lastNormalizedPos;
    //    QRectF rect, sceneRect, screenRect;
    qreal pressure = 0;

    //    ds >> id >> state >> pos >> startPos >> lastPos
    //        >> scenePos >> startScenePos >> lastScenePos
    //        >> screenPos >> startScreenPos >> lastScreenPos
    //        >> normalizedPos >> startNormalizedPos >> lastNormalizedPos
    //        >> rect >> sceneRect >> screenRect >> pressure;

    pt.setId(id);
    pt.setState((Qt::TouchPointStates)state);
    pt.setPos(pos);
    pt.setStartPos(startPos);
    pt.setLastPos(lastPos);
    pt.setScenePos(scenePos);
    pt.setStartScenePos(startScenePos);
    pt.setLastScenePos(lastScenePos);
    pt.setScreenPos(screenPos);
    pt.setStartScreenPos(startScreenPos);
    pt.setLastScreenPos(lastScreenPos);
    pt.setNormalizedPos(normalizedPos);
    pt.setStartNormalizedPos(startNormalizedPos);
    pt.setLastNormalizedPos(lastNormalizedPos);
    //    pt.setRect(rect);
    //    pt.setSceneRect(sceneRect);
    //    pt.setScreenRect(screenRect);
    pt.setPressure(pressure);

    //    return ds;
}

void readLogger(qint32* timeoffset, QContextMenuEvent*& ev, const QString& ds)
{
    Q_UNUSED(timeoffset);
    Q_UNUSED(ds);
    //    qint32 type;
    qint32 reason = QContextMenuEvent::Mouse;
    QPoint pos, globalPos;
    qint32 modifiers = Qt::NoModifier;
    //    ds >> type >> reason >> pos >> globalPos >> modifiers;
    ev = new QContextMenuEvent((QContextMenuEvent::Reason)reason, pos, globalPos, (Qt::KeyboardModifiers)modifiers);
}

void readLogger(qint32* timeoffset, QKeyEvent*& ev, const QStringList& ds)
{
    qint32 type, key, modifiers;
    QString text;
    bool autorep;
    qint32 count;

    *timeoffset = ds.at(1).toInt();
    type = ds.at(2).toInt();
    key = ds.at(3).toInt();
    modifiers = ds.at(4).toInt();
    autorep = ds.at(5).toInt();
    count = ds.at(6).toInt();
    text = ds.at(7);

    ev = new QKeyEvent((QEvent::Type)type, key, (Qt::KeyboardModifiers)modifiers, text, autorep, count);
}

void readLogger(qint32* timeoffset, QMouseEvent*& ev, const QStringList& ds)
{
    qint32 type;
    QPoint pos, globalPos;
    qint32 button, buttons, modifiers;

    *timeoffset = ds.at(1).toInt();
    type = ds.at(2).toInt();
    pos.setX(ds.at(3).toInt());
    pos.setY(ds.at(4).toInt());
    globalPos.setX(ds.at(5).toInt());
    globalPos.setY(ds.at(6).toInt());
    button = ds.at(7).toInt();
    buttons = ds.at(8).toInt();
    modifiers = ds.at(9).toInt();
    //    qDebug() << "readLogger" << *timeoffset;
    //    ds >> type >> pos >> globalPos >> button >> buttons >> modifiers;
    ev = new QMouseEvent((QEvent::Type)type, pos, globalPos, (Qt::MouseButton)button, (Qt::MouseButtons)buttons,
                         (Qt::KeyboardModifiers)modifiers);
    //    return ds;
}

void readLogger(qint32* timeoffset, QTabletEvent*& ev, const QString& ds)
{
    Q_UNUSED(timeoffset);
    Q_UNUSED(ds);
    qint32 type = 0;
    QPoint pos(0, 0), globalPos(0, 0);
    qint32 device = 0, pointerType = 0;
    qreal pressure = 0;
    qint32 xTilt = 0, yTilt = 0;
    qreal tangentialPressure = 0, rotation = 0;
    qint32 z = 0, keyState = 0;
    qint64 uniqueID = 0;

//    ds >> type >> pos >> globalPos >> hiResGlobalPos >> device >>
//        pointerType >> pressure >> xTilt >> yTilt >>
//        tangentialPressure >> rotation >> z >> keyState >> uniqueID;
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
    QPointF hiResGlobalPos;
    ev = new QTabletEvent((QEvent::Type)type, pos, globalPos, hiResGlobalPos, device, pointerType, pressure, xTilt,
                          yTilt, tangentialPressure, rotation, z, (Qt::KeyboardModifiers)keyState, uniqueID);
#else
    //    QTabletEvent(Type t, const QPointF &pos, const QPointF &globalPos,
    //                 int device, int pointerType, qreal pressure, int xTilt, int yTilt,
    //                 qreal tangentialPressure, qreal rotation, int z,
    //                 Qt::KeyboardModifiers keyState, qint64 uniqueID,
    //                 Qt::MouseButton button, Qt::MouseButtons buttons);
    ev = new QTabletEvent((QEvent::Type)type, pos, globalPos, device, pointerType, pressure, xTilt, yTilt,
                          tangentialPressure, rotation, z, (Qt::KeyboardModifiers)keyState, uniqueID, Qt::LeftButton,
                          Qt::LeftButton);
#endif
}

void readLogger(qint32* timeoffset, QTouchEvent*& ev, const QString& ds)
{
    Q_UNUSED(timeoffset);
    Q_UNUSED(ds);
    qint32 type = 0, modifiers = 0, touchPointStates = 0;
    QList<QTouchEvent::TouchPoint> touchPoints;

//    ds >> type >> deviceType >> modifiers >> touchPointStates >> touchPoints;
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
    qint32 deviceType;
    ev = new QTouchEvent((QEvent::Type)type, (QTouchEvent::DeviceType)deviceType, (Qt::KeyboardModifiers)modifiers,
                         (Qt::TouchPointStates)touchPointStates, touchPoints);
#else
    ev = new QTouchEvent((QEvent::Type)type, nullptr, (Qt::KeyboardModifiers)modifiers,
                         (Qt::TouchPointStates)touchPointStates, touchPoints);
#endif
    //    return ds;
}

void readLogger(qint32* timeoffset, QWheelEvent*& ev, const QString& ds)
{
    Q_UNUSED(timeoffset);
    Q_UNUSED(ds);
    //    qint32 type;
    QPoint pos(0, 0), globalPos(0, 0), pixelDelta(0, 0), angleDelta(0, 0);
    qint32 buttons = 0, modifiers = 0;
    //    ds >> type >> pos >> globalPos >> delta >> buttons >> modifiers >> orientation;
    //    QWheelEvent(QPointF pos, QPointF globalPos, QPoint pixelDelta, QPoint angleDelta,
    //                Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers, Qt::ScrollPhase phase,
    //                bool inverted, Qt::MouseEventSource source = Qt::MouseEventNotSynthesized);
    ev = new QWheelEvent(pos, globalPos, pixelDelta, angleDelta, (Qt::MouseButtons)buttons,
                         (Qt::KeyboardModifiers)modifiers, Qt::NoScrollPhase, false);
    //    return ds;
}

void readLogger(qint32* timeoffset, StateEvent*& ev, const QStringList& ds)
{
    //    qint32 type;
    QString eventName;

    *timeoffset = ds.at(1).toInt();
    eventName = ds.at(2);

    ev = new StateEvent(eventName);
}

void readLogger(qint32* timeoffset, TGCDataEvent*& ev, const QString& ds)
{
    Q_UNUSED(timeoffset);
    Q_UNUSED(ds);
    //    qint32 type;
    QByteArray tgcData;
    //    ds >> type >> tgcData;
    ev = new TGCDataEvent(tgcData);
    //    return ds;
}

void readLogger(qint32* timeoffset, MessageEvent*& ev, const QString& ds)
{
    Q_UNUSED(timeoffset);
    Q_UNUSED(ds);
    //    qint32 type;
    QString message;
    //    ds >> type >> message;
    ev = new MessageEvent(message);
    //    return ds;
}

void readLogger(qint32* timeoffset, QInputEvent*& ev, const QStringList& paraList)
{
    switch (paraList.at(0).toInt())
    {
    case EventClass::ContextMenuEvent:
    {
        QContextMenuEvent* tmp;
        //            ds >> tmp;
        QString str;
        readLogger(timeoffset, tmp, str);
        ev = tmp;
    }
    break;
    case EventClass::KeyEvent:
    {
        QKeyEvent* tmp;
        readLogger(timeoffset, tmp, paraList);
        //            ds >> tmp;
        ev = tmp;
    }
    break;
    case EventClass::MouseEvent:
    {
        QMouseEvent* tmp;
        readLogger(timeoffset, tmp, paraList);
        //            ds >> tmp;
        ev = tmp;
    }
    break;
    case EventClass::TabletEvent:
    {
        QTabletEvent* tmp;
        QString str;
        readLogger(timeoffset, tmp, str);
        //            ds >> tmp;
        ev = tmp;
    }
    break;
    case EventClass::TouchEvent:
    {
        QTouchEvent* tmp;
        QString str;
        readLogger(timeoffset, tmp, str);
        //            ds >> tmp;
        ev = tmp;
    }
    break;
    case EventClass::WheelEvent:
    {
        QWheelEvent* tmp;
        QString str;
        readLogger(timeoffset, tmp, str);
        //            ds >> tmp;
        ev = tmp;
    }
    break;
    case EventClass::StateEvent:
    {
        StateEvent* tmp;
        readLogger(timeoffset, tmp, paraList);
        //            ds >> tmp;
        ev = tmp;
    }
    break;
    case EventClass::TGCDataEvent:
    {
        TGCDataEvent* tmp;
        QString str;
        readLogger(timeoffset, tmp, str);
        //            ds >> tmp;
        ev = tmp;
    }
    break;
    case EventClass::MessageEvent:
    {
        MessageEvent* tmp;
        QString str;
        readLogger(timeoffset, tmp, str);
        //            ds >> tmp;
        ev = tmp;
    }
    break;
    }
}

void writeLogger(int timeOffset, const QTouchEvent::TouchPoint& pt)
{
    Q_UNUSED(timeOffset);
    Q_UNUSED(pt);
    //    OperateLogger::Logger()->info("%1:%2:%3:",
    //                                  (qint32) pt.id() ,
    //                                  (qint32) pt.state() ,
    //                                  pt.pos() ,
    //                                  pt.startPos() ,
    //                                  pt.lastPos() ,
    //                                  pt.scenePos() ,
    //                                  pt.startScenePos() ,
    //                                  pt.lastScenePos() ,
    //                                  pt.screenPos() ,
    //                                  pt.startScreenPos() ,
    //                                  pt.lastScreenPos() ,
    //                                  pt.normalizedPos() ,
    //                                  pt.startNormalizedPos() ,
    //                                  pt.lastNormalizedPos() ,
    //                                  pt.rect() ,
    //                                  pt.sceneRect() ,
    //                                  pt.screenRect() ,
    //                                  (qreal) pt.pressure() );

    //    ds << (qint32) pt.id() << (qint32) pt.state() <<
    //        pt.pos() << pt.startPos() << pt.lastPos() <<
    //        pt.scenePos() << pt.startScenePos() << pt.lastScenePos() <<
    //        pt.screenPos() << pt.startScreenPos() << pt.lastScreenPos() <<
    //        pt.normalizedPos() << pt.startNormalizedPos() << pt.lastNormalizedPos() <<
    //        pt.rect() << pt.sceneRect() << pt.screenRect() << (qreal) pt.pressure();
}

void writeLogger(int timeOffset, const QContextMenuEvent& ev)
{
    Q_UNUSED(timeOffset);
    Q_UNUSED(ev);
    //    ds
    //        << (qint32) EventClass::ContextMenuEvent
    //        << (qint32) ev.type()
    //        << (qint32) ev.reason()
    //        << ev.pos()
    //        << ev.globalPos()
    //        << (qint32) ev.modifiers();
}

void writeLogger(int timeOffset, const QKeyEvent& ev)
{
    char log[128];
    sprintf(log, "%d:%d:%d:%d:%d:%d:%d", (qint32)EventClass::KeyEvent, timeOffset, (qint32)ev.type(), (qint32)ev.key(),
            (qint32)ev.modifiers(), ev.isAutoRepeat(), (qint32)ev.count());
    //    OperateLogger::Logger()->info("%1:%2", log, ev.text());
}

void writeLogger(int timeOffset, const QMouseEvent& ev)
{
    char log[128];
    sprintf(log, "%d:%d:%d:%d:%d:%d:%d:%d:%d:%d", (qint32)EventClass::MouseEvent, timeOffset, (qint32)ev.type(),
            ev.pos().x(), ev.pos().y(), ev.globalPos().x(), ev.globalPos().y(), (qint32)ev.button(),
            (qint32)ev.buttons(), (qint32)ev.modifiers());
    //    OperateLogger::Logger()->info("%1", log);
}

void writeLogger(int timeOffset, const QTabletEvent& ev)
{
    Q_UNUSED(timeOffset);
    Q_UNUSED(ev);
    //    ds
    //        << (qint32) EventClass::TabletEvent
    //        << (qint32) ev.type()
    //        << ev.pos()
    //        << ev.globalPos()
    //        << ev.hiResGlobalPos()
    //        << (qint32) ev.device()
    //        << (qint32) ev.pointerType()
    //        << (qreal) ev.pressure()
    //        << (qint32) ev.xTilt()
    //        << (qint32) ev.yTilt()
    //        << (qreal) ev.tangentialPressure()
    //        << (qreal) ev.rotation()
    //        << (qint32) ev.z()
    //        << (qint32) ev.modifiers()
    //        << (qint64) ev.uniqueId();

    //    return ds;
}

void writeLogger(int timeOffset, const QTouchEvent& ev)
{
    Q_UNUSED(timeOffset);
    Q_UNUSED(ev);
    //    ds
    //        << (qint32) EventClass::TouchEvent
    //        << (qint32) ev.type()
    //        << (qint32) ev.deviceType()
    //        << (qint32) ev.modifiers()
    //        << (qint32) ev.touchPointStates()
    //        << ev.touchPoints();

    //    return ds;
}

void writeLogger(int timeOffset, const QWheelEvent& ev)
{
    Q_UNUSED(timeOffset);
    Q_UNUSED(ev);
    //    ds
    //        << (qint32) EventClass::WheelEvent
    //        << (qint32) ev.type()
    //        << ev.pos()
    //        << ev.globalPos()
    //        << (qint32) ev.delta()
    //        << (qint32) ev.buttons()
    //        << (qint32) ev.modifiers()
    //        << (qint32) ev.orientation();
    //    return ds;
}

void writeLogger(int timeOffset, const StateEvent& ev)
{
    char log[128];
    sprintf(log, "%d:%d", (qint32)EventClass::StateEvent, timeOffset);
    //    OperateLogger::Logger()->info("%1:%2", log, ev.eventName());
}

void writeLogger(int timeOffset, const TGCDataEvent& ev)
{
    Q_UNUSED(timeOffset);
    Q_UNUSED(ev);
    //    ds
    //        << (qint32) EventClass::TGCDataEvent
    //        << (qint32) ev.type()
    //        << ev.tgcData();
    //    return ds;
}

void writeLogger(int timeOffset, const MessageEvent& ev)
{
    Q_UNUSED(timeOffset);
    Q_UNUSED(ev);
    //    ds
    //        << (qint32) EventClass::MessageEvent
    //        << (qint32) ev.type()
    //        << ev.message();
    //    return ds;
}

void writeLogger(int timeOffset, const QInputEvent* ev)
{
    if (dynamic_cast<const QContextMenuEvent*>(ev))
        writeLogger(timeOffset, *static_cast<const QContextMenuEvent*>(ev));
    else if (dynamic_cast<const QKeyEvent*>(ev))
        writeLogger(timeOffset, *static_cast<const QKeyEvent*>(ev));
    else if (dynamic_cast<const QMouseEvent*>(ev))
        writeLogger(timeOffset, *static_cast<const QMouseEvent*>(ev));
    else if (dynamic_cast<const QWheelEvent*>(ev))
        writeLogger(timeOffset, *static_cast<const QWheelEvent*>(ev));
    else if (dynamic_cast<const StateEvent*>(ev))
        writeLogger(timeOffset, *static_cast<const StateEvent*>(ev));
    else if (dynamic_cast<const TGCDataEvent*>(ev))
        writeLogger(timeOffset, *static_cast<const TGCDataEvent*>(ev));
    else if (dynamic_cast<const MessageEvent*>(ev))
        writeLogger(timeOffset, *static_cast<const MessageEvent*>(ev));
}

QDataStream& operator<<(QDataStream& ds, const QTouchEvent::TouchPoint& pt)
{
    ds << (qint32)pt.id() << (qint32)pt.state() << pt.pos() << pt.startPos() << pt.lastPos() << pt.scenePos()
       << pt.startScenePos() << pt.lastScenePos() << pt.screenPos() << pt.startScreenPos() << pt.lastScreenPos()
       << pt.normalizedPos() << pt.startNormalizedPos() << pt.lastNormalizedPos() << (qreal)pt.pressure();

    return ds;
}

QDataStream& operator>>(QDataStream& ds, QTouchEvent::TouchPoint& pt)
{
    qint32 id, state;
    QPointF pos, startPos, lastPos;
    QPointF scenePos, startScenePos, lastScenePos;
    QPointF screenPos, startScreenPos, lastScreenPos;
    QPointF normalizedPos, startNormalizedPos, lastNormalizedPos;
    QRectF rect, sceneRect, screenRect;
    qreal pressure;

    ds >> id >> state >> pos >> startPos >> lastPos >> scenePos >> startScenePos >> lastScenePos >> screenPos >>
        startScreenPos >> lastScreenPos >> normalizedPos >> startNormalizedPos >> lastNormalizedPos >> rect >>
        sceneRect >> screenRect >> pressure;

    pt.setId(id);
    pt.setState((Qt::TouchPointStates)state);
    pt.setPos(pos);
    pt.setStartPos(startPos);
    pt.setLastPos(lastPos);
    pt.setScenePos(scenePos);
    pt.setStartScenePos(startScenePos);
    pt.setLastScenePos(lastScenePos);
    pt.setScreenPos(screenPos);
    pt.setStartScreenPos(startScreenPos);
    pt.setLastScreenPos(lastScreenPos);
    pt.setNormalizedPos(normalizedPos);
    pt.setStartNormalizedPos(startNormalizedPos);
    pt.setLastNormalizedPos(lastNormalizedPos);
    pt.setPressure(pressure);

    return ds;
}

QDataStream& operator<<(QDataStream& ds, const QContextMenuEvent& ev)
{
    ds << (qint32)EventClass::ContextMenuEvent << (qint32)ev.type() << (qint32)ev.reason() << ev.pos() << ev.globalPos()
       << (qint32)ev.modifiers();
    return ds;
}

QDataStream& operator<<(QDataStream& ds, const QKeyEvent& ev)
{
    ds << (qint32)EventClass::KeyEvent << (qint32)ev.type() << (qint32)ev.key() << (qint32)ev.modifiers() << ev.text()
       << ev.isAutoRepeat() << (qint32)ev.count();
    return ds;
}

QDataStream& operator<<(QDataStream& ds, const QMouseEvent& ev)
{
    ds << (qint32)EventClass::MouseEvent << (qint32)ev.type() << ev.pos() << ev.globalPos() << (qint32)ev.button()
       << (qint32)ev.buttons() << (qint32)ev.modifiers();
    return ds;
}

QDataStream& operator<<(QDataStream& ds, const QTabletEvent& ev)
{
    ds << (qint32)EventClass::TabletEvent << (qint32)ev.type() << ev.pos()
       << ev.globalPos()
       //        << ev.hiResGlobalPos()
       << (qint32)ev.deviceType() << (qint32)ev.pointerType() << (qreal)ev.pressure() << (qint32)ev.xTilt()
       << (qint32)ev.yTilt() << (qreal)ev.tangentialPressure() << (qreal)ev.rotation() << (qint32)ev.z()
       << (qint32)ev.modifiers() << (qint64)ev.uniqueId() << (qint32)ev.button() << (qint32)ev.buttons();

    return ds;
}

QDataStream& operator<<(QDataStream& ds, const QTouchEvent& ev)
{
    ds << (qint32)EventClass::TouchEvent
       << (qint32)ev.type()
       //        << (qint32) ev.deviceType()
       << (qint32)ev.modifiers() << (qint32)ev.touchPointStates() << ev.touchPoints();

    return ds;
}

QDataStream& operator<<(QDataStream& ds, const QWheelEvent& ev)
{
    //    QWheelEvent(QPointF pos, QPointF globalPos, QPoint pixelDelta, QPoint angleDelta,
    //                Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers, Qt::ScrollPhase phase,
    //                bool inverted, Qt::MouseEventSource source = Qt::MouseEventNotSynthesized);
    ds << (qint32)EventClass::WheelEvent << (qint32)ev.type() << ev.position() << ev.globalPosition() << ev.pixelDelta()
       << (QPoint)ev.angleDelta() << (qint32)ev.buttons() << (qint32)ev.modifiers() << ev.phase() << ev.inverted()
       << ev.source();
    return ds;
}

QDataStream& operator<<(QDataStream& ds, const StateEvent& ev)
{
    ds << (qint32)EventClass::StateEvent << (qint32)ev.type() << ev.eventName();
    return ds;
}

QDataStream& operator<<(QDataStream& ds, const TGCDataEvent& ev)
{
    ds << (qint32)EventClass::TGCDataEvent << (qint32)ev.type() << ev.tgcData();
    return ds;
}

QDataStream& operator<<(QDataStream& ds, const MessageEvent& ev)
{
    ds << (qint32)EventClass::MessageEvent << (qint32)ev.type() << ev.message();
    return ds;
}

QDataStream& operator<<(QDataStream& ds, const QInputEvent* ev)
{
    if (dynamic_cast<const QContextMenuEvent*>(ev))
        ds << *static_cast<const QContextMenuEvent*>(ev);
    else if (dynamic_cast<const QKeyEvent*>(ev))
        ds << *static_cast<const QKeyEvent*>(ev);
    else if (dynamic_cast<const QMouseEvent*>(ev))
        ds << *static_cast<const QMouseEvent*>(ev);
    else if (dynamic_cast<const QWheelEvent*>(ev))
        ds << *static_cast<const QWheelEvent*>(ev);
    else if (dynamic_cast<const StateEvent*>(ev))
        ds << *static_cast<const StateEvent*>(ev);
    else if (dynamic_cast<const TGCDataEvent*>(ev))
        ds << *static_cast<const TGCDataEvent*>(ev);
    else if (dynamic_cast<const MessageEvent*>(ev))
        ds << *static_cast<const MessageEvent*>(ev);

    return ds;
}

QDataStream& operator>>(QDataStream& ds, QContextMenuEvent*& ev)
{
    qint32 type;
    qint32 reason;
    QPoint pos, globalPos;
    qint32 modifiers;
    ds >> type >> reason >> pos >> globalPos >> modifiers;
    ev = new QContextMenuEvent((QContextMenuEvent::Reason)reason, pos, globalPos, (Qt::KeyboardModifiers)modifiers);
    return ds;
}

QDataStream& operator>>(QDataStream& ds, QKeyEvent*& ev)
{
    qint32 type, key, modifiers;
    QString text;
    bool autorep;
    qint32 count;
    ds >> type >> key >> modifiers >> text >> autorep >> count;
    ev = new QKeyEvent((QEvent::Type)type, key, (Qt::KeyboardModifiers)modifiers, text, autorep, count);
    return ds;
}

QDataStream& operator>>(QDataStream& ds, QMouseEvent*& ev)
{
    qint32 type;
    QPoint pos, globalPos;
    qint32 button, buttons, modifiers;
    ds >> type >> pos >> globalPos >> button >> buttons >> modifiers;
    ev = new QMouseEvent((QEvent::Type)type, pos, globalPos, (Qt::MouseButton)button, (Qt::MouseButtons)buttons,
                         (Qt::KeyboardModifiers)modifiers);
    return ds;
}

QDataStream& operator>>(QDataStream& ds, QTabletEvent*& ev)
{
    qint32 type;
    QPoint pos, globalPos;
    QPointF hiResGlobalPos;
    qint32 device, pointerType;
    qreal pressure;
    qint32 xTilt, yTilt;
    qreal tangentialPressure, rotation;
    qint32 z, keyState;
    qint64 uniqueID;
    Qt::MouseButton button;
    Qt::MouseButtons buttons;

    ds >> type >> pos >> globalPos >> hiResGlobalPos >> device >> pointerType >> pressure >> xTilt >> yTilt >>
        tangentialPressure >> rotation >> z >> keyState >> uniqueID >> button >> buttons;
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
    ev = new QTabletEvent((QEvent::Type)type, pos, globalPos, hiResGlobalPos, device, pointerType, pressure, xTilt,
                          yTilt, tangentialPressure, rotation, z, (Qt::KeyboardModifiers)keyState, uniqueID);
#else
    //    QTabletEvent(Type t, const QPointF &pos, const QPointF &globalPos,
    //                 int device, int pointerType, qreal pressure, int xTilt, int yTilt,
    //                 qreal tangentialPressure, qreal rotation, int z,
    //                 Qt::KeyboardModifiers keyState, qint64 uniqueID,
    //                 Qt::MouseButton button, Qt::MouseButtons buttons);
    ev = new QTabletEvent((QEvent::Type)type, pos, globalPos, device, pointerType, pressure, xTilt, yTilt,
                          tangentialPressure, rotation, z, (Qt::KeyboardModifiers)keyState, uniqueID, button, buttons);
#endif
    return ds;
}

QDataStream& operator>>(QDataStream& ds, QTouchEvent*& ev)
{
    qint32 type, deviceType, modifiers, touchPointStates;
    QList<QTouchEvent::TouchPoint> touchPoints;

    ds >> type >> deviceType >> modifiers >> touchPointStates >> touchPoints;
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
    ev = new QTouchEvent((QEvent::Type)type, (QTouchEvent::DeviceType)deviceType, (Qt::KeyboardModifiers)modifiers,
                         (Qt::TouchPointStates)touchPointStates, touchPoints);
#else
    ev = new QTouchEvent((QEvent::Type)type, nullptr, (Qt::KeyboardModifiers)modifiers,
                         (Qt::TouchPointStates)touchPointStates, touchPoints);
#endif
    return ds;
}

QDataStream& operator>>(QDataStream& ds, QWheelEvent*& ev)
{
    qint32 type;
    QPointF pos, globalPos;
    QPoint angleDelta, pixelDelta;
    qint32 buttons, modifiers, phase, source;
    bool inverted;
    ds >> type >> pos >> globalPos >> pixelDelta >> angleDelta >> buttons >> modifiers >> phase >> inverted >> source;
    //    QWheelEvent(QPointF pos, QPointF globalPos, QPoint pixelDelta, QPoint angleDelta,
    //                Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers, Qt::ScrollPhase phase,
    //                bool inverted, Qt::MouseEventSource source = Qt::MouseEventNotSynthesized);
    ev = new QWheelEvent(pos, globalPos, pixelDelta, angleDelta, (Qt::MouseButtons)buttons,
                         (Qt::KeyboardModifiers)modifiers, (Qt::ScrollPhase)phase, inverted,
                         (Qt::MouseEventSource)source);
    return ds;
}

QDataStream& operator>>(QDataStream& ds, StateEvent*& ev)
{
    qint32 type;
    QString eventName;
    ds >> type >> eventName;
    ev = new StateEvent(eventName);
    return ds;
}

QDataStream& operator>>(QDataStream& ds, TGCDataEvent*& ev)
{
    qint32 type;
    QByteArray tgcData;
    ds >> type >> tgcData;
    ev = new TGCDataEvent(tgcData);
    return ds;
}

QDataStream& operator>>(QDataStream& ds, MessageEvent*& ev)
{
    qint32 type;
    QString message;
    ds >> type >> message;
    ev = new MessageEvent(message);
    return ds;
}

QDataStream& operator>>(QDataStream& ds, QInputEvent*& ev)
{
    qint32 cls;
    ds >> cls;

    switch (cls)
    {
    case EventClass::ContextMenuEvent:
    {
        QContextMenuEvent* tmp;
        ds >> tmp;
        ev = tmp;
    }
    break;
    case EventClass::KeyEvent:
    {
        QKeyEvent* tmp;
        ds >> tmp;
        ev = tmp;
    }
    break;
    case EventClass::MouseEvent:
    {
        QMouseEvent* tmp;
        ds >> tmp;
        ev = tmp;
    }
    break;
    case EventClass::TabletEvent:
    {
        QTabletEvent* tmp;
        ds >> tmp;
        ev = tmp;
    }
    break;
    case EventClass::TouchEvent:
    {
        QTouchEvent* tmp;
        ds >> tmp;
        ev = tmp;
    }
    break;
    case EventClass::WheelEvent:
    {
        QWheelEvent* tmp;
        ds >> tmp;
        ev = tmp;
    }
    break;
    case EventClass::StateEvent:
    {
        StateEvent* tmp;
        ds >> tmp;
        ev = tmp;
    }
    break;
    case EventClass::TGCDataEvent:
    {
        TGCDataEvent* tmp;
        ds >> tmp;
        ev = tmp;
    }
    break;
    case EventClass::MessageEvent:
    {
        MessageEvent* tmp;
        ds >> tmp;
        ev = tmp;
    }
    break;
    }
    return ds;
}
