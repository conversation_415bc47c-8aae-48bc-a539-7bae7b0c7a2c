#include "testcontainer.h"
#include "itestcommand.h"

TestContainer& TestContainer::instance()
{
    static TestContainer instance;
    return instance;
}

TestContainer::~TestContainer()
{
    qDeleteAll(m_testCommands.values());
    m_testCommands.clear();
}

void TestContainer::addTestCommand(const QString& name, ITestCommand* testCommand)
{
    if (!m_testCommands.contains(name))
    {
        m_testCommands.insert(name, testCommand);
    }
}

ITestCommand* TestContainer::testCommand(const QString& name) const
{
    if (m_testCommands.contains(name))
    {
        return m_testCommands.value(name);
    }
    else
    {
        return NULL;
    }
}

QHash<QString, ITestCommand*>& TestContainer::testCommands()
{
    return m_testCommands;
}

TestContainer::TestContainer()
{
}
