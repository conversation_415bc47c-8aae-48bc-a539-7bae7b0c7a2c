#include "worklistsearchwidget.h"
#include "ui_worklistsearchwidget.h"
#include "dicomregister.h"
#include "commonprogressbar.h"
#include <QThreadPool>
#include <QStandardItemModel>
#include <QFile>
#include <QProcess>
#include "globaldef.h"
#include "resource.h"
#include "util.h"
#include "syncer.h"
#include "applogger.h"
#include <QDebug>
#include "statefilterlocker.h"
#include "PersonName.h"
#include "i18nstrings.h"
#include "setting.h"
#include "appsetting.h"
#include <QDate>
#include "settingpanel.h"
#include "stateeventnames.h"

////.cpp
WorkListSearchDialog::WorkListSearchDialog(IStateManager* stateManager, QWidget* parent)
    : BaseInputAbleDialogFrame(parent /*, true*/)
    , m_Child(NULL)
{
    setIsShowOk(false);
    m_Child = new WorkListSearchWidget(stateManager, this);
    connect(m_Child, SIGNAL(accept()), this, SLOT(accept()));
    connect(m_Child, SIGNAL(getDicomResponse(DcmResponseInfo)), this, SIGNAL(getDicomResponse(DcmResponseInfo)));
    this->setContent(m_Child);
}

void WorkListSearchDialog::doDicomWorklistTask()
{
    m_Child->doDicomWorklistTask();
}

void WorkListSearchDialog::showEvent(QShowEvent* e)
{
    BaseInputAbleDialogFrame::showEvent(e);
    //采用异步的方式执行初始查询,解决查询后edit不能编辑的问题,具体原因不确定,只能知道大概原因:
    // WorkListSearchDialog.exec函数中会触发showEvent,如果使用同步方式这时会被m_ProgressBar阻塞,
    //导致dialog.exec函数未执行完毕
    metaObject()->invokeMethod(this, "doDicomWorklistTask", Qt::QueuedConnection);
}

WorkListSearchWidget::WorkListSearchWidget(IStateManager* stateManager, QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::WorkListSearchWidget)
    , m_ProgressBar(new CommonProgressBarFrame(nullptr))
    , m_Index(-1)
    , m_StateManager(stateManager)
{
    m_ProgressBar->setFrameTitle(tr("Searching..."));
    m_ProgressBar->hide();
    ui->setupUi(this);
    initTreeView();
    connect(this, SIGNAL(findscustarted()), this, SLOT(onFindscustarted()));
    connect(this, SIGNAL(findscufinished(int)), this, SLOT(getRespond(int)));
    if (AppSetting::isAnimalAndChinese())
    {
        setID2AnimalID();
    }
}

WorkListSearchWidget::~WorkListSearchWidget()
{
    delete ui;
}

void WorkListSearchWidget::doDicomWorklistTask()
{
    if (DicomRegister::instance().checkIfDicomWorklistUsable())
    {
        if (standardmodel->rowCount() > 0)
        {
            standardmodel->removeRows(0, standardmodel->rowCount());
        }

        ui->appButton->setEnabled(false);
        ui->clearButton->setEnabled(false);

        DicomWorklistTask* task = new DicomWorklistTask(this);

        Util::runRunnable(task);
    }
}

void WorkListSearchWidget::initTreeView()
{
    QStringList header = getRealTimeTreeviewHeader();
    standardmodel = new QStandardItemModel(0, header.count(), this);
    standardmodel->setHorizontalHeaderLabels(header);
    QStandardItem* itemPrototype = new QStandardItem;
    itemPrototype->setEditable(false);
    standardmodel->setItemPrototype(itemPrototype);

    ui->treeView->setRootIsDecorated(false); //隐藏root
    ui->treeView->setModel(standardmodel);
    ui->treeView->hideColumn(StudyInstanceUIDTag);
    ui->treeView->hideColumn(InternalPatientsNameTag);

    ui->treeView->setColumnWidth(0, 190);
    ui->treeView->setColumnWidth(1, 251);
    ui->treeView->setColumnWidth(2, 180);
    ui->treeView->setColumnWidth(3, 80);
    ui->treeView->setColumnWidth(4, 251);
    ui->treeView->setColumnWidth(5, 80);
    m_ProgressBar->hide();
}

void WorkListSearchWidget::doDicomFindScu()
{
    emit findscustarted();

    QStringList keys;
    keys << DicomChison::netKeyOfAetitleName() << DicomChison::netKeyOfIPName() << DicomChison::netKeyOfPortName()
         << DicomChison::netKeyOfTimeoutName() << DicomChison::scheduledStationTitleIndex()
         << DicomChison::scheduledStationAETitleName() << DicomChison::netKeyOfStartDateName()
         << DicomChison::netKeyOfEndDateName() << DicomChison::netKeyOfPriorDateValue()
         << DicomChison::netKeyOfNextDateValue() << DicomChison::netKeyOfStartDateIndex();

    QMap<QString, QString> keyValues = DicomChison::getDefaultServerInfo(DicomChison::worklist, keys);

    QString startDate = keyValues.value(DicomChison::netKeyOfStartDateName());
    QString endDate = keyValues.value(DicomChison::netKeyOfEndDateName());
    QString priorDate = keyValues.value(DicomChison::netKeyOfPriorDateValue());
    QString nextDate = keyValues.value(DicomChison::netKeyOfNextDateValue());
    int StartDateIndex = (keyValues.value(DicomChison::netKeyOfStartDateIndex())).toInt();
    int priorDateValue = -priorDate.toInt();
    int nextDateValue = nextDate.toInt();
    QDate currentDate = QDate::currentDate();

    if (StartDateIndex == 0) // 0 is today
    {
        QDate newStartDateValue = currentDate;
        QDate oldEndDateValue = currentDate;
        QString newStartDate = newStartDateValue.toString("yyyyMMdd");
        QString oldEndDate = oldEndDateValue.toString("yyyyMMdd");
        keyValues[DicomChison::netKeyOfStartDateName()] = newStartDate;
        keyValues[DicomChison::netKeyOfEndDateName()] = oldEndDate;
    }
    else if (StartDateIndex == 1) // 1 is range
    {
        QDate newStartDateValue = currentDate.addDays(priorDateValue);
        QDate oldEndDateValue = currentDate.addDays(nextDateValue);
        QString newStartDate = newStartDateValue.toString("yyyyMMdd");
        QString oldEndDate = oldEndDateValue.toString("yyyyMMdd");
        keyValues[DicomChison::netKeyOfStartDateName()] = newStartDate;
        keyValues[DicomChison::netKeyOfEndDateName()] = oldEndDate;
    }
    else if (StartDateIndex == 2) // 2 is past week
    {
        priorDateValue = -7;
        QDate newStartDateValue = currentDate.addDays(priorDateValue);
        QDate oldEndDateValue = currentDate;
        QString newStartDate = newStartDateValue.toString("yyyyMMdd");
        QString oldEndDate = oldEndDateValue.toString("yyyyMMdd");
        keyValues[DicomChison::netKeyOfStartDateName()] = newStartDate;
        keyValues[DicomChison::netKeyOfEndDateName()] = oldEndDate;
    }
    else if (StartDateIndex == 3) // 3 is past month
    {
        QDate newStartDateValue = currentDate.addMonths(-1);
        QDate oldEndDateValue = currentDate;
        QString newStartDate = newStartDateValue.toString("yyyyMMdd");
        QString oldEndDate = oldEndDateValue.toString("yyyyMMdd");
        keyValues[DicomChison::netKeyOfStartDateName()] = newStartDate;
        keyValues[DicomChison::netKeyOfEndDateName()] = oldEndDate;
    }

    int res = -1;
    if (!keyValues.isEmpty())
    {
        res = dicomFindScu(
            keyValues.value(DicomChison::netKeyOfAetitleName()), keyValues.value(DicomChison::netKeyOfIPName()),
            keyValues.value(DicomChison::netKeyOfPortName()),
            keyValues.value(DicomChison::netKeyOfTimeoutName()).toInt(),
            keyValues.value(DicomChison::scheduledStationTitleIndex()).toInt(),
            keyValues.value(DicomChison::scheduledStationAETitleName()),
            keyValues.value(DicomChison::netKeyOfStartDateName()), keyValues.value(DicomChison::netKeyOfEndDateName()));
    }

    emit findscufinished(res);
}

int WorkListSearchWidget::dicomFindScu(const QString& title, const QString& ip, const QString& port, int timeout,
                                       int scheduledStationTitleIndex, const QString& scheduledStationAETitle,
                                       const QString& startDate, const QString& endDate)
{
    if (title.isEmpty())
    {
        return -1;
    }

    QString localAETitle = DicomChison::getLocalAETitle();

    QStringList args;
    if (ip.isEmpty() || port.isEmpty())
        return -1;
    if (timeout <= 0)
        timeout = 10;

    QString patientId;
    QString patientName;
    if (ui->searchByComboBox->currentIndex() == 0)
    {
        patientId = ui->searchkeyLineEdit->text();
    }
    else
    {
        patientName = ui->searchkeyLineEdit->text();
    }
    // QString birthDay =  ui->birthdayEdit->getDate().isValid() ? ui->birthdayEdit->getDate().toString("yyyyMMdd") :
    // QString();
    int res = 0;
#if USE_DICOMSCU_LIB
    QTextStream stream(&cmd);
    stream << Resource::dicomFindScuCmd << " -P -X -W -to " << timeout << " --call "
           << "\"" << title << "\"";
    if (!localAETitle.isEmpty())
    {
        stream << " --aetitle "
               << "\"" << localAETitle << "\"";
        //        QTextStream(&cmd) << "./chison_findscu  -P -v -X -W -to " << timeout << " --call " << title << "
        //        --aetitle " << localAETitle << " " << ip << " " << port
        //                          << " -k " << "0010,0010" << "=" << patientName << " "
        //                          << " -k " << "0010,0020" << "=" << patientId << " "
        //                          << " -k " << "0010,0030" << "=\"" << QString("") << "\" "
        //                          << " -k " << "0032,1032" << "=\"" << QString("") << "\" "
        //                          << " -k " << "0010,0040" << "=\"" << QString("") << "\" "
        //                          << " -k " << "0008,0050";
    }

    if (SettingPanel::ThisSystem == scheduledStationTitleIndex)
    {
        stream << " --sq-aetitle "
               << "\"" << localAETitle << "\"";
    }
    else if (SettingPanel::Another == scheduledStationTitleIndex)
    {
        stream << " --sq-aetitle "
               << "\"" << scheduledStationAETitle << "\"";
    }

    if (!startDate.isEmpty() || endDate.isEmpty())
    {
        stream << " --sq-date "
               << "\"" << startDate << "-" << endDate << "\"";
    }
    if (AppLogger::dicomDebug())
    {
        stream << " -d ";
    }
    if (AppLogger::dicomVerbose())
    {
        stream << " -v ";
    }

    // DCM_SpecificCharacterSet DcmTagKey(0x0008, 0x0005) setto utf8
    stream << " " << ip << " " << port << " -k "
           << "0010,0010"
           << "=" << patientName << " "
           << " -k "
           << "0010,0020"
           << "=" << patientId << " "
           << " -k "
           << "0010,0030"
           << "=\"" << QString("") << "\" "
           << " -k "
           << "0032,1032"
           << "=\"" << QString("") << "\" "
           << " -k "
           << "0010,0040"
           << "=\"" << QString("") << "\" "
           << " -k "
           << "0008,0050"
           << "=\"" << QString("") << "\" "
           << " -k "
           << "0008,0050";

    if (AppLogger::dicomVerbose())
    {
        qDebug() << cmd;
    }

    res = DicomChison::doDicomTask(cmd);
#else
    args << "-P"
         << "-X"
         << "-W"
         << "-to" << QString::number(timeout) << "--call" << title;
    if (!localAETitle.isEmpty())
    {
        args << "--aetitle" << localAETitle;
        //        QTextStream(&cmd) << "./chison_findscu  -P -v -X -W -to " << timeout << " --call " << title << "
        //        --aetitle " << localAETitle <<" " << ip << " " << port
        //                          << " -k " << "0010,0010" << "=\"" << patientName<< "\" "
        //                          << " -k " << "0010,0020" << "=\"" << patientId << "\" "
        //                          << " -k " << "0010,0030" << "=\"" << QString("") << "\" "
        //                          << " -k " << "0032,1032" << "=\"" << QString("") << "\" "
        //                          << " -k " << "0010,0040" << "=\"" << QString("") << "\" "
        //                          << " -k " << "0008,0050";
    }

    if (SettingPanel::ThisSystem == scheduledStationTitleIndex)
    {
        args << "--sq-aetitle" << localAETitle;
    }
    else if (SettingPanel::Another == scheduledStationTitleIndex)
    {
        args << "--sq-aetitle" << scheduledStationAETitle;
    }

    if (!startDate.isEmpty() || !endDate.isEmpty())
    {
        args << "--sq-date" << startDate + "-" + endDate;
    }
    if (AppLogger::dicomDebug())
    {
        args << "-d";
    }
    if (AppLogger::dicomVerbose())
    {
        args << "-v";
    }

    // DCM_SpecificCharacterSet DcmTagKey(0x0008, 0x0005) setto utf8
    args << ip << port << "-k" << QString("0010,0010=%1").arg(patientName) << "-k"
         << QString("0010,0020=%1").arg(patientId) << "-k" << QString("0010,0030=%1").arg("") << "-k"
         << QString("0032,1032=%1").arg("") << "-k" << QString("0010,0040=%1").arg("") << "-k"
         << QString("0008,0050=%1").arg("") << "-k" << QString("0020,000D=%1").arg("") << "-k"
         << QString("0008,1030=%1").arg("") << "-k" << QString("0010,1020=%1").arg("") << "-k"
         << QString("0010,1030=%1").arg("") << "-k" << QString("0040,0007=%1").arg("") << "-k"
         << QString("0032,1060=%1").arg("") << "-k" << QString("0008,0104=%1").arg("");
    if (DicomChison::checkIfDefaultServerExist(DicomChison::mpps))
    {
        args << "-M"
             //<< "-k" << QString("0040,0100=%1").arg("")
             << "-k" << QString("0040,1001=%1").arg("") << "-k" << QString("0032,1060=%1").arg("") << "-k"
             << QString("0032,1064=%1").arg("") << "-k" << QString("0040,0270=%1").arg("") << "-k"
             << QString("0008,1120=%1").arg("") << "-k" << QString("0040,0253=%1").arg("") << "-k"
             << QString("0040,0241=%1").arg("") << "-k" << QString("0040,0242=%1").arg("") << "-k"
             << QString("0040,0243=%1").arg("") << "-k" << QString("0040,0244=%1").arg("") << "-k"
             << QString("0040,0245=%1").arg("") << "-k" << QString("0040,0252=%1").arg("") << "-k"
             << QString("0040,0254=%1").arg("") << "-k" << QString("0040,0255=%1").arg("") << "-k"
             << QString("0008,1032=%1").arg("") << "-k" << QString("0040,0250=%1").arg("") << "-k"
             << QString("0040,0251=%1").arg("") << "-k" << QString("0040,0281=%1").arg("") << "-k"
             << QString("0008,0060=%1").arg("") << "-k" << QString("0020,0010=%1").arg("") << "-k"
             << QString("0040,0260=%1").arg("") << "-k" << QString("0040,0340=%1").arg("");
    }

    if (AppLogger::dicomVerbose())
    {
        qDebug() << args;
    }

    QProcess process;
    process.start(Resource::dicomFindScuCmd, args);
    res = process.waitForFinished(-1) ? 0 : -1;
#endif

    if (res == 0)
    {
        qDebug() << ("chison_findscu succeed\n");
    }
    else
    {
        qDebug() << "chison_findscu failed error no is %d in DicomFindscu()\n";
    }

    return res;
}

QString WorkListSearchWidget::localCodec() const
{
    QMap<QString, QString> keyValues =
        DicomChison::getDefaultServerInfo(DicomChison::worklist, QStringList() << DicomChison::encoding());
    QString codec = keyValues.value(DicomChison::encoding());

    if (codec.isEmpty())
    {
        // 如果这个worklist 服务设置是老版本的软件，codec是空，使用当前语言默认的codec
        codec = QString(I18nStrings::instance().sl2Codec(Setting::instance().defaults().shortLanguage()));
    }

    return codec;
}

QStringList WorkListSearchWidget::getRealTimeTreeviewHeader()
{
    QStringList header;
    if (AppSetting::isAnimalAndChinese())
    {
        header << tr("Animal ID") << tr("Animal Name") << tr("BirthDate")
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
               << QApplication::translate("Sex", Resource::sexLabel().toUtf8().data(), 0, QApplication::UnicodeUTF8)
#else
               << QApplication::translate("Sex", Resource::sexLabel().toUtf8().data())
#endif
               << tr("Doctor") << tr("Acc#") << "StudyInstanceUID"
               << "InternalName";
    }
    else
    {
        header << tr("ID") << tr("Name") << tr("BirthDate")
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
               << QApplication::translate("Sex", Resource::sexLabel().toUtf8().data(), 0, QApplication::UnicodeUTF8)
#else
               << QApplication::translate("Sex", Resource::sexLabel().toUtf8().data())
#endif
               << tr("Doctor") << tr("Acc#") << "StudyInstanceUID" << tr("Height") << tr("Weight") << "InternalName";
    }
    return header;
}

void WorkListSearchWidget::retranslateUi()
{
    m_ProgressBar->setFrameTitle(tr("Searching..."));
    ui->retranslateUi(this);

    QStringList header = getRealTimeTreeviewHeader(); //表头的字符串需要在切换语言情况下被实时翻译
    if (nullptr != standardmodel)
    {
        standardmodel->setHorizontalHeaderLabels(header);
    }

    if (AppSetting::isAnimalAndChinese())
    {
        setID2AnimalID(); //仅在中文的兽超下，显示“动物”字眼，否则与人超翻译相同
    }
}

void WorkListSearchWidget::onFindscustarted()
{
    StateFilterLocker locker(m_StateManager, StateEventNames::NothingStateFilter());
    m_ProgressBar->exec(); // hide()结束阻塞
}

void WorkListSearchWidget::on_searchButton_clicked()
{
    doDicomWorklistTask();
}

void WorkListSearchWidget::getRespond(int res)
{
    m_ProgressBar->hide();
    //    UTIL_DATA_SYNCER

    if (res == 0)
    {
        FILE* pFile;

        QByteArray tempByt = DicomChison::findscuResultFileName().toLatin1();
        char* findscuname = tempByt.data();
        if ((pFile = fopen(findscuname, "rb")) != NULL)
        {
            QString codec = localCodec();

            DcmResponse_Tag respond;

            QStringList temp;

            m_Index = -1;
            m_RespondList.clear();
            for (; fread(&respond, sizeof(DcmResponse_Tag), 1, pFile);)
            { // order :model的显示顺序
                temp << Util::strToUnicode(respond.PatientID, codec) << Util::strToUnicode(respond.PatientsName, codec)
                     << Util::strToUnicode(respond.PatientsBirthDate, codec)
                     << Util::strToUnicode(respond.PatientsSex, codec)
                     << Util::strToUnicode(respond.PhysiciansName, codec)
                     << Util::strToUnicode(respond.AccessionNumber, codec)
                     << Util::strToUnicode(respond.StudyInstanceID, codec)
                     << Util::strToUnicode(respond.PatientSize, codec)
                     << Util::strToUnicode(respond.PatientWeight, codec);

                m_RespondList.append(respond);
                //显示在界面
                standardmodel->insertRow(0);
                for (int i = 0; i < temp.size(); ++i)
                {
                    QString content;
                    if (i == 1)
                    {
                        // convert PaitentsName from DICOM format to readable string
                        NameComponent name;
                        name.setInternalPersonName(temp.at(i));
                        content = name.formattedName();
                        // [BUG:15314] 病人姓名需要存储一份原始的internal 格式的姓名，用于apply到新建病人界面
                        // internal格式的姓名为： A^B^C
                        standardmodel->setData(standardmodel->index(0, InternalPatientsNameTag), temp.at(i));
                    }
                    else
                    {
                        content = temp.at(i);
                    }
                    standardmodel->setData(standardmodel->index(0, i), content);
                }
                temp.clear();
            }
            fclose(pFile);
            if (standardmodel->rowCount() > 0)
            {
                // Apply data of first row to patientTopWidget
                m_DcmResponseInfo = convertData(standardmodel->index(0, 0));
                m_Index = -1;
                ui->clearButton->setEnabled(true);
            }

            QFile::remove(DicomChison::findscuResultFileName());
        }
        else
        {
            qDebug() << " Can not open file " << DicomChison::findscuResultFileName();
            return;
        }
    }
}

void WorkListSearchWidget::on_searchByComboBox_activated(const QString& arg1)
{
    ui->searchKeyLabel->setText(arg1);
    ui->searchkeyLineEdit->clear();
}

void WorkListSearchWidget::on_appButton_clicked()
{
    if (m_Index < 0)
    {
        return;
    }
    else
    {
        emit getDicomResponse(m_DcmResponseInfo);
        emit accept();
    }
}

void WorkListSearchWidget::on_clearButton_clicked()
{
    ui->appButton->setEnabled(false);

    if (standardmodel->rowCount() > 0)
    {
        standardmodel->removeRows(0, standardmodel->rowCount());
    }

    if (QFile(DicomChison::findscuResultFileName()).exists())
    {
        UTIL_DATA_SYNCER
        QFile::remove(DicomChison::findscuResultFileName());
    }
    ui->clearButton->setEnabled(false);
}

void WorkListSearchWidget::on_treeView_clicked(const QModelIndex& index)
{
    m_Index = index.row();
    m_DcmResponseInfo = convertData(index);
    ui->appButton->setEnabled(true);
    ui->clearButton->setEnabled(true);
}

void WorkListSearchWidget::on_treeView_doubleClicked(const QModelIndex& index)
{
    m_Index = index.row();
    m_DcmResponseInfo = convertData(index);
    on_appButton_clicked();
}

DcmResponseInfo WorkListSearchWidget::convertData(const QModelIndex& index)
{
    DcmResponseInfo dcmResponseInfo;

    if (!index.isValid() || standardmodel->columnCount() > ResponseTagCount)
    {
        return dcmResponseInfo;
    }
    int row = index.row();

    dcmResponseInfo.PatientID = standardmodel->index(row, PatientIDTag).data().toString();
    dcmResponseInfo.PatientsName = standardmodel->index(row, InternalPatientsNameTag).data().toString();
    dcmResponseInfo.PatientsBirthDate = standardmodel->index(row, PatientsBirthDateTag).data().toString();
    dcmResponseInfo.PatientsSex = standardmodel->index(row, PatientsSexTag).data().toString();
    dcmResponseInfo.PhysiciansName = standardmodel->index(row, PhysiciansNameTag).data().toString();
    dcmResponseInfo.AccessionNumber = standardmodel->index(row, AccessionNumberTag).data().toString();
    dcmResponseInfo.StudyInstanceID = standardmodel->index(row, StudyInstanceUIDTag).data().toString();
    dcmResponseInfo.PatientSize = standardmodel->index(row, PatientSize).data().toString();
    dcmResponseInfo.PatientWeight = standardmodel->index(row, PatientWeight).data().toString();
    dcmResponseInfo.StudyDescription = m_RespondList.at(m_RespondList.size() - 1 - row).StudyDescription;

    return dcmResponseInfo;
}

void WorkListSearchWidget::setID2AnimalID()
{
    for (int i = 0; i < ui->searchByComboBox->count(); i++)
    {
        if (ui->searchByComboBox->itemText(i) == tr("ID"))
        {
            ui->searchByComboBox->setItemText(i, tr("Animal ID"));
        }
        if (ui->searchByComboBox->itemText(i) == tr("Name"))
        {
            ui->searchByComboBox->setItemText(i, tr("Animal Name"));
        }
    }
    if (ui->searchKeyLabel->text() == tr("ID"))
    {
        ui->searchKeyLabel->setText(tr("Animal ID"));
    }
    if (ui->searchKeyLabel->text() == tr("Name"))
    {
        ui->searchKeyLabel->setText(tr("Animal Name"));
    }
}
