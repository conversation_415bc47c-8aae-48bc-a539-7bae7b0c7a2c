#ifndef ODSFOURDLICENESEPROCESSOR_H
#define ODSFOURDLICENESEPROCESSOR_H
#include "abstractfourdlicenseprocessor.h"

class CONTROLSSHARED_EXPORT ODSFourDLiceneseProcessor : public AbstractFourDLicenseProcessor
{
    Q_OBJECT
public:
    explicit ODSFourDLiceneseProcessor(QObject* parent = 0);
    ~ODSFourDLiceneseProcessor();
    void verifyLicenseState();
    void processFourDLicenseState(bool showInfo = true);
public slots:
    void onFourDStatusChanged(bool isOpen);
    void onSystemDateChanged();
};

#endif // ODSFOURDLICENESEPROCESSOR_H
