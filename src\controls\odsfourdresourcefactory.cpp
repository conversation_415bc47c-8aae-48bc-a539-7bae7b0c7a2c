#include "odsfourdresourcefactory.h"
#include "odsfourddatasender.h"
#include "odsfourdlivewidget.h"
#include "odsfourdapi.h"
#include "odsfourdliceneseprocessor.h"

ODSFourDResourceFactory::ODSFourDResourceFactory()
{
}

ODSFourDResourceFactory::~ODSFourDResourceFactory()
{
}

IFourDLiveWidget* ODSFourDResourceFactory::createFourDLiveWidget()
{
    return new ODSFourDLiveWidget(new ODSFourDDataSender);
}

IFourDAPI* ODSFourDResourceFactory::createFourDAPI()
{
    return new ODSFourDAPI;
}

IFourDLicenseProcessor* ODSFourDResourceFactory::createFourDLicenseProcessor()
{
    return new ODSFourDLiceneseProcessor;
}
