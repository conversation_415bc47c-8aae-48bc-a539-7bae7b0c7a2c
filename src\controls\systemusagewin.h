#ifndef SYSTEMUSAGEWIN_H
#define SYSTEMUSAGEWIN_H
#ifdef SYS_WINDOWS

#include <windows.h>
#include "controls_global.h"
#include "isystemusage.h"

struct IMMDevice;
struct IAudioEndpointVolume;
class CONTROLSSHARED_EXPORT SystemUsageWin : public ISystemUsage
{
public:
    SystemUsageWin();
    ~SystemUsageWin();

    virtual int getCpuUsage();
    virtual int getCpuTemperature();
    virtual unsigned long getCpuThreadCount();
    virtual unsigned long getCpuCoresCount();
    virtual void getMemoryUsage(unsigned int& nMemTotal, unsigned int& nMemFree);
    virtual int getCurVolumeVal();
    virtual bool setCurVolumeVal(int nVolumeVal);
    virtual bool playVideo(const QString& fileName);

private:
    void initCpuInformation();
    void initWinRing0();

private:
    int getProcessorNumber();
    DWORD countSetBits(ULONG_PTR bitMask);
    IMMDevice* getDefaultAudioDevice();
    IAudioEndpointVolume* getAudioEndpointVolume();

private:
    int m_nProcessorsNum;
    unsigned long m_nCpuCoreCount;
    unsigned long m_nCpuThreadCount;
    HANDLE m_hCurProcess;
    HANDLE m_hDevice;
    HMODULE m_hWinRing0;

    TCHAR m_cDriverFileName[MAX_PATH];
    TCHAR m_cDriverPath[MAX_PATH];
};

#endif
#endif // SYSTEMUSAGEWIN_H
