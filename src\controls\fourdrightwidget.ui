<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FourDRightWidget</class>
 <widget class="QWidget" name="FourDRightWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>2</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="BorderedQFrame" name="fourDRightFrame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>1</number>
      </property>
      <property name="topMargin">
       <number>2</number>
      </property>
      <property name="rightMargin">
       <number>1</number>
      </property>
      <property name="bottomMargin">
       <number>2</number>
      </property>
      <item>
       <widget class="ImageSkimManager" name="imageClipWidget" native="true"/>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BorderedQFrame</class>
   <extends>QFrame</extends>
   <header>borderedqframe.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>ImageSkimManager</class>
   <extends>QWidget</extends>
   <header>imageskimmanager.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
