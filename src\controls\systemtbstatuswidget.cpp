#include "systemtbstatuswidget.h"
#include "ui_systemtbstatuswidget.h"
#include "systemhintmodel.h"
#include "util.h"

SystemTBStatusWidget::SystemTBStatusWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::SystemTBStatusWidget)
    , m_Model(NULL)
{
    ui->setupUi(this);
    ui->horizontalLayout->setAlignment(Qt::AlignLeft);
}

SystemTBStatusWidget::~SystemTBStatusWidget()
{
    delete ui;
}

void SystemTBStatusWidget::setModel(SystemHintModel* model)
{
    m_Model = model;

    connect(m_Model, SIGNAL(tBStatusChanged()), this, SLOT(slot_onTBStatusChanged()));

    slot_onTBStatusChanged();
}

void SystemTBStatusWidget::slot_onTBStatusChanged()
{
    if (m_Model != NULL)
    {
        if (m_Model->tBStatus().isEmpty())
        {
            ui->touchBallStatusLabel->setText(QString());
        }
        else
        {
            ui->touchBallStatusLabel->setText(tr("TB:") + /*Util::translate(m_Model, */ m_Model->tBStatus() /*)*/);
        }
    }
}

void SystemTBStatusWidget::retranslateUi()
{
    if (m_Model != NULL)
    {
        slot_onTBStatusChanged();
    }
}
