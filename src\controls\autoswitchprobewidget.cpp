/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#include "autoswitchprobewidget.h"
#include "ui_autoswitchprobewidget.h"
#include "itestcommand.h"
#include "diskselectionwidget.h"
#include "htmlexporter.h"
#include "util.h"
#include "appsetting.h"
#include <QFile>
#include <QDateTime>

const QString AutoSwitchProbeWidget::m_testExportFileName = "switchprobetest";
const QString AutoSwitchProbeWidget::m_exportSuffix = ".html";

AutoSwitchProbeWidget::AutoSwitchProbeWidget(QWidget* parent)
    : BaseWidget(parent)
    , m_isStarted(false)
    , m_finishTimes(0)
    , ui(new Ui::AutoSwitchProbeWidget)
    , m_command(NULL)
    , m_exporter(NULL)
    , m_htmlRow(1)
    , m_htmlCol(2)
{
    ui->setupUi(this);
    ui->pushButtonExport->setEnabled(false);
    ui->pushButtonStop->setEnabled(false);
}

AutoSwitchProbeWidget::~AutoSwitchProbeWidget()
{
    delete ui;
}

void AutoSwitchProbeWidget::setTestCommand(ITestCommand* command)
{
    m_command = command;
    QObject* obj = dynamic_cast<QObject*>(command);

    connect(obj, SIGNAL(finishTest()), this, SLOT(onFinish()));
}

AutoSwitchProbeDialog::AutoSwitchProbeDialog(QWidget* parent)
    : BaseInputAbleDialogFrame(parent)
    , m_child(NULL)
{
    m_child = new AutoSwitchProbeWidget(this);
    this->setContent(m_child);
    this->setFrameTitle("Auto Switch Probe");
}

void AutoSwitchProbeDialog::setTestCommand(ITestCommand* command)
{
    m_child->setTestCommand(command);
}

void AutoSwitchProbeWidget::on_pushButtonStart_clicked()
{
    if (m_command)
    {
        if (!m_isStarted)
        {
            m_isStarted = true;
            m_startTime = QDateTime::currentDateTime().toString();
            ui->pushButtonExport->setEnabled(false);
            m_finishTimes = 0;
            ui->pushButtonStart->setText(tr("Restart Test"));
            ui->pushButtonStop->setText(tr("Stop Test"));
            ui->pushButtonStop->setEnabled(true);
            m_command->execute();
        }
        else
        {
            m_startTime = QDateTime::currentDateTime().toString();
            ui->pushButtonExport->setEnabled(false);
            m_finishTimes = 0;
            m_command->undo();
            m_command->execute();
        }
    }
}

void AutoSwitchProbeWidget::on_pushButtonApply_clicked()
{
    if (ui->lineEdit->text().toInt() >= 2 && ui->lineEdit->text().toInt() <= 99)
    {
        m_command->setTestTime(ui->lineEdit->text().toInt());
    }
    else
    {
        ui->lineEdit->setText("3");
        m_command->setTestTime(3);
    }
}

void AutoSwitchProbeWidget::onFinish()
{
    m_finishTimes += 1;
}

QString AutoSwitchProbeWidget::generateHTML()
{
    if (!m_exporter)
    {
        m_exporter = new HTMLExporter();
    }
    m_exporter->clearValue();
    m_exporter->setRow(m_htmlRow);
    m_exporter->setCol(m_htmlCol);
    QStringList titleList;
    titleList << "Chison AutoTest Name"
              << "Times";

    m_exporter->setTitle("Chison Switch Probe Test");
    m_exporter->setTitleList(titleList);
    m_exporter->setHead("Chison Switch Probe Test");
    m_exporter->addValueList(QList<QString>() << "Auto Switch Probe Test");
    m_exporter->addValueList(QList<QString>() << QString::number(m_finishTimes));

    QString startTimeVal = "Start Time: " + m_startTime;
    QString endTimeVal = "End Time: " + m_endTime;

    QStringList frontList = QStringList() << startTimeVal << endTimeVal;
    m_exporter->setFront(frontList);
    return m_exporter->create();
}

void AutoSwitchProbeWidget::on_pushButtonExport_clicked()
{
    QString destPath = DiskSelectionDialog::getFileUDiskPath("");
    if (destPath == QString())
        return;

    QString fileDateTime = QDateTime::currentDateTime().toString("yyMMddhhmmss");
    QString fileName =
        destPath + "/" + AppSetting::model() + "_" + m_testExportFileName + "_" + fileDateTime + m_exportSuffix;

    QFile file(fileName);
    if (file.open(QIODevice::WriteOnly))
    {
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
        file.write(generateHTML().toAscii());
#else
        file.write(generateHTML().toLatin1());
#endif
    }
    else
    {
        return;
    }
    Util::sync();
    Util::processEvents();
}

void AutoSwitchProbeWidget::on_pushButtonStop_clicked()
{
    if (m_isStarted)
    {
        ui->pushButtonStop->setText(tr("Resume"));
        m_isStarted = false;
        m_endTime = QDateTime::currentDateTime().toString();
        ui->pushButtonExport->setEnabled(true);
        m_command->undo();
    }
    else
    {
        ui->pushButtonStop->setText(tr("Stop Test"));
        ui->pushButtonExport->setEnabled(false);
        m_isStarted = true;
        m_command->execute();
    }
}
