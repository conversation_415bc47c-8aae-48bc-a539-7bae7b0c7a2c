#include "filiationtreeviewmodel.h"
#include <QFileInfo>
#include "messageboxframe.h"
#include "inputdialogframe.h"
#include <QDir>
#include <QDebug>

FiliationTreeViewModel::FiliationTreeViewModel(QObject* parent)
    : QObject(parent)
{
}

QString FiliationTreeViewModel::newFolderName(const QDir& dir, const QString& str)
{
    int n = 0;
    QFileInfoList infoList = dir.entryInfoList(QDir::AllDirs);
    foreach (QFileInfo info, infoList)
    {
        if (info.fileName().startsWith(str))
        {
            n++;
        }
    }
    return n > 0 ? str + QString::number(n) : str;
}

QString FiliationTreeViewModel::deFoldername()
{
    return tr("NewFolder");
}

void FiliationTreeViewModel::mkDir(const QFileInfoList& infoList, QWidget* widget)
{
    bool ok;
    QDir dir(infoList.last().filePath());
    QString str = InputDialogFrame::getText(widget, tr("New Directory"), tr("Please input the new folder name:"),
                                            QLineEdit::Normal, newFolderName(dir, deFoldername()), &ok)
                      .trimmed();
    if (ok && !str.isEmpty())
    {
        createDir(dir, str);
    }
}

// void FiliationTreeViewModel::deleteFile(const QFileInfoList &infoList, QWidget* widget)
//{
//    int ret = MessageBoxFrame::question(widget, tr("Delete"),
//                                        tr("Are you sure you want to delete the selected items?"),
//                                        QMessageBox::Yes | QMessageBox::No);
//    if (ret == QMessageBox::Yes)
//    {
//        m_DeleteThread.setDeleteArgs(infoList);
//        m_DeleteThread.start();
//    }
//}

// void FiliationTreeViewModel::addItems(const QFileInfo &info, const QString &dirName, const QStringList &filePaths)
//{
//    //QDir dir();
//////    qDebug() << info.filePath() << dirName << filePaths;
//    QDir dir(info.filePath());
//    createDir(dir, dirName);
//    if(QDir(dir.filePath(dirName)).exists())
//    {
//        m_CopyThread.setCopyArgs(filePaths);
//        m_CopyThread.setCurPath(dir.filePath(dirName));
//        if (m_CopyThread.isVolumeEnough())
//        {
//            m_CopyThread.start();
//        }
//    }
//}

bool FiliationTreeViewModel::createDir(const QDir& dir, const QString& dirName, QWidget* widget)
{
    if (QDir(dir.filePath(dirName)).exists())
    {
        if (widget == 0)
        {
            MessageBoxFrame::warning(tr("Folder already exists"));
        }
        else
        {
            int ret =
                MessageBoxFrame::question(widget, tr("Export"), tr("Folder already exists!\nAre you sure to combine?"),
                                          QMessageBox::Yes | QMessageBox::No);
            if (ret == QMessageBox::Yes)
            {
                return true;
            }
        }
        return false;
    }
    else
    {
        bool result = dir.mkdir(dirName);
        if (!result)
        {
            MessageBoxFrame::warning(tr("Export failed."));
        }
        return result;
    }
}
