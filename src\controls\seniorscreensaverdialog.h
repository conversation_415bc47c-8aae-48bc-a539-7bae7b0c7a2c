#ifndef SENIORSCREENSAVERDIALOG_H
#define SENIORSCREENSAVERDIALOG_H
#include "controls_global.h"
#include "screensaverdialog.h"

namespace Ui
{
class SeniorScreenSaverDialog;
}

class CONTROLSSHARED_EXPORT SeniorScreenSaverDialog : public ScreenSaverDialog
{
    Q_OBJECT

public:
    explicit SeniorScreenSaverDialog(QWidget* parent = nullptr);
    void doExec(int pictureIndex);
    void doQuit();

    ~SeniorScreenSaverDialog();

signals:
    void screenSaverClicked();

private slots:
    void onCursorMoveTimeout();
    void onfplayIsRuning();
    void onffplayIsEnded();

private:
    Ui::SeniorScreenSaverDialog* ui;
    QTimer* m_CursorMovedTimer;
    QPoint m_CursorPos;
    QSize m_ScreenSize;
};

#endif // SENIORSCREENSAVERDIALOG_H
