#
# The module defines the following variables:
# ULTRASOUNDDEVICE_FOUND - True if ultrasounddevice found.
# ULTRASOUNDDEVICE_INCLUDE_DIRS - where to find ultrasounddevice.h, etc.
# ULTRASOUNDDEVICE_LIBRARIES - List of libraries when using ultrasounddevice.
#

thirdparty_prefix_path(ultrasounddevice)

find_path ( ULTRASOUNDDEVICE_INCLUDE_DIR
            NAMES
                ultrasounddevice.h
            HINTS
                ${ULTRASOUNDDEVICE_ROOT}/include
            )
set(ULTRASOUNDDEVICE_INCLUDE_DIRS ${ULTRASOUNDDEVICE_INCLUDE_DIR})
message("Found UltrasoundDevice headers: ${ULTRASOUNDDEVICE_INCLUDE_DIRS}")

find_library ( ULTRASOUNDDEVICE_LIBRARY
            NAMES 
                ultrasounddevice
            HINTS
                ${ULTRASOUNDDEVICE_LIBPATH}
             )
set(ULTRASOUNDDEVICE_LIBRARIES ${ULTRASOUNDDEVICE_LIBRARY})
message("Found UltrasoundDevice libs: ${ULTRASOUNDDEVICE_LIBRARIES}")