<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>VMonitorSelect</class>
 <widget class="QWidget" name="VMonitorSelect">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>419</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <item>
    <widget class="QPushButton" name="ExportGrabScreen">
     <property name="text">
      <string>Export Screen Pic/Clip</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonVMonitor">
     <property name="text">
      <string>VMonitor</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonProbeCode">
     <property name="text">
      <string>Probe Code</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonSwitchProbe">
     <property name="text">
      <string>Auto Switch Probe</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonProbeTemp">
     <property name="text">
      <string>Probe Temperature</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonBurnInTest">
     <property name="text">
      <string>Burn-In Test</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonMotorTest">
     <property name="text">
      <string>motor test</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonProbeSelfTest">
     <property name="text">
      <string>Probe Self Test</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPushButton" name="pushButtonAutoTest">
     <property name="text">
      <string>Auto Test</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
