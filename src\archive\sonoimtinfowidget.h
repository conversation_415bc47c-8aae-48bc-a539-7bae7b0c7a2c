#ifndef SONOIMTINFOWIDGET_H
#define SONOIMTINFOWIDGET_H

#include "basewidget.h"

class Study;
namespace Ui
{
class SonoIMTInfoWidget;
}

class SonoIMTInfoWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit SonoIMTInfoWidget(QWidget* parent = nullptr);
    ~SonoIMTInfoWidget();
    void translateSmoker();
    void translateDia();
    void flawlessStudy(Study* study);
    void setStudy(const Study* study);
    void clearStudyInfo();
    void initSet();
    void setCurrentWidgetEnabled(bool enabled);

protected:
    void retranslateUi();

private slots:
    void on_lineEditTotal_editingFinished();

private:
    Ui::SonoIMTInfoWidget* ui;
};

#endif // SONOIMTINFOWIDGET_H
