#include "sonocalcalg.h"

AutoCystisAlg::AutoCystisAlg()
    : VirtualAlg()
    , m_Instance(nullptr)
{
    m_Lib.setFileName("Ofeli_c");
}

AutoCystisAlg::~AutoCystisAlg()
{
    if (m_Instance)
        call_Dispose(m_Instance);
}

int AutoCystisAlg::SetParameters(float r0, float alpha0, float simga0, float gI_threshold0, float gI_balloon_v0,
                                 float num_iters0)
{
    if (!m_Instance)
        m_Instance = call_Instance();

    if (m_IsError)
    {
        return -1;
    }
    if (!m_Instance)
    {
        m_IsError = true;
        return -1;
    }

    return call_SetParameters(m_Instance, r0, alpha0, simga0, gI_threshold0, gI_balloon_v0, num_iters0);
}

std::vector<AutoCystisAlg::Point> AutoCystisAlg::getContour(unsigned char* imgData, int imgWidth, int imgHeight,
                                                            int posX, int posY)
{
    if (!m_Instance)
        m_Instance = call_Instance();
    if (m_IsError)
        return {};

    if (m_Instance)
    {
        int count = call_GetContourNum(m_Instance, imgData, imgWidth, imgHeight, posX, posY);

        if (count <= 0)
            m_IsError = true;

        if (m_IsError)
            return {};

        if (count > 0)
        {
            int everyContourPntNum[1];
            int ret = call_GetEveryContourPointsNum(m_Instance, everyContourPntNum);
            if (m_IsError)
                return {};

            int m = 1, n = 1000;
            Point** dataOut = new Point*[m];
            for (int k = 0; k < m; k++)
            {
                dataOut[k] = new Point[n];
            }

            ret = call_GetEveryContourPoints(m_Instance, 0, everyContourPntNum, dataOut);

            std::vector<Point> points;

            if (!m_IsError)
            {
                points.resize(everyContourPntNum[0]);

                for (int i = 0; i < everyContourPntNum[0]; i++)
                {
                    Point& point = points.at(i);
                    point.x = dataOut[0][i].x;
                    point.y = dataOut[0][i].y;
                }
            }

            for (int k = 0; k < m; k++)
            {
                delete[] dataOut[k];
            }
            delete[] dataOut;

            return std::move(points);
        }
    }

    return {};
}

std::pair<int, int> AutoCystisAlg::getAreaAndLength(std::vector<PointF>& points)
{
    PointF* data = points.data();
    size_t count = points.size();

    int area = call_GetPointsArea(data, count);
    int length = call_GetPointsLength(data, count);

    return {area, length};
}

int AutoCystisAlg::getLOngAndShortDiameter(std::vector<PointF>& points, Ellipse& gl)
{
    PointF* data = points.data();
    size_t count = points.size();
    int res = call_GetPointsEllipseFitting(data, count, gl);

    return res;
}

void* AutoCystisAlg::call_Instance(void)
{
    typedef void* (*Instance_Prototype)();
    Instance_Prototype Instance = (Instance_Prototype)m_Lib.resolve("Instance");
    if (Instance == NULL)
    {
        m_IsError = true;
        return nullptr;
    }

    return Instance();
}

void AutoCystisAlg::call_Dispose(void* instance)
{
    typedef void (*Dispose_Prototype)(void*);
    Dispose_Prototype Dispose = (Dispose_Prototype)m_Lib.resolve("Dispose");
    if (Dispose == NULL)
    {
        m_IsError = true;
        return;
    }

    Dispose(instance);
}

int AutoCystisAlg::call_SetParameters(void* instance, float r0, float alpha0, float simga0, float gI_threshold0,
                                      float gI_balloon_v0, float num_iters0)
{
    typedef int (*SetParameters_Prototype)(void*, float, float, float, float, float, float);
    SetParameters_Prototype SetParameters = (SetParameters_Prototype)m_Lib.resolve("SetParameters");
    if (SetParameters == NULL)
    {
        m_IsError = true;
        return -1;
    }

    return SetParameters(instance, r0, alpha0, simga0, gI_threshold0, gI_balloon_v0, num_iters0);
}

int AutoCystisAlg::call_GetContourNum(void* instance, unsigned char* imgData, int imgWidth, int imgHeight, int posX,
                                      int posY)
{
    typedef int (*GetContourNum_Prototype)(void*, unsigned char*, int, int, int, int);
    GetContourNum_Prototype GetContourNum = (GetContourNum_Prototype)m_Lib.resolve("GetContourNum");

    if (GetContourNum == NULL)
    {
        m_IsError = true;
        return -1;
    }

    return GetContourNum(instance, imgData, imgWidth, imgHeight, posX, posY);
}

int AutoCystisAlg::call_GetEveryContourPointsNum(void* instance, int* everyContourPntNum)
{
    typedef int (*GetEveryContourPointsNum_Prototype)(void*, int*);
    GetEveryContourPointsNum_Prototype GetEveryContourPointsNum =
        (GetEveryContourPointsNum_Prototype)m_Lib.resolve("GetEveryContourPointsNum");
    if (GetEveryContourPointsNum == NULL)
    {
        m_IsError = true;
        return -1;
    }

    return GetEveryContourPointsNum(instance, everyContourPntNum);
}

int AutoCystisAlg::call_GetEveryContourPoints(void* instance, size_t count, int* everyContourPntNum,
                                              Point** m_ContourPnts)
{
    typedef int (*GetEveryContourPoints_Prototype)(void*, size_t, int*, Point**);
    GetEveryContourPoints_Prototype GetEveryContourPoints =
        (GetEveryContourPoints_Prototype)m_Lib.resolve("GetEveryContourPoints");
    if (GetEveryContourPoints == NULL)
    {
        m_IsError = true;
        return -1;
    }

    return GetEveryContourPoints(instance, count, everyContourPntNum, m_ContourPnts);
}

int AutoCystisAlg::call_GetPointsArea(PointF* pnts, size_t count)
{
    typedef int (*GetPointsArea_Prototype)(PointF*, size_t);
    GetPointsArea_Prototype GetPointsArea = (GetPointsArea_Prototype)m_Lib.resolve("GetPointsArea");
    if (GetPointsArea == NULL)
    {
        m_IsError = true;
        return -1;
    }

    return GetPointsArea(pnts, count);
}

int AutoCystisAlg::call_GetPointsLength(PointF* pnts, size_t count)
{
    typedef int (*GetPointsLength_Prototype)(PointF*, size_t);
    GetPointsLength_Prototype GetPointsLength = (GetPointsLength_Prototype)m_Lib.resolve("GetPointsLength");
    if (GetPointsLength == NULL)
    {
        m_IsError = true;
        return -1;
    }

    return GetPointsLength(pnts, count);
}

int AutoCystisAlg::call_GetPointsEllipseFitting(PointF* pnts, size_t count, Ellipse& gl)
{
    typedef int (*GetPointsEllipseFitting_Prototype)(PointF*, size_t, float&, float&, float&, float&, float&);
    GetPointsEllipseFitting_Prototype GetPointsEllipseFitting =
        (GetPointsEllipseFitting_Prototype)m_Lib.resolve("GetPointsEllipseFitting");
    if (GetPointsEllipseFitting == NULL)
    {
        m_IsError = true;
        return -1;
    }

    return GetPointsEllipseFitting(pnts, count, gl.x, gl.y, gl.width, gl.height, gl.rotationAngle);
}
