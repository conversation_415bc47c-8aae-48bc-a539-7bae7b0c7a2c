#include "fourdrightwidget.h"
#include "imageskimwidget.h"
#include "modeluiconfig.h"
#include "resource.h"
#include "ui_fourdrightwidget.h"

FourDRightWidget::FourDRightWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::FourDRightWidget)
{
    ui->setupUi(this);

    initImageSkimManager();

    //    UiUtil::setObjectProperty(this, Resource::fourdRightWidgetPropertyCfgName);
}

FourDRightWidget::~FourDRightWidget()
{
    delete ui;
}

ImageSkimManager* FourDRightWidget::imageClipWidget() const
{
    return ui->imageClipWidget;
}

void FourDRightWidget::retranslateUi()
{
}

void FourDRightWidget::initImageSkimManager()
{
    // 需要从4D配置文件中读取toolwidget类型,位置

    ui->imageClipWidget->changeToolWidgetAndLocation(ThumbnailViewUtil::Full, ThumbnailViewUtil::AtBottom);

    int row, column = 0;
    if (ModelUiConfig::instance().readRowAndColumn(ModelUiConfig::FourDRightWidgetThumbnailRowAndColumn, row, column))
    {
        ui->imageClipWidget->setRowAndColumn(row, column);
    }
    ui->imageClipWidget->setSelectType(ThumbnailViewUtil::Single);
    ui->imageClipWidget->getSkimWidget()->setBorderDraw(true);
}
