#include "fourdrendermodewidget.h"
#include "ui_fourdrendermodewidget.h"
#include "fourdparascontainer.h"
#include "messageboxframe.h"
#include "bfpnames.h"

FourDRenderModeWidget::FourDRenderModeWidget(QWidget* parent)
    : FourDParaSetBaseWidget(parent)
    , ui(new Ui::FourDRenderModeWidget)
{
    ui->setupUi(this);
    m_ParaType = FourDParasInfo::RenderModeMap;
    m_RelateParasName = BFPNames::FourDRenderModeIndexStr;
}

FourDRenderModeWidget::~FourDRenderModeWidget()
{
    delete ui;
}

void FourDRenderModeWidget::initalize()
{
    ui->comboBoxVirtualHD->clear();
    ui->comboBoxSurface->clear();
    ui->comboBoxVessel->clear();
    ui->comboBoxXRay->clear();
    ui->comboBoxSkeleton->clear();
    ui->comboBoxDepthView->clear();
    QStringList names = FourDParasContainer::get<FourDPresetPara>(FourDParasInfo::FourDPreset).names();
    ui->comboBoxVirtualHD->addItems(names);
    ui->comboBoxSurface->addItems(names);
    ui->comboBoxVessel->addItems(names);
    ui->comboBoxXRay->addItems(names);
    ui->comboBoxSkeleton->addItems(names);
    ui->comboBoxDepthView->addItems(names);
}

void FourDRenderModeWidget::setFourDRenderModePara(const FourDParasInfo::FourDRenderModePara& para)
{
    m_FourDRenderModePara = para;
    ui->comboBoxVirtualHD->setCurrentIndex(para.m_VirtualHDID);
    ui->comboBoxSurface->setCurrentIndex(para.m_SurfaceID);
    ui->comboBoxVessel->setCurrentIndex(para.m_VesselID);
    ui->comboBoxXRay->setCurrentIndex(para.m_xRayID);
    ui->comboBoxSkeleton->setCurrentIndex(para.m_SkeletonID);
    ui->comboBoxDepthView->setCurrentIndex(para.m_DepthViewID);
}

FourDParasInfo::FourDRenderModePara FourDRenderModeWidget::fourDRenderModePara()
{
    FourDParasInfo::FourDRenderModePara para = m_FourDRenderModePara;
    para.m_VirtualHDID = ui->comboBoxVirtualHD->currentIndex();
    para.m_SurfaceID = ui->comboBoxSurface->currentIndex();
    para.m_VesselID = ui->comboBoxVessel->currentIndex();
    para.m_xRayID = ui->comboBoxXRay->currentIndex();
    para.m_SkeletonID = ui->comboBoxSkeleton->currentIndex();
    para.m_DepthViewID = ui->comboBoxDepthView->currentIndex();
    return para;
}

void FourDRenderModeWidget::retranslateUi()
{
    ui->retranslateUi(this);
}
