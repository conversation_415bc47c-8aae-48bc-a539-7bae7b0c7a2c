<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PatientEditWidget</class>
 <widget class="QWidget" name="PatientEditWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>947</width>
    <height>332</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Patient Information</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout" stretch="0">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QFrame" name="frame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="0" column="0">
       <widget class="PatientTopWidget" name="widgetTop" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QTabWidget" name="tabWidget">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="currentIndex">
         <number>0</number>
        </property>
        <widget class="QWidget" name="tabABD">
         <attribute name="title">
          <string>ABD</string>
         </attribute>
         <layout class="QGridLayout" name="gridLayout_9">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="spacing">
           <number>0</number>
          </property>
          <item row="0" column="0">
           <widget class="PatientMiddleWidget" name="widgetMiddle" native="true"/>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="tabOb">
         <attribute name="title">
          <string>OB</string>
         </attribute>
         <layout class="QGridLayout" name="gridLayout_2">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="spacing">
           <number>0</number>
          </property>
         </layout>
        </widget>
        <widget class="QWidget" name="tabGyn">
         <attribute name="title">
          <string>GYN</string>
         </attribute>
         <layout class="QGridLayout" name="gridLayout_3">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="spacing">
           <number>0</number>
          </property>
         </layout>
        </widget>
        <widget class="QWidget" name="tabCard">
         <attribute name="title">
          <string>CARD</string>
         </attribute>
         <layout class="QGridLayout" name="gridLayout_4">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="spacing">
           <number>0</number>
          </property>
         </layout>
        </widget>
        <widget class="QWidget" name="tabUro">
         <attribute name="title">
          <string>URO</string>
         </attribute>
         <layout class="QGridLayout" name="gridLayout_6">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="spacing">
           <number>0</number>
          </property>
         </layout>
        </widget>
        <widget class="QWidget" name="tabOrgan">
         <attribute name="title">
          <string>SMP</string>
         </attribute>
         <layout class="QGridLayout" name="gridLayout_7">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="spacing">
           <number>0</number>
          </property>
         </layout>
        </widget>
        <widget class="QWidget" name="tabPed">
         <attribute name="title">
          <string>PED</string>
         </attribute>
         <layout class="QGridLayout" name="gridLayout_8">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="spacing">
           <number>0</number>
          </property>
         </layout>
        </widget>
        <widget class="QWidget" name="tabBlood">
         <attribute name="title">
          <string>VAS</string>
         </attribute>
         <layout class="QGridLayout" name="gridLayout_5">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <property name="spacing">
           <number>0</number>
          </property>
         </layout>
        </widget>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>PatientTopWidget</class>
   <extends>QWidget</extends>
   <header>patienttopwidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>PatientMiddleWidget</class>
   <extends>QWidget</extends>
   <header>patientmiddlewidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
