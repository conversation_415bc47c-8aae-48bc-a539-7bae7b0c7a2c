<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>NetworkStorageFileWidget</class>
 <widget class="QWidget" name="NetworkStorageFileWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>564</width>
    <height>387</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout" columnstretch="2,2,0,3,0">
   <item row="0" column="0">
    <widget class="QLabel" name="label">
     <property name="text">
      <string>Service Name</string>
     </property>
    </widget>
   </item>
   <item row="4" column="0">
    <widget class="QLabel" name="label_3">
     <property name="text">
      <string>Folder Name</string>
     </property>
    </widget>
   </item>
   <item row="5" column="1">
    <widget class="QCheckBox" name="checkBoxDeleteAfterSending">
     <property name="text">
      <string>Delete exam after sending</string>
     </property>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QLabel" name="label_2">
     <property name="text">
      <string>Image Type</string>
     </property>
    </widget>
   </item>
   <item row="4" column="1" colspan="4">
    <widget class="QLineEdit" name="lineEditDir"/>
   </item>
   <item row="1" column="4">
    <widget class="BaseComboBox" name="comboBox_2">
     <item>
      <property name="text">
       <string>AVI</string>
      </property>
     </item>
     <item>
      <property name="text">
       <string>MP4</string>
      </property>
     </item>
    </widget>
   </item>
   <item row="6" column="1">
    <widget class="QCheckBox" name="checkBoxGDPR">
     <property name="text">
      <string>De-Identification</string>
     </property>
    </widget>
   </item>
   <item row="7" column="2">
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="8" column="0" colspan="5">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QPushButton" name="pushButtonExport">
       <property name="maximumSize">
        <size>
         <width>120</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="text">
        <string>Export</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonClose">
       <property name="maximumSize">
        <size>
         <width>120</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="text">
        <string>Close</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="1" colspan="2">
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QRadioButton" name="radioButtonBMP">
       <property name="text">
        <string>BMP</string>
       </property>
       <property name="checked">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QRadioButton" name="radioButtonPNG">
       <property name="text">
        <string>PNG</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QRadioButton" name="radioButtonJPG">
       <property name="text">
        <string>JPG</string>
       </property>
       <property name="checked">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QRadioButton" name="radioButtonDCM">
       <property name="text">
        <string>DCM</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="0" column="1" colspan="4">
    <widget class="BaseComboBox" name="comboBoxNetworkServer"/>
   </item>
   <item row="2" column="1">
    <widget class="QCheckBox" name="checkBoxDcmSR">
     <property name="text">
      <string>DICOM SR</string>
     </property>
    </widget>
   </item>
   <item row="3" column="1" colspan="4">
    <widget class="MakeDcmFilesOptionWidget" name="dcmOptionwidget" native="true"/>
   </item>
   <item row="1" column="3">
    <widget class="QCheckBox" name="checkBoxVideo">
     <property name="text">
      <string>Cine transform to:</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BaseComboBox</class>
   <extends>QComboBox</extends>
   <header>basecombobox.h</header>
  </customwidget>
  <customwidget>
   <class>MakeDcmFilesOptionWidget</class>
   <extends>QWidget</extends>
   <header location="global">makedcmfilesoptionwidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
