#include "exportfilewidget.h"
#include "ui_exportfilewidget.h"
#include "filesystemmodel.h"
//#include "mainmodule.h"
#include "util.h"
#include "messageboxframe.h"
#include <QDesktopWidget>
#include "filiationtreeviewmodel.h"
#include "progressbarview.h"
#include "confirmdialog.h"
#include "systeminfo.h"
#include "resource.h"
#include <QDebug>
#include "imageprocessedcopy.h"
#include "rpt2pdfconvert.h"
#include "generalworkflowfunction.h"
#include <QSettings>
#include "deletefilescontroller.h"
#include "setting.h"
#include "checkinfofilter.h"
#include "dicomsrmodels.h"
#include "gdprutility.h"
#include "appsetting.h"

const char* const ExportFileWidget::m_AnimalString[] = {
    QT_TRANSLATE_NOOP("ExportFileWidget", "Delete animal exam after sending")};

ExportFileWidgetDialog::ExportFileWidgetDialog(IStateManager* stateManager, IColorMapManager* colorMapManager,
                                               IDiskDevice* diskDevice, QWidget* parent)
    : BaseInputAbleDialogFrame(parent /*,true*/)
    , m_Child(NULL)
{
    m_Child = new ExportFileWidget(stateManager, colorMapManager, diskDevice, this);
    this->setContent(m_Child);
    this->setIsShowOk(false);
    connect(m_Child, SIGNAL(closed()), this, SLOT(reject()));
}

ExportFileWidget* ExportFileWidgetDialog::getFileWidget()
{
    return m_Child;
}

bool ExportFileWidgetDialog::isUDiskMounted() const
{
    return m_Child->isUDiskMounted();
}

ExportFileWidget::ExportFileWidget(IStateManager* stateManager, IColorMapManager* colorMapManager,
                                   IDiskDevice* diskDevice, QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::ExportFileWidget)
    , model(NULL)
    , m_FilePaths(QStringList())
    , m_ProgressBar(NULL)
    , m_ConfirmDialog(NULL)
    , m_copy(NULL)
    , m_DicomTaskManager(NULL)
    , m_DeleteFilesController(NULL)
    , m_ImageType(QString())
    , m_isCurrentMulticChoice(false)
    , m_isCopy(false)
    , m_IsGDPR(false)
    , m_IsShowTransformInfo(false)
    , m_IsCopyThreadStarted(false)
    , cpLock(NULL)
    , m_DefaultDir(QString())
    , m_DirPath(QString())
    , m_ContainRetrieveExam(false)
{
    ui->setupUi(this);
    if (m_ProgressBar == NULL)
    {
        m_ProgressBar = new ProgressBarView(stateManager, this);
    }

    if (NULL == cpLock)
    {
        cpLock = baseCreateLock("ExportFileWidget");
    }

    m_ConfirmDialog = new ConfirmDialogFrame(m_ProgressBar->widget());
    connect(m_ConfirmDialog, SIGNAL(clickedButton(int)), this, SLOT(onConfirmButtonClicked(int)));
    m_ThreadModel.setProgressBar(m_ProgressBar);
    m_ThreadModel.setConfirmDialog(m_ConfirmDialog);
    m_copy = new ImageProcessedCopy(colorMapManager, &m_ThreadModel);
    connect(m_copy, SIGNAL(infoShowed(bool)), this, SLOT(onInfoShowed(bool)));
    m_ThreadModel.setICopy(m_copy);
    connect(&m_ThreadModel, SIGNAL(threadFinished(bool)), this, SLOT(onThreadFinished()));

    //    connect(ui->widgetDirView, SIGNAL(fileInfoList(QFileInfoList)), this, SLOT(deleteFileInfo(QFileInfoList)));

    on_comboBox_currentIndexChanged(ui->comboBox->currentIndex());

    QRegExp rxName("[^/:*?<>|`\"\\\\]+$"); //根据windows文件夹命名方式禁用以下字符/:*?<>|"\.
    QValidator* validatorName = new QRegExpValidator(rxName, this);
    ui->lineEditDir->setValidator(validatorName);

    ui->checkBoxVideo->setChecked(true);
    ui->comboBox->setDiskDevice(diskDevice);
    initRadioButtons();
    if (AppSetting::isAnimalAndChinese())
    {
        Util::setPatient2Animal(ui->checkBoxDeleteAfterSending, "ExportFileWidget",
                                m_AnimalString[Delete_animal_exam_after_sending]);
    }
}

void ExportFileWidget::initRadioButtons()
{
    ui->checkBoxDcmSR->setChecked(false);
    ui->dcmOptionwidget->setVisible(false);

    QSettings settings(Resource::optionsforTransmittedImagesIniName(), QSettings::IniFormat);
    settings.beginGroup(Resource::colormapGeneralDevice);
    QString screenSuffix = settings.value("ScreenSuffix").toString();
    settings.endGroup();

    if (Resource::bmpSuffix == screenSuffix)
    {
        m_ImageType = Resource::bmpSuffix;
        ui->radioButtonBMP->setChecked(true);
    }
    else if (Resource::jpgSuffix == screenSuffix)
    {
        m_ImageType = Resource::jpgSuffix;
        ui->radioButtonJPG->setChecked(true);
    }
    else if (Resource::pngSuffix == screenSuffix)
    {
        m_ImageType = Resource::pngSuffix;
        ui->radioButtonPNG->setChecked(true);
    }
    else if (Resource::dcmSuffix == screenSuffix)
    {
        m_ImageType = Resource::dcmSuffix;
        ui->radioButtonDCM->setChecked(true);
        ui->dcmOptionwidget->setVisible(true);
    }
}

void ExportFileWidget::onInfoShowed(bool isShowInfo)
{
    m_IsShowTransformInfo = isShowInfo;
}

ExportFileWidget::~ExportFileWidget()
{
    baseDestroyLock(cpLock);
    //在窗口退出时先通知子线程退出,否则子线程访问的地址将非法.
    m_ThreadModel.stopThread();

    delete ui;
    //有几种情况:直接点cancel和X,合并提示框选NO,磁盘已满;
    //这几种情况下未开启过copythread,直接关闭widget会导致m_DeleteFilesController不delete
    //如果u盘和net2个widget都构造了,关闭时2个widget都会析构,加入isHidden条件,只有当前widget才做delete操作
    if (!isHidden() && !m_IsCopyThreadStarted && m_DeleteFilesController != NULL)
    {
        m_DeleteFilesController->setIsCopyThreadStarted(m_IsCopyThreadStarted);
        m_DeleteFilesController->doDelete();
    }

    Util::SafeDeletePtr(m_ConfirmDialog);
    Util::SafeDeletePtr(m_ProgressBar);
    Util::SafeDeletePtr(model);
    m_FilePaths.clear();

    QSettings settings(Resource::optionsforTransmittedImagesIniName(), QSettings::IniFormat);
    settings.beginGroup(Resource::colormapGeneralDevice);
    settings.setValue("ScreenSuffix", m_ImageType);
    settings.endGroup();
}

void ExportFileWidget::setDefaultDirName(const QString& dir)
{
    ui->lineEditDir->setText(dir);
}

void ExportFileWidget::setExportFilePaths(const QStringList& filePaths)
{
    m_FilePaths = filePaths;
    responseUIToFilePaths();
}

bool ExportFileWidget::isUDiskMounted() const
{
    return ui->comboBox->count() > 0;
}

void ExportFileWidget::setIsCurrentMultiChoice(bool value)
{
    m_isCurrentMulticChoice = value;

    setLineEditAndLableVisible(!value);
}

void ExportFileWidget::setDeleteAfterSendExam(bool value)
{
    if (!value)
    {
        ui->checkBoxDeleteAfterSending->setEnabled(false);
    }
    else
    {
        ui->checkBoxDeleteAfterSending->setEnabled(true);
    }
}

void ExportFileWidget::setDicomTaskManager(DicomTaskManager* dicomTaskManager)
{
    m_DicomTaskManager = dicomTaskManager;
}

void ExportFileWidget::setDeleteFilesController(DeleteFilesController* controller)
{
    m_DeleteFilesController = controller;
}

void ExportFileWidget::showDcmSRCheckBox(bool value)
{
    ui->checkBoxDcmSR->setVisible(value);
}

void ExportFileWidget::setContainRetrieveExam(bool value)
{
    m_ContainRetrieveExam = value;
    if (m_ContainRetrieveExam)
    {
        ui->radioButtonPNG->setEnabled(false);
        ui->radioButtonJPG->setEnabled(false);
        ui->checkBoxDcmSR->setEnabled(false);
        ui->checkBoxGDPR->setEnabled(false);
        if (m_ImageType == Resource::jpgSuffix || m_ImageType == Resource::pngSuffix)
        {
            ui->radioButtonBMP->setChecked(true);
        }
    }
}

void ExportFileWidget::setLineEditAndLableVisible(bool value)
{
    ui->lineEditDir->setVisible(value);
    ui->label_2->setVisible(value);
}

void ExportFileWidget::onThreadFinished()
{
    if (m_IsGDPR)
    {
        QDir dir(m_DirPath);
        if (dir.exists())
        {
            dir.setFilter(QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot);
            QFileInfoList list = dir.entryInfoList();
            if (list.count() <= 0)
            {
                Util::Rmdir(m_DirPath);
            }
        }
    }
    convertRpt2Pdf();

    if (m_DeleteFilesController != NULL)
    {
        m_DeleteFilesController->setIsCopyThreadFinished(true);
        m_DeleteFilesController->doDelete();
    }

    if (ui->checkBoxDcmSR->isChecked() && m_copy->dcmSRSuccessNum() == 0)
    {
        ui->checkBoxDcmSR->setChecked(false);
        MessageBoxFrame::warning(this, QString(), tr("There are no measurement results for DICOM SR!"));
    }
    emit closed();

    if (m_IsShowTransformInfo)
    {
        m_IsShowTransformInfo = false;
        MessageBoxFrame::tipInformation(tr("Transform Task has been added to the Task Queue!"));
    }
    setCopyStatus(false);
}

void ExportFileWidget::convertRpt2Pdf()
{
    QStringList rptFileNames = m_ThreadModel.currentRptFileNames();

    Rpt2PdfConvert convert;

    convert.setIsGDPR(ui->checkBoxGDPR->isChecked());

    foreach (QString rptFile, rptFileNames)
    {
        QFileInfo rptFileInfo(rptFile);
        convert.tryToConvertRpt2pdf(rptFileInfo.dir().absolutePath());
    }
}

void ExportFileWidget::setCopyOptions()
{
    QStringList suffixFilters = QStringList() << Resource::calcSuffix;
    suffixFilters << Resource::iniSuffix << "bin"
                  << "txt"; // for exclude max image number file, added 2017-07-20 by liujia
    suffixFilters << Resource::singleFourdImgSuffix << Resource::multiFourdImgSuffix; // 4D c3d and c4d
    m_ThreadModel.setNeedSingleFrameImgData(false);
    checkIfNeedSingleFrameImgData(m_FilePaths);
    m_copy->setCinetransTo(ui->checkBoxVideo->isChecked() ? ui->comboBox_2->currentText() : QString());
    if (ui->checkBoxDcmSR->isVisible())
    {
        m_copy->setIsExportDcmSR(ui->checkBoxDcmSR->isChecked());
    }
    m_copy->setTaskManager(m_DicomTaskManager);
    if (m_DeleteFilesController != NULL)
    {
        m_DeleteFilesController->setIsDeleteExamAfterSending(ui->checkBoxDeleteAfterSending->isChecked());
    }
    m_copy->setDeleteFilesController(m_DeleteFilesController);
    m_ThreadModel.setNotCopyFileSuffixFilters(suffixFilters);
    ui->dcmOptionwidget->save();
}

void ExportFileWidget::onExportTypeChanged(QString type)
{
    if (Resource::bmpSuffix == type)
    {
        m_ImageType = Resource::bmpSuffix;
        ui->radioButtonBMP->setChecked(true);
    }
    else if (Resource::jpgSuffix == type)
    {
        m_ImageType = Resource::jpgSuffix;
        ui->radioButtonJPG->setChecked(true);
    }
    else if (Resource::pngSuffix == type)
    {
        m_ImageType = Resource::pngSuffix;
        ui->radioButtonPNG->setChecked(true);
    }
    else if (Resource::dcmSuffix == type)
    {
        m_ImageType = Resource::dcmSuffix;
        ui->radioButtonDCM->setChecked(true);
        ui->dcmOptionwidget->setVisible(true);
    }
}

void ExportFileWidget::onConfirmButtonClicked(int value)
{
    if (value == ConfirmDialog::Cancel && m_DeleteFilesController != NULL)
    {
        m_DeleteFilesController->setIsCanceled(true);
    }
}

void ExportFileWidget::showEvent(QShowEvent* e)
{
    BaseWidget::showEvent(e);
}

void ExportFileWidget::retranslateUi()
{
    ui->retranslateUi(this);
    if (AppSetting::isAnimalAndChinese())
    {
        Util::setPatient2Animal(ui->checkBoxDeleteAfterSending, "ExportFileWidget",
                                m_AnimalString[Delete_animal_exam_after_sending]);
    }
}

void ExportFileWidget::on_pushButton_clicked()
{
    if (ui->checkBoxGDPR->isChecked())
    {
        if (!GDPRUtility::isSupportGDPR(m_FilePaths))
        {
            MessageBoxFrame::warning(QObject::tr("Some images cannot be de-identified!"));
        }
    }

    if (!getCopyStatus())
    {
        setCopyStatus(true);
        QString dirName = ui->lineEditDir->text().trimmed();

        if (!m_isCurrentMulticChoice)
        {
            if (dirName.isEmpty())
            {
                MessageBoxFrame::warning(tr("Enter the file name!"));
                setCopyStatus(false);
            }
            else
            {
                QDir dir(ui->comboBox->currentDiskPath());
                if (!FiliationTreeViewModel::createDir(dir, dirName, this))
                {
                    setCopyStatus(false);
                    return;
                }
                m_DirPath = ui->comboBox->currentDiskPath() + "/" + dirName;
                m_ThreadModel.setRootPath(ui->comboBox->currentDiskPath());
                if (QDir(dir.filePath(dirName)).exists())
                {
                    setCopyOptions();
                    m_IsCopyThreadStarted = m_ThreadModel.copy(m_FilePaths, dir.filePath(dirName));
                }
                else
                {
                    setCopyStatus(false);
                }
            }
        }
        else
        {
            QDir dir(ui->comboBox->currentDiskPath());

            m_ThreadModel.setIsMkPatientWithNameDir(true);
            setCopyOptions();
            m_IsCopyThreadStarted = m_ThreadModel.copy(m_FilePaths, dir.absolutePath());
        }
    }
    else
    {
        MessageBoxFrame::tipInformation(tr("The background copy is busy, please wait a moment."));
    }
}

void ExportFileWidget::checkIfNeedSingleFrameImgData(QStringList& filePaths)
{
    QFileInfoList infoList;

    foreach (QString fileName, filePaths)
    {
        infoList << QFileInfo(fileName);
    }

    //    if (!ui->checkBoxSingleFrameWithRawData->isChecked())
    {
        QFileInfoList tempInfoList = infoList;
        QSet<QString> filters;

        foreach (QFileInfo info, tempInfoList)
        {
            if (info.completeSuffix() == Resource::jpgSuffix || info.completeSuffix() == Resource::bmpSuffix)
            {
                filters << info.baseName();
            }
        }

        foreach (QFileInfo info, tempInfoList)
        {
            if (filters.contains(info.baseName()) &&
                (info.completeSuffix() == Resource::imgSuffix || info.completeSuffix() == Resource::paraSuffix))
            {
                infoList.removeOne(info);
            }
        }
    }

    QStringList resPaths;

    foreach (QFileInfo info, infoList)
    {
        resPaths << info.absoluteFilePath();
    }

    filePaths = resPaths;
}

void ExportFileWidget::on_pushButton_2_clicked()
{
    emit closed();
}

void ExportFileWidget::stopCopy()
{
}

// void ExportFileWidget::deleteFileInfo(const QFileInfoList &infoList)
//{
//    int ret = MessageBoxFrame::question(this, tr("Delete"),
//                                        tr("Are you sure to delete the selected items?"),
//                                        QMessageBox::Yes | QMessageBox::No, QMessageBox::No);
//    if (ret == QMessageBox::Yes)
//    {
//        m_ThreadModel.deleteFile(infoList);
//    }
//}

void ExportFileWidget::on_comboBox_currentIndexChanged(int index)
{
    if (index < 0)
    {
        if (ui->comboBox->count() == 0)
        {
            emit closed();
        }
        return;
    }

    //    if(model == NULL)
    //    {
    //        model = new FileSystemModel(this);
    //    }

    //    QModelIndex modelIndex = model->setRootPath(ui->comboBox->currentDiskPath());
    //    ui->widgetDirView->setModel(model);
    //    ui->widgetDirView->setRootIndex(modelIndex);
    //    ui->widgetDirView->setRootIsDecorated(false);
    //    ui->widgetDirView->setAnimated(false);
    //    ui->widgetDirView->setIndentation(20);
    //    ui->widgetDirView->setSortingEnabled(false);
    //    ui->widgetDirView->setItemsExpandable(false);
}

void ExportFileWidget::on_radioButtonBMP_toggled(bool checked)
{
    if (checked)
    {
        m_ImageType = Resource::bmpSuffix;
        m_copy->setImageType(ImageProcessedCopy::BMP);
        emit exportTypeChanged(m_ImageType);
    }
}

void ExportFileWidget::on_radioButtonJPG_toggled(bool checked)
{
    if (checked)
    {
        m_ImageType = Resource::jpgSuffix;
        m_copy->setImageType(ImageProcessedCopy::JPG);
        emit exportTypeChanged(m_ImageType);
    }
}

void ExportFileWidget::on_radioButtonPNG_toggled(bool checked)
{
    if (checked)
    {
        m_ImageType = Resource::pngSuffix;
        m_copy->setImageType(ImageProcessedCopy::PNG);
        emit exportTypeChanged(m_ImageType);
    }
}

void ExportFileWidget::on_radioButtonDCM_toggled(bool checked)
{
    if (checked)
    {
        m_ImageType = Resource::dcmSuffix;
        m_copy->setImageType(ImageProcessedCopy::DCM);
        emit exportTypeChanged(m_ImageType);
    }
    ui->dcmOptionwidget->setVisible(checked);
}

void ExportFileWidget::responseUIToFilePaths()
{
    foreach (QString fileName, m_FilePaths)
    {
        QFileInfo file(fileName);

        if (file.suffix() == Resource::imgSuffix || file.suffix() == Resource::jpgSuffix ||
            file.suffix() == Resource::bmpSuffix)
        {
            ui->checkBoxDeleteAfterSending->hide();
            return;
        }
    }
}

void ExportFileWidget::setCopyStatus(bool bStatus)
{
    baseMutexLock(cpLock);
    m_isCopy = bStatus;
    baseUnLock(cpLock);
}

bool ExportFileWidget::getCopyStatus()
{
    bool isCopy = false;
    baseMutexLock(cpLock);
    isCopy = m_isCopy;
    baseUnLock(cpLock);
    return isCopy;
}

void ExportFileWidget::on_checkBoxGDPR_toggled(bool checked)
{
    m_copy->setIsGDPR(checked);
    m_IsGDPR = checked;
    if (checked)
    {
        m_DefaultDir = ui->lineEditDir->text();
        QDateTime current_date_time = QDateTime::currentDateTime();
        QString current_date_time_ = current_date_time.toString("yyyyMMdd.hhmmss.z");
        setDefaultDirName(current_date_time_);
        ui->lineEditDir->setEnabled(false);
    }
    else
    {
        setDefaultDirName(m_DefaultDir);
        ui->lineEditDir->setEnabled(true);
    }
}
