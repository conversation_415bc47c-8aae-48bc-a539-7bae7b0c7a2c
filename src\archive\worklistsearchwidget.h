#ifndef WORKLISTSEARCHWIDGET_H
#define WORKLISTSEARCHWIDGET_H
#include "archive_global.h"

#include <QWidget>
#include <QRunnable>
#include "baseinputabledialogframe.h"
#include <QModelIndex>
class QStandardItemModel;
class WorkListSearchWidget;
class CommonProgressBarFrame;
class IStateManager;
struct DcmResponse_Tag
{
    char PatientsName[65]; // VR:PN MaxLen:64 dcmtk:// not correct: max length of PN is 3*64+2 = 194 characters (not
                           // bytes!)
    char PatientID[64];    // VR:LO MaxLen:64
    char PatientsBirthDate[10]; // VR:DA MaxLen:10
    char PatientsSex[16];       // VR:CS MaxLen:16
    char PhysiciansName[64];    // VR:PN MaxLen:64
    char AccessionNumber[16];   // VR:SH MaxLen:16 // added 2010-07-13
    char StudyInstanceID[64];   // VR:UI MaxLen:64
    char StudyDescription[64];  // VR:LO MaxLen:64
    char PatientSize[16];       // VR:DS MaxLen:16
    char PatientWeight[16];     // VR:DS MaxLen:16
};

struct DcmResponseInfo
{
    QString
        PatientsName;  // VR:PN MaxLen:64 dcmtk:// not correct: max length of PN is 3*64+2 = 194 characters (not bytes!)
    QString PatientID; // VR:LO MaxLen:64
    QString PatientsBirthDate; // VR:DA MaxLen:10
    QString PatientsSex;       // VR:CS MaxLen:16
    QString PhysiciansName;    // VR:PN MaxLen:64
    QString AccessionNumber;   // VR:SH MaxLen:16 // added 2010-07-13
    QString StudyInstanceID;   // VR:SH MaxLen:64 // added 2020-06-16
    QString StudyDescription;  // VR:LO MaxLen:64
    QString PatientSize;       // VR:DS MaxLen:16
    QString PatientWeight;     // VR:DS MaxLen:16
};
////.h
class ARCHIVESHARED_EXPORT WorkListSearchDialog : public BaseInputAbleDialogFrame
{
    Q_OBJECT
public:
    explicit WorkListSearchDialog(IStateManager* stateManager, QWidget* parent = 0);

signals:
    void getDicomResponse(const DcmResponseInfo& tag);
private slots:
    void doDicomWorklistTask();

protected:
    void showEvent(QShowEvent* e);

private:
    WorkListSearchWidget* m_Child;
};

namespace Ui
{
class WorkListSearchWidget;
}

class ARCHIVESHARED_EXPORT WorkListSearchWidget : public BaseWidget
{
    Q_OBJECT

    friend class DicomWorklistTask;

public:
    explicit WorkListSearchWidget(IStateManager* stateManager, QWidget* parent = 0);
    ~WorkListSearchWidget();
    void doDicomWorklistTask();

protected:
    virtual void retranslateUi();

private:
    enum ResponseTag
    {
        PatientIDTag,
        StudyDescription,
        //        PatientsNameTag,
        PatientsBirthDateTag,
        PatientsSexTag,
        PhysiciansNameTag,
        AccessionNumberTag,
        StudyInstanceUIDTag,
        PatientSize,
        PatientWeight,
        InternalPatientsNameTag,
        ResponseTagCount
    };
    void initTreeView();
    void doDicomFindScu();
    int dicomFindScu(const QString& title, const QString& ip, const QString& port, int timeout,
                     int scheduledStationTitleIndex, const QString& scheduledStationAETitle, const QString& startDate,
                     const QString& endDate);
    QString localCodec() const;
    QStringList getRealTimeTreeviewHeader();

signals:
    void findscustarted();
    void findscufinished(int);
    void getDicomResponse(const DcmResponseInfo& tag);
    void accept();
private slots:
    void onFindscustarted();
    void getRespond(int res);
    void on_searchButton_clicked();
    void on_searchByComboBox_activated(const QString& arg1);
    void on_appButton_clicked();
    void on_clearButton_clicked();
    void on_treeView_clicked(const QModelIndex& index);
    void on_treeView_doubleClicked(const QModelIndex& index);
    /**
     * @brief convertData 将树行列表的一行数据转化为DcmResponseInfo结构
     * @param index　列表的行下标
     * @return　DcmResponseInfo
     */
    DcmResponseInfo convertData(const QModelIndex& index);

private:
    Ui::WorkListSearchWidget* ui;
    QStandardItemModel* standardmodel;
    CommonProgressBarFrame* m_ProgressBar;
    DcmResponseInfo m_DcmResponseInfo;
    QList<DcmResponse_Tag> m_RespondList;
    int m_Index;
    void setID2AnimalID();
    IStateManager* m_StateManager;
};

class ARCHIVESHARED_EXPORT DicomWorklistTask : public QRunnable
{
public:
    DicomWorklistTask(WorkListSearchWidget* pWW)
        : m_pWidget(pWW)
    {
    }

private:
    void run()
    {
        m_pWidget->doDicomFindScu();
    }

    WorkListSearchWidget* m_pWidget;
};

#endif // WORKLISTSEARCHWIDGET_H
