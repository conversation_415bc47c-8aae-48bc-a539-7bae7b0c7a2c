#ifndef ODSFOURDRESOURCEFACTORY_H
#define ODSFOURDRESOURCEFACTORY_H

#include "controls_global.h"
#include "ifourdresourcefactory.h"

class CONTROLSSHARED_EXPORT ODSFourDResourceFactory : public IFourDResourceFactory
{
public:
    ODSFourDResourceFactory();
    ~ODSFourDResourceFactory();
    IFourDLiveWidget* createFourDLiveWidget();
    IFourDAPI* createFourDAPI();
    IFourDLicenseProcessor* createFourDLicenseProcessor();
};

#endif // ODSFOURDRESOURCEFACTORY_H
