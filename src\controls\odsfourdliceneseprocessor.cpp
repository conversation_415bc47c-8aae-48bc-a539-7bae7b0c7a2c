#include "odsfourdliceneseprocessor.h"
#include <QFile>
#include "fourdapihandler.h"
#include "resource.h"
#include "appsetting.h"
#include "statemanager.h"
#include "licenseinitializer.h"
#include "util.h"
#include "diskselectionwidget.h"
#include "messageboxframe.h"
#include "bfpnames.h"
#include <QFileInfo>
#include "sonoparameters.h"
#include "parameter.h"
#include "bfpnames.h"

ODSFourDLiceneseProcessor::ODSFourDLiceneseProcessor(QObject* parent)
    : AbstractFourDLicenseProcessor(parent)
{
}

ODSFourDLiceneseProcessor::~ODSFourDLiceneseProcessor()
{
}

void ODSFourDLiceneseProcessor::verifyLicenseState()
{
    if (!QFile::exists(Resource::fourDLicenseFileName) && !QFile::exists(Resource::fourDProductLicenseFileName))
    {
        m_FourDLicenseState = NotFound;
        m_RemainDays = 0;
        m_FourDOpened = false;
        FourDAPIHandler::instance().setRemainDays(m_RemainDays);
        return;
    }
    if (QFile::exists(Resource::fourDLicenseFileName))
    {
        int ret = FourDAPIHandler::instance().setLicenseFile(Resource::fourDLicenseFileName.toLatin1().data());
        if (ret < 0)
        {
            m_FourDLicenseState = Invalid;
            m_RemainDays = 0;
        }
        else if (ret == 1)
        {
            m_FourDLicenseState = UltimateUser;
            m_RemainDays = 0;
        }
        else if (ret == 2)
        {
            m_FourDLicenseState = RemainDays;
            m_RemainDays = FourDAPIHandler::instance().getRemainDate() + 1;
        }
    }

    if (m_FourDLicenseState != UltimateUser && m_FourDLicenseState != RemainDays &&
        QFile::exists(Resource::fourDProductLicenseFileName))
    {
        int ret = FourDAPIHandler::instance().setLicenseFile(Resource::fourDProductLicenseFileName.toLatin1().data());
        if (ret < 0)
        {
            m_FourDLicenseState = Invalid;
            m_RemainDays = 0;
        }
        else
        {
            m_FourDLicenseState = ProductionTest;
            m_RemainDays = FourDAPIHandler::instance().getRemainDate() + 1;
        }
    }
    FourDAPIHandler::instance().setRemainDays(m_RemainDays);
    m_FourDOpened = (m_FourDLicenseState == RemainDays || m_FourDLicenseState == UltimateUser ||
                     m_FourDLicenseState == ProductionTest);
}

void ODSFourDLiceneseProcessor::processFourDLicenseState(bool showInfo)
{
    if (AppSetting::isFourD())
    {
        StateManager::getInstance().setPaused(true);
        switch (m_FourDLicenseState)
        {
        case NotFound:
            AppSetting::setFunctionEnabled(LicenseItemKey::Key4D, false);
            LicenseInitializer::instance().saveToLicenseFile(LicenseItemKey::Key4D, false);
            if (showInfo)
            {
                MessageBoxFrame::warning(tr("The 4D license file can not be found. Please import a valid license."));
            }
            break;
        case Invalid:
            AppSetting::setFunctionEnabled(LicenseItemKey::Key4D, false);
            LicenseInitializer::instance().saveToLicenseFile(LicenseItemKey::Key4D, false);
            if (showInfo)
            {
                MessageBoxFrame::warning(tr("The 4D license file is invalid. Please import a valid license."));
            }
            break;
        case RemainDays:
            if (showInfo)
            {
                MessageBoxFrame::tipInformation(tr("4D function can also be used for %n day(s)", "", m_RemainDays));
            }
            break;
        case ProductionTest:
            if (showInfo)
            {
                MessageBoxFrame::tipInformation(
                    tr("4D production test and can also be used for %n day(s)", "", m_RemainDays));
            }
            break;
        default:
            break;
        }
        StateManager::getInstance().setPaused(false);
        Util::processEvents(QEventLoop::ExcludeUserInputEvents);
    }
}

void ODSFourDLiceneseProcessor::onFourDStatusChanged(bool isOpen)
{
    if (isOpen)
    {
        QString odsLicenseFile =
            DiskSelectionDialog::getUDiskFilePath(QFileInfo(Resource::fourDLicenseFileName).fileName(), false);
        QString odsProductLicenseFile =
            DiskSelectionDialog::getUDiskFilePath(QFileInfo(Resource::fourDProductLicenseFileName).fileName(), false);
        bool cpODSFile = false;
        bool cpODSProductionFile = false;
        bool curODSLicenseInvalid = false;
        bool isODSVerified = (m_FourDLicenseState != NotVerified);
        switch (m_FourDLicenseState)
        {
        case NotVerified:
            verifyLicenseState();
            switch (m_FourDLicenseState)
            {
            case NotFound:
            case Invalid:
                curODSLicenseInvalid = false;
                break;
            case ProductionTest:
            case RemainDays:
                curODSLicenseInvalid = true;
                break;
            case UltimateUser:
                emit activeFourD(false);
                return;
            default:
                break;
            }
            break;
        case NotFound:
        case Invalid:
            curODSLicenseInvalid = false;
            break;
        case ProductionTest:
        case RemainDays:
            curODSLicenseInvalid = true;
            break;
        case UltimateUser:
            return;
        default:
            break;
        }
        if (!odsLicenseFile.isEmpty() || !odsProductLicenseFile.isEmpty())
        {
            if (!odsLicenseFile.isEmpty())
            {
                int ret = FourDAPIHandler::instance().setLicenseFile(odsLicenseFile.toLatin1().data());
                if (ret == 1)
                {
                    cpODSFile = true;
                }
                else if (ret == 2)
                {
                    int remainDays = FourDAPIHandler::instance().getRemainDate() + 1;
                    if (remainDays > m_RemainDays || (remainDays > 0 && m_FourDLicenseState == ProductionTest))
                    {
                        cpODSFile = true;
                    }
                }
            }

            if (!cpODSFile && !odsProductLicenseFile.isEmpty())
            {
                int ret = FourDAPIHandler::instance().setLicenseFile(odsProductLicenseFile.toLatin1().data());
                if (ret > 0 && !curODSLicenseInvalid)
                {
                    cpODSProductionFile = true;
                }
            }

            if (cpODSFile || cpODSProductionFile)
            {
                QString cmd = QString("cp %1 %2 %3 -fra")
                                  .arg(cpODSFile ? odsLicenseFile : QString(""))
                                  .arg(cpODSProductionFile ? odsProductLicenseFile : QString(""))
                                  .arg(Resource::generalFileDir);
                if (Util::System(cmd) == 0)
                {
                    Util::sync();
                }

                if (cpODSFile && QFile::exists(Resource::fourDProductLicenseFileName))
                {
                    if (Util::System(QString("rm %1").arg(Resource::fourDProductLicenseFileName)) == 0)
                    {
                        Util::sync();
                    }
                }
            }
            else if (!curODSLicenseInvalid)
            {
                MessageBoxFrame::warning(tr("The 4D license file from UDisk is invalid"));
            }
        }
        else if (!curODSLicenseInvalid)
        {
            MessageBoxFrame::warning(tr("Can not find the 4D license file from UDisk!"));
        }
        if (cpODSFile || cpODSProductionFile || (!isODSVerified && curODSLicenseInvalid))
        {
            FourDAPIHandler::instance().destroy();
            bool needVerify = cpODSFile || cpODSProductionFile;
            emit activeFourD(needVerify);
        }
        else if (!curODSLicenseInvalid)
        {
            processFourDLicenseState(false);
        }
    }
    else
    {
        m_FourDLicenseState = NotVerified;
        m_FourDOpened = false;
        FourDAPIHandler::instance().destroy();
        m_RemainDays = 0;
        FourDAPIHandler::instance().setRemainDays(m_RemainDays);
    }
}

void ODSFourDLiceneseProcessor::onSystemDateChanged()
{
    if (m_FourDLicenseState == ProductionTest || m_FourDLicenseState == RemainDays)
    {
        bool isOpen = true;
        switch (m_FourDLicenseState)
        {
        case ProductionTest:
            isOpen =
                FourDAPIHandler::instance().setLicenseFile(Resource::fourDProductLicenseFileName.toLatin1().data()) > 0;
            break;
        case RemainDays:
            isOpen = FourDAPIHandler::instance().setLicenseFile(Resource::fourDLicenseFileName.toLatin1().data()) > 0;
            break;
        default:
            break;
        }
        QHash<int, bool> keyValues;

        if (isOpen)
        {
            m_RemainDays = FourDAPIHandler::instance().getRemainDate() + 1;
            FourDAPIHandler::instance().setRemainDays(m_RemainDays);
        }
        else
        {
            onFourDStatusChanged(false);
        }

        if (AppSetting::isFourD())
        {
            AppSetting::setFunctionRemainDays(LicenseItemKey::Key4D, m_RemainDays);
            AppSetting::setFunctionEnabled(LicenseItemKey::Key4D, isOpen);
            keyValues[LicenseItemKey::Key4D] = isOpen;
        }
        if (AppSetting::isVirtualHD())
        {
            AppSetting::setFunctionRemainDays(LicenseItemKey::KeyVirtualHD, m_RemainDays);
            AppSetting::setFunctionEnabled(LicenseItemKey::KeyVirtualHD, isOpen);
            keyValues[LicenseItemKey::KeyVirtualHD] = isOpen;
        }
        LicenseInitializer::instance().saveToLicenseFile(keyValues);
    }
}
