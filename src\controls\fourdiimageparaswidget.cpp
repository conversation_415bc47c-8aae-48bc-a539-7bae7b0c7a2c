#include "fourdiimageparaswidget.h"
#include "ui_fourdiimageparaswidget.h"
#include <QApplication>
#include <QDesktopWidget>
#include <QSettings>
#include "resource.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include "parameter.h"

FourDiImageParasDialog::FourDiImageParasDialog(QWidget* parent)
    : BaseInputAbleDialogFrame(parent)
    , m_Child(NULL)
{
    m_Child = new FourDiImageParasWidget(this);
    setIsShowOk(false);
    this->setContent(m_Child);
    this->setFrameTitle("iImage Paras");
}

void FourDiImageParasDialog::setSonoParameters(SonoParameters* value)
{
    m_Child->setSonoParameters(value);
}

void FourDiImageParasDialog::resetiImageParas()
{
    m_Child->resetiImageParas();
}

FourDiImageParasWidget::FourDiImageParasWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::FourDiImageParasWidget)
    , m_SonoParameters(NULL)
    , m_GammaValue(2)
    , m_MapOf2DValue(14)
{
    ui->setupUi(this);
}

FourDiImageParasWidget::~FourDiImageParasWidget()
{
    delete ui;
}

void FourDiImageParasWidget::setSonoParameters(SonoParameters* value)
{
    if (m_SonoParameters != NULL && m_SonoParameters != value)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::FourDGrayCurveIndexStr), SIGNAL(valueChanging(QVariant)), this,
                   SLOT(onFourDGrayCurveIndexChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::FourDGammaStr), SIGNAL(valueChanging(QVariant)), this,
                   SLOT(onFourDGammaChanged(QVariant)));
    }
    if (value != NULL && m_SonoParameters != value)
    {
        m_SonoParameters = value;
        connect(m_SonoParameters->parameter(BFPNames::FourDGrayCurveIndexStr), SIGNAL(valueChanging(QVariant)), this,
                SLOT(onFourDGrayCurveIndexChanged(QVariant)));
        connect(m_SonoParameters->parameter(BFPNames::FourDGammaStr), SIGNAL(valueChanging(QVariant)), this,
                SLOT(onFourDGammaChanged(QVariant)));
        ui->spinBoxGamma->setMaximum(m_SonoParameters->parameter(BFPNames::FourDGammaStr)->max());
        ui->spinBoxGamma->setMinimum(m_SonoParameters->parameter(BFPNames::FourDGammaStr)->min());
        ui->spinBoxMapOf2D->setMaximum(m_SonoParameters->parameter(BFPNames::FourDGrayCurveIndexStr)->max());
        ui->spinBoxMapOf2D->setMinimum(m_SonoParameters->parameter(BFPNames::FourDGrayCurveIndexStr)->min());

        m_GammaValue = m_SonoParameters->pIV(BFPNames::FourDGammaStr);
        m_MapOf2DValue = m_SonoParameters->pIV(BFPNames::FourDGrayCurveIndexStr);
        resetiImageParas();
    }
}

void FourDiImageParasWidget::retranslateUi()
{
}

void FourDiImageParasWidget::onFourDGrayCurveIndexChanged(const QVariant& val)
{
    if (ui->spinBoxMapOf2D->value() != val.toInt())
    {
        m_MapOf2DValue = val.toInt();
        ui->spinBoxMapOf2D->setValue(val.toInt());
    }
}

void FourDiImageParasWidget::onFourDGammaChanged(const QVariant& val)
{
    if (ui->spinBoxGamma->value() != val.toInt())
    {
        m_GammaValue = val.toInt();
        ui->spinBoxGamma->setValue(m_GammaValue);
    }
}

void FourDiImageParasWidget::resetiImageParas()
{
    ui->spinBoxGamma->setValue(m_GammaValue);
    ui->spinBoxMapOf2D->setValue(m_MapOf2DValue);
}

void FourDiImageParasWidget::on_spinBoxGamma_valueChanged(int arg1)
{
    m_GammaValue = arg1;
    if (m_SonoParameters != NULL)
    {
        m_SonoParameters->setPV(BFPNames::FourDGammaStr, m_GammaValue);
    }
}

void FourDiImageParasWidget::on_spinBoxMapOf2D_valueChanged(int arg1)
{
    m_MapOf2DValue = arg1;
    if (m_SonoParameters != NULL)
    {
        m_SonoParameters->setPV(BFPNames::FourDGrayCurveIndexStr, m_MapOf2DValue);
    }
}
