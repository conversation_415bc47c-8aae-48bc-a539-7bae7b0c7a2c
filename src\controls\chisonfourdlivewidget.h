#ifndef CHISONFOURDLIVEWIDGET_H
#define CHISONFOURDLIVEWIDGET_H

#include "controls_global.h"
#include "abstractfourdlivewidget.h"
#include "presetparameters.h"

namespace Ui
{
class ChisonFourDLiveWidget;
}

class IFourDParameter;
class ChisonFourDDataSender;
class IFourDDataSender;

class CONTROLSSHARED_EXPORT ChisonFourDLiveWidget : public AbstractFourDLiveWidget
{
    Q_OBJECT

public:
    explicit ChisonFourDLiveWidget(IFourDDataSender* dataSender, QWidget* parent = 0);
    ~ChisonFourDLiveWidget();
    void setContext(ImageTile* context, ILineBufferManager* bufferManager);
    void init();
    void reset();
    void saveCurGroupParas();
    void saveROIInfo();
    IFourDRenderWidget* fourDRenderWidget() const;
    FourDTransformWidget* transformWidget() const;
    GrayCurveWidget* fourdGrayCurveWidget() const;
    void setFreezeBarAllIndex();

protected:
    void showEvent(QShowEvent* e);
public slots:
    void onBeforecurSonoParametersChanged();
    void onCurSonoParametersChanged();
    void onCurvedLineCtrlPointMoved(const QString& direction);
private slots:
    void onPresetChanged(const PresetParameters& presets);
    void onFreezeChanged(const QVariant& value);
    void onFourDGainChanged(const QVariant& val);
    void onRenderModeChanged(const QVariant& value);
    void onVirtualHDOnChanged(const QVariant& value);
    void onFourdMouseActionChanged(const QVariant& value);
    void onPaletteChanged(const QVariant& value);

private:
    typedef QList<QPointF> ClipCurvePoints;
    void setupRects();
    static void callbackFrom4D();
    void connectSignals();
    void disconnectSignals();
    void updateFourDGain(const QVariant& val);
    void resetClipCurvePoints();
    void updateFourDMouseState(bool forceUpdate = false);
    void updateMouseStateRange();

private:
    Ui::ChisonFourDLiveWidget* ui;
    bool m_IsInited;
    ChisonFourDDataSender* m_ChisonFourDDataSender;
    static ChisonFourDLiveWidget* m_ChisonFourDLiveWidget;
};

#endif // CHISONFOURDLIVEWIDGET_H
