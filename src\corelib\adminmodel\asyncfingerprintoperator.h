#ifndef ASYNCFINGERPRINTOPERATOR_H
#define ASYNCFINGERPRINTOPERATOR_H

#include "fingerprintoperatorbase.h"
#include <QScopedPointer>

#ifdef USE_OPENFINGERPRINT
class OpenFingerprintDev;
#endif
class AdminConfigModel;

class ADMINMODELSHARED_EXPORT ASyncFingerPrintOperator : public FingerPrintOperatorBase
{
    Q_OBJECT

public:
    ASyncFingerPrintOperator(AdminConfigModel* model, QObject* parent = nullptr);
    ~ASyncFingerPrintOperator();

    virtual FPrintType fprintType() const;
    virtual int maxCount() override;
    virtual bool enroll(const QString& userId) override;
    virtual bool verify() override;
    virtual void stop() override;
    virtual bool remove(const FingerprintInfo& fpinfo) override;
    virtual const QList<FingerprintInfo> getFingerprintInfo(const QString& userId) const override;
    virtual bool addFingerprint(const QString& fingerName, int fingerId, int status, const QString& userId) override;
    virtual bool hasFingerprint() override;

    void enrollCallBack(int finishedStage, int allStages);
    void verifyCallFunc(const char* username, bool verified);

private:
    AdminConfigModel* m_AdminModel;
#ifdef USE_OPENFINGERPRINT
#ifdef USE_OPENFINGERPRINT_LIB
    QScopedPointer<OpenFingerprintDev> m_FingerprintDev;
#endif
#endif
    QString m_VerifiedUserId;
    bool m_IsRunning;
};

#endif // ASYNCFINGERPRINTOPERATOR_H
