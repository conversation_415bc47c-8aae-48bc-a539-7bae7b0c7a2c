#ifndef OPENFINGERPRINTTASK_H
#define OPENFINGERPRINTTASK_H

#include <QObject>
#ifdef USE_OPENFINGERPRINT
#include "openfingerprintdev.h"
#endif

class OpenFingerprintTask : public QObject
{
    Q_OBJECT
public:
    explicit OpenFingerprintTask(QObject* parent = nullptr);

    void startEnroll();
    void setEnrollUserId(const QString& userId);

    void startVerify();
    const QString& verifiedUserId() const;

    void stopTask();

    bool deleteFp(const QString& userId);

    void enrollCallBack(int finishedStage, int allStages);
    void verifyCallBack();

private:
    void enroll();
    void verify();

signals:
    void pressFinger(int timer);
    void enrollState(uint state, int id);
    void verifyState(uint state);

private:
    friend class OpenFingerprintEnrollTask;
    friend class OpenFingerprintVerifyTask;
#ifdef USE_OPENFINGERPRINT
    QScopedPointer<OpenFingerprintDev> m_FingerprintDev;
#endif
    QString m_EnrollUserId;
    QString m_VerifiedUserId;
    bool m_IsRunning;
};

#endif // OPENFINGERPRINTTASK_H
