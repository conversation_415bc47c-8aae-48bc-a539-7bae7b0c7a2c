#include "focusescombinewidget.h"
#include "ui_focusescombinewidget.h"
#include "uiutil.h"
#include "presetmodeitemwidget.h"
#include "focusescombinemodel.h"
#include "bfpnames.h"
#include "parameter.h"
#include "messageboxframe.h"
#include <QDebug>

FocusesCombineWidget::FocusesCombineWidget(QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::FocusesCombineWidget)
    , m_model(NULL)
{
    ui->setupUi(this);
}

FocusesCombineWidget::~FocusesCombineWidget()
{
    delete ui;
}

FocusesCombineModel* FocusesCombineWidget::model() const
{
    return m_model;
}

bool FocusesCombineWidget::save(bool showTip)
{
    if (m_model->save())
    {
        if (showTip)
        {
            MessageBoxFrame::tipInformation(QString("Focuses combine data file has been saved successfully!"));
        }
        return true;
    }
    else
    {
        MessageBoxFrame::warning(QString("Focuses combine data save failed."));
        return false;
    }
}

void FocusesCombineWidget::setModel(FocusesCombineModel* model)
{
    m_model = model;
    connect(m_model, SIGNAL(modelChanged()), this, SLOT(onModelChanged()));
    onModelChanged();
}

void FocusesCombineWidget::onModelChanged()
{
    ui->spinBoxCombineStart->blockSignals(true);
    ui->spinBoxCombineStart->setValue(m_model->combineStart());
    ui->spinBoxCombineStart->blockSignals(false);
    ui->spinBoxCombineLen->blockSignals(true);
    ui->spinBoxCombineLen->setValue(m_model->combineLength());
    ui->spinBoxCombineLen->blockSignals(false);
    setFocusPosWidget(m_model->focusNum(), m_model->focusPos());
}

void FocusesCombineWidget::onSetSonoParameters()
{
    autoCallConnectedChangedSlots();
}

void FocusesCombineWidget::setFocusPosWidget(int focuses, int focusPos)
{
    if (focuses < 1 && focusPos < 0)
    {
        return;
    }
    UiUtil::clearLayout(ui->gridLayout, true);
    for (int i = 0; i < focuses; i++)
    {
        QLabel* labelFocus = new QLabel(QString("Focus %1").arg(i + 1), this);
        ui->gridLayout->addWidget(labelFocus, i, 0);

        PresetModeItemWidget* w = new PresetModeItemWidget(this);
        w->setParameter(m_model->focuesParameters().at(focusPos).at(i));
        ui->gridLayout->addWidget(w, i, 1);
    }
}

void FocusesCombineWidget::on_spinBoxCombineStart_valueChanged(const QString& arg1)
{
    QString text = arg1;
    int combineStart = text.remove("%").toInt();
    setPV(BFPNames::FocusCombineStartStr, combineStart);
}

void FocusesCombineWidget::on_spinBoxCombineLen_valueChanged(const QString& arg1)
{
    QString text = arg1;
    int combineLen = text.remove("%").toInt();
    setPV(BFPNames::FocusCombineLenStr, combineLen);
}

void FocusesCombineWidget::on_pushButtonSave_clicked()
{
    bool needSave = (MessageBoxFrame::question(this, QString(), "Are you sure to save the focus combine setting?",
                                               QMessageBox::Yes | QMessageBox::No) == QMessageBox::Yes);
    if (needSave)
    {
        save();
    }
}

void FocusesCombineWidget::on_pushButtonReload_clicked()
{
    m_model->reload();
}

void FocusesCombineWidget::on_pushButtonDefault_clicked()
{
    m_model->setDefault();
}

void FocusesCombineWidget::onFocusNumBChanged(const QVariant& value)
{
    if (pIV(BFPNames::SyncModeStr) == Sync_None)
    {
        ui->labelFocusNum->setText(QString::number(value.toInt() + 1));
    }
}

void FocusesCombineWidget::onFocusPosBChanged(const QVariant& value)
{
    if (pIV(BFPNames::SyncModeStr) == Sync_None)
    {
        if (m_model->focusNum() > 1)
        {
            ui->labelFocusPos->setText(QString::number(value.toInt() + 1));
        }
        else if (m_model->focusNum() == 1)
        {
            ui->labelFocusPos->setText(QString::number(value.toInt()));
        }
    }
}

void FocusesCombineWidget::onSyncModeChanged(const QVariant& value)
{
    SyncModeType syncMode = (SyncModeType)value.toInt();

    if (syncMode != Sync_M && syncMode != Sync_None)
    {
        if (pIV(BFPNames::FocusNumBStr) > 0)
        {
            ui->labelFocusNum->setText(
                QString::number(parameter(BFPNames::FocusNumBStr)->controlTableValueSended() + 1));
            ui->labelFocusPos->setText(QString::number(parameter(BFPNames::FocusPosBStr)->controlTableValueSended()));
        }
    }
}
