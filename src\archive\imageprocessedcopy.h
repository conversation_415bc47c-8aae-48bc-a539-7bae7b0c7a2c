#ifndef IMAGEPROCESSEDCOPY_H
#define IMAGEPROCESSEDCOPY_H
#include "archive_global.h"
#include "basecopy.h"

class IColorMapCalculatorModel;
class DicomTaskManager;
class Patient;
class DeleteFilesController;
class PackagesMeasResults;
class IColorMapManager;

class ARCHIVESHARED_EXPORT ImageProcessedCopy : public BaseCopy
{
    Q_OBJECT
public:
    ImageProcessedCopy(IColorMapManager* colorMapManager, QObject* parent = 0);
    virtual ~ImageProcessedCopy();

    enum ImageType
    {
        BMP,
        JPG,
        PNG,
        DCM,
        Count
    };
    enum DicomTaskType
    {
        Storage,
        SR,
        AVI,
        MP4
    };
    Q_ENUM(DicomTaskType);
    virtual bool qCopyFile(const QString& source, const QString& target);
    qint64 fileSize(const QFileInfo& info) const;
    QString destFileName(const QString& srcName) const;

    void setImageType(ImageType type);
    void setIsToAvi(bool isToAvi);
    void setCinetransTo(const QString type);
    void setIsExportDcmSR(bool isExportDcmSR);
    void setTaskManager(DicomTaskManager* taskManager);
    void setDeleteFilesController(DeleteFilesController* controller);
    void setPatient(Patient* patient);
    void setIsGDPR(bool isGDPR);
    int dcmSRSuccessNum() const;
    void setCopyRetrieveExam(bool value);

private:
    void addCount();
    void decCount(bool isSuccessed, const QString& srcFileNames);
    Patient* getPatient(const QString& studyOid);
    /**
     * @brief addDicomTask 添加dicom后台导出任务
     * @param source .cin, .bmp, .jpg, .mes
     * @param target 导出路径
     * @return
     */
    void addDicomTask(const QString& source, const QString& target, DicomTaskType type);
signals:
    void infoShowed(bool);

private:
    IColorMapManager* m_ColorMapManager;
    IColorMapCalculatorModel* m_Model;
    ImageType m_ImageType;
    bool m_IsToAvi;
    bool m_IsToMp4;
    bool m_IsExportDcmSR;
    int m_DcmSRSuccessNum;
    DicomTaskManager* m_TaskManager;
    Patient* m_Patient;
    DeleteFilesController* m_DeleteFilesController;
    const PackagesMeasResults* m_Results;
    bool m_IsGDPR;
    bool m_CopyRetrieveExam;
};

#endif // IMAGEPROCESSEDCOPY_H
