#include "workstatuswidget.h"
#include "ui_workstatuswidget.h"
#include "systemhintmodel.h"
#include "util.h"

WorkStatusWidget::WorkStatusWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::WorkStatusWidget)
    , m_Model(NULL)
{
    ui->setupUi(this);
    ui->horizontalLayout->setAlignment(Qt::AlignLeft);
}

WorkStatusWidget::~WorkStatusWidget()
{
    delete ui;
}

void WorkStatusWidget::setModel(SystemHintModel* value)
{
    m_Model = value;

    connect(m_Model, SIGNAL(workStatusChanged()), this, SLOT(onWorkStatusChanged()));

    retranslateUi();
}

void WorkStatusWidget::onWorkStatusChanged()
{
    if (m_Model != NULL)
    {
        if (m_Model->workStatus().isEmpty())
        {
            ui->label->setText(QString());
        }
        else
        {
            ui->label->setText(Util::translate(m_Model, m_Model->workStatus()));
        }
    }
}

void WorkStatusWidget::retranslateUi()
{
    if (m_Model != NULL)
    {
        onWorkStatusChanged();
    }
}
