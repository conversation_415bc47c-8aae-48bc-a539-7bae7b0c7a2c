add_definitions(-DADMINMODEL_LIBRARY)
include_depends(utilitymodel support usf.common.core.database usf.interface.exam.business)

set(srcs
    authorityinfo.cpp 
    adminconfigmodel.cpp 
    userpresetdatahandler.cpp
    adminconfigmodeldata.cpp 
    userinfo.cpp
    loginmanager.cpp
    fingerprintinfo.cpp
    fingerprinttask.cpp
    ifingerprintoperator.cpp
    fingerprintoperatorbase.cpp
    syncfingerprintoperator.cpp
    fingerworker.cpp
    fingercontroller.cpp
    asyncfingerprintoperator.cpp
)

add_library_qt(adminmodel ${srcs})

target_link_libraries(adminmodel utilitymodel support usf.common.core.database usf.interface.exam.business ${FINGERPRINT_LIBRARIES} ${OPENFPRINT_LIBRARIES})

if(${USE_FINGERPRINT})
    add_custom_command(TARGET adminmodel
        POST_BUILD
        COMMAND cmake -DsourceDirector=${FINGERPRINT_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )
endif(${USE_FINGERPRINT})

if(${USE_OPENFINGERPRINT})
    add_custom_command(TARGET adminmodel
        POST_BUILD
        COMMAND cmake -DsourceDirector=${OPENFPRINT_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )
endif(${USE_OPENFINGERPRINT})
