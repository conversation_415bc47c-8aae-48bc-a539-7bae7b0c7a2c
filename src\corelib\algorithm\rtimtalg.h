#ifndef RTIMTALG_H
#define RTIMTALG_H

#include "algorithm_global.h"
#include <QLibrary>
#ifdef USE_RTIMT
#include "rtimt.h"
#else
struct IMOutput // typedef
{
    int p_middle_y = -1;
    int x_center_latest = -1;
    int y_begin_latest = -1;
    int y_end_latest = -1;
    int backendType1 = -100000;
    int backendType2 = -100000;
    int coord_list[7680] = {-1};
    int index_list[8] = {-1};
    float angle = 10000.0;
    double thickness_max_near = -1.0;
    double thickness_max_far = -1.0;
    double upper_lower_diffs_mean = -1.0;
};
struct IMRoibox
{
    int use_still_roi = 0;
    int change_still_roi = 0;
    int num_frames_since_still = 0;
    int x_center_still = -1;
    int y_begin_still = -1;
    int y_end_still = -1;
    int p_middle_ys_diff[2] = {-1};
    int x_centers[5] = {-1};
    int y_begins[5] = {-1};
    int y_ends[5] = {-1};

    float angles_diff[2] = {-10000.0};
};
struct IMCycle
{
    int local_minmax[10] = {-1};
    double lumen_diameters[3] = {-1.0};
};
struct IMTest // typedef
{
    int index_list[8] = {-1};
};
#endif
#include "virtualalg.h"

class ALGORITHMSHARED_EXPORT RtimtAlg : public VirtualAlg
{
    friend class AlgInstance;

private:
    RtimtAlg();

public:
    typedef void (*CreateViewontextRtimt)(const char* modelpath, int width, int height);
    typedef void (*RemoveViewontextRtimt)();
    typedef void (*IntimaMeasureRtimt)(unsigned char* imgin, int width, int height, int left_bbox_x, int right_bbox_x,
                                       int lower_bbox_y, int upper_bbox_y, int crop_width, int crop_height,
                                       int width_bias, int height_bias, IMOutput* output);
    typedef void (*RoibboxCalculateRtimt)(int* p_middle_ys_diff, float* angles_diff, int* x_centers, int* y_begins,
                                          int* y_ends, int duration_determine, int duration_roi_calculate,
                                          int use_still_roi, int change_still_roi, int num_frames_since_still,
                                          int x_center_still, int y_begin_still, int y_end_still, IMRoibox* roibox);
    typedef void (*CardiacCycleDetection)(double* lumen_diameters, int length, int start_idx, int* local_minmax,
                                          IMCycle* cycle);
    typedef void (*GetMarginValues)(unsigned char* imgin, int width, int height, int& left_margin_x,
                                    int& right_margin_x, int& lower_margin_y, int& upper_margin_y);

    void createViewontextRtimt(const char* modelpath, int width, int height);
    void removeViewontextRtimt();
    void intimaMeasureRtimt(unsigned char* imgin, int width, int height, int left_bbox_x, int right_bbox_x,
                            int lower_bbox_y, int upper_bbox_y, int crop_width, int crop_height, int width_bias,
                            int height_bias, IMOutput* output);
    void roibboxCalculateRtimt(int* p_middle_ys_diff, float* angles_diff, int* x_centers, int* y_begins, int* y_ends,
                               int duration_determine, int duration_roi_calculate, int use_still_roi,
                               int change_still_roi, int num_frames_since_still, int x_center_still, int y_begin_still,
                               int y_end_still, IMRoibox* roibox);
    void cardiacCycleDetection(double* lumen_diameters, int length, int start_idx, int* local_minmax, IMCycle* cycle);
    void getMarginValues(unsigned char* imgin, int width, int height, int& left_margin_x, int& right_margin_x,
                         int& lower_margin_y, int& upper_margin_y);

private:
    CreateViewontextRtimt m_CreateContext;
    RemoveViewontextRtimt m_RemoveContext;
    IntimaMeasureRtimt m_InitMa;
    RoibboxCalculateRtimt m_RoiCal;
    CardiacCycleDetection m_CycleDetect;
    GetMarginValues m_GetMargin;
};

#endif // RTIMTALG_H
