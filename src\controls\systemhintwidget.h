#ifndef SYSTEMHINTWIDGET_H
#define SYSTEMHINTWIDGET_H
#include "controls_global.h"

#include "borderedwidget.h"
#include "basewidget.h"

class SystemHintModel;
class MultiFuncOperationAreaModel;

namespace Ui
{
class SystemHintWidget;
}

class CONTROLSSHARED_EXPORT SystemHintWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit SystemHintWidget(QWidget* parent = 0);
    ~SystemHintWidget();
    void setModel(SystemHintModel* value);
    void setCurrentIndex(int index);

protected:
    void retranslateUi();

private:
    Ui::SystemHintWidget* ui;
};

#endif // SYSTEMHINTWIDGET_H
