build-sonoeye_win-job:
  stage: build
  tags:
    - SR9
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  script:
    - ./ci/shell/USAPI_Release.bat -X E:\GitLab-Runner\builds\44z3iQM5\0\usplatform\xunit -R E:\GitLab-Runner\code\resource-xunit -m SonoEye -a x86
    - ./ci/shell/exit_ConsoleWindowHost.bat

release-sonoeye_win-job:
  stage: release
  tags:
    - SR9
  rules:
    - if: $model == "SonoEye_win"
  before_script:
    - ./ci/shell/get_known_hosts.bat
    - ./ci/shell/pullrelatedrep.bat -r $ResourceBranch -e E:\GitLab-Runner
  script:
    - ./ci/shell/USAPI_Release.bat -X E:\GitLab-Runner\builds\44z3iQM5\0\usplatform\xunit -R E:\GitLab-Runner\code\resource-xunit -m SonoEye -a x86
    - ./ci/shell/exit_ConsoleWindowHost.bat