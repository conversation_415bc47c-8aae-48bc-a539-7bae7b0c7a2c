#ifndef PATIENTMIDDLEWIDGET_H
#define PATIENTMIDDLEWIDGET_H
#include "archive_global.h"

//#include <QWidget>
#include "basewidget.h"
#include "screenorientationmodel.h"

class Patient;
class Study;
namespace Ui
{
class PatientMiddleWidget;
}

/**
 * @brief 新建病人界面的中间部分
 */
class ARCHIVESHARED_EXPORT PatientMiddleWidget : public BaseWidget
{
    Q_OBJECT
    Q_PROPERTY(bool isHor READ isHor)
public:
    explicit PatientMiddleWidget(QWidget* parent = 0);
    ~PatientMiddleWidget();
    /**
     * @brief setCurrentIndex 当前的科室
     *
     * @param index
     */
    void setCurrentIndex(const int index);
    /**
     * @brief setPatient 设在一个patient
     *
     * @param patient
     */
    void setPatient(Patient* patient);
    /**
     * @brief setStudy 设置一个study
     *
     * @param study
     */
    void setStudy(Study* study);
    /**
     * @brief flawlessStudy 完善一个study
     *
     * @param study
     */
    void flawlessStudy(Study* study);
    /**
     * @brief clearStudyInfo 清除study的信息
     */
    void clearStudyInfo();
    void setCurrentWidgetEnabled(bool enabled);
    void setPhysiciansName(const QString& value);
    void setAccession(const QString& value);
    void setAnimalSpeciesIndex(int index);
    bool isHor();

protected:
    void retranslateUi();
    void orientationChanged(Qt::ScreenOrientation orientation);

private:
    Ui::PatientMiddleWidget* ui;
    ScreenOrientationModel* m_Model;
};

#endif // PATIENTMIDDLEWIDGET_H
