#include "sonodiaphalg.h"

SonoDiaphAlg::SonoDiaphAlg()
    : VirtualAlg()
{
    m_Lib.setFileName("diaphragmproject");
}

int SonoDiaphAlg::createDiapher(void*& handle)
{
    CreateDiapher func = (CreateDiapher)m_Lib.resolve("createDiapher");
    if (func == NULL)
    {
        m_IsError = true;
        return -1;
    }
    m_Initialized = true;
    return func(handle);
}

int SonoDiaphAlg::releaseDiapher(void* handle)
{
    ReleaseDiapher func = (ReleaseDiapher)m_Lib.resolve("releaseDiapher");
    if (func == NULL || !m_Initialized)
    {
        m_IsError = true;
        return -1;
    }
    m_Initialized = false;
    func(handle);
    return 1;
}

int SonoDiaphAlg::diaphragmAnalysis(void* handle, unsigned char* inImage, int width, int height, int rect[],
                                    DARetData* retData)
{
    DiaphragmAnalysis func = (DiaphragmAnalysis)m_Lib.resolve("diaphragmAnalysis");
    if (func == NULL || !m_Initialized)
    {
        m_IsError = true;
        return -1;
    }
    return func(handle, inImage, width, height, rect, retData);
}
