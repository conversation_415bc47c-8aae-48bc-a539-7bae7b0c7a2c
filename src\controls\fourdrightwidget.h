#ifndef FOURDRIGHTWIDGET_H
#define FOURDRIGHTWIDGET_H

#include "controls_global.h"
#include "basewidget.h"

namespace Ui
{
class FourDRightWidget;
}

class ImageSkimManager;

class CONTROLSSHARED_EXPORT FourDRightWidget : public BaseWidget
{
    Q_OBJECT
public:
    explicit FourDRightWidget(QWidget* parent = 0);
    ~FourDRightWidget();
    ImageSkimManager* imageClipWidget() const;

protected:
    void retranslateUi();
    void initImageSkimManager();

private:
    Ui::FourDRightWidget* ui;
};

#endif // FOURDRIGHTWIDGET_H
