#include "glanceimagestopwidget.h"
#include "ui_glanceimagestopwidget.h"
#include "model/patient.h"
#include "dataaccesslayerhelper.h"
#include "appsetting.h"

GlanceImagesTopWidget::GlanceImagesTopWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::GlanceImagesTopWidget)
    , m_Model(ScreenOrientationModel::getInstance())
{
    ui->setupUi(this);
    connect(ui->comboBoxId, SIGNAL(currentIndexChanged(QString)), this, SIGNAL(currentPatientInfoChanged(QString)));
    if (AppSetting::isAnimalAndChinese())
    {
        setID2AnimalID();
    }
}

GlanceImagesTopWidget::~GlanceImagesTopWidget()
{
    delete ui;
}

void GlanceImagesTopWidget::setPatientInfoList(const QStringList& idList)
{
    // BUG:15862 开机第一次，存图，end病例，Review界面可以继续检查
    //原因：当idList为空的时候，不会触发currentIndexChanged，那么，Review界面显示的是默认的页面。
    //解决办法,向Item中插入一个空值，当idList为空时，向Item中插入空值,触发currentIndexChanged消息
    if (idList.isEmpty())
    {
        ui->comboBoxId->addItem(QString());
    }
    ui->comboBoxId->clear();
    ui->comboBoxId->addItems(idList);
}

bool GlanceImagesTopWidget::isHor()
{
    return m_Model->isHor();
}

void GlanceImagesTopWidget::retranslateUi()
{
    ui->retranslateUi(this);
    if (AppSetting::isAnimalAndChinese())
    {
        setID2AnimalID();
    }
}

void GlanceImagesTopWidget::orientationChanged(Qt::ScreenOrientation orientation)
{
    QString className(this->metaObject()->className());
    OrientationConfigItem config;
    m_Model->getInfosByClassName(orientation, className, config);
}

void GlanceImagesTopWidget::on_comboBoxId_currentIndexChanged(const QString& arg1)
{
    QString patientId = arg1.split("(").at(0);
    if (!patientId.isEmpty())
    {
        Patient* patient = DataAccessLayerHelper::getPatient(patientId, false);
        if (patient != NULL)
        {
            ui->lineEditName->setText(patient->formattedName());
            delete patient;
        }
    }
    else
    {
        ui->lineEditName->clear();
    }
}

void GlanceImagesTopWidget::setID2AnimalID()
{
    ui->label->setText(tr("Animal ID"));
}
