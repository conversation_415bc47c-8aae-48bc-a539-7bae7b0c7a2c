#ifndef TREEVIEWBUTTON_H
#define TREEVIEWBUTTON_H
#include "archive_global.h"

#include "basewidget.h"

namespace Ui
{
class TreeViewButton;
}

class ARCHIVESHARED_EXPORT TreeViewButton : public BaseWidget
{
    Q_OBJECT

public:
    explicit TreeViewButton(QWidget* parent = 0);
    ~TreeViewButton();
    void patientView();
    void studyView();
    void setSelectAllButtonEnabled(bool value);
signals:
    void buttonPatientClicked();
    void buttonStudyClicked();
    void buttonExpandClicked();
    void buttonCollapseClicked();
    void buttonSelectClicked();

protected:
    void setButtonsEnable(bool enable);
    void retranslateUi();
private slots:
    void on_pushButtonPatient_clicked();
    void on_pushButtonStudy_clicked();

private:
    Ui::TreeViewButton* ui;
    static const char* const m_AnimalString[];
    enum ANIMALSTRING
    {
        Animal_View
    };
};

#endif // TREEVIEWBUTTON_H
