<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ConfirmDialog</class>
 <widget class="QDialog" name="ConfirmDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>473</width>
    <height>113</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout" stretch="1,0">
   <item>
    <widget class="QLabel" name="label">
     <property name="text">
      <string/>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QPushButton" name="cancelButton">
       <property name="focusPolicy">
        <enum>Qt::NoFocus</enum>
       </property>
       <property name="text">
        <string>Cancel</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="skipAllButton">
       <property name="focusPolicy">
        <enum>Qt::NoFocus</enum>
       </property>
       <property name="text">
        <string>SkipAll</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="replaceAllButton">
       <property name="focusPolicy">
        <enum>Qt::NoFocus</enum>
       </property>
       <property name="text">
        <string>ReplaceAll</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="skipButton">
       <property name="focusPolicy">
        <enum>Qt::NoFocus</enum>
       </property>
       <property name="text">
        <string>Skip</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="replaceButton">
       <property name="focusPolicy">
        <enum>Qt::NoFocus</enum>
       </property>
       <property name="text">
        <string>Replace</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
