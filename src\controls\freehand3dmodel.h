#ifndef FREEHAND3DMODEL_H
#define FREEHAND3DMODEL_H
#include "controls_global.h"
#include <QObject>
#include "infostruct.h"
#include "dataarg.h"

#define DELETE_PTR(ptr)                                                                                                \
    if (ptr != NULL)                                                                                                   \
    {                                                                                                                  \
        delete ptr;                                                                                                    \
        ptr = NULL;                                                                                                    \
    }
#define DELETE_ARR(ptr)                                                                                                \
    if (ptr != NULL)                                                                                                   \
    {                                                                                                                  \
        delete[] ptr;                                                                                                  \
        ptr = NULL;                                                                                                    \
    }

class ILineBufferManager;
class ImageTile;

class CONTROLSSHARED_EXPORT FreeHand3DModel : public QObject
{
    Q_OBJECT
public:
    explicit FreeHand3DModel(ImageTile* imageTile, ILineBufferManager* bufferManager, QObject* parent = 0);
    ~FreeHand3DModel();
public slots:
    void process3DData();
    void onVolumeSpacingChanged(const QVariant& value);

private:
    void setUpdateProgressBar(int index);
    int catchPicureSize(unsigned char* dest, const unsigned char* src);
    void getFreeHand3DProperty(FreeHand3DProperty& property);
signals:
    void updateProgressBarText(const QString& text);
    void updateProgressBarValue(qint64 value);

    void volumeDataSended();

private:
    ImageTile* m_ImageTile;
    ILineBufferManager* m_BufferManager;
    uchar* m_FrameDestData;
    DataSizeInfo m_DataSizeInfo;
    int m_FrameCount;
    int m_FrameSize;
};

#endif // FREEHAND3DMODEL_H
