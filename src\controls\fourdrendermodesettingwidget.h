#ifndef FOURDRENDERMODESETTINGWIDGET_H
#define FOURDRENDERMODESETTINGWIDGET_H

#include "controls_global.h"
#include "fourdparassettingbasewidget.h"
#include "fourdrendermodewidget.h"

class CONTROLSSHARED_EXPORT FourDRenderModeSettingWidget : public FourDParasSettingBaseWidget
{
    Q_OBJECT

public:
    explicit FourDRenderModeSettingWidget(QWidget* parent = 0);
    void initalize();
    void setSonoParameters(SonoParameters* sonoParameters);

protected:
    virtual void currenIndexChanged(const QString& arg1);
    virtual bool contains(const QString& name);
    virtual void add(const QString& name);
    virtual void save(const QString& name);
    virtual void del(const QString& name);
    virtual bool rename(const QString& oldName, const QString& newName);
    virtual QStringList names();

private slots:
    void onFourDPresetParasChanged();
    void onFourDPresetParaDeleted(int index);

private:
    FourDRenderModeWidget* m_Child;
};

#endif // FOURDRENDERMODESETTINGWIDGET_H
