#include "fourdlightanglesettingwidget.h"
#include "fourdparascontainer.h"
#include "fourdrealskinlightsetting.h"

FourDLightAngleSettingWidget::FourDLightAngleSettingWidget(QWidget* parent)
    : FourDParasSettingBaseWidget(parent)
    , m_<PERSON>(new FourDLightAngleWidget(this))
{
    addWidget(m_Child);
    m_SettingName = QString("FourDSkinLightAngle");
    m_ParaType = FourDParasInfo::FourDLightAngle;
}

void FourDLightAngleSettingWidget::initalize()
{
    m_Child->initalize();
    FourDParasSettingBaseWidget::initalize();
}

void FourDLightAngleSettingWidget::setSonoParameters(SonoParameters* sonoParameters)
{
    m_Child->setSonoParameters(sonoParameters);
}

void FourDLightAngleSettingWidget::currenIndexChanged(const QString& arg1)
{
    m_Child->setFourDLightAnglePara(oneParasT<FourDParasInfo::FourDLightAnglePara>(arg1));
}

bool FourDLightAngleSettingWidget::contains(const QString& name)
{
    return isExistNameT<FourDParasInfo::FourDLightAnglePara>(name);
}

void FourDLightAngleSettingWidget::add(const QString& name)
{
    addOneParaT<FourDParasInfo::FourDLightAnglePara>(name, m_Child->fourDLightAnglePara());
    FourDRealSkinLightSetting::instance().setValue(name.toInt(), m_Child->fourDLightAnglePara().m_HorDegree,
                                                   m_Child->fourDLightAnglePara().m_VerDegree);
}

void FourDLightAngleSettingWidget::save(const QString& name)
{
    add(name);
    FourDRealSkinLightSetting::instance().writeIni();
    // FourDParasManager::instance().saveFourDLightAngleParas();
}

void FourDLightAngleSettingWidget::del(const QString& name)
{
}

bool FourDLightAngleSettingWidget::rename(const QString& oldName, const QString& newName)
{
    return true;
}

QStringList FourDLightAngleSettingWidget::names()
{
    return namesT<FourDParasInfo::FourDLightAnglePara>();
}
