#ifndef PARAMETERSETUPWIDGET_H
#define PARAMETERSETUPWIDGET_H
#include "controls_global.h"

#include <QWidget>
#include "basewidget.h"
namespace Ui
{
class ParameterSetUpWidget;
}

class CONTROLSSHARED_EXPORT ParameterSetUpWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit ParameterSetUpWidget(QWidget* parent = 0);
    ~ParameterSetUpWidget();

    void initializeParameter(const QString& name, int min, int max, int value);
    int value() const;
    void setDefaultValue(int value);

protected:
    virtual void retranslateUi();

private slots:
    void on_horizontalSlider_valueChanged(int value);

private:
    Ui::ParameterSetUpWidget* ui;
    QString m_Name;
    int m_Value;
    const static QStringList Names;
};

#endif // PARAMETERSETUPWIDGET_H
