/*
 * =====================================================================================
 *
 *    Filename:  seniormediawidget.h
 *
 *    Description:
 *      Version:  1.0
 *      Created:  2023年08月14日 13时28分18秒
 *      Function: 基于FFMPEG和QLabel实现的支持图片和多媒体播放窗口
 *
 *    Author:  wangshang, <EMAIL>
 *
 * =====================================================================================
 */

#ifndef SENIORMEDIAWIDGET_H
#define SENIORMEDIAWIDGET_H
#include "controls_global.h"

#include <QWidget>

class QLabel;
class MediaPlayerController;

class CONTROLSSHARED_EXPORT SeniorMediaWidget : public QWidget
{
    Q_OBJECT

public:
    SeniorMediaWidget(QWidget* parent = 0);
    ~SeniorMediaWidget();
    void setPaths(const QStringList& paths);
    void start(const int imageDuration = 2000);
    void stop();
    void setMute(bool isMute);

signals:
    void ffplayIsRuning();
    void ffplayIsEnded();
    void ffplayExited();

private:
    QLabel* m_ImageLabel;
    MediaPlayerController* m_MediaPlayerController;
};

#endif // SENIORMEDIAWIDGET_H
