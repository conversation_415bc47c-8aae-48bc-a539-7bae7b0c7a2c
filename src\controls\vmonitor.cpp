#include "vmonitor.h"
#include "ui_vmonitor.h"
#include <QDebug>
#include <QSettings>
#include "util.h"
#include "bfhwinfoformula.h"
#include "generalinfo.h"
#include <QTime>
#include "generalinfo.h"
#include "isystemusage.h"
#include "systemusage.h"

#ifdef SYS_WINDOWS
#define SYSTEM_SOUNDS_PATH "C:\\Windows\\Media\\Windows Notify Messaging.wav"
#elif defined(SYS_APPLE) || defined(SYS_UNIX)
#define SYSTEM_SOUNDS_PATH "/usr/share/sounds/alsa/Side_Left.wav"
#else
#define SYSTEM_SOUNDS_PATH
#endif

VMonitorDialog::VMonitorDialog(QWidget* parent)
    : BaseInputAbleDialogFrame(parent)
    , m_Speed(0)
{
    m_Child = new VMonitor(this);
    this->setContent(m_Child);
}

void VMonitorDialog::startWriteFileTimer()
{
    m_Child->startTimer();
}

void VMonitorDialog::stopWriteFileTimer()
{
    m_Child->stopTimer();
}

void VMonitorDialog::fillData(const QVector<VoltageData>& data, const QVector<unsigned short>& states)
{
    if (m_Child->isRunning())
    {
        int speed = m_Child->getSPeed();
        if (speed <= 0)
        {
            speed = 1;
        }
        static int count = 0;

        if ((count++ % speed) == 0)
        {
            m_Child->fillData(data, states);
            count = 1;
        }
    }
}

void VMonitorDialog::fillHeadData(uchar* data, int len)
{
    if (m_Child->isRunning())
    {
        int speed = m_Child->getSPeed();
        if (speed <= 0)
        {
            speed = 1;
        }
        static int count = 0;

        if ((count++ % speed) == 0)
        {
            m_Child->fillHeadData(data, len);
            count = 1;
        }
    }
}

void VMonitorDialog::showEvent(QShowEvent* e)
{
    BaseDialogFrame::showEvent(e);
    startWriteFileTimer();
}

void VMonitorDialog::hideEvent(QHideEvent* e)
{
    BaseDialogFrame::hideEvent(e);
    stopWriteFileTimer();
}

void VMonitorDialog::closeEvent(QCloseEvent* e)
{
    BaseDialogFrame::closeEvent(e);
    stopWriteFileTimer();
}

VMonitor::VMonitor(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::VMonitor)
    , m_Speed(20)
    , m_Run(true)
    , m_TimerSay(new QTimer())
    , time(0)
    , setting(QString("./voltagemonitor.ini"), QSettings::IniFormat)
{
    ui->setupUi(this);
    initSystemUsage();
    setting.clear();
    setting.sync();
    m_Timer.setInterval(30 * 1000);
    m_cpuTimer.setInterval(1000);
    //音量控制是不需要实时刷新，因此单独调用
    volumeControler();
    connect(&m_Timer, SIGNAL(timeout()), this, SLOT(writeIntoIni()));
    //定时器用来实时更新cpu和内存信息
    connect(&m_cpuTimer, SIGNAL(timeout()), this, SLOT(fillComputerInfo()));
}

void VMonitor::startTimer()
{
    m_Timer.start();
    m_cpuTimer.start();
}

void VMonitor::stopTimer()
{
    m_Timer.stop();
    m_TimerSay->stop();
    m_cpuTimer.stop();
}

void VMonitor::retranslateUi()
{
    ui->retranslateUi(this); // added by jyq
}

VMonitor::~VMonitor()
{
    delete m_TimerSay;
    delete ui;
}

void VMonitor::fillData(const QVector<VoltageData>& data, const QVector<unsigned short>& states)
{
    if (!this->isVisible())
    {
        return;
    }
    //为了保持显示一致性，故先计算出来

    uint temp = states[0];
    ui->sysState_0->setText(QString("0x%1").arg(temp, 4, 16, QChar('0')));

    temp = states[1];
    ui->sysState_1->setText(QString("0x%1").arg(temp, 4, 16, QChar('0')));

    temp = states[2];
    ui->sysState_2->setText(QString("0x%1").arg(temp, 4, 16, QChar('0')));

    // real time
    qreal a_plus_128 = BFHWInfoFormula::cal_a_plus_128(data[i_a_plus_128].real_time);
    qreal a_dec_55 = BFHWInfoFormula::cal_a_dec_55(data[i_a_dec_55].real_time);
    qreal emit_voltage = BFHWInfoFormula::cal_emit_voltage(data[i_emit_voltage].real_time);
    qreal d_plus_12 = BFHWInfoFormula::cal_d_plus_12(data[i_d_plus_12].real_time);
    qreal a_dec_5 = BFHWInfoFormula::cal_a_dec_5(data[i_a_dec_5].real_time);
    qreal a_plus_5 = BFHWInfoFormula::cal_a_plus_5(data[i_a_plus_5].real_time);
    qreal battery = BFHWInfoFormula::cal_battery(data[i_battery].real_time);
    qreal power_temp = BFHWInfoFormula::cal_fpga_and_5805_temperature(data[i_power_temp].real_time);

    qreal fpga_temp = BFHWInfoFormula::cal_fpga_and_5805_temperature(data[i_fpga_temp].real_time);
    qreal five805_temp = BFHWInfoFormula::cal_fpga_and_5805_temperature(data[i_5805_temp].real_time);

    ui->a_plus_128_r->setText(QString::number(a_plus_128));
    ui->a_dec_55_r->setText(QString::number(a_dec_55));
    ui->emitVoltage_r->setText(QString::number(emit_voltage));
    ui->d_plus_12_r->setText(QString::number(d_plus_12));
    ui->a_dec_5_r->setText(QString::number(a_dec_5));
    ui->a_plus_5_r->setText(QString::number(a_plus_5));
    ui->battery_r->setText(QString::number(battery));
    ui->power_temp_r->setText(QString::number(power_temp));
    ui->fpga_temp_r->setText(QString::number(fpga_temp));
    ui->five805_temp_r->setText(QString::number(five805_temp));
    Util::processEvents();
    // max
    a_plus_128 = BFHWInfoFormula::cal_a_plus_128(data[i_a_plus_128].max);
    a_dec_55 = BFHWInfoFormula::cal_a_dec_55(data[i_a_dec_55].max);
    emit_voltage = BFHWInfoFormula::cal_emit_voltage(data[i_emit_voltage].max);
    d_plus_12 = BFHWInfoFormula::cal_d_plus_12(data[i_d_plus_12].max);
    a_dec_5 = BFHWInfoFormula::cal_a_dec_5(data[i_a_dec_5].max);
    a_plus_5 = BFHWInfoFormula::cal_a_plus_5(data[i_a_plus_5].max);
    battery = BFHWInfoFormula::cal_battery(data[i_battery].max);
    power_temp = BFHWInfoFormula::cal_fpga_and_5805_temperature(data[i_power_temp].max);

    fpga_temp = BFHWInfoFormula::cal_fpga_and_5805_temperature(data[i_fpga_temp].max);
    five805_temp = BFHWInfoFormula::cal_fpga_and_5805_temperature(data[i_5805_temp].max);

    ui->a_plus_128_max->setText(QString::number(a_plus_128));
    ui->a_dec_55_max->setText(QString::number(a_dec_55));
    ui->emitVoltage_max->setText(QString::number(emit_voltage));
    ui->d_plus_12_max->setText(QString::number(d_plus_12));
    ui->a_dec_5_max->setText(QString::number(a_dec_5));
    ui->a_plus_5_max->setText(QString::number(a_plus_5));
    ui->battery_max->setText(QString::number(battery));
    ui->power_temp_max->setText(QString::number(power_temp));
    ui->fpga_temp_max->setText(QString::number(fpga_temp));
    ui->five805_temp_max->setText(QString::number(five805_temp));
    Util::processEvents();
    // min
    a_plus_128 = BFHWInfoFormula::cal_a_plus_128(data[i_a_plus_128].min);
    a_dec_55 = BFHWInfoFormula::cal_a_dec_55(data[i_a_dec_55].min);
    emit_voltage = BFHWInfoFormula::cal_emit_voltage(data[i_emit_voltage].min);
    d_plus_12 = BFHWInfoFormula::cal_d_plus_12(data[i_d_plus_12].min);
    a_dec_5 = BFHWInfoFormula::cal_a_dec_5(data[i_a_dec_5].min);
    a_plus_5 = BFHWInfoFormula::cal_a_plus_5(data[i_a_plus_5].min);
    battery = BFHWInfoFormula::cal_battery(data[i_battery].min);
    power_temp = BFHWInfoFormula::cal_fpga_and_5805_temperature(data[i_power_temp].min);

    fpga_temp = BFHWInfoFormula::cal_fpga_and_5805_temperature(data[i_fpga_temp].min);
    five805_temp = BFHWInfoFormula::cal_fpga_and_5805_temperature(data[i_5805_temp].min);

    ui->a_plus_128_min->setText(QString::number(a_plus_128));
    ui->a_dec_55_min->setText(QString::number(a_dec_55));
    ui->emitVoltage_min->setText(QString::number(emit_voltage));
    ui->d_plus_12_min->setText(QString::number(d_plus_12));
    ui->a_dec_5_min->setText(QString::number(a_dec_5));
    ui->a_plus_5_min->setText(QString::number(a_plus_5));
    ui->battery_min->setText(QString::number(battery));
    ui->power_temp_min->setText(QString::number(power_temp));
    ui->fpga_temp_min->setText(QString::number(fpga_temp));
    ui->five805_temp_min->setText(QString::number(five805_temp));
    Util::processEvents();
    //    writeIntoIni();
}

void VMonitor::fillHeadData(uchar* data, int len)
{
    QString allHex;
    for (int i = 0; i < 16; i++)
    {
        allHex.append(QString("%1 ").arg(i, 2, 16, QChar('0')).toUpper());
    }
    for (int i = 0; i < len; i++)
    {
        if ((i % 16) == 0)
        {
            allHex.append("\n");
        }
        allHex.append(QString("%1 ").arg(data[i], 2, 16, QChar('0')).toUpper());
    }

    ui->textEditRawData->setText(allHex);
}

void VMonitor::on_pushButton_clicked()
{
    m_Run = !m_Run;
    if (m_Run)
    {
        ui->pushButton->setText(QString("Pause"));
    }
    else
    {
        ui->pushButton->setText(QString("Continue"));
    }
}

void VMonitor::on_accelButton_clicked()
{
    if (m_Speed > 0)
    {
        m_Speed -= 2;
    }

    m_Speed = m_Speed < 0 ? 0 : m_Speed;

    ui->accelButton->setEnabled(m_Speed != 0);
}

void VMonitor::on_decelButton_clicked()
{
    m_Speed += 2;
    ui->accelButton->setEnabled(true);
}

void VMonitor::writeIntoIni()
{
    //    QSettings setting("./voltagemonitor.ini", QSettings::IniFormat);

    //    setting.clear();
    //    setting.sync();
    //    static int i = 0;
    //    setting.beginGroup(QString::number(i));
    if (!this->isVisible())
    {
        m_Timer.stop();
        return;
    }

    setting.beginGroup("System State");
    setting.setValue("sys_state_0", ui->sysState_0->text());
    setting.setValue("sys_state_1", ui->sysState_1->text());
    setting.setValue("sys_state_2", ui->sysState_2->text());
    setting.endGroup();

    //    setting.setValue("title1", "-----------------real time-----------------");
    setting.beginGroup("real time");
    setting.setValue("a_plus_128_r", ui->a_plus_128_r->text());
    setting.setValue("a_dec_55_r", ui->a_dec_55_r->text());
    setting.setValue("emitVoltage_r", ui->emitVoltage_r->text());
    setting.setValue("d_plus_12_r", ui->d_plus_12_r->text());
    setting.setValue("a_dec_5_r", ui->a_dec_5_r->text());
    setting.setValue("a_plus_5_r", ui->a_plus_5_r->text());
    setting.setValue("battery_r", ui->battery_r->text());
    setting.setValue("power_temp_r", ui->power_temp_r->text());
    setting.setValue("fpga_temp_r", ui->fpga_temp_r->text());
    setting.setValue("five805_temp_r", ui->five805_temp_r->text());
    setting.endGroup();

    Util::processEvents();

    setting.beginGroup("max");

    setting.setValue("a_plus_128_max", ui->a_plus_128_max->text());
    setting.setValue("a_dec_55_max", ui->a_dec_55_max->text());
    setting.setValue("emitVoltage_max", ui->emitVoltage_max->text());
    setting.setValue("d_plus_12_max", ui->d_plus_12_max->text());
    setting.setValue("a_dec_5_max", ui->a_dec_5_max->text());
    setting.setValue("a_plus_5_max", ui->a_plus_5_max->text());
    setting.setValue("battery_max", ui->battery_max->text());
    setting.setValue("power_temp_max", ui->power_temp_max->text());
    setting.setValue("fpga_temp_max", ui->fpga_temp_max->text());
    setting.setValue("five805_temp_max", ui->five805_temp_max->text());
    setting.endGroup();

    Util::processEvents();

    setting.beginGroup("min");

    setting.setValue("a_plus_128_min", ui->a_plus_128_min->text());
    setting.setValue("a_dec_55_min", ui->a_dec_55_min->text());
    setting.setValue("emitVoltage_min", ui->emitVoltage_min->text());
    setting.setValue("d_plus_12_min", ui->d_plus_12_min->text());
    setting.setValue("a_dec_5_min", ui->a_dec_5_min->text());
    setting.setValue("a_plus_5_min", ui->a_plus_5_min->text());
    setting.setValue("battery_min", ui->battery_min->text());
    setting.setValue("power_temp_min", ui->power_temp_min->text());
    setting.setValue("fpga_temp_min", ui->fpga_temp_min->text());
    setting.setValue("five805_temp_min", ui->five805_temp_min->text());
    setting.endGroup();
    Util::processEvents();

    //    ++i;
}

//用来填写PC界面需要定时刷新的信息
void VMonitor::fillComputerInfo()
{
    fillCPUTemp();
    fillCPUUsage();
    fillMemory();
    fillHWInfo();

    //获取风扇转速信息
    fillAllFanSpeed();
    //获取电压、电流、温度
    fillAllFanRelatedInfo();
}

//获取cpu温度，并输出在ui
void VMonitor::fillCPUTemp()
{
    int cpu_temperature = m_pSystemUsage->getCpuTemperature();
    ui->lineEdit_cputemp->setText(QString::number(cpu_temperature));
}

//获取cpu使用率
void VMonitor::fillCPUUsage()
{
    unsigned long cpu_core_count = m_pSystemUsage->getCpuCoresCount();
    ui->lineEdit_cpucorenum->setText(QString::number(cpu_core_count));

    unsigned long cpu_thread_count = m_pSystemUsage->getCpuThreadCount();
    ui->lineEdit_cputhread->setText(QString::number(cpu_thread_count));

    int cpu_usage = m_pSystemUsage->getCpuUsage();
    ui->lineEdit_cpuusage->setText(QString::number(cpu_usage));
}

//音量控件的控制逻辑
void VMonitor::volumeControler()
{
    int min = 0;
    int max = 100;
    int step = 1;
    //  滑动条属性设置
    ui->horizontalSlider->setMaximum(max);
    ui->horizontalSlider->setMinimum(min);
    ui->horizontalSlider->setSingleStep(step);
    //  slider与lineEdit的连接
    connect(ui->horizontalSlider, &QSlider::valueChanged, this,
            [=](int value) { ui->lineEdit->setText(QString::number(value)); });

    //  设置slider的值，放在这里保证第一次打开界面，slider和lineEdit都有值，同时避免第一次打开窗口出现提示音的播放。
    int volume = m_pSystemUsage->getCurVolumeVal();
    ui->horizontalSlider->setValue(volume);
    //  slider与系统音量的连接
    connect(ui->horizontalSlider, &QSlider::valueChanged, this, &VMonitor::setVolume);
    //  定时器用来控制提示音的播放
    connect(m_TimerSay, &QTimer::timeout, this, &VMonitor::playAudio);
}

void VMonitor::fillAllFanSpeed()
{
    LineData::AllFanSpeed data = GeneralInfo::instance().getAllFanSpeed();

    ui->value_rearfan_1->setText(QString::number(caculatorFanSpeed(data.Rear_FAN1Speed)));
    ui->value_rearfan_2->setText(QString::number(caculatorFanSpeed(data.Rear_FAN2Speed)));
    ui->value_rearfan_3->setText(QString::number(caculatorFanSpeed(data.Rear_FAN3Speed)));
    ui->value_rearfan_4->setText(QString::number(caculatorFanSpeed(data.Rear_FAN4Speed)));
    ui->value_adpfan_1->setText(QString::number(caculatorFanSpeed(data.ADP_FAN1Speed)));
    ui->value_adpfan_2->setText(QString::number(caculatorFanSpeed(data.ADP_FAN2Speed)));
    ui->value_adpfan_3->setText(QString::number(caculatorFanSpeed(data.ADP_FAN3Speed)));
    ui->value_bottomfan_1->setText(QString::number(caculatorFanSpeed(data.BOT_FAN1Speed)));
    ui->value_bottomfan_2->setText(QString::number(caculatorFanSpeed(data.BOT_FAN2Speed)));
    ui->value_bottomfan_3->setText(QString::number(caculatorFanSpeed(data.BOT_FAN3Speed)));
    ui->value_bottomfan_4->setText(QString::number(caculatorFanSpeed(data.BOT_FAN4Speed)));
    ui->value_bottomfan_5->setText(QString::number(caculatorFanSpeed(data.BOT_FAN5Speed)));
    ui->value_bottomfan_6->setText(QString::number(caculatorFanSpeed(data.BOT_FAN6Speed)));
    ui->value_bottomfan_7->setText(QString::number(caculatorFanSpeed(data.BOT_FAN7Speed)));
    ui->value_bottomfan_8->setText(QString::number(caculatorFanSpeed(data.BOT_FAN8Speed)));
    ui->value_bottomfan_9->setText(QString::number(caculatorFanSpeed(data.BOT_FAN9Speed)));
}

void VMonitor::fillAllFanRelatedInfo()
{
    LineData::AllFanRelatedInfo data = GeneralInfo::instance().getAllFanRelatedInfo();

    ui->lineEdit_BFL_VCCINT_0V95->setText(stringNumber(caculatorType1(data.BFL_VCCINT_0V95)) + QString(" V"));
    ui->lineEdit_BFH_VCCINT_0V95->setText(stringNumber(caculatorType1(data.BFH_VCCINT_0V95)) + QString(" V"));
    ui->lineEdit_DSP_VCCINT_0V95->setText(stringNumber(caculatorType1(data.DSP_VCCINT_0V95)) + QString(" V"));
    ui->lineEdit_MGTAVCC_1V->setText(stringNumber(caculatorType1(data.MGTAVCC_1V)) + QString(" V"));
    ui->lineEdit_MGTAVTT_1V2->setText(stringNumber(caculatorType1(data.MGTAVTT_1V2)) + QString(" V"));
    ui->lineEdit_VCC0_1V2->setText(stringNumber(caculatorType1(data.VCC0_1V2)) + QString(" V"));
    ui->lineEdit_VCCAUX_1V8->setText(stringNumber(caculatorType1(data.VCCAUX_1V8)) + QString(" V"));
    ui->lineEdit_MGTVCCAUX_1V8->setText(stringNumber(caculatorType1(data.MGTVCCAUX_1V8)) + QString(" V"));
    ui->lineEdit_VCC0_1V8->setText(stringNumber(caculatorType1(data.VCC0_1V8)) + QString(" V"));
    ui->lineEdit_DVDD_2V5->setText(stringNumber(caculatorType1(data.DVDD_2V5) * 2) + QString(" V")); // 51
    ui->lineEdit_DVDD_3V3->setText(stringNumber(caculatorType1(data.DVDD_3V3) * 2) + QString(" V")); // 52
    ui->lineEdit_MGTVCCAUX_1V8_IOUT->setText(stringNumber(caculatorType1(data.MGTVCCAUX_1V8_IOUT) / 0.001 / 200) +
                                             QString(" A")); // 53
    ui->lineEdit_VCC0_1V8_IOUT->setText(stringNumber(caculatorType1(data.VCC0_1V8_IOUT) / 0.001 / 200) +
                                        QString(" A")); // 54
    ui->lineEdit_VCC0_3V3_IOUT->setText(stringNumber(caculatorType1(data.VCC0_3V3_IOUT) / 0.001 / 200) +
                                        QString(" A"));                                                            // 55
    ui->lineEdit_DSP_FPGA_TEMP->setText(stringNumber(caculatorType2(data.DSP_FPGA_TEMP), 0) + QString(" °C"));     // 60
    ui->lineEdit_BFL_AFE_TEMP->setText(stringNumber(caculatorType2(data.BFL_AFE_TEMP), 0) + QString(" °C"));       // 61
    ui->lineEdit_BFL_5589_TEMP->setText(stringNumber(caculatorType2(data.BFL_5589_TEMP), 0) + QString(" °C"));     // 62
    ui->lineEdit_BFL_VCCINT_TEMP->setText(stringNumber(caculatorType2(data.BFL_VCCINT_TEMP), 0) + QString(" °C")); // 63
    ui->lineEdit_BFL_FPGA_TEMP->setText(stringNumber(caculatorType2(data.BFL_FPGA_TEMP), 0) + QString(" °C"));     // 64
    ui->lineEdit_RESV->setText(QString::number(data.RESV));                                                        // 65
    ui->lineEdit_HV1_DCDC_TEMP->setText(stringNumber(caculatorType2(data.HV1_DCDC_TEMP), 0) + QString(" °C"));     // 66
    ui->lineEdit_HV2_DCDC_TEMP->setText(stringNumber(caculatorType2(data.HV2_DCDC_TEMP), 0) + QString(" °C"));     // 67
    ui->lineEdit_DN12V_PWR_TEMP->setText(stringNumber(caculatorType2(data.DN12V_PWR_TEMP), 0) + QString(" °C"));   // 68
    ui->lineEdit_CW_DCDC_TEMP->setText(stringNumber(caculatorType2(data.CW_DCDC_TEMP), 0) + QString(" °C"));       // 69
    ui->lineEdit_PWR_MIDTOP_TEMP->setText(stringNumber(caculatorType2(data.PWR_MIDTOP_TEMP), 0) + QString(" °C")); // 70
    ui->lineEdit_AP1V6_DCDC_TEMP->setText(stringNumber(caculatorType2(data.AP1V6_DCDC_TEMP), 0) + QString(" °C")); // 71
    ui->lineEdit_AP13V5_PWR_TEMP->setText(stringNumber(caculatorType2(data.AP13V5_PWR_TEMP), 0) + QString(" °C")); // 72
    ui->lineEdit_CW_LD0_TEMP->setText(stringNumber(caculatorType2(data.CW_LD0_TEMP), 0) + QString(" °C"));         // 73
    ui->lineEdit_BFL_VCCINT_0V95_IOUT->setText(stringNumber(caculatorType1(data.BFL_VCCINT_0V95_IOUT) / 0.001 / 50) +
                                               QString(" A")); // 124
    ui->lineEdit_BFH_VCCINT_0V95_IOUT->setText(stringNumber(caculatorType1(data.BFH_VCCINT_0V95_IOUT) / 0.001 / 50) +
                                               QString(" A")); // 125
    ui->lineEdit_DSP_VCCINT_0V95_IOUT->setText(stringNumber(caculatorType1(data.DSP_VCCINT_0V95_IOUT) / 0.001 / 50) +
                                               QString(" A")); // 126
    ui->lineEdit_MGTAVCC_1V_IOUT->setText(stringNumber(caculatorType1(data.MGTAVCC_1V_IOUT) / 0.001 / 50) +
                                          QString(" A")); // 127
    ui->lineEdit_MGTAVTT_1V2_IOUT->setText(stringNumber(caculatorType1(data.MGTAVTT_1V2_IOUT) / 0.001 / 200) +
                                           QString(" A")); // 128
    ui->lineEdit_VCC0_1V2_IOUT->setText(stringNumber(caculatorType1(data.VCC0_1V2_IOUT) / 0.001 / 50) +
                                        QString(" A")); // 129
    ui->lineEdit_VCCAUX_1V8_IOUT->setText(stringNumber(caculatorType1(data.VCCAUX_1V8_IOUT) / 0.001 / 200) +
                                          QString(" A")); // 130
    ui->lineEdit_DVDD_2V5_IOUT->setText(stringNumber(caculatorType1(data.DVDD_2V5_IOUT) / 0.001 / 200) +
                                        QString(" A"));                                                          // 131
    ui->lineEdit_PWR_MID_TEMP->setText(stringNumber(caculatorType2(data.PWR_MID_TEMP), 0) + QString(" °C"));     // 132
    ui->lineEdit_AP5V6_PWR_TEMP->setText(stringNumber(caculatorType2(data.AP5V6_PWR_TEMP), 0) + QString(" °C")); // 133
    ui->lineEdit_AP3V6_PWR_TEMP->setText(stringNumber(caculatorType2(data.AP3V6_PWR_TEMP), 0) + QString(" °C")); // 134
    ui->lineEdit_AP2V3_PWR_TEMP->setText(stringNumber(caculatorType2(data.AP2V3_PWR_TEMP), 0) + QString(" °C")); // 135
    ui->lineEdit_VDD3V3_PWR_TEMP->setText(stringNumber(caculatorType2(data.VDD3V3_PWR_TEMP), 0) +
                                          QString(" °C"));                                                       // 136
    ui->lineEdit_VDD5V_PWR_TEMP->setText(stringNumber(caculatorType2(data.VDD5V_PWR_TEMP), 0) + QString(" °C")); // 137
    ui->lineEdit_PWR_BOT_TEMP->setText(stringNumber(caculatorType2(data.PWR_BOT_TEMP), 0) + QString(" °C"));     // 138
    ui->lineEdit_RESV2->setText(QString::number(data.RESV2));                                                    // 139
    ui->lineEdit_VDD_3V3_IOUT->setText(stringNumber(caculatorType1(data.VDD_3V3_IOUT) / 0.001 / 200) +
                                       QString(" A"));                                                            // 140
    ui->lineEdit_AP1V6_IOUT->setText(stringNumber(caculatorType1(data.AP1V6_IOUT) / 0.001 / 50) + QString(" A")); // 141
    ui->lineEdit_AP2V3_IOUT->setText(stringNumber(caculatorType1(data.AP2V3_IOUT) / 0.001 / 200) +
                                     QString(" A"));                                                              // 142
    ui->lineEdit_AP3V6_IOUT->setText(stringNumber(caculatorType1(data.AP3V6_IOUT) / 0.001 / 50) + QString(" A")); // 143
    ui->lineEdit_AP5V6_IOUT->setText(stringNumber(caculatorType1(data.AP5V6_IOUT) / 0.001 / 200) +
                                     QString(" A")); // 144
    ui->lineEdit_AN5V6_IOUT->setText(stringNumber(caculatorType1(data.AN5V6_IOUT) / 0.05 / 10.1) +
                                     QString(" A")); // 145
    ui->lineEdit_5V_PRB_IOUT->setText(stringNumber(caculatorType1(data._5V_PRB_IOUT) / 0.001 / 200) +
                                      QString(" A")); // 146
    ui->lineEdit_VDD5V_IOUT->setText(stringNumber(caculatorType1(data.VDD5V_IOUT) / 0.001 / 200) +
                                     QString(" A")); // 147
    ui->lineEdit_12VIN_ALL_IOUT->setText(stringNumber(caculatorType1(data._12VIN_ALL_IOUT) / 0.001 / 3 / 50) +
                                         QString(" A")); // 148
    ui->lineEdit_12VIN_MAIN_IOUT->setText(stringNumber(caculatorType1(data._12VIN_MAIN_IOUT) / 0.001 / 200) +
                                          QString(" A"));                                                        // 149
    ui->lineEdit_N12V_IOUT->setText(stringNumber(caculatorType1(data.N12V_IOUT) / 0.05 / 10.1) + QString(" A")); // 150
    ui->lineEdit_AP13V5_IOUT->setText(stringNumber(caculatorType1(data.AP13V5_IOUT) / 0.001 / 200) +
                                      QString(" A"));                                                          // 151
    ui->lineEdit_HVPA_IOUT->setText(stringNumber(caculatorType1(data.HVPA_IOUT) / 10.1) + QString(" A"));      // 152
    ui->lineEdit_HVNA_IOUT->setText(stringNumber(caculatorType1(data.HVNA_IOUT) / 3.1) + QString(" A"));       // 153
    ui->lineEdit_HVPB_IOUT->setText(stringNumber(caculatorType1(data.HVPB_IOUT) / 10.1) + QString(" A"));      // 154
    ui->lineEdit_HVNB_IOUT->setText(stringNumber(caculatorType1(data.HVNB_IOUT) / 3.1) + QString(" A"));       // 155
    ui->lineEdit_MCW1_FB->setText(stringNumber(caculatorType1(data.MCW1_FB) * 10) + QString(" V"));            // 156
    ui->lineEdit_AP1V6_FB->setText(stringNumber(caculatorType1(data.AP1V6_FB) * 57 / 47) + QString(" V"));     // 157
    ui->lineEdit_AP2V3_FB->setText(stringNumber(caculatorType1(data.AP2V3_FB) * 2) + QString(" V"));           // 158
    ui->lineEdit_AP3V6_FB->setText(stringNumber(caculatorType1(data.AP3V6_FB) * 2) + QString(" V"));           // 159
    ui->lineEdit_AP5V6_FB->setText(stringNumber(caculatorType1(data.AP5V6_FB) * 5.7) + QString(" V"));         // 160
    ui->lineEdit_AN5V6_FB->setText(stringNumber(caculatorType1(data.AN5V6_FB) * 4.7) + QString(" V"));         // 161
    ui->lineEdit_VDD5V_PRB_FB->setText(stringNumber(caculatorType1(data.VDD5V_PRB_FB) * 5.7) + QString(" V")); // 162
    ui->lineEdit_CWLD0_IN_FB->setText(stringNumber(caculatorType1(data.CWLD0_IN_FB) * 11) + QString(" V"));    // 163
    ui->lineEdit_12V_IN_FB->setText(stringNumber(caculatorType1(data._12V_IN_FB) * 11) + QString(" V"));       // 164
    ui->lineEdit_N12V_FB->setText(stringNumber(caculatorType1(data.N12V_FB) * 10) + QString(" V"));            // 165
    ui->lineEdit_AP13V5_FB->setText(stringNumber(caculatorType1(data.AP13V5_FB) * 11) + QString(" V"));        // 166
    ui->lineEdit_AN13V5_FB->setText(stringNumber(caculatorType1(data.AN13V5_FB) * 10) + QString(" V"));        // 167
    ui->lineEdit_HVPA_FB->setText(stringNumber(caculatorType1(data.HVPA_FB) * 191 / 3) + QString(" V"));       // 168
    ui->lineEdit_HVNA_FB->setText(stringNumber(caculatorType1(data.HVNA_FB) * 188 / 3) + QString(" V"));       // 169
    ui->lineEdit_HVPB_FB->setText(stringNumber(caculatorType1(data.HVPB_FB) * 191 / 3) + QString(" V"));       // 170
    ui->lineEdit_HVNB_FB->setText(stringNumber(caculatorType1(data.HVNB_FB) * 188 / 3) + QString(" V"));       // 171
    ui->lineEdit_VDD_3V3_FB->setText(stringNumber(caculatorType1(data.VDD_3V3_FB) * 5.7) + QString(" V"));     // 172
    ui->lineEdit_AP3V3_FB->setText(stringNumber(caculatorType1(data.AP3V3_FB) * 5.7) + QString(" V"));         // 173
    ui->lineEdit_VDD_5V_FB->setText(stringNumber(caculatorType1(data.VDD_5V_FB) * 5.7) + QString(" V"));       // 174
    ui->lineEdit_RESV3->setText(QString::number(data.RESV3));                                                  // 175
}

int VMonitor::caculatorFanSpeed(const unsigned short& speed)
{
    float ret = 0;
    if (0 != speed)
    {
        //计算方式参考自UsContextApiP9::onFanSpeedUpdated
        ret = 90000 * 60 / speed;
    }
    return ret;
}

double VMonitor::caculatorType1(const unsigned short& value)
{
    return value * 0.0098;
}

double VMonitor::caculatorType2(const qint8& value)
{
    // fpga已经除过8了，软件不需要去除（实际调试Atom机器，和fpga确认的）
    //    return value * 0.125;
    return value;
}

QString VMonitor::stringNumber(const double& value, int decimal)
{
    return QString::number(value, 'f', decimal);
}

//设置音量和系统提示音的播放
int VMonitor::setVolume(int volumeNum)
{
    m_pSystemUsage->setCurVolumeVal(volumeNum);
    qint64 oldTime = time;
    time = QDateTime::currentMSecsSinceEpoch();
    if ((time - oldTime) < 1000)
    {
        m_TimerSay->stop();
    }
    m_TimerSay->start(1000);
    return 0;
}

//播放系统提示音
void VMonitor::playAudio()
{
    if (m_TimerSay->isActive())
    {
        m_TimerSay->stop();
    }
    m_pSystemUsage->playVideo(SYSTEM_SOUNDS_PATH);
}

void VMonitor::fillMemory()
{
    unsigned int memory_total = 0;
    unsigned int memory_free = 0;
    m_pSystemUsage->getMemoryUsage(memory_total, memory_free);
    ui->lineEdit_memfree->setText(QString::number(memory_free));
    ui->lineEdit_memtotal->setText(QString::number(memory_total));
}

void VMonitor::fillHWInfo()
{
    QString ecVersion = GeneralInfo::instance().fpgaECVersion();
    QString uSB3_0 = GeneralInfo::instance().uSB3Version();
    ui->lineEdit_EC_version->setText(ecVersion);
    ui->lineEdit_USB_version->setText(uSB3_0);
}

void VMonitor::initSystemUsage()
{
#ifdef SYS_WINDOWS
    m_pSystemUsage = new SystemUsageWin();
#elif defined(SYS_APPLE) || defined(SYS_UNIX)
    m_pSystemUsage = new SystemUsageLinux();
#endif
}
