add_definitions(-DALGORITHM_LIBRARY)

include_depends(utilitymodel)

include_directories(${AUTODIAPHRAGM_INCLUDE_DIRS} ${SONOTHYROID_INCLUDE_DIRS} ${SONOTHYROIDTRACE_INCLUDE_DIRS}
    ${AUTOCYSTIC_INCLUDE_DIRS} ${RTIMT_INCLUDE_DIRS} ${OPENCV_INCLUDE_DIRS} ${SONONERVE_INCLUDE_DIRS}
    ${SONOCARDIAC_INCLUDE_DIRS} ${SONOMSK_INCLUDE_DIRS} ${SONOCAROTIDGUIDE_INCLUDE_DIRS} ${OB_INCLUDE_DIRS}
    ${AUTOEF_INCLUDE_DIRS} ${SONOAV_INCLUDE_DIRS})

set(srcs
    alginstance.cpp
    autobladderalg.cpp
    jsrgalg.cpp
    rtimtalg.cpp
    sonocalcalg.cpp
    sonodiaphalg.cpp
    sonomskalg.cpp
    sononervealg.cpp
    sonothyroidalg.cpp
    sonocarotidguidealg.cpp
    sonocardiacalg.cpp
    sonocarotidguidealg.cpp
    sonoobalg.cpp
    autoefalg.cpp
    sonoavalg.cpp
    virtualalg.cpp
    )

add_library_qt(algorithm ${srcs})

target_link_libraries(algorithm utilitymodel)

if(${USE_SONONERVE})
    add_custom_command(TARGET algorithm
        POST_BUILD
        COMMAND cmake -DsourceDirector=${SONONERVE_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )
endif(${USE_SONONERVE})

if(${USE_SONOCARDIAC})
    add_custom_command(TARGET algorithm
        POST_BUILD
        COMMAND cmake -DsourceDirector=${SONOCARDIAC_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        COMMAND cmake -DsourceDirector=${OPENVINO_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )
endif(${USE_SONOCARDIAC})

if(${USE_SONOMSK})
    add_custom_command(TARGET algorithm
        POST_BUILD
        COMMAND cmake -DsourceDirector=${OPENCV_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        COMMAND cmake -DsourceDirector=${SONOMSK_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )
endif(${USE_SONOMSK})

if(${USE_SONOCAROTIDGUIDE})
    add_custom_command(TARGET algorithm
        POST_BUILD
        COMMAND cmake -DsourceDirector=${SONOCAROTIDGUIDE_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )
endif()

if(DEFINED MNN261U_ROOT)
    add_custom_command(TARGET algorithm
        POST_BUILD
        COMMAND cmake -DsourceDirector=${MNN261U_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )
endif()

if(${USE_RTIMT})
    add_custom_command(TARGET algorithm
        POST_BUILD
        COMMAND cmake -DsourceDirector=${RTIMT_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )
endif(${USE_RTIMT})

if(${USE_AUTOCYSTIC})
    add_custom_command(TARGET algorithm
        POST_BUILD
        COMMAND cmake -DsourceDirector=${AUTOCYSTIC_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )
endif(${USE_AUTOCYSTIC})


if(${USE_AUTOCYSTIC})
    add_custom_command(TARGET algorithm
        POST_BUILD
        COMMAND cmake -DsourceDirector=${AUTOCYSTIC_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )
endif(${USE_AUTOCYSTIC})

if(${USE_AUTOBLADDER})
    add_custom_command(TARGET algorithm
        POST_BUILD
        COMMAND cmake -DsourceDirector=${AUTOBLADDER_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )
endif(${USE_AUTOBLADDER})

if(${USE_SONOOB})
    add_custom_command(TARGET algorithm
        POST_BUILD
        COMMAND cmake -DsourceDirector=${OB_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )
endif(${USE_SONOOB})

if(${USE_AUTODIAPH})
    add_custom_command(TARGET algorithm
        POST_BUILD
        COMMAND cmake -DsourceDirector=${AUTODIAPHRAGM_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )
endif(${USE_AUTODIAPH})

if(${USE_SONOTHYROID})
    add_custom_command(TARGET algorithm
        POST_BUILD
        COMMAND cmake -DsourceDirector=${SONOTHYROID_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )
endif(${USE_SONOTHYROID})

if(${USE_AUTOEF})
    add_custom_command(TARGET algorithm
        POST_BUILD
        COMMAND cmake -DsourceDirector=${AUTOEF_RUNTIMELIB_PATH}/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )
endif(${USE_AUTOEF})

if(${USE_SONOAV})
    add_custom_command(TARGET algorithm
        POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_directory ${SONOAV_RUNTIMELIB_PATH}/ ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
        )
endif()


