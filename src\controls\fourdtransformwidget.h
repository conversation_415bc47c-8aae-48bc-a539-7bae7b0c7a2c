#ifndef FOURDTRANSFORMWIDGET_H
#define FOURDTRANSFORMWIDGET_H

#include "ibasewidget.h"
#include "controls_global.h"
#include "dataarg.h"
#include "fourddatatypedef.h"

namespace Ui
{
class FourDTransformWidget;
}

class SonoParameters;

class CONTROLSSHARED_EXPORT FourDTransformWidget : public IBaseWidget
{
    Q_OBJECT
public:
    typedef enum
    {
        Rota<PERSON>,
        Roi
    } TransformMode;
    typedef enum
    {
        RotateXAxis,
        RotateYAxis,
        RotateZAxis,
        ROIXAxis,
        ROIYAxis,
        RO<PERSON>ZAxis,
        Menu,
        Count
    } LabelType;

    explicit FourDTransformWidget(QWidget* parent = 0);
    ~FourDTransformWidget();
    void init();
    void updateFourDGain(const QVariant& val);
    void setIsStatic3D(bool value);
public slots:
    void onTransformModeChanged(int mode);
    void onAxisChanged(const QVariant& val);
    void onTransformation(bool increase, int axis);
    void onFourdMouseActionChanged(const QVariant& value);
    void onMouseMovedAction(const QPoint& offset, const QPoint& pos);
    void onSplineSlopeChanged(bool increase);
    void onFreezeChanged(const QVariant& value);
    void onVirtualHDOnChanged(const QVariant& value);

protected:
    void retranslateUi();
private slots:
    void onFlashAxisLabelEnd();
    void onFlashMenuLabelEnd();
    void onTimeOut();

private:
    void highlightCurrentAxis(AxisTypeEnum::AxisType axis);
    void fourDImageRotate(bool increase);
    void fourDROIZoom(bool increase);

    void flashAxisLabel(int time = 300);
    void flashMenuLabel(int time = 300);

private:
    Ui::FourDTransformWidget* ui;
    FourDMouseStateEnum::FourDMouseActionMode m_MouseActionMode;
    FourDMouseStateEnum::FourDMouseActionMode m_MouseActionModeBeforeFreeze;
    TransformMode m_CurrentMode;
    AxisTypeEnum::AxisType m_CurrentAxis;
    QHash<LabelType, LabelWithIconData*> m_LabelHash;
    bool m_isFrozen;
    bool m_IsStatic3D;
    QTimer* m_timer;
};

#endif // FOURDTRANSFORMWIDGET_H
