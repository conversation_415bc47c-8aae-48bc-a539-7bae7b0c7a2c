#include "chisonfourdresourcefactory.h"
#include "chisonfourdlivewidget.h"
#include "chisonfourddatasender.h"
#include "chisonfourdapi.h"
#include "chisonfourdlicenseprocessor.h"

ChisonFourDResourceFactory::ChisonFourDResourceFactory()
{
}

ChisonFourDResourceFactory::~ChisonFourDResourceFactory()
{
}

IFourDLiveWidget* ChisonFourDResourceFactory::createFourDLiveWidget()
{
    return new ChisonFourDLiveWidget(new ChisonFourDDataSender);
}

IFourDAPI* ChisonFourDResourceFactory::createFourDAPI()
{
    return new ChisonFourDAPI;
}

IFourDLicenseProcessor* ChisonFourDResourceFactory::createFourDLicenseProcessor()
{
    return new ChisonFourDLicenseProcessor;
}
