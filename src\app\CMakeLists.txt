
include_depends(utilitymodel)
set(binname ${PROJECT_NAME}${APP_SUFFIX})

add_executable_qt(${binname} main.cpp)


add_custom_command(TARGET ${binname}
   POST_BUILD
   COMMAND cmake -DsourceDirector=${PROJECT_SOURCE_DIR}/src/app/shell/${BUILD_SYS_TAR_ARC_NAME}/ -DtargetDirector=${CMAKE_RUNTIME_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
   COMMAND cmake -DsourceDirector=${THIRDPARTYLIB_PATH}/otherapp/ -DtargetDirector=${CMAKE_RUNTIME_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
   )


target_link_libraries(${binname}
    utilitymodel resource ${QT_LIBRARIES})

add_dependencies(${binname} application_kb plot_plugin)
