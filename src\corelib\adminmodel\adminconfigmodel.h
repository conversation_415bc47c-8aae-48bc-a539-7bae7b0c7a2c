#ifndef ADMINCON<PERSON>GMODEL_H
#define ADMINCONFIGMODEL_H

#include <QList>
#include <QStringList>
#include <QObject>
#include "adminmodel_global.h"

class AdminConfigModelData;
class UserPresetDataHandler;
class UserInfo;
class FingerprintInfo;
class <PERSON>ingerPrintOperator;
class IMachine;

class ADMINMODELSHARED_EXPORT AdminConfigModel : public QObject
{
    Q_OBJECT
private:
    AdminConfigModel(QObject* parent = NULL);

public:
    static AdminConfigModel* instance();

    typedef enum
    {
        SUCCEED = 0,
        NO_USER = -1,
        CUR_PASSWORD_INCORRECT = -2,
        NEW_PASSWORD_SAMEAS_CUR_PASSWORD = -3,
    } ChangedPassWordResult;

    void showLoginDlg();
    void hideLoginDlg();

    void setMachine(IMachine* value);

    const QStringList getAuthorityNames() const;
    const QString getAuthorityName(int authorityId) const;
    int getUserAuthorityId(const QString& userName) const;

    const QList<UserInfo> getAllUserInfo() const;
    const QStringList getAllUserNames() const;
    const QString getUserName(const QString& userId) const;
    bool getUserInfo(const QString& username, UserInfo& user) const;
    const UserInfo getUserInfo(const QString& userId) const;
    bool isUserExist(const QString& username) const;
    bool addUser(const QString& username, const QString& pwd, const QString& quthorityName);
    bool removeUser(const QString& username);
    int login(const QString& username, const QString& pwd);
    int isCorrectPwd(const QString& username, const QString& pwd);
    int fpLogin(int fpid);
    int fpLogin(const QString& userId);
    bool logOut() const;
    bool isLogin() const;
    int changePwd(const QString& pwd, const QString& newPwd);
    void setIsChanged(bool);
    int resetPassword(const QString& username);
    const UserInfo getCurUserInfo() const;
    const QString getCurUserId() const;

    void emergency() const;
    bool isEmergency() const;

    const QList<FingerprintInfo> getFingerprintInfo(const QString& userId) const;
    const FingerprintInfo getFingerprintInfo(int fingerIndex) const;

    bool addFingerprint(const FingerprintInfo& info);
    bool removeFingerprint(int fingerIndex);

    bool changeFingerName(int fingerIndex, const QString& newname);
    IFingerPrintOperator* fPrintOperator() const;

    QString getServiceId();
    bool isValid(QString s);
    /**
     * @brief 函数为了清理老版本的Service类型用户，修改Service用户的类型为User
     */
    void updateUserAuthorityInfoToUser(UserInfo& userInfo);
    bool removeUserById(const QString& userId);
    void updateUsers();
    bool isSameUserId();
    bool isEmptyOfpreUserId();
    void setPreUserId();

signals:
    void showScreenLoginDlg();
    void clossLoginDlg();

private:
    void initAdmin();
    void initEmergency();
    void initService();
    void createFPrintOperator();

private slots:
    void onBeginToStandby(bool& accept);

private:
    UserPresetDataHandler* db;
    AdminConfigModelData* data;
    IFingerPrintOperator* m_FPrintOperator;
    IMachine* m_Machine;
    QString m_emgencyId;
    QString m_serviceId;
    QString m_preUserId;
    QString m_curUserId;
};

#endif // ADMINCONFIGMODEL_H
