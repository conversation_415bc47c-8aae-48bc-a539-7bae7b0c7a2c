thirdparty_prefix_path(SONOCARDIAC)

find_path ( SONOCARDIAC_INCLUDE_DIR
            NAMES
                SonoCardiac64.h
            HINTS
                ${SONOCARDIAC_ROOT}/include
            )
set(SONOCARDIAC_INCLUDE_DIRS ${SONOCARDIAC_INCLUDE_DIR})
message("Found SONOCARDIAC headers: ${SONOCARDIAC_INCLUDE_DIRS}")

find_library ( SONOCARDIAC_LIBRARY
            NAMES 
                SonoCardiac64
            HINTS
                ${SONOCARDIAC_ROOT}/lib
             )
set(SONOCARDIAC_LIBRARIES ${SONOCARDIAC_LIBRARY})
message("Found SONOCARDIAC libs: ${SONOCARDIAC_LIBRARIES}")

