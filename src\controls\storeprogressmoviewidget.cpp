#include "storeprogressmoviewidget.h"
#include "ui_storeprogressmoviewidget.h"
#include "modeluiconfig.h"
#include "util.h"
#include <QMovie>

StoreProgressMovieWidget::StoreProgressMovieWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::StoreProgressMovieWidget)
{
    ui->setupUi(this);

    setWindowFlags(Qt::ToolTip);

    // 针对纯触摸屏机器需要支持存图时的界面操作
    if (!ModelUiConfig::instance().value(ModelUiConfig::OnlyHaveTouchScreen).toBool())
    {
        setAttribute(Qt::WA_ShowModal);
    }

    setVisible(false);

    setup();
}

StoreProgressMovieWidget::~StoreProgressMovieWidget()
{
    if (m_gif != NULL)
    {
        ui->label->setMovie(NULL);
        delete m_gif;
        m_gif = NULL;
    }
    delete ui;
}

void StoreProgressMovieWidget::setPositionWidgets(QWidget* FreezeBarW, QWidget* SonoParasW)
{
    m_FreezeBarWidget = FreezeBarW;
    m_SonoParasWidget = SonoParasW;
    setPosAndSize();
}

void StoreProgressMovieWidget::showSavingMovie(bool inFourDWidget)
{
    emit isShow();
    setPosAndSize(inFourDWidget);
    m_gif->start();
    show();
    Util::processEvents(QEventLoop::ExcludeUserInputEvents);
}

void StoreProgressMovieWidget::hideProgessMovie()
{
    m_gif->stop();
    hide();
    emit isHide();
}

void StoreProgressMovieWidget::setFourDProgressPos(QWidget* fourdRightWidget)
{
    int y = fourdRightWidget->mapToGlobal(fourdRightWidget->rect().bottomLeft()).y() + 1;
    int x = fourdRightWidget->mapToGlobal(fourdRightWidget->rect().bottomLeft()).x() +
            (fourdRightWidget->width() - m_MovieSize.width()) / 2;
    m_FourDProgressPos = QPoint(x, y);
}

void StoreProgressMovieWidget::retranslateUi()
{
}

void StoreProgressMovieWidget::setPosAndSize(bool inFourDWidget)
{
    QPoint currentPos;
    if (!inFourDWidget)
    {
        if (m_FreezeBarWidget != NULL && m_SonoParasWidget != NULL)
        {
            int y = m_FreezeBarWidget->mapToGlobal(m_FreezeBarWidget->rect().bottomLeft()).y() - m_MovieSize.height();
            int x = m_SonoParasWidget->mapToGlobal(m_SonoParasWidget->rect().topLeft()).x();
            currentPos = QPoint(x, y);
        }
    }
    else
    {
        currentPos = m_FourDProgressPos;
    }
    move(currentPos);
}

void StoreProgressMovieWidget::setup()
{
    QString gifName = ModelUiConfig::instance().value(ModelUiConfig::SaveMovieIcon).toString();
    m_gif = new QMovie(gifName, QByteArray(), this);
    QPixmap pic(gifName);
    m_MovieSize = pic.size();
    resize(m_MovieSize);
    ui->label->setMovie(m_gif);
}
