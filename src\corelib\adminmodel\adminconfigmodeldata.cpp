#include "adminconfigmodeldata.h"
#include "authorityinfo.h"
#include "userinfo.h"
#include "fingerprintinfo.h"
#include <QDebug>

AdminConfigModelData::AdminConfigModelData()
{
}

const QStringList AdminConfigModelData::getAllAuthorityNames() const
{
    QStringList authritynames;
    foreach (const AuthorityInfo& auinfo, m_authorities)
    {
        if (auinfo.Id() > AuthorityInfo::Admin && auinfo.Id() != AuthorityInfo::Service &&
            auinfo.Id() != AuthorityInfo::Administrator)
        {
            authritynames.append(auinfo.name());
        }
    }

    return authritynames;
}

const QString AdminConfigModelData::getAuthorityName(int authorityid) const
{
    QString name;
    foreach (const AuthorityInfo& auinfo, m_authorities)
    {
        if (auinfo.Id() == authorityid)
        {
            name = auinfo.name();
            break;
        }
    }
    return name;
}

int AdminConfigModelData::getAuthorityId(const QString& authorityname) const
{
    int id = 0;
    foreach (const AuthorityInfo& auinfo, m_authorities)
    {
        if (auinfo.name() == authorityname)
        {
            id = auinfo.Id();
            break;
        }
    }
    return id;
}

int AdminConfigModelData::getUserAuthorityId(const QString& userName) const
{
    int id = 0;
    foreach (const UserInfo& user, m_baseUsers.values())
    {
        if (user.userName() == userName)
        {
            id = user.authorityId();
            break;
        }
    }
    return id;
}

const QStringList AdminConfigModelData::getAllUserNames() const
{
    QStringList usernames;
    foreach (const UserInfo& user, m_baseUsers.values())
    {
        if (user.userName().isEmpty())
        {
            continue;
        }
        usernames.append(user.userName());
    }
    return usernames;
}

bool AdminConfigModelData::getUserInfoByName(const QString& username, UserInfo& retUser) const
{
    bool ret = false;
    foreach (const UserInfo& user, m_baseUsers.values())
    {
        if (user.userName() == username)
        {
            ret = true;
            retUser = user;
            break;
        }
    }
    return ret;
}

bool AdminConfigModelData::getUserInfoById(const QString& userId, UserInfo& retUser) const
{
    bool ret = false;
    if (m_baseUsers.keys().contains(userId))
    {
        retUser = m_baseUsers.value(userId);
        ret = true;
    }
    return ret;
}

void AdminConfigModelData::updateUsers(const QList<UserInfo>& users)
{
    m_baseUsers.clear();
    foreach (const UserInfo& user, users)
    {
        qDebug() << "# " << user.userName() << user.userId();
        m_baseUsers[user.userId()] = user;
    }
    qDebug() << "# " << m_baseUsers.keys();
}

USERID AdminConfigModelData::getCurUserId() const
{
    return m_curUserId;
}

USERID AdminConfigModelData::getPreUserId() const
{
    return m_preUserId;
}

QList<FingerprintInfo> AdminConfigModelData::getFingerprintInfo(const QString& userid)
{
    QList<FingerprintInfo> ret;
    foreach (const FingerprintInfo& finger, m_fingerprints)
    {
        if (finger.userId() == userid)
        {
            ret.append(finger);
        }
    }

    return ret;
}

QList<int> AdminConfigModelData::getFingerIndex(const QString& userid)
{
    QList<int> ret;
    foreach (const FingerprintInfo& finger, m_fingerprints)
    {
        if (finger.userId() == userid)
        {
            ret.append(finger.fingerIndex());
        }
    }

    return ret;
}

bool AdminConfigModelData::getFingerByIndex(int fingerIndex, FingerprintInfo& retFinger)
{
    bool ret = false;
    foreach (const FingerprintInfo& finger, m_fingerprints)
    {
        if (finger.fingerIndex() == fingerIndex)
        {
            retFinger = finger;
            ret = true;
            break;
        }
    }
    return ret;
}

bool AdminConfigModelData::getFingerById(int fingerId, FingerprintInfo& retFinger)
{
    bool ret = false;
    if (fingerId == -1)
    {
        return false;
    }
    foreach (const FingerprintInfo& finger, m_fingerprints)
    {
        if (finger.fingerprintId() == fingerId)
        {
            retFinger = finger;
            ret = true;
            break;
        }
    }
    return ret;
}
