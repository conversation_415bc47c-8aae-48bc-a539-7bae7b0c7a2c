<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SystemHintEllipseWidget</class>
 <widget class="QWidget" name="SystemHintEllipseWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout" stretch="2,1">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="PartitionPaintLabel" name="labelUp">
     <property name="text">
      <string/>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="PartitionPaintLabel" name="labelDown">
     <property name="text">
      <string/>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>PartitionPaintLabel</class>
   <extends>QLabel</extends>
   <header>partitionpaintlabel.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
