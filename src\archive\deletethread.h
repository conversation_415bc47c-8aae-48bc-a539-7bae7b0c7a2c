#ifndef DELETETHREAD_H
#define DELETETHREAD_H
#include "archive_global.h"

#include <QThread>
#include "fileoperationthread.h"
#include <QFileInfoList>
class DataBaseViewModel;
class ARCHIVESHARED_EXPORT DeleteThread : public FileOperationThread
{
    Q_OBJECT
public:
    explicit DeleteThread(QObject* parent = 0);
    void setDeleteArgs(const QFileInfoList& fileInfoList);
    bool deleteItem(const QFileInfo& fileInfo);
    void setIfDeleteParentDir(bool deleteParentDir);
    void setDataBaseViewModel(DataBaseViewModel* model);
    /**
     * @brief deletePatienFromDataBase
     * 从数据库中删除病人信息
     * @param fileInfoList
     * @param model
     */
    void deletePatienFromDataBase(const QFileInfoList& fileInfoList, DataBaseViewModel* model);

protected:
    void run();
protected slots:
    void onFinished();
    void onUpdateQueryView(DataBaseViewModel* model);
    void prepareBaseNameToDeleteFile();
signals:
    void updateQueryView(DataBaseViewModel* model);

private:
    DataBaseViewModel* m_model;
    QFileInfoList m_FileInfoList;
    qint64 m_DelValue;
    qint64 m_CurValue;
    qint64 m_SourceSize;
    QStringList m_BaseNameList;
    bool m_DeleteParentDir;
};

#endif // DELETETHREAD_H
