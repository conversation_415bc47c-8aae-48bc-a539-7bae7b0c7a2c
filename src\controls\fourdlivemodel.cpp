#include "fourdlivemodel.h"
#include "fourddataprocess.h"
#include "imageeventargs.h"
#include "ifourddatasender.h"
#include "QSize"
#include "fourdapihandler.h"
#include "imagetile.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include "setting.h"
#include "ibuffermanager.h"
#include "ilinebuffermanager.h"
#include <QDebug>
#include "parameter.h"
#include "probeparameters.h"
#ifdef USE_IIMAGE
#include "iimageprocesshelper.h"
#endif

FourDLiveModel::FourDLiveModel(IFourDDataSender* dataSender, QObject* parent)
    : QObject(parent)
    , m_SonoParameters(NULL)
    , m_FourDDataProcess(new FourDDataProcess())
    , m_FourDDataSender(dataSender)
    , m_ROIWidth(0)
    , m_ROIHeight(0)
    , m_timer(new QTimer)
#ifdef USE_IIMAGE
    , m_IImageProcessHelper(new IImageProcessHelper(Layout_Max + 1))
#endif
{
    if (m_FourDDataSender != NULL)
    {
        connect(m_FourDDataProcess, SIGNAL(newFourDImage(const unsigned char*, quint8, int)), m_FourDDataSender,
                SLOT(sendOneImage(const unsigned char*, quint8, int)), Qt::DirectConnection);
        connect(m_FourDDataProcess, SIGNAL(oneVolumeDataSended()), m_FourDDataSender, SLOT(makeVolume()),
                Qt::DirectConnection);
    }
    m_timer->setSingleShot(true);
    connect(m_timer, SIGNAL(timeout()), this, SLOT(onTimeOut()));
#ifdef USE_IIMAGE
    m_FourDDataProcess->setIImageProcessHelper(m_IImageProcessHelper);
#endif
}

FourDLiveModel::~FourDLiveModel()
{
    delete m_FourDDataProcess;
    m_FourDDataProcess = NULL;
    delete m_FourDDataSender;
    m_FourDDataSender = NULL;
    delete m_timer;
    m_timer = NULL;
#ifdef USE_IIMAGE
    delete m_IImageProcessHelper;
    m_IImageProcessHelper = NULL;
#endif
}
void FourDLiveModel::setContext(ImageTile* context)
{
    if (context != NULL)
    {
        setSonoParameters(context->sonoParameters());
        m_FourDDataSender->setColorMapAssembly(context->colorMapAssembly());
    }
}
void FourDLiveModel::setSonoParameters(SonoParameters* sonoParameters)
{
    disconnectSignals();
    m_SonoParameters = sonoParameters;
    connectSignals();
}

void FourDLiveModel::connectSignals()
{
#ifdef USE_IIMAGE
    m_IImageProcessHelper->connectSonoParameters(m_SonoParameters);
#endif
}

void FourDLiveModel::disconnectSignals()
{
#ifdef USE_IIMAGE
    m_IImageProcessHelper->disconnectSonoParameters(m_SonoParameters);
#endif
}

void FourDLiveModel::prepareFourDParameters(bool resetParameters)
{
    qDebug() << PRETTY_FUNCTION << "StartLine" << m_SonoParameters->pIV(BFPNames::StartLineStr) << "StopLine"
             << m_SonoParameters->pIV(BFPNames::StopLineStr) << "FourDStartLine"
             << m_SonoParameters->pIV(BFPNames::FourDStartLineStr) << "FourDStopLine"
             << m_SonoParameters->pIV(BFPNames::FourDStopLineStr) << "HighDenisity"
             << m_SonoParameters->pIV(BFPNames::HighDensityStr) << "MB:" << m_SonoParameters->pIV(BFPNames::MBStr)
             << "LineSpaceValue" << m_SonoParameters->pIV(BFPNames::LineSpaceCodeStr);
    if (resetParameters)
    {
        double factor = ProbeParameters::lineInterval(m_SonoParameters->pIV(BFPNames::HighDensityStr));
        m_ROIWidth = (int)((m_SonoParameters->pIV(BFPNames::FourDStopLineStr) -
                            m_SonoParameters->pIV(BFPNames::FourDStartLineStr)) /
                           factor) +
                     1;
        quint32 imageHeight = m_SonoParameters->pIV(BFPNames::PointNumPerLineStr);
        qreal midDepth = m_SonoParameters->pDV(BFPNames::RoiMidDepthMMFourDStr);
        qreal halfDepth = m_SonoParameters->pDV(BFPNames::RoiHalfDepthMMFourDStr);
        quint32 offset = ((midDepth - halfDepth) / (midDepth + halfDepth)) * imageHeight + 1; // todo 1 pixel
        m_ROIHeight = imageHeight;

        m_SonoParameters->setPV(BFPNames::FourDStartHeightStr, offset);
        m_FourDDataSender->prepareFourDParameters(m_ROIWidth, m_ROIHeight, 8, m_SonoParameters);

        m_FourDDataProcess->setPointCountPerLine(m_SonoParameters->pIV(BFPNames::PointNumPerLineStr));
        m_FourDDataProcess->initRealData(QSize(m_ROIWidth, m_ROIHeight));
    }
    else
    {
        m_FourDDataProcess->runProcess(true);
        FourDAPIHandler::instance().start();
    }
}

void FourDLiveModel::clearFourDData()
{
    m_FourDDataProcess->clearQueue();
}

void FourDLiveModel::setBufferManager(ILineBufferManager* bufferManager)
{
    m_LineBufferManager = bufferManager;
}
void FourDLiveModel::sendVolumeData(const FourDVolumeLineImageArgs& arg)
{
    m_FourDDataProcess->addFourdVolumeArg(arg);
}

void FourDLiveModel::updateCurrentIndex()
{
    if (m_LineBufferManager != NULL)
    {
        m_LineBufferManager->updateCurrentIndex();
    }
}

void FourDLiveModel::zoom(bool value)
{
    FourDAPIHandler::instance().zoom(value);
}

void FourDLiveModel::moveCurvedLineCtrlPoint(bool isVHDon, const QString& direction)
{
    if (m_timer->isActive())
    {
        m_timer->stop();
    }
    if (!isVHDon)
    {
        FourDAPIHandler::instance().adjustRoiCtrlPoint(direction);
    }
    else
    {
        FourDAPIHandler::instance().switchLightBall(true);
        FourDAPIHandler::instance().adjustVHDLight(direction);
        m_timer->start(500);
    }
}
void FourDLiveModel::onTimeOut()
{
    FourDAPIHandler::instance().switchLightBall(false);
}
