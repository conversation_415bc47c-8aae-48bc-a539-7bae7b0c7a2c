#ifndef PATIENTTOPWIDEGET_H
#define PATIENTTOPWIDEGET_H
#include "archive_global.h"

#include "basewidget.h"
#include "worklistsearchwidget.h"
#include "model/study.h"
#include "screenorientationmodel.h"
class Patient;
class PatientsAge;
class IStateManager;
/**
 * @brief 新建病人界面的起始部分，包括姓名、性别、年龄等
 */
namespace Ui
{
class PatientTopWidget;
}

class ARCHIVESHARED_EXPORT PatientTopWidget : public BaseWidget
{
    Q_OBJECT
    Q_PROPERTY(bool isHor READ isHor)
public:
    explicit PatientTopWidget(QWidget* parent = 0);
    ~PatientTopWidget();
    void setPatient(Patient* patient);
    void setStateManager(IStateManager* value);
    void flawlessPatient(Patient* patient);
    void flawlessStudy(Study* study);
    /**
       @brief setStudy 兽用study设置
       @param study
     */
    void setStudy(const Study* study);
    QString patientId() const;
    void setPatientId(const QString& id);
    void createId();
    void clearPatientInfo();
    void clearStudyInfo();
    void setCurrentWidgetEnabled(bool enabled);
    bool isNameLengthValid() const;
    void showWorklist();
    QString mppsDcmFileName() const;
    void clearMPPSDcmFileName();
    bool isHor();

protected:
    void translateSex();
    void translateSpecies();
    void initSet();
    void retranslateUi();
    void orientationChanged(Qt::ScreenOrientation orientation);
    void calBsa(int index);

public slots:
    void on_pushButtonQR_clicked();

private slots:
    void on_pushButtonArchive_clicked();
    void on_lineEditID_textEdited(const QString& arg1);
    void setAge();
    void setBirthDate();
    void on_lineEditAgeY_editingFinished();
    void on_lineEditAgeM_editingFinished();
    void on_lineEditID_editingFinished();
    void on_lineEditDateTime_dateChanged(const QDate& date);

    void on_pushButtonWorkList_clicked();
    void onResponse(const DcmResponseInfo& tag);
    void deleteItem(const int& index);
    void deleteAllItem();

    void on_comboBoxSpecies_currentIndexChanged(int index);

    void on_lineEditWeight_editingFinished();

    void on_lineEditWeight_textEdited(const QString& arg1);

    void on_lineEditAgeY_textEdited(const QString& arg1);

    void on_lineEditAgeM_textEdited(const QString& arg1);

protected:
    void hideEvent(QHideEvent* e);
signals:
    void clickedArchive();
    void isNewPatientId(bool isNew);
    void physiciansNameSetted(const QString& value);
    void accessionSetted(const QString& value);
    void comboBoxCurrentIndexChanged(int);
    void animalSpeciesIndexChanged(int index);
    void updateStudyInfo(Patient* patient);

private:
    Ui::PatientTopWidget* ui;
    QString m_MPPSDcmFileName;
    ScreenOrientationModel* m_Model;
    WorkListSearchDialog* m_WorkListSearchDialog;
    IStateManager* m_StateManager;
};

#endif // PATIENTTOPWIDEGET_H
