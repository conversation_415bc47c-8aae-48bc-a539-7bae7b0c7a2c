#ifndef DEMOIMPORTERS_H
#define DEMOIMPORTERS_H
#include "controls_global.h"
#include "iimporter.h"
#include "progressbar.h"
#include <QList>

class DemoImporter;
class CONTROLSSHARED_EXPORT DemoImporters : public IImporter
{
    Q_OBJECT
public:
    DemoImporters(QObject* parent = 0);
    void import();
    void check();
public slots:
    void onProgressValueChanged();

private:
    QList<DemoImporter*> m_Importers;
    ProgressBarFrame m_ProgressBar;
    int m_TotalFilesCount;
    int m_Index;
};

#endif // DEMOIMPORTERS_H
