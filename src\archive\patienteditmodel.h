#ifndef PATIENTEDITMODEL_H
#define PATIENTEDITMODEL_H
#include "archive_global.h"

#include <QObject>
class PatientEditDialog;
class QWidget;
class PatientWorkflow;
class PatientWorkflowModel;
class PatientWorkflow;
class Patient;
class GeneralWorkflowFunction;
class MppsSendControl;
class IStateManager;
/**
 * @brief 管理新建病人界面的运行
 */
class ARCHIVESHARED_EXPORT PatientEditModel : public QObject
{
    Q_OBJECT
public:
    explicit PatientEditModel(IStateManager* stateManager, QWidget* parent = 0);
    ~PatientEditModel();
    PatientEditDialog* patientEditDialog() const;
    void setPatientWorkflow(PatientWorkflow* patientWorkflow);
    GeneralWorkflowFunction* generalWorkflowFunction() const;
    void setGeneralWorkflowFunction(GeneralWorkflowFunction* generalWorkflowFunction);
    /**
     * @brief backastageCreatePatient 在后台直接创建一个病人，该病人的ID
     *        自动生成，并且记录最大的ID
     *
     * @return
     */
    void backstageCreatePatient();
    void setPatientDialogButtonEnable();
    void exec();
    void exec(Patient* patient);
    bool isPatientExist();

protected:
    /**
     * @brief create 相应PatientEditDialog的OK键,根据实际情况
     *                新建病人或者新建检查
     *
     * @return
     */
    void create();
    /**
     * @brief savePatient 将信息更改或者新建的病人、检查保存
     *
     * @param patient
     *
     * @return
     */
    void savePatient(Patient* patient);
    void createStudy(Patient* patient);
protected slots:
    void onButtonClicked(int index);
    void clearStudyOid();
    void temporaryPatient();
    void onClose();
    void onCurrentExamEnded();
signals:
    void closed();
    void finished(int result);
    void onlyJump();
    void entryArchiveState();
    void clearPatientInfo();
    void clearStudyInfo();
    void createNewExam();
    /**
     * @brief entryContinueExamState 内部END了病人，需求需要回到实时状态
     * 否则超声界面会可能看到上个病人的图像
     */
    void entryContinueExamState();

public slots:
    /**
     * @brief onSetStudyOid 响应Archive 和 Review中的newExam 按钮
     *
     * @param id
     */
    void onSetStudyOid(const QString& id);
    void onCurrentDiskPathChanged(const QString& diskPath);
    void onIsNewPatientId(bool isNew);

private:
    void updateStudy();
    void createStudyFromWorkflow();
    /**
     * @brief createNewPatient 录入一个新的病人
     *
     * @return
     */
    void createNewPatient(const QString& patientId);
    void createStudyFromPatientID();
    /**
     * @brief checkEndedState 检查通过 ok cancel 退出 病人窗口时，是否end了之前的病人
     * 如果是，会触发外部信号，通知进入实时扫图状态
     */
    void checkEndedState();

    void sendMPPSCmd(bool ncreate = true);

private:
    bool m_createPatient;
    PatientWorkflow* m_PatientWorkflow;
    PatientEditDialog* m_PatientEditDialog;
    GeneralWorkflowFunction* m_GeneralWorkflowFunction;
    MppsSendControl* m_MppsSendControl;
    QString m_StudyOid;
    QString m_DiskPath;
    Patient* m_Patient;
    bool m_IsNewPatientId;
    bool m_bNewStudy;
    bool m_IsEnded;
};

#endif // PATIENTEDITMODEL_H
