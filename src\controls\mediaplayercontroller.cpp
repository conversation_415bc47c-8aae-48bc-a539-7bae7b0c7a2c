#include "mediaplayercontroller.h"
#include "processutil.h"
#include "resource.h"
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QGuiApplication>
#include <QProcess>
#include <QScreen>
#include <QTime>
#include <QTimer>

MediaPlayerController::MediaPlayerController(QObject* parent)
    : QObject(parent)
    , m_FFPlayProcess(new QProcess(this))
    , m_FileListPath(Resource::customScreenSaverDir + Resource::pathSeparator + "filenames.txt")
    , m_FilePaths()
    , m_ImageShowTime(2000)
    , m_IsMute(true)
    , m_PlayControlTimer(new QTimer)
    , m_CurrentPlayIndex(0)

{
    QList<QScreen*> screens = QGuiApplication::screens();
    if (!screens.isEmpty())
    {
        m_Width = screens.first()->size().width();
        m_Height = screens.first()->size().height();
    }

    m_PlayControlTimer->setSingleShot(true);
    connect(m_PlayControlTimer, &QTimer::timeout, this, &MediaPlayerController::onPlayControlTimeout);

    connect(m_FFPlayProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished), this,
            &MediaPlayerController::playSingleFileFinished);
}

MediaPlayerController::~MediaPlayerController()
{
    if (m_PlayControlTimer != nullptr)
    {
        delete m_PlayControlTimer;
        m_PlayControlTimer = nullptr;
    }

    disconnect(m_FFPlayProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished), this,
               &MediaPlayerController::playSingleFileFinished);
    delete m_FFPlayProcess;
    m_FFPlayProcess = nullptr;
}

void MediaPlayerController::play(const QString& filePath, const bool isLoop)
{
    if (filePath.isEmpty()) // 如果没有传入地址，则挨个播放用户前期通过setFilePaths设置的地址
    {
        if (m_FilePaths.isEmpty())
        {
            qDebug() << PRETTY_FUNCTION << "filePaths is empty";
            return;
        }

        if (m_FilePaths.count() == 1) // 文件列表只有一个文件，开启循环播放
        {
            play(m_FilePaths.first());
        }
        else // 文件列表有多个，则开启顺序播放
        {
            m_PlayControlTimer->start();
        }

        m_LoopShowMedia = false;
    }
    else // 如果用户传入了filePath，则播放此地址对应的多媒体，并根据isLoop确定是否开启循环播放
    {
        m_LoopShowMedia = isLoop;
        m_LoopMediaFileName = isLoop ? filePath : "";
        playSingleFile(filePath);
    }
}

void MediaPlayerController::stop()
{
    if (m_PlayControlTimer != nullptr)
    {
        m_PlayControlTimer->stop();
    }
    ProcessUtil::killProcess("ffplay");
}

void MediaPlayerController::onPlayControlTimeout()
{
    ProcessUtil::killProcess("ffplay");

    m_CurrentPlayIndex++;
    if (m_CurrentPlayIndex == m_FilePaths.count())
    {
        m_CurrentPlayIndex = 0;
    }

    emit ffplayIsEnded();

    QTimer::singleShot(100, this, [=]() {
        QString filePath = m_FilePaths.at(m_CurrentPlayIndex);
        playSingleFile(filePath);
    });
}

void MediaPlayerController::playSingleFileFinished(int code, QProcess::ExitStatus status)
{
    Q_UNUSED(code)

    if (status == QProcess::NormalExit)
    {
        ffplayExited();
    }
}

void MediaPlayerController::playSingleFile(const QString& filePath)
{
    if (m_LoopShowMedia)
    {
        QFile file(m_FileListPath);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text))
        {
            QFileInfo info(m_LoopMediaFileName);
            QTextStream out(&file);
            for (int i = 0; i < 500; i++)
            {
                out << QString("file '%1'\n").arg(info.fileName());
            }
            file.close();
        }
    }

    bool isImage = false;
    if (!isSupport(filePath, isImage))
    {
        return;
    }

    QString absolutePath = QDir(QDir::currentPath()).absoluteFilePath(filePath);

    QStringList m_ProcessControlParams;

    if (m_LoopShowMedia)
    {
        m_ProcessControlParams << "-exitonmousedown"
                               << "-noborder"
                               << "-f"
                               << "concat"
                               << "-i" << m_FileListPath << "-x" << QString::number(m_Width) << "-y"
                               << QString::number(m_Height) << "-top" << QString::number(0) << "-left"
                               << QString::number(0);
    }
    else
    {
        m_ProcessControlParams << "-exitonmousedown"
                               << "-noborder"
                               << "-x" << QString::number(m_Width) << "-y" << QString::number(m_Height) << "-top"
                               << QString::number(0) << "-left" << QString::number(0) << absolutePath;
    }

    if (m_IsMute)
    {
        m_ProcessControlParams.append("-an");
    }

    m_FFPlayProcess->start("ffplay", m_ProcessControlParams);
    if (!m_LoopShowMedia)
    {
        int videoTime = getMediaTime(filePath);

        qInfo() << PRETTY_FUNCTION << "filename: " << filePath << "videoTime: " << videoTime;

        m_PlayControlTimer->start(videoTime);

        QTimer::singleShot(150, this, [=]() { emit ffplayIsRuning(); });
    }
    else
    {
        emit ffplayIsRuning();
    }
}

void MediaPlayerController::createFileNames()
{
    QString path = Resource::customScreenSaverDir + Resource::pathSeparator + "filenames.txt";
    QFile file(path);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text))
    {
        QTextStream out(&file);
        for (int i = 0; i < 500; i++)
        {
            out << QString("file '%1'\n").arg(m_FilePaths.first());
        }
        file.close();
    }
}

int MediaPlayerController::getMediaTime(const QString& filePath)
{
    bool isImage = false;
    if (!isSupport(filePath, isImage))
    {
        return -1;
    }

    QString absolutePath = QDir(QDir::currentPath()).absoluteFilePath(filePath);
    int videoTime = m_ImageShowTime;
    if (!isImage)
    {
        QProcess durationProcess;
        durationProcess.setProcessChannelMode(QProcess::MergedChannels);
        durationProcess.start("ffprobe", QStringList() << "-i" << absolutePath);
        durationProcess.waitForFinished();
        durationProcess.waitForReadyRead();
        QString playInfo = durationProcess.readAll();

        char* find_p = strstr(playInfo.toUtf8().data(), " Duration: ");
        if (find_p)
        {
            find_p += 11;
            QString find_str = find_p;
            find_str = find_str.section(',', 0, 0);
            QTime time = QTime::fromString(find_str);
            videoTime = time.hour() * 60 * 60 * 1000 + time.minute() * 60 * 1000 + time.second() * 1000 +
                        time.msec(); //视频总时间
        }
        durationProcess.kill();
        durationProcess.waitForFinished(1000);
    }

    return videoTime;
}

bool MediaPlayerController::isSupport(const QString& filePath, bool& isImage)
{
    if (filePath.isEmpty())
    {
        return false;
    }

    QFile file(filePath);

    if (!file.exists())
    {
        return false;
    }

    QStringList temp = filePath.split(".");
    if (temp.count() < 2)
    {
        return false;
    }

    QString fileType = temp.at(1);

    bool isSupport = Resource::customScreenSaverFilters.contains("*." + fileType);

    if (isSupport)
    {
        if (fileType == "png" || fileType == "jpg" || fileType == "bmp")
        {
            isImage = true;
        }
    }

    return isSupport;
}

void MediaPlayerController::setImageShowTime(int ImageShowTime)
{
    m_ImageShowTime = ImageShowTime;
}

void MediaPlayerController::setMute(bool isMute)
{
    m_IsMute = isMute;
}

void MediaPlayerController::setFilePaths(const QStringList& FilePaths)
{
    m_FilePaths = FilePaths;
}
