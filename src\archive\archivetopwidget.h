#ifndef ARCHIVETOPWIDGET_H
#define ARCHIVETOPWIDGET_H
#include "archive_global.h"

#include "basewidget.h"
#include "screenorientationmodel.h"
#include <QValidator>

/**
 * @brief 维护archive界面关于检索和磁盘的部分
 */
namespace Ui
{
class ArchiveTopWidget;
}

class IDiskDevice;
class ARCHIVESHARED_EXPORT ArchiveTopWidget : public BaseWidget
{
    Q_OBJECT
public:
    explicit ArchiveTopWidget(QWidget* parent = 0);
    ~ArchiveTopWidget();
    void initArchiveTopWidget();
    /**
     * @brief addDiskItem 添加一个磁盘
     *
     * @param disk
     */
    void addDiskItem(const QString& disk);
    void updateDiskPathComboBox();
    /**
     * @brief setDiskIndex  设置磁盘index
     *
     * @param index
     */
    void setDiskIndex(int index);
    /**
     * @brief squeryCurrentIndex 设定当前检索的是id 或是name
     *
     * @return
     */
    int squeryCurrentIndex() const;
    /**
     * @brief timeperiodCurrentIndex 检索的时间段改变
     *
     * @return
     */
    int timeperiodCurrentIndex() const;
    QString keyString() const;
    // added by jinyuqi to return current disk path to make restore & local funciton
    QString currentDisk() const;
    bool isDiskLocal() const; //当前disk是否处于本地目录
    void clearKeyword();
    void setValidatorName(QValidator* validatorName);
    void setDiskDevice(IDiskDevice* diskDevice);

protected:
    void retranslateUi();
    void orientationChanged(Qt::ScreenOrientation orientation);
signals:
    void discChanged(const QString& diskPath);
    void MultiClicked(bool checked);
    void comboBoxItemCurrentIndexChanged(int index);
    void comboBoxTimeCurrentIndexChanged(int index);
    void lineEditKeystringChanged(const QString& text);

private slots:
    void on_comboBoxDisk_currentIndexChanged(int index);

private:
    void updateKeyValidator(QValidator* validator);

private:
    Ui::ArchiveTopWidget* ui;
    ScreenOrientationModel* m_Model;
    void setID2AnimalID();
};

#endif // ARCHIVETOPWIDGET_H
