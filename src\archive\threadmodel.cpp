#include "threadmodel.h"
#include "progressbarview.h"
#include "confirmdialog.h"
#include "imageprocessedcopy.h"
#include "imageunprocessedcopy.h"
#include "util.h"
#include "backupmodel.h"
#include "resource.h"
#include "messageboxframe.h"

ThreadModel::ThreadModel(QObject* parent)
    : QObject(parent)
    , m_ProgressBar(NULL)
    , m_ConfirmDialog(NULL)
    , m_RootPath("")
{
    ImageUnprocessedCopy* copy = new ImageUnprocessedCopy(this);
    m_CopyThread.setICopy(copy);
    connect(&m_CopyThread, SIGNAL(started()), this, SLOT(emitThreadStarted()));
    connect(&m_CopyThread, SIGNAL(finished()), this, SLOT(emitThreadFinished()));
    connect(&m_BurnThread, SIGNAL(started()), this, SLOT(emitThreadStarted()));   //刻录进度控制
    connect(&m_BurnThread, SIGNAL(finished()), this, SLOT(emitThreadFinished())); //刻录
    connect(&m_CopyThread, SIGNAL(showConfirmDlg(bool)), this, SLOT(showConfirmDlg(bool)));
    connect(&m_DeleteThread, SIGNAL(started()), this, SLOT(emitThreadStarted()));
    connect(&m_DeleteThread, SIGNAL(finished()), this, SIGNAL(deleteThreadFinished()));
    connect(&m_CopyThread, SIGNAL(rptFileCopyed(QString)), this, SLOT(onRptFileCopyed(QString)));
    // connect(ui->widgetDirView, SIGNAL(fileInfoList(QFileInfoList)), this, SLOT(deleteFileInfo(QFileInfoList)));
}

ThreadModel::~ThreadModel()
{
}

void ThreadModel::setProgressBar(ProgressBarView* bar)
{
    m_ProgressBar = bar;
    connect(&m_BurnThread, SIGNAL(finished()), m_ProgressBar, SLOT(hideProgressBar()), Qt::UniqueConnection); //刻录
    connect(&m_BurnThread, SIGNAL(startBurning(qint64)), m_ProgressBar, SLOT(updateProgressBar(qint64)),
            Qt::UniqueConnection); //
    connect(&m_CopyThread, SIGNAL(started()), m_ProgressBar, SLOT(execProgressBar()), Qt::UniqueConnection);
    connect(&m_CopyThread, SIGNAL(finished()), m_ProgressBar, SLOT(hideProgressBar()), Qt::UniqueConnection);
    connect(&m_CopyThread, SIGNAL(curValue(qint64)), m_ProgressBar, SLOT(updateProgressBar(qint64)),
            Qt::UniqueConnection);
    connect(&m_DeleteThread, SIGNAL(finished()), m_ProgressBar, SLOT(hideProgressBar()), Qt::UniqueConnection);
    connect(&m_DeleteThread, SIGNAL(curValue(qint64)), m_ProgressBar, SLOT(updateProgressBar(qint64)),
            Qt::UniqueConnection);
}

void ThreadModel::setConfirmDialog(ConfirmDialogFrame* fram)
{
    m_ConfirmDialog = fram;
    connect(m_ConfirmDialog, SIGNAL(clickedButton(int)), this, SLOT(resumeCopy(int)));
}

void ThreadModel::setICopy(ICopy* copy)
{
    m_CopyThread.setICopy(copy);
}

bool ThreadModel::copy(const QStringList& fileList, const QString& dir, bool checkDiskSpace)
{
    m_CopyThread.setCopyArgs(fileList);
    m_CopyThread.setCurPath(dir);
    m_CopyThread.setRootPath(m_RootPath);
    m_currentRptFileNames.clear();

    if (!checkDiskSpace)
    {
        m_CopyThread.start();
        return true;
    }

    if (m_CopyThread.isVolumeEnough())
    {
        m_CopyThread.start();
        return true;
    }
    else
    {
        MessageBoxFrame::warningNonModal(QObject::tr("There is no enough space!"));
        return false;
    }
}

void ThreadModel::deleteFile(const QFileInfoList& fileList)
{
    m_DeleteThread.setDeleteArgs(fileList);
    m_DeleteThread.start();
}

void ThreadModel::setDeleteDataBaseModel(DataBaseViewModel* model)
{
    m_DeleteThread.setDataBaseViewModel(model);
}

void ThreadModel::setValue(int value)
{
    m_CopyThread.setConfirmValue(value);
}

void ThreadModel::setIsMkPatientDir(bool enable)
{
    m_CopyThread.setIsMkPatientDir(enable);
}

void ThreadModel::setIsMkPatientWithNameDir(bool enable)
{
    m_CopyThread.setIsMkPatientWithNameDir(enable);
}

void ThreadModel::setNeedCreateEmptyCopy(bool value)
{
    m_CopyThread.setNeedCreateEmptyCopy(value);
}

void ThreadModel::setRootPath(const QString& rootPath)
{
    m_RootPath = rootPath;
}

void ThreadModel::setIfDeleteParentDir(bool deleteParentDir)
{
    m_DeleteThread.setIfDeleteParentDir(deleteParentDir);
}

void ThreadModel::setNeedSingleFrameImgData(bool value)
{
    m_CopyThread.setNeedSingleFrameImgData(value);
}

const QStringList& ThreadModel::currentRptFileNames() const
{
    return m_currentRptFileNames;
}

void ThreadModel::setNotCopyFileSuffixFilters(const QStringList& value)
{
    m_CopyThread.setNotCopyFileSuffixFilters(value);
}

bool ThreadModel::burn(const QStringList& fileList, bool checkDiskSpace)
{
    m_BurnThread.setBurnArgs(fileList);
    m_currentRptFileNames.clear();

    if (!checkDiskSpace)
    {
        m_BurnThread.start();
        return true;
    }
    else
    {
        return false;
    }
}

bool ThreadModel::terminateBurnThread()
{
    if (m_BurnThread.isRunning())
    {
        if (!m_BurnThread.isDiskWriterBusy())
        {
            m_BurnThread.terminate();
            MessageBoxFrame::information(tr("Cancel the burning successfully! The disc is not burned!"));
        }
        else
        {
            MessageBoxFrame::warning(tr("Can't cancel the burning task while the disc writer is busy!"));
        }
    }
    return true;
}

void ThreadModel::setBackupModel(BackupModel* backupModel)
{
    m_BurnThread.setBackupModel(backupModel);
}

void ThreadModel::setBackupPatients(const QList<Patient*>& patients)
{
    m_BurnThread.setBackupPatients(patients);
}

void ThreadModel::emitThreadStarted()
{
    emit threadStarted(&m_CopyThread);
}

void ThreadModel::emitThreadFinished()
{
    bool isSuccessful = !m_CopyThread.isCopyFailed();

    emit threadFinished(isSuccessful);
}

void ThreadModel::showConfirmDlg(bool show)
{
    m_CopyThread.suspend();
    if (m_ConfirmDialog != NULL)
    {
        m_ConfirmDialog->setText(m_CopyThread.copyingFile());
        m_ConfirmDialog->setButtonState(show);
        m_ConfirmDialog->excuteFrame();
    }
}

void ThreadModel::resumeCopy(int value)
{
    m_CopyThread.setConfirmValue(value);
    m_CopyThread.resume();
}

void ThreadModel::onRptFileCopyed(const QString& fileName)
{
    if (!m_currentRptFileNames.contains(fileName))
    {
        m_currentRptFileNames << fileName;
    }
}

void ThreadModel::stopThread()
{
    m_CopyThread.quit();
    m_CopyThread.wait();

    if (m_DeleteThread.isRunning())
    {
        m_DeleteThread.quit();
        m_DeleteThread.wait();
    }
}

bool ThreadModel::copyThreadIsRunning()
{
    return m_CopyThread.isRunning();
}
