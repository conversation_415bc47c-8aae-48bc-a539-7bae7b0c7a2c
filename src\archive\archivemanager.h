#ifndef ARCHIVEMANAGER_H
#define ARCHIVEMANAGER_H
#include "archive_global.h"
#include <QWidget>
#include "basewidget.h"
#include "baseinputabledialogframe.h"
#include "basebuttonsboxframecontent.h"
#include "threadmodel.h"
#include "progressbarview.h"
#include <QMultiHash>
#include "querycriteria.h"
#include "dicomtaskmanager.h"
#include "screenorientationmodel.h"
#include <QModelIndexList>

class ArchiveManager;
class DataBaseStudyViewModel;
class DataBasePatientViewModel;
class DataBaseViewModel;
class QModelIndex;
class ProgressBarView;
class PatientWorkflowModel;
class PatientWorkflow;
class Patient;
class DiskInfo;
class GeneralWorkflowFunction;
class QItemSelectionModel;
class QItemSelection;
class ImageSkimManager;
class IToolsFacade;
class IStateManager;
class IDiskDevice;
class IColorMapManager;

// Archive的主界面
/**
 * @brief rchive的主界面
 */
class ARCHIVESHARED_EXPORT ArchiveManagerDialog : public BaseInputAbleDialogFrame
{
    Q_OBJECT
public:
    enum BUTTON
    {
        button_null,
        button_new,
        button_continue,
        button_easyview,
        button_cancel
    };
    explicit ArchiveManagerDialog(IStateManager* stateManager, QWidget* patient = 0);
    void setPatientWorkflow(PatientWorkflow* patientWorkflow);
    void setGeneralWorkflowFunction(GeneralWorkflowFunction* generalWorkflowFunction);
    void setDicomTaskManager(DicomTaskManager* dicomTaskManager);
    void setToolsFacade(IToolsFacade* value);
    void setColorMapManager(IColorMapManager* value);
    void setDiskDevice(IDiskDevice* diskDevice);
public slots:
    void onTPButClicked(const QString& category, const QString& butName, const QString& value);
    void onOrientationChanged(bool isHor);

signals:
    void entryPatientState();
    void entryBrowseState();
    void sendDirPathList(const QStringList& list);
    void currentDiskPathChanged(const QString& disk);
    void sendStudyOid(const QString& id);
    void entryContinueExamState();
    void entryEditExamState();
    void onlyJump();
    void autoCallBack(int index);

protected:
    void showEvent(QShowEvent*);

private:
    ArchiveManager* m_Child;
};

class BackupModel;
namespace Ui
{
class ArchiveManager;
}

class ARCHIVESHARED_EXPORT ArchiveManager : public BaseWidget, public BaseButtonsBoxFrameContent
{
    Q_OBJECT
    Q_PROPERTY(bool isHor READ isHor)
    Q_PROPERTY(int imageSkimSpacing WRITE setImageSkimSpacing)
public:
    enum KRYITEM
    {
        PatientId,
        PatientName,
    };
    enum TIMEPERIOD
    {
        OneDay,
        OneWeek,
        OneMonth,
        ThreeMonths,
        SixMonths,
        OneYear,
        All,
    };
    explicit ArchiveManager(IStateManager* stateManager, QWidget* parent = 0);
    ~ArchiveManager();
    void initArchiveManager();
    void setPatientWorkflow(PatientWorkflow* patientWorkflow);
    void setGeneralWorkflowFunction(GeneralWorkflowFunction* generalWorkflowFunction);
    void setDicomTaskManager(DicomTaskManager* dicomTaskManager);
    void setToolsFacade(IToolsFacade* value);
    void setColorMapManager(IColorMapManager* value);
    void setDiskDevice(IDiskDevice* diskDevice);
    void deleteCurrentSelection();
    void setParentDialog(ArchiveManagerDialog* d)
    {
        m_Parent = d;
    }
    bool isHor();
    void setImageSkimSpacing(int spacing);
signals:
    void orientationChanged(bool isHor);

protected:
    void showEvent(QShowEvent*);
    void hideEvent(QHideEvent*);
    void retranslateUi();
    void orientationChanged(Qt::ScreenOrientation orientation);
    /**
     * @brief createFileSystemModel 初始化DataBaseViewModel
     * @return
     */
    void ctrateFileSystemModel();
    void setCurrentModel(DataBaseViewModel* value);
    void setTreeViewsetColumnWidth(/*DataBaseViewModel *model*/);
    /**
     * @brief getDiskList 获取所有的磁盘名
     *
     * @retun QStringList
     */
    QStringList getDiskList();
    /**
     * @brief prependRootDisc 确定将根目录放在磁盘列表的首位
     *
     * @return
     */
    QStringList prependRootDisc();
    /**
     * @brief savePatientToDisc no use
     */
    void savePatientToDisc();
    /**
     * @brief clearSelection 将所有选中的study清空
     */
    void clearSelection();
    /**
     * @brief mkDateBadeDir 指定文件夹中不存在数据库，将空的数据库copy进去
     *
     * @param dir
     *
     * @return
     */
    void mkDateBaseDir(const QString& dir);
    /**
     * @brief toStartQuery 第一次进入Archive界面
     */
    void toStartQuery();
    /**
     * @brief setIDOrNameCriteria 设置lineEditID，dateTime 作为检索数据库的关键词
     *
     * @param index
     * @param text
     */
    void setIDOrNameCriteria(int index, const QString& text);
    /**
     * @brief controlDeleteButtonsEnable 如果选择的study中正在进行检查
     *  则delete按钮不可用
     * @param oid
     */
    void controlDeleteButtonsEnable();

    /**
     * @brief controlExpandCollapseEnable 控制patientview下展开收起all的操作
     * @param doExpand
     */
    void controlExpandCollapseEnable(bool doExpand);

    /**
     * @brief fetchMore 在selectall,expandall,collapseall的时候确保对所有item进行操作
     */
    void fetchMore();

    /**
     * @brief changeToStudyPath 将当前所选病人或者study全部转成study路径
     * @param srcPaths
     * @return
     */
    QStringList changeToStudyPath(const QStringList& srcPaths) const;

    bool isContainsSendingItem() const;

protected slots:
    void onTPButClicked(const QString& category, const QString& butName, const QString& value);
    /**
     * @brief autoCallBackImage 双击列表时或双击底部的小图标时，退到主界面，并完成回调操作
     * @param index
     */
    void autoCallBackImage(const int index = 0);

signals:
    void closedCurrentWidget();
    void entryPatientState();
    void entryBrowseState();
    void sendDirPathList(const QStringList& list);
    void currentDiskPathChanged(const QString& disk);
    void sendStudyOid(const QString& id);
    void selectedModelChanged(const QStringList& modelList, bool isDir);
    void activeExamClicked(bool flag);
    void showEventSignal(); // 同步m_ActiveExam
    void entryContinueExamState();
    void entryEditExamState();
    void onlyJump();
    void autoCallBack(int index);

private slots:
    void onDeleteCurrentSelections();
    void on_treeView_clicked(const QModelIndex& index);
    void onButtonPatientClicked();
    void onButtonStudyClicked();
    void onButtonExpandClicked();
    void onButtonCollapseClicked();
    void onButtonSelectClicked();

    void showPicture();
    void onDiskChanged(const QString& diskPath);
    void backupToDisk(const QString& disk);
    void treeViewSelectionModel(bool multi);
    void sortChanged();
    void onButtonClicked(int index);
    void onInfoButtonClicked();
    void onReportButtonClicked();
    void onDeleteButtonClicked();
    void onBackupButtonClicked();
    void onSendButtonClicked();
    void onRestoreButtonClicked();
    void onComboBoxItemCurrentIndexChanged(int index);
    void onComboBoxTimeCurrentIndexChanged(int index);
    void onLineEditKeystringChanged(const QString& text);
    void onMultiClicked(bool multi);
    void setStudyOid(const QString& oid);
    void setImageSkimWidgetPath(const QString& value);
    void onSelectionChanged(const QItemSelection& selected, const QItemSelection& deselected);
    void onDeleteExam(const QStringList& studyPaths, bool isDeleteFile);

private:
    void initImageSkimManager();

private:
    Ui::ArchiveManager* ui;
    DataBaseViewModel* m_Model;
    DataBaseViewModel* m_StudyModel;
    DataBaseViewModel* m_PatientModel;

    QStringList m_TreeViewSelectedModel;
    QList<QStringList> m_CurrentSendingModels;
    BackupModel* m_BackupModel;
    QString m_CurrentDisk;
    ThreadModel m_ThreadModel;
    ProgressBarView* m_ProgressBar;
    PatientWorkflow* m_PatientWorkflow;
    QString m_StudyOid;
    QString m_ImageSkimWidgetPath;
    GeneralWorkflowFunction* m_GeneralWorkflowFunction;
    BaseInputAbleDialogFrame* m_BaseInputAbleDialogFrame;
    QueryCriteria m_QueryCriteria;
    QItemSelectionModel* m_ItemSelectionModel;
    DicomTaskManager* m_DicomTaskManager;
    ArchiveManagerDialog* m_Parent;
    //    QHash<QString, Patient*> m_Hash;
    bool m_buttonTextIsContinue;
    bool m_Multi;
    bool m_IsDir;
    ScreenOrientationModel* m_ScreenModel;
    IStateManager* m_StateManager;
    IColorMapManager* m_ColorMapManager;
    IDiskDevice* m_DiskDevice;
};

#endif // ARCHIVEMANAGER_H
