<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TreeViewWidget</class>
 <widget class="QWidget" name="TreeViewWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>544</width>
    <height>280</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="SeniorTreeView" name="treeView">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="focusPolicy">
      <enum>Qt::NoFocus</enum>
     </property>
     <property name="sortingEnabled">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item>
    <widget class="TreeViewButton" name="widgetButton" native="true">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>30</height>
      </size>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>TreeViewButton</class>
   <extends>QWidget</extends>
   <header location="global">treeviewbutton.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>SeniorTreeView</class>
   <extends>QTreeView</extends>
   <header>seniortreeview.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
