#include "mppssendcontrol.h"
#include "dicomregister.h"
#include <QThreadPool>
#include "globaldef.h"
#include "resource.h"
#include "util.h"
#include "applogger.h"
#include "dicomgenerateuniqueidentifier.h"
#include <QProcess>
#include "messageboxframe.h"

MppsSendControl::MppsSendControl(QObject* parent)
    : QObject(parent)
    , m_DcmAttributesFileName(QString())
    , m_SopInstanceUID(QString())
{
    connect(this, SIGNAL(mppsFailed()), this, SLOT(onMppsFailed()));
}

MppsSendControl::~MppsSendControl()
{
    Util::Rmdir(Resource::dicomRspTempDir, true, true);
}

void MppsSendControl::doMppsNCreateTask(const QString& mppDcmFileName)
{
    m_DcmAttributesFileName = mppDcmFileName;
    if (DicomRegister::instance().checkIfDicomMPPSUsable() && !m_DcmAttributesFileName.isEmpty())
    {
        MppsNCreateTask* task = new MppsNCreateTask(this);
        Util::runRunnable(task);
    }
}

void MppsSendControl::doMppsNSetTask()
{
    if (DicomRegister::instance().checkIfDicomMPPSUsable() && !m_DcmAttributesFileName.isEmpty())
    {
        MppsNSetTask* task = new MppsNSetTask(this);

        Util::runRunnable(task);
    }
}

void MppsSendControl::sendMppsCmd(bool ncreate)
{
    if (m_DcmAttributesFileName.isEmpty() || (!ncreate && m_SopInstanceUID.isEmpty()))
    {
        emit mppsFailed();
        return;
    }
    QStringList keys;
    keys << DicomChison::netKeyOfIPName() << DicomChison::netKeyOfPortName() << DicomChison::netKeyOfAetitleName()
         << DicomChison::netKeyOfTimeoutName();

    QMap<QString, QString> keyValues = DicomChison::getDefaultServerInfo(DicomChison::mpps, keys);
    if (!keyValues.isEmpty())
    {
        QString ip = keyValues.value(DicomChison::netKeyOfIPName());
        QString port = keyValues.value(DicomChison::netKeyOfPortName());
        QString aetitle = keyValues.value(DicomChison::netKeyOfAetitleName());
        int timeout = keyValues.value(DicomChison::netKeyOfTimeoutName()).toInt();
        QString localAETitle = DicomChison::getLocalAETitle();

        if (ip.isEmpty() || port.isEmpty() || aetitle.isEmpty())
        {
            return;
        }
        if (timeout < 10)
        {
            timeout = 10;
        }

        QStringList args;
        int res = 0;
        args << ip << port << "--call" << aetitle << "--aetitle" << localAETitle << "-to" << QString::number(timeout);

        if (ncreate)
        {
            args << "--ncreate";
        }
        else
        {
            args << "--nset";
        }

        if (AppLogger::dicomDebug())
        {
            args << "-d";
        }
        if (AppLogger::dicomVerbose())
        {
            args << "-v";
        }

        if (ncreate)
        {
            m_SopInstanceUID = DicomGenerateUniqueIdentifier::newSopInstanceID();
        }

        args << "-k" << QString("0008,0018=%1").arg(m_SopInstanceUID) << m_DcmAttributesFileName;

        if (AppLogger::dicomVerbose())
        {
            qDebug() << args;
        }
        QProcess process;
        process.start(Resource::dicomMppsScuCmd, args);
        res = process.waitForFinished(-1) ? 0 : -1;

        if (res == 0 && process.exitCode() == 0)
        {
            if (ncreate)
            {
                qDebug() << ("chison_mppsscu n-create succeed\n");
            }
            else
            {
                m_DcmAttributesFileName.clear();
                m_SopInstanceUID.clear();
                Util::Rmdir(Resource::dicomRspTempDir, true, true);
                qDebug() << ("chison_mppsscu n-set succeed\n");
            }
        }
        else
        {
            m_DcmAttributesFileName.clear();
            m_SopInstanceUID.clear();
            if (ncreate)
            {
                qDebug() << "chison_mppsscu n-create failed\n";
            }
            else
            {
                qDebug() << "chison_mppsscu n-set failed\n";
            }
            emit mppsFailed();
        }
    }
}

void MppsSendControl::onMppsFailed()
{
    MessageBoxFrame::tipInformation(tr("MPPS Update Failed."));
}
