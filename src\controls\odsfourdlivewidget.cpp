#include "odsfourdlivewidget.h"
#include "ui_odsfourdlivewidget.h"
#include "imagetile.h"
#include "screenshots.h"
#include "sonoparameters.h"
#include "parameter.h"
#include "bfpnames.h"
#include "resource.h"
#include "fourdapihandler.h"
#include "odsfourdparameter.h"
#include "fourdlivemodel.h"
#include "aviplaywidget.h"
#include <QFile>

ODSFourDLiveWidget* ODSFourDLiveWidget::m_ODSFourDLiveWidget = NULL;

ODSFourDLiveWidget::ODSFourDLiveWidget(IFourDDataSender* dataSender, QWidget* parent)
    : AbstractFourDLiveWidget(dataSender, parent)
    , ui(new Ui::ODSFourDLiveWidget)
    , m_IsInited(false)
{
    ui->setupUi(this);
    m_ODSFourDLiveWidget = this;
    ui->grayCurveThumbnail->setVisible(false);
}

ODSFourDLiveWidget::~ODSFourDLiveWidget()
{
    delete ui;
    delete m_fourDParameter;
    if (m_AviPlayDialog != NULL)
    {
        delete m_AviPlayDialog;
    }
}

void ODSFourDLiveWidget::setContext(ImageTile* context, ILineBufferManager* bufferManager)
{
    m_ImageTile = context;
    m_LineBufferManager = bufferManager;
    m_SonoParameters = m_ImageTile->sonoParameters();
    ui->fourDRenderWidget->setSonoParameters(m_SonoParameters);

    m_Model->setContext(context);
    //    ui->fourDTransformWidget->setIsRealTime(m_SonoParameters->isRealTime());

    m_fourDParameter = new ODSFourDParameter(m_SonoParameters);

    connect(m_SonoParameters, SIGNAL(presetChanged(PresetParameters)), this, SLOT(onPresetChanged(PresetParameters)));
    connect(m_SonoParameters->parameter(BFPNames::FreezeStr), SIGNAL(valueChanged(QVariant)), this,
            SLOT(onFreezeChanged(QVariant)));
}

void ODSFourDLiveWidget::init()
{
#ifdef TARGET_TK1
    createFourD((void*)callbackFrom4D);
#endif

    if (!m_IsInited)
    {
        m_IsInited = true;
        m_fourDParameter->connectSignals();
        connectSignals();
    }
}

void ODSFourDLiveWidget::reset()
{
    m_fourDParameter->resetParameters();
    //    ui->fourDTransformWidget->onVirtualHDOnChanged(m_SonoParameters->pV(BFPNames::FourDVirtualHDOnStr));
}

void ODSFourDLiveWidget::saveCurGroupParas()
{
    m_fourDParameter->saveCurGroupParas();
}

IFourDRenderWidget* ODSFourDLiveWidget::fourDRenderWidget() const
{
    return ui->fourDRenderWidget;
}

FourDTransformWidget* ODSFourDLiveWidget::transformWidget() const
{
    return ui->fourDTransformWidget;
}

GrayCurveWidget* ODSFourDLiveWidget::fourdGrayCurveWidget() const
{
    return ui->grayCurveThumbnail;
}

void ODSFourDLiveWidget::onAviPlay(const QString& aviFileName)
{
    if (m_AviPlayDialog == NULL)
    {
        m_AviPlayDialog = new AviPlayDialog(aviFileName, this);
        int x = (this->width() - m_AviPlayDialog->width()) / 2;
        m_AviPlayDialog->move(this->mapToGlobal(QPoint(x, 0)));
    }
    else
    {
        m_AviPlayDialog->setFileName(aviFileName);
    }
    m_AviPlayDialog->exec();
}

void ODSFourDLiveWidget::showEvent(QShowEvent* e)
{
    QWidget::showEvent(e);
    setupRects();
}

void ODSFourDLiveWidget::onBeforecurSonoParametersChanged()
{
    m_fourDParameter->onBeforecurSonoParametersChanged();
    disconnectSignals();
}

void ODSFourDLiveWidget::onCurSonoParametersChanged()
{
    m_SonoParameters = m_ImageTile->sonoParameters();

    m_fourDParameter->onCurSonoParametersChanged(m_SonoParameters);
    connectSignals();

    //    ui->fourDTransformWidget->setIsRealTime(m_SonoParameters->isRealTime());
    ui->fourDTransformWidget->updateFourDGain(m_SonoParameters->pV(BFPNames::FourDGainStr));
    //    ui->fourDTransformWidget->onVirtualHDOnChanged(m_SonoParameters->pV(BFPNames::FourDVirtualHDOnStr));
}

void ODSFourDLiveWidget::onPresetChanged(const PresetParameters& presets)
{
    m_fourDParameter->updatePresetParameters(presets);
    updateFourDGain(m_SonoParameters->pV(BFPNames::FourDGainStr));
}

void ODSFourDLiveWidget::onFreezeChanged(const QVariant& value)
{
    m_SonoParameters->parameter(BFPNames::FourDFrameRateStr)->setEnabled(!value.toBool());
    m_SonoParameters->parameter(BFPNames::FourDDynamicRangeStr)->setEnabled(!value.toBool());
    m_SonoParameters->parameter(BFPNames::FourDGainStr)->setEnabled(!value.toBool());
    if (m_SonoParameters->pBV(BFPNames::ShowFourDWidgetStr))
    {
        ui->fourDTransformWidget->onFreezeChanged(value);
    }
}

void ODSFourDLiveWidget::onFourDGainChanged(const QVariant& val)
{
    updateFourDGain(val);
}

void ODSFourDLiveWidget::setupRects()
{
    QPoint topLeft, rightBottom;
    topLeft = ui->fourDRenderWidget->mapToGlobal(QPoint(0, 0));

    rightBottom = ui->fourDRenderWidget->mapToGlobal(ui->fourDRenderWidget->rect().bottomRight());
    topLeft.rx() -= 4;
    rightBottom.rx() += 4;
    Screenshots::instance().setFourDImageRect(QRect(topLeft, rightBottom));
    QRect infoRect(QPoint(topLeft.x(), 0), rightBottom);
    Screenshots::instance().setFourDImageAndInfoRect(infoRect);
}

void ODSFourDLiveWidget::callbackFrom4D(int wParam, int lParam)
{
    Q_UNUSED(wParam)
    Q_UNUSED(lParam)
    m_ODSFourDLiveWidget->updateCurrentIndex();
}

void ODSFourDLiveWidget::createFourD(void* callbackFunc)
{
    int ret = 0;
    if (QFile::exists(Resource::fourDLicenseFileName))
    {
        ret = FourDAPIHandler::instance().setLicenseFile(Resource::fourDLicenseFileName.toLatin1().data());
        if (ret > 0)
        {
            ui->fourDRenderWidget->createFourD(callbackFunc);
            return;
        }
    }
    if (ret <= 0)
    {
        ret = FourDAPIHandler::instance().setLicenseFile(Resource::fourDProductLicenseFileName.toLatin1().data());
        if (ret > 0)
        {
            ui->fourDRenderWidget->createFourD(callbackFunc);
        }
    }
}

void ODSFourDLiveWidget::connectSignals()
{
    if (NULL != m_SonoParameters)
    {
        connect(m_SonoParameters->parameter(BFPNames::FourDGainStr), SIGNAL(valueChanging(QVariant)), this,
                SLOT(onFourDGainChanged(QVariant)));
        connect(m_SonoParameters->parameter(BFPNames::TransformationAxisStr), SIGNAL(valueChanging(QVariant)),
                ui->fourDTransformWidget, SLOT(onAxisChanged(QVariant)));
        connect(m_SonoParameters->parameter(BFPNames::FourDVirtualHDOnStr), SIGNAL(valueChanged(QVariant)),
                ui->fourDTransformWidget, SLOT(onVirtualHDOnChanged(QVariant)));
    }
}

void ODSFourDLiveWidget::disconnectSignals()
{
    if (NULL != m_SonoParameters)
    {
        disconnect(m_SonoParameters->parameter(BFPNames::FourDGainStr), SIGNAL(valueChanging(QVariant)), this,
                   SLOT(onFourDGainChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::TransformationAxisStr), SIGNAL(valueChanging(QVariant)),
                   ui->fourDTransformWidget, SLOT(onAxisChanged(QVariant)));
        disconnect(m_SonoParameters->parameter(BFPNames::FourDVirtualHDOnStr), SIGNAL(valueChanged(QVariant)),
                   ui->fourDTransformWidget, SLOT(onVirtualHDOnChanged(QVariant)));
    }
}

void ODSFourDLiveWidget::updateFourDGain(const QVariant& val)
{
    ui->fourDTransformWidget->updateFourDGain(val);
    m_fourDParameter->updateGainValue(val);
}

void ODSFourDLiveWidget::onCurvedLineCtrlPointMoved(const QString& direction)
{
    if (m_Model != NULL)
    {
        m_Model->moveCurvedLineCtrlPoint(m_SonoParameters->pBV(BFPNames::FourDVirtualHDOnStr), direction);
    }
}
