#-------------------------------------------------
#
# Project created by QtCreator 2012-06-15T15:06:50
#
#-------------------------------------------------

TARGET = archive
TEMPLATE = lib

DEFINES += ARCHIVE_LIBRARY
HEADERS += archive_global.h \
    networkstoragefilewidget.h \
    makedcmfilesoptionwidget.h \
    patientworkflowworker.h \
    filesexporter.h \
    burnthread.h \
    patientinfoinputwidget.h \
    inputunitgroupwidget.h \
    queryretrievewidget.h \
    examdatebetweenwidget.h \
    mppssendcontrol.h

QT += sql \

include(../variable.pri)
include($$SRC_TREE/dest.pri)


#unix:!symbian {
#    maemo5 {
#        target.path = /opt/usr/lib
#    } else {
#        target.path = /usr/lib
#    }
#    INSTALLS += target
#}

#OTHER_FILES += \
#    Makefile

HEADERS += \
    treeitem.h \
    threadmodel.h \
    showpicture.h \
    progressbarview.h \
    patienttopwidget.h \
    patientmiddlewidget.h \
    patienteditwidget.h \
    patienteditmodel.h \
    filiationtreeviewmodel.h \
    filiationtreeview.h \
    filetreeview.h \
    filesystemmodel.h \
    exportfilewidget.h \
    databaseviewmodel.h \
    databasestudyviewmodel.h \
    databasepatientviewmodel.h \
    databackupwidget.h \
    copythread.h \
    basefilesystemview.h \
    backupmodel.h \
    archivetopwidget.h \
    archivemanager.h \
    fileoperationthread.h \
    deletethread.h \
    confirmdialog.h \
    archiveimagelabel.h \
    glanceimages.h \
    glanceimagestopwidget.h \
    bodyinfowidget.h \
    obinfowidget.h \
    cardinfowidget.h \
    archivebuttonwidget.h \
    patienthaterinfowidget.h \
    patienthomogeneouswidget.h \
    patientsage.h \
    patientmiddlemodel.h \
    glanceimagesmodel.h \
    archivemodel.h \
    patientworkflowmodel.h \
    generalworkflowfunction.h \
    treeviewwidget.h \
    treeviewbutton.h \
    diskpathcombobox.h \
    dicomseverinfowidget.h \
    worklistsearchwidget.h \
    dicommodel.h \
    dicomserverwidget.h \
    basecopy.h \
    imageprocessedcopy.h \
    imageunprocessedcopy.h

SOURCES += \
    treeitem.cpp \
    threadmodel.cpp \
    showpicture.cpp \
    progressbarview.cpp \
    patienttopwidget.cpp \
    patientmiddlewidget.cpp \
    patienteditwidget.cpp \
    patienteditmodel.cpp \
    filiationtreeviewmodel.cpp \
    filiationtreeview.cpp \
    filetreeview.cpp \
    filesystemmodel.cpp \
    exportfilewidget.cpp \
    databaseviewmodel.cpp \
    databasestudyviewmodel.cpp \
    databasepatientviewmodel.cpp \
    databackupwidget.cpp \
    copythread.cpp \
    basefilesystemview.cpp \
    backupmodel.cpp \
    archivetopwidget.cpp \
    archivemanager.cpp \
    fileoperationthread.cpp \
    deletethread.cpp \
    confirmdialog.cpp \
    archiveimagelabel.cpp \
    glanceimages.cpp \
    glanceimagestopwidget.cpp \
    bodyinfowidget.cpp \
    obinfowidget.cpp \
    cardinfowidget.cpp \
    archivebuttonwidget.cpp \
    patienthaterinfowidget.cpp \
    patienthomogeneouswidget.cpp \
    patientsage.cpp \
    patientmiddlemodel.cpp \
    glanceimagesmodel.cpp \
    archivemodel.cpp \
    patientworkflowmodel.cpp \
    generalworkflowfunction.cpp \
    treeviewwidget.cpp \
    treeviewbutton.cpp \
    diskpathcombobox.cpp \
    dicomseverinfowidget.cpp \
    worklistsearchwidget.cpp \
    dicommodel.cpp \
    dicomserverwidget.cpp \
    basecopy.cpp \
    imageprocessedcopy.cpp \
    imageunprocessedcopy.cpp \
    networkstoragefilewidget.cpp \
    makedcmfilesoptionwidget.cpp \
    patientworkflowworker.cpp \
    filesexporter.cpp \
    burnthread.cpp \
    patientinfoinputwidget.cpp \
    inputunitgroupwidget.cpp \
    queryretrievewidget.cpp \
    examdatebetweenwidget.cpp \
    mppssendcontrol.cpp

FORMS += \
    showpicture.ui \
    patienttopwidget.ui \
    patientmiddlewidget.ui \
    patienteditwidget.ui \
    filetreeview.ui \
    databackupwidget.ui \
    basefilesystemview.ui \
    archivetopwidget.ui \
    archivemanager.ui \
    confirmdialog.ui \
    glanceimages.ui \
    glanceimagestopwidget.ui \
    bodyinfowidget.ui \
    obinfowidget.ui \
    cardinfowidget.ui \
    archivebuttonwidget.ui \
    patienthaterinfowidget.ui \
    patienthomogeneouswidget.ui \
    treeviewwidget.ui \
    treeviewbutton.ui \
    exportfilewidget.ui \
    dicomseverinfowidget.ui \
    worklistsearchwidget.ui \
    dicomserverwidget.ui \
    networkstoragefilewidget.ui \
    makedcmfilesoptionwidget.ui \
    patientinfoinputwidget.ui \
    inputunitgroupwidget.ui \
    queryretrievewidget.ui \
    examdatebetweenwidget.ui


INCLUDEPATH += $$PWD

LIBS += -L$$OUT_PWD/../corelib/dicommodel/ -ldicommodel

INCLUDEPATH += $$PWD/../corelib/dicommodel
DEPENDPATH += $$PWD/../corelib/dicommodel

LIBS += -L$$OUT_PWD/../ui/dicomview -ldicomview

INCLUDEPATH += $$PWD/../ui/dicomview
DEPENDPATH += $$PWD/../ui/dicomview

LIBS += -L$$OUT_PWD/../corelib/basecontrols/ -lbasecontrols

INCLUDEPATH += $$PWD/../corelib/basecontrols
DEPENDPATH += $$PWD/../corelib/basecontrols

#LIBS += -L$$OUT_PWD/../util/ -lutility

#INCLUDEPATH += $$PWD/../utility
#DEPENDPATH += $$PWD/../utility

#LIBS += -lcommonunit

#INCLUDEPATH += $$PWD/../corelib/commonunit
#DEPENDPATH += $$PWD/../corelib/commonunit

LIBS += -L$$OUT_PWD/../ui/reportview -lreportview

INCLUDEPATH += $$PWD/../ui/reportview
DEPENDPATH += $$PWD/../ui/reportview

LIBS += -lpatientworkflow

INCLUDEPATH += $$PWD/../corelib/patientworkflow
DEPENDPATH += $$PWD/../corelib/patientworkflow

LIBS += -L$$OUT_PWD/../corelib/database/ -ldatabase

INCLUDEPATH += $$PWD/../corelib/database
DEPENDPATH += $$PWD/../corelib/database
INCLUDEPATH += $$PWD/../corelib/database/model
DEPENDPATH += $$PWD/../corelib/database/model
INCLUDEPATH += $$PWD/../corelib/database/sql
DEPENDPATH += $$PWD/../corelib/database/sql

LIBS += -L$$OUT_PWD/../corelib/dicom/ -ldicom

INCLUDEPATH += $$PWD/../corelib/dicom
DEPENDPATH += $$PWD/../corelib/dicom

LIBS += -L$$OUT_PWD/../dicomtools/ -ldicomtools

INCLUDEPATH += $$PWD/../dicomtools
DEPENDPATH += $$PWD/../dicomtools

LIBS += -L$$OUT_PWD/../corelib/beamformer/ -lbeamformer

INCLUDEPATH += $$PWD/../corelib/beamformer
DEPENDPATH += $$PWD/../corelib/beamformer


LIBS += -L$$OUT_PWD/../corelib/reportmodel/ -lreportmodel

INCLUDEPATH += $$PWD/../corelib/reportmodel
DEPENDPATH += $$PWD/../corelib/reportmodel

INCLUDEPATH += $$PWD/../corelib/measurementtool

LIBS += -L$$OUT_PWD/../corelib/measurementmodel/ -lmeasurementmodel
INCLUDEPATH += $$PWD/../corelib/measurementmodel
DEPENDPATH += $$PWD/../corelib/measurementmodel

LIBS += -L$$OUT_PWD/../corelib/fourd/ -lfourd

INCLUDEPATH += $$PWD/../corelib/fourd
DEPENDPATH += $$PWD/../corelib/fourd

LIBS += -L$$OUT_PWD/../system/utilitymodel/ -lutilitymodel

INCLUDEPATH += $$PWD/../system/utilitymodel
INCLUDEPATH += $$PWD/../system/utilitymodel/common
INCLUDEPATH += $$PWD/../system/utilitymodel/common/setting
DEPENDPATH += $$PWD/../system/utilitymodel
INCLUDEPATH += $$PWD/../system/utilitymodel/tool
DEPENDPATH += $$PWD/../system/utilitymodel/tool
INCLUDEPATH += $$PWD/../system/utilitymodel/commonunit
DEPENDPATH += $$PWD/../system/utilitymodel/commonunit

INCLUDEPATH += $$PWD/../system/support/
DEPENDPATH += $$PWD/../system/support/

LIBS += -L$$OUT_PWD/../corelib/systeminfo/ -lsysteminfo

INCLUDEPATH += $$PWD/../corelib/systeminfo
DEPENDPATH += $$PWD/../corelib/systeminfo

unix:!macx: LIBS += -L$$OUT_PWD/../ui/thumbnailview/ -lthumbnailview

INCLUDEPATH += $$PWD/../ui/thumbnailview
DEPENDPATH += $$PWD/../ui/thumbnailview

LIBS += -L$$OUT_PWD/../corelib/adminmodel/ -ladminmodel

INCLUDEPATH += $$PWD/../corelib/adminmodel
DEPENDPATH += $$PWD/../corelib/adminmodel
