#ifndef GLANCEIMAGES_H
#define GLANCEIMAGES_H
#include "archive_global.h"

#include <QWidget>
#include "basewidget.h"
#include "baseinputabledialogframe.h"
#include "basebuttonsboxframecontent.h"
#include "threadmodel.h"
#include <QHash>
#include "iprintable.h"
#include "iprintableimages.h"
#include "dicomtaskmanager.h"
#include "screenorientationmodel.h"

/**
 * @brief essyview界面
 */
class GlanceImages;
class PatientWorkflow;
class GeneralWorkflowFunction;
class ProgressBarView;
class StressEchoBufferManager;
class IBeamFormer;
class IToolsFacade;
class IStateManager;
class IColorMapManager;

class ARCHIVESHARED_EXPORT GlanceImagesDialog : public BaseInputAbleDialogFrame,
                                                public IPrintable,
                                                public IPrintableImages

{
    Q_OBJECT
public:
    enum BUTTON
    {
        button_null,
        button_new,
        button_continue,
        button_archive,
        button_cancel
    };
    explicit GlanceImagesDialog(IStateManager* stateManager, QWidget* parent = 0);

    GlanceImages* glanceImages() const;
    /**
     * @brief load 指定图片存放的文件夹，将所有的图片在界面上显示
     * @param fileName
     * @return
     */
    void load(const QString& fileName);
    /**
     * @brief load 文件夹列表
     * @param list
     */
    void load(const QStringList& list);
    void setPatientWorkflow(PatientWorkflow* patientWorkflow);
    void setGeneralWorkflowFunction(GeneralWorkflowFunction* generalWorkflowFunction);
    /**
     * @brief setPatientInfoList 设置patientId list
     *
     * @param idInfo
     */
    void setPatientInfoList(const QHash<QString, QString>& idInfo);
    /**
     * @brief setDiskPath 磁盘路径
     *
     * @param path
     */
    void setDiskPath(const QString& path);
    void setDicomTaskManager(DicomTaskManager* manager);
    void print();
    QString diskPath() const;

    /**
     * @brief printableImages 供usb接口打印的GlanceImages图像
     * @return
     */
    virtual QStringList printableImages() const;
    void setToolsFacade(IToolsFacade* value);
    void setColorMapManager(IColorMapManager* colorMapManager);
public slots:
    void onTPButClicked(const QString& category, const QString& butName, const QString& value);
    void onOrientationChanged(bool isHor);
signals:
    void entryPatientState();
    void entryArchiveState();
    void studyOidChanged(const QString& oid);
    void currentDiskPathChanged(const QString& diskPath);
    void entryContinueExamState();
    void entryEditExamState();
    void fourdCallBack(const QString& filepath);
    void onlyJump();

protected:
    void resizeEvent(QResizeEvent*);

private:
    GlanceImages* m_Child;
};

namespace Ui
{
class GlanceImages;
}
class AviPlayDialog;
class ARCHIVESHARED_EXPORT GlanceImages : public BaseWidget, public BaseButtonsBoxFrameContent
{
    Q_OBJECT
    Q_PROPERTY(bool isHor READ isHor)
    Q_PROPERTY(int imageSkimSpacing WRITE setImageSkimSpacing)
public:
    typedef enum
    {
        All,
        CurrentPage,
        Count
    } SelectAllProperty;

public:
    explicit GlanceImages(QWidget* parent = 0);
    ~GlanceImages();

    void setBufferManager(StressEchoBufferManager* bufferManager);

    void setBeamFormer(IBeamFormer* beamformer);
    /**
     * @brief load 指定图片存放的文件夹，将所有的图片在界面上显示
     *
     * @param fileName
     */
    void load(const QString& fileName);
    /**
     * @brief load 文件夹列表
     *
     * @param list
     */
    void load(const QStringList& list);
    void setPatientWorkflow(PatientWorkflow* patientWorkflow);
    void setGeneralWorkflowFunction(GeneralWorkflowFunction* generalWorkflowFunction);
    QWidget* imageView() const;
    void setPatientInfoList(const QHash<QString, QString>& idInfo);
    /**
     * @brief setDiskPath 磁盘路径
     *
     * @param path
     */
    void setDiskPath(const QString& path);
    /**
     * @brief ascertainButtonState 设置按钮的状态
     *
     * @param studyOid
     */
    void ascertainButtonState(const QString& studyOid);

    void setDicomTaskManager(DicomTaskManager* manager);

    void print();

    /**
     * @brief printableImages 供usb接口打印的GlanceImages图像
     * @return
     */
    QStringList printableImages();

    void setSelectAllProperty(GlanceImages::SelectAllProperty property = All);
    QString diskPath() const;
    bool isHor();

    void setParentDialog(GlanceImagesDialog* d)
    {
        m_Parent = d;
    }
    void showEvent(QShowEvent* e);

    int getRowCol();

    void setToolsFacade(IToolsFacade* value);

    void setStateManager(IStateManager* value);

    void setColorMapManager(IColorMapManager* colorMapManager);

    void setImageSkimSpacing(int spacing);
signals:
    /**
     * @brief closedCurrentWidget 用来关闭GlanceImagesDialog
     *
     * @return
     */
    void closedCurrentWidget();
    void entryPatientState();
    void entryArchiveState();
    void studyOidChanged(const QString& oid);
    void currentDiskPathChanged(const QString& diskPath);
    void entryContinueExamState();
    void entryEditExamState();
    void fourdCallBack(const QString& filepath);
    void onlyJump();
    void orientationChanged(bool isHor);

protected:
    void retranslateUi();
    void orientationChanged(Qt::ScreenOrientation orientation);
    /**
     * @brief changeRC 改变界面显示图片的行、列数
     *
     * @param rc
     *
     * @return
     */
    void changeRC(const int rc);
    void setRadioButtonEnable(QWidget* radio1, QWidget* radio2, QWidget* radio3);
    QStringList selectedImagePathsList();
protected slots:
    void onTPButClicked(const QString& category, const QString& butName, const QString& value);
private slots:
    void onButtonClicked(int index);
    void on_pushButtonInformation_clicked();
    void on_pushButtonReport_clicked();
    void on_pushButtonSend_clicked();
    void on_pushButtonPrint_clicked();
    void on_pushButtonDelete_clicked();
    void on_radioButton4_toggled(bool checked);
    void on_radioButton2_toggled(bool checked);
    void on_radioButton1_toggled(bool checked);
    void onClosed();
    void onCurrentPatientInfoChanged(const QString& text);

    void on_pushButtonSelectAll_clicked();

    void on_pushButtonDeselectAll_clicked();

    void onFourDCallBack(const QString& filepath);

private:
    void initImageSkimManager();

private:
    Ui::GlanceImages* ui;
    PatientWorkflow* m_PatientWorkflow;
    GeneralWorkflowFunction* m_GeneralWorkflowFunction;
    QString m_StudyOid;
    ProgressBarView* m_ProgressBarView;
    ThreadModel m_ThreadModel;
    QStringList m_ImageSuffixes;
    QString m_DiskPath;
    QHash<QString, QString> m_PatientBaseInfo;
    BaseInputAbleDialogFrame* m_BaseInputAbleDialogFrame;
    DicomTaskManager* m_DicomTaskManager;
    bool m_ButtonTextIsContinue;
    SelectAllProperty m_selectProperty;
    AviPlayDialog* m_AviPlayDialog;
    GlanceImagesDialog* m_Parent;
    static const char* const m_AnimalString[];
    enum ANIMALSTRING
    {
        Animal_Information
    };
    ScreenOrientationModel* m_Model;
    IStateManager* m_StateManager;
    IColorMapManager* m_ColorMapManager;
};

#endif // GLANCEIMAGES_H
