<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QueryRetrieveWidget</class>
 <widget class="QWidget" name="QueryRetrieveWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1400</width>
    <height>960</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>DICOM Query/Retrieve</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>Detail Infomation</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <property name="spacing">
       <number>3</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <layout class="QGridLayout" name="gridLayout">
        <item row="1" column="4">
         <widget class="BaseLineEdit" name="lineEditSearchValue"/>
        </item>
        <item row="1" column="1">
         <widget class="QLineEdit" name="lineEditAccession"/>
        </item>
        <item row="0" column="2">
         <widget class="QLabel" name="labelPatientName">
          <property name="text">
           <string>Patient Name</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="2">
         <widget class="QLabel" name="labelSearchKey">
          <property name="text">
           <string>Search Key</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLineEdit" name="lineEditPatientID"/>
        </item>
        <item row="1" column="3">
         <widget class="BaseComboBox" name="comboBox">
          <item>
           <property name="text">
            <string>Study ID</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Exam Today</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Exam Between</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Examdate Before</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Exam Date</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Examdate After</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Exam Description</string>
           </property>
          </item>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="labelPatientID">
          <property name="text">
           <string>Patient ID</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="labelAccession">
          <property name="text">
           <string>Accession #</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="0" column="3" colspan="3">
         <widget class="QLineEdit" name="lineEditPatientName"/>
        </item>
        <item row="1" column="5">
         <widget class="BaseDateEdit" name="dateEdit">
          <property name="calendarPopup">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <property name="spacing">
         <number>0</number>
        </property>
        <item>
         <widget class="QLabel" name="labelRecords">
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>317</width>
            <height>24</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="queryButton">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>0</height>
           </size>
          </property>
          <property name="text">
           <string>Query</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="clearButton">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>0</height>
           </size>
          </property>
          <property name="text">
           <string>Clear</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QGroupBox" name="groupBoxQuery">
        <property name="title">
         <string/>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QTreeView" name="treeViewQuery">
           <property name="selectionMode">
            <enum>QAbstractItemView::ExtendedSelection</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_6">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_4">
          <property name="spacing">
           <number>0</number>
          </property>
          <item>
           <widget class="QPushButton" name="selectAllButton">
            <property name="minimumSize">
             <size>
              <width>120</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string>Select All</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="deselectButton">
            <property name="minimumSize">
             <size>
              <width>120</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string>Deselect All</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="retrieveButton">
            <property name="minimumSize">
             <size>
              <width>120</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string>Retrieve</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QProgressBar" name="retrieveProgressBar">
          <property name="maximumSize">
           <size>
            <width>200</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="value">
           <number>24</number>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="cancelRetrieveButton">
          <property name="text">
           <string>Cancel</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QGroupBox" name="groupBoxRetrieve">
        <property name="title">
         <string/>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QTreeView" name="treeViewRetrieve"/>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_5">
        <item>
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="cancelButton">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>0</height>
           </size>
          </property>
          <property name="text">
           <string>Cancel</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BaseComboBox</class>
   <extends>QComboBox</extends>
   <header>basecombobox.h</header>
  </customwidget>
  <customwidget>
   <class>BaseDateEdit</class>
   <extends>QDateEdit</extends>
   <header>basedateedit.h</header>
  </customwidget>
  <customwidget>
   <class>BaseLineEdit</class>
   <extends>QLineEdit</extends>
   <header>baselineedit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
