#ifndef MOTOROPERATIONWIDGET_H
#define MOTOROPERATIONWIDGET_H

#include "controls_global.h"
#include "basewidget.h"
#include "baseinputabledialogframe.h"
#include <QByteArray>

namespace Ui
{
class MotorOperationWidget;
}
class MotorControl;
class MotorOperationWidget;

class CONTROLSSHARED_EXPORT MotorOperationDialog : public BaseInputAbleDialogFrame
{
    Q_OBJECT

public:
    MotorOperationDialog(QWidget* parent = nullptr);
    void setMotorControl(MotorControl* motorCtrl);

signals:
    void sendParameter();

private:
    MotorOperationWidget* m_Child;
};

class CONTROLSSHARED_EXPORT MotorOperationWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit MotorOperationWidget(QWidget* parent = nullptr);
    ~MotorOperationWidget();

    void setMotorControl(MotorControl* motorCtrl);

protected:
    virtual void retranslateUi();

private slots:
    void on_pushButtonGetVer_clicked();

    void on_pushButtonSendParam_clicked();

    void on_pushButtonstartMotor_clicked();

    void on_pushButtonstopMotor_clicked();

    void on_pushButtonresetMotor_clicked();

    void on_pushButtonMotorPosition_clicked();

    void on_pushButtonMotorInfo_clicked();

    void onDataReached(const QByteArray& data);

signals:
    void sendParameter();

private:
    Ui::MotorOperationWidget* ui;
    MotorControl* m_MotorCtrl{nullptr};
};

#endif // MOTOROPERATIONWIDGET_H
