#
# The module defines the following variables:
# PINYINIME_FOUND - True if pinyinime found.
# PINYINIME_INCLUDE_DIRS - where to find *.h, etc.
# PINYINIME_LIBRARIES - List of libraries when using pinyinime.
#
thirdparty_prefix_path(pinyinime)

find_path ( PINYINIME_INCLUDE_DIR
            NAMES
                dictbuilder.h
                dictdef.h
                dictlist.h
                dicttrie.h
                lpicache.h
                matrixsearch.h
                mystdlib.h
                ngram.h
                pinyinime_global.h
                pinyinime.h
                searchutility.h
                spellingtable.h
                spellingtrie.h
                splparser.h
                sync.h
                userdict.h
                utf16char.h
                utf16reader.h
            HINTS
                ${PINYINIME_ROOT}/include
            )
set(PINYINIME_INCLUDE_DIRS ${PINYINIME_INCLUDE_DIR})
message("Found pinyinime headers: ${PINYINIME_INCLUDE_DIRS}")


find_library ( PINYINIME_LIBRARY
            NAMES
                pinyinime
            HINTS
                ${PINYINIME_LIBPATH}
             )
set(PINYINIME_LIBRARIES ${PINYINIME_LIBRARY})
message("Found pinyinime libs: ${PINYINIME_LIBRARIES}")
