#include "imageprocessedcopy.h"
#include <QImage>
#include "icolormapcalculatormodel.h"
#include "icolormapmanager.h"
#include <QFile>
#include <QDir>
#include "util.h"
#include <qmath.h>
#include "resource.h"
#include "avitransform.h"
#include "dicomtaskmanager.h"
#include "generalworkflowfunction.h"
#include "makedicomfile.h"
#include "makedicomsrfile.h"
#include "setting.h"
#include "deletefilescontroller.h"
#include "filenameorganize.h"
#include "measresult.h"
#include "dicomsrmodels.h"
#include <QSettings>
#include <QFileInfo>
#include <QTextCodec>
#include "gdprutility.h"
#include <QMetaEnum>

ImageProcessedCopy::ImageProcessedCopy(IColorMapManager* colorMapManager, QObject* parent)
    : BaseCopy(parent)
    , m_ColorMapManager(colorMapManager)
    , m_Model(m_ColorMapManager->createColorMapCalModel())
    , m_ImageType(BMP)
    , m_IsToAvi(false)
    , m_IsToMp4(false)
    , m_IsExportDcmSR(false)
    , m_DcmSRSuccessNum(-1)
    , m_TaskManager(NULL)
    , m_Patient(NULL)
    , m_DeleteFilesController(NULL)
    , m_Results(NULL)
    , m_IsGDPR(false)
    , m_CopyRetrieveExam(false)
{
    m_Model->loadAndSetParameters(Resource::colormapGeneralDevice);
}

ImageProcessedCopy::~ImageProcessedCopy()
{
    if (m_Model != NULL)
    {
        m_ColorMapManager->deleteColorMapCalModel(m_Model);
    }
}

bool ImageProcessedCopy::qCopyFile(const QString& source, const QString& target)
{
    if (m_IsGDPR)
    {
        QStringList list;
        list.append(source);
        if (!source.endsWith(".rpt") && !GDPRUtility::isSupportGDPR(list))
        {
            return true;
        }
    }

    QString resultTarget = target;
    QFileInfo fileInfo(source);
    if (m_DcmSRSuccessNum == -1)
    {
        m_DcmSRSuccessNum++;
    }
    if ((fileInfo.suffix() == Resource::measSuffix) && m_IsExportDcmSR)
    {
        addDicomTask(source, target, SR);
        return true;
    }
    else if (source.endsWith(Resource::cineExt) && m_IsToAvi)
    {
        addDicomTask(source, target, AVI);
        if (m_ImageType != DCM)
        {
            return true;
        }
    }
    else if (source.endsWith(Resource::cineExt) && m_IsToMp4)
    {
        addDicomTask(source, target, MP4);
        if (m_ImageType != DCM)
        {
            return true;
        }
    }

    if (m_ImageType == DCM && !m_CopyRetrieveExam)
    {
        bool success = true;
        if (source.endsWith(Resource::aviExt) && m_IsToAvi)
        {
            addCount();
            success = copyCommonFile(source, resultTarget);
            decCount(success, source);
        }

        if (QFile::exists(Util::getSameBaseNamesFileName(source, Setting::instance().defaults().screenExt())))
        {
            addDicomTask(source, target, Storage);
        }

        return success;
    }
    else if (source.endsWith(Resource::dcmExt))
    {
        if (m_ImageType == DCM && m_CopyRetrieveExam)
        {
            addCount();
            bool ret = copyCommonFile(source, resultTarget);
            if (ret)
            {
                calculateTheCopyProgress(QFile(source).size());
            }
            decCount(ret, source);
            return ret;
        }
        return true;
    }
    else if (source.endsWith(Resource::bmpExt) || source.endsWith(Resource::jpgExt))
    {
        if (m_ImageType == DCM && m_CopyRetrieveExam)
        {
            return true;
        }
        QString targetName = resultTarget;
        QImage image(source);
        GDPRUtility::processedImageGDPR(image, source, m_IsGDPR);

        if (target.endsWith(Resource::bmpExt) && m_ImageType == JPG)
        {
            targetName = Util::getSameBaseNamesFileName(targetName, Resource::jpgExt);
        }
        else if (target.endsWith(Resource::bmpExt) && m_ImageType == PNG)
        {
            targetName = Util::getSameBaseNamesFileName(targetName, Resource::pngExt);
        }

        if (m_DoCopy)
        {
            bool ret = true;
            if (m_Model->isNeedCalculate())
            {
                if (m_NeedCreateEmptyCopy)
                {
                    Util::CreateEmptyFile(targetName);
                }
                addCount();
                QImage processedImage = m_Model->calculate(image);
                if (m_ImageType == PNG)
                {
                    ret = processedImage.save(targetName, "PNG");
                }
                else
                {
                    ret = processedImage.save(targetName);
                }

                if (ret)
                {
                    calculateTheCopyProgress(QFile(source).size());
                }
                decCount(ret, source);
                return ret;
            }
            else
            {
                //不进行图像处理但是要转jpg的情况
                if (target.endsWith(Resource::bmpExt) && (m_ImageType == JPG || m_ImageType == PNG))
                {
                    if (m_NeedCreateEmptyCopy)
                    {
                        Util::CreateEmptyFile(targetName);
                    }
                    addCount();
                    if (m_ImageType == JPG)
                    {
                        ret = image.save(targetName);
                    }
                    else if (m_ImageType == PNG)
                    {
                        ret = image.save(targetName, "PNG");
                    }

                    if (ret)
                    {
                        calculateTheCopyProgress(QFile(source).size());
                    }
                    decCount(ret, source);
                    return ret;
                }
            }
        }
    }

    addCount();
    bool ret;
    QImage temporaryImage(source);
    if (!temporaryImage.isNull())
    {
        GDPRUtility::processedImageGDPR(temporaryImage, source, m_IsGDPR);
        QString temporarySource(fileInfo.path() + "/temporary.bmp");
        temporaryImage.save(temporarySource);
        ret = copyCommonFile(temporarySource, resultTarget);
        QFile::remove(temporarySource);
    }
    else
    {
        ret = copyCommonFile(source, resultTarget);
    }
    decCount(ret, source);
    return ret;
}

qint64 ImageProcessedCopy::fileSize(const QFileInfo& info) const
{
    if (info.suffix() == Resource::cineSuffix && m_IsToAvi)
    {
        return 1;
    }
    else
    {
        return info.size();
    }
}

QString ImageProcessedCopy::destFileName(const QString& srcName) const
{
    QString tmpName = srcName;
    if (tmpName.endsWith(Resource::cineExt) && m_IsToAvi)
    {
        tmpName.replace(Resource::cineExt, Resource::aviExt);
    }
    else if (tmpName.endsWith(Resource::cineExt) && m_IsToMp4)
    {
        tmpName.replace(Resource::cineExt, Resource::mp4Ext);
    }
    else if (tmpName.endsWith(Resource::bmpExt) && m_ImageType == JPG)
    {
        tmpName.replace(Resource::bmpExt, Resource::jpgExt);
    }
    else if (tmpName.endsWith(Resource::bmpExt) && m_ImageType == PNG)
    {
        tmpName.replace(Resource::bmpExt, Resource::pngExt);
    }
    return tmpName;
}

void ImageProcessedCopy::setImageType(ImageType type)
{
    m_ImageType = type;
}

void ImageProcessedCopy::setIsToAvi(bool isToAvi)
{
    m_IsToAvi = isToAvi;
}

void ImageProcessedCopy::setCinetransTo(const QString type)
{
    if (type.isEmpty())
    {
        return;
    }
    QMetaEnum meta = QMetaEnum::fromType<DicomTaskType>();
    if (type == meta.valueToKey(AVI))
    {
        m_IsToAvi = true;
    }
    else if (type == meta.valueToKey(MP4))
    {
        m_IsToMp4 = true;
    }
}

void ImageProcessedCopy::setIsExportDcmSR(bool isExportDcmSR)
{
    m_IsExportDcmSR = isExportDcmSR;
}

void ImageProcessedCopy::setTaskManager(DicomTaskManager* taskManager)
{
    m_TaskManager = taskManager;
}

void ImageProcessedCopy::setDeleteFilesController(DeleteFilesController* controller)
{
    m_DeleteFilesController = controller;
}

void ImageProcessedCopy::setPatient(Patient* patient)
{
    m_Patient = patient;
}

void ImageProcessedCopy::setIsGDPR(bool isGDPR)
{
    m_IsGDPR = isGDPR;
}

int ImageProcessedCopy::dcmSRSuccessNum() const
{
    return m_DcmSRSuccessNum;
}

void ImageProcessedCopy::setCopyRetrieveExam(bool value)
{
    m_CopyRetrieveExam = value;
}

void ImageProcessedCopy::addCount()
{
    if (m_DeleteFilesController != NULL)
    {
        m_DeleteFilesController->addCount();
    }
}

void ImageProcessedCopy::decCount(bool isSuccessed, const QString& srcFileNames)
{
    if (m_DeleteFilesController != NULL)
    {
        m_DeleteFilesController->decCommonFileCount(isSuccessed, QFileInfo(srcFileNames).absolutePath());
    }
}

Patient* ImageProcessedCopy::getPatient(const QString& studyOid)
{
    Patient* patient = NULL;
    if (m_Patient == NULL)
    {
        patient = GeneralWorkflowFunction::getPatient(studyOid);
    }
    else
    {
        patient = m_Patient;
    }
    return patient;
}

void ImageProcessedCopy::addDicomTask(const QString& source, const QString& target, DicomTaskType type)
{
    if (m_DoCopy)
    {
        QFileInfo info(source);
        QString studyOid = info.dir().dirName();
        Patient* usedPatient = getPatient(studyOid);
        bool succeed = true;
        if (usedPatient != NULL)
        {
            CheckInfoFilter c(*usedPatient);
            QFileInfo tarInfo(target);
            switch (type)
            {
            case MP4:
            case AVI:
            {
                AviTransform* transform =
                    new AviTransform(source, destFileName(target), FileFormatEnum::format_yes, &c);
                transform->setDeleteFilesController(m_DeleteFilesController);
                transform->setNeedCreateEmptyCopy(m_NeedCreateEmptyCopy);
                m_TaskManager->add(transform);
                addCount();
                break;
            }
            case SR:
            {
                PackagesMeasResults results;
                QString measResultPath = info.absoluteFilePath();
                if (m_Results == NULL && !measResultPath.isEmpty())
                {
                    if (results.load(measResultPath))
                    {
                        m_Results = &results;
                    }
                }
                if (m_Results != NULL)
                {
                    QFileInfo tarInfo(target);
                    DicomSRModels* sRModels =
                        new DicomSRModels(*usedPatient, *m_Results, info.absolutePath(), m_IsGDPR);
                    if (sRModels->hasValidMeasResult())
                    {
                        MakeDicomSRFile* task =
                            new MakeDicomSRFile(sRModels, FileFormatEnum::format_no, &c, m_ImageType == DCM,
                                                tarInfo.absolutePath() + Resource::pathSeparator);
                        task->setDeleteFilesController(m_DeleteFilesController);
                        task->setIsGDPR(m_IsGDPR);
                        task->setNeedCreateEmptyCopy(m_NeedCreateEmptyCopy);
                        m_TaskManager->add(task);
                        addCount();
                        m_DcmSRSuccessNum++;
                    }
                    else
                    {
                        delete sRModels;
                        succeed = false;
                    }
                    m_Results = NULL;
                }

                break;
            }
            case Storage:
            {
                if (source.endsWith(Resource::bmpSuffix))
                {
                    QStringList list(source);
                    QStringList resultList = FileNameOrganize::toImgScreenFileNames(list);
                    if (resultList.count() <= 0)
                    {
                        break;
                    }
                }
                FileFormatEnum::FileFormat format =
                    source.endsWith(Resource::cineExt) ? FileFormatEnum::format_yes : FileFormatEnum::format_no;
                MakeDicomFile* makeFile = new MakeDicomFile(QStringList() << source, format, &c,
                                                            tarInfo.absolutePath() + Resource::pathSeparator,
                                                            tarInfo.baseName() + Resource::dcmExt);
                makeFile->setDeleteFilesController(m_DeleteFilesController);
                makeFile->setIsGDPR(m_IsGDPR);
                makeFile->setNeedCreateEmptyCopy(m_NeedCreateEmptyCopy);
                m_TaskManager->add(makeFile);
                addCount();

                break;
            }
            default:
                break;
            }

            if (succeed)
            {
                emit infoShowed(true);
            }
            if (m_Patient == NULL)
            {
                // m_Patient为NULL,patient就是通过getPatient获取的,需要delete
                delete usedPatient;
                usedPatient = NULL;
            }
        }
    }
}
