#include "generalworkflowfunction.h"
#include <QString>
#include <QScopedPointer>
#include "model/patient.h"
#include "model/study.h"
#include "dataaccesslayerhelper.h"
#include <QDateTime>
#include "patientworkflow.h"
#include "patienteditwidget.h"
#include "patienteditmodel.h"
#include "patientworkflowmodel.h"
#include "progressbarview.h"
#include "messageboxframe.h"
#include <QDir>
#include "resource.h"
#include "modeldirectorygetter.h"
#include "exportfilewidget.h"
#include "dicomregister.h"
#include "appsetting.h"
#include "netinfo.h"
#include "idiskdevice.h"
#include "diskinfo.h"
#include "globaldef.h"
#include "dicomseverinfowidget.h"
#include "smbnetfscontroller.h"
#include "bluetoothinfo.h"
#include "logger.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, GeneralWorkflowFunction)

IDiskDevice* GeneralWorkflowFunction::m_DiskDevice = NULL;

GeneralWorkflowFunction::GeneralWorkflowFunction(QObject* parent)
    : QObject(parent)
    , m_PatientWorkflow(NULL)
    , m_PatientWorkflowModel(NULL)
{
}

GeneralWorkflowFunction::~GeneralWorkflowFunction()
{
}

void GeneralWorkflowFunction::setPatientWorkflow(PatientWorkflow* patientWorkflow)
{
    m_PatientWorkflow = patientWorkflow;
}

void GeneralWorkflowFunction::setPatientWorkflowModel(PatientWorkflowModel* patientWorkflowModel)
{
    m_PatientWorkflowModel = patientWorkflowModel;
}

bool GeneralWorkflowFunction::isOrNotContinueStudy(const QString& studyOid)
{
    if (AppSetting::isAnimal())
    {
        return true;
    }
    Study* study = DataAccessLayerHelper::getStudy(studyOid);
    if (study != NULL)
    {
        QDateTime dateTime = study->StoreTime();
        delete study;
        int sec = dateTime.secsTo(QDateTime::currentDateTime());
        int standard = 60 * 60 * 24 * 3;
        if (sec <= standard)
        {
            return true;
        }
    }
    return false;
}

void GeneralWorkflowFunction::continueStudy(const QString& oid)
{
    Patient* patient = getPatient(oid);
    m_PatientWorkflow->continueStudy(patient);
}

void GeneralWorkflowFunction::editStudy(const QString& oid)
{
    // to do 可能需要修改下
    continueStudy(oid);
}

void GeneralWorkflowFunction::showPatientInfo(const QString& studyOid)
{
    PatientEditModel* patientEditModel = m_PatientWorkflowModel->patientEditModel();
    Patient* patient = getPatient(studyOid);
    if (patient != NULL)
    {
        patientEditModel->exec(patient);
        delete patient;
    }
}

void GeneralWorkflowFunction::setExamEnd(const QString& path)
{
    changeDbFilepath();
    m_PatientWorkflow->setCurrentExamEnd();
    changeDbFilepath(path);
}

Patient* GeneralWorkflowFunction::getPatient(const QString& studyOid)
{
    Study* study = DataAccessLayerHelper::getStudy(studyOid);
    if (study != NULL)
    {
        Patient* patient = DataAccessLayerHelper::getPatient(study->getPatient()->PatientId(), false);
        delete study->getPatient();
        study->setParent(patient);
        study->setPatient(patient);
        patient->Studies().append(study);
        return patient;
    }
    else
    {
        return NULL;
    }
}

bool GeneralWorkflowFunction::ifContinueOperate(QWidget* widget)
{
    return MessageBoxFrame::question(widget, tr("Delete"), tr("Are you sure to delete the selected items?"),
                                     QMessageBox::Yes | QMessageBox::No) == QMessageBox::Yes;
}

void GeneralWorkflowFunction::deleteFileList(const QStringList& pathList, ThreadModel* model, ProgressBarView* barWiew,
                                             bool isDeleteDir)
{
    model->setProgressBar(barWiew);
    model->setIfDeleteParentDir(isDeleteDir);
    model->deleteFile(changeStringToFileInfo(pathList));
}

QFileInfoList GeneralWorkflowFunction::changeStringToFileInfo(const QStringList& pathList)
{
    QFileInfoList infoList;
    foreach (QString filePath, pathList)
    {
        infoList.append(QFileInfo(filePath));
    }
    return infoList;
}

void GeneralWorkflowFunction::changeDbFilepath(const QString& path)
{
    if (!path.isEmpty() && path != Resource::hardDiskDir)
    {
        DataAccessLayerHelper::setDbFileName(PatientPath::instance().dbFilePathName(path));
    }
    else
    {
        DataAccessLayerHelper::setDbFileName();
    }
}

void GeneralWorkflowFunction::exportFiles(QWidget* parent, IStateManager* stateManager,
                                          IColorMapManager* colorMapManager, const QStringList& fileList,
                                          const QString& studyOid)
{
    if (fileList.isEmpty())
    {
        return;
    }
    //    qDebug() << fileList;
    ExportFileWidgetDialog sendDialog(stateManager, colorMapManager, m_DiskDevice, parent);
    if (!sendDialog.isUDiskMounted())
    {
        MessageBoxFrame::warning(parent, QString(), tr("Please plug U disk!"));
    }
    else
    {
        QScopedPointer<Patient> patient(getPatient(studyOid));
        sendDialog.getFileWidget()->setDefaultDirName(PatientPath::instance().exportPatientDirName(patient.data()));
        sendDialog.getFileWidget()->setExportFilePaths(fileList);
        sendDialog.exec();
    }
}

QStringList GeneralWorkflowFunction::imageInstanceFiles(const QStringList& imageFileNames)
{
    const static QStringList imageSuffixes = QStringList()
                                             << Resource::imgExt << Resource::cineExt << Resource::singleFourdImgExt
                                             << Resource::multiFourdImgExt << Resource::aviExt;
    QStringList fullFileList;
    foreach (const QString& filePath, imageFileNames)
    {
        fullFileList.append(filePath);

        QFileInfo info(filePath);
        foreach (QString suffixe, imageSuffixes)
        {
            QFileInfo fi(info.dir(), info.baseName() + suffixe);
            if (fi.exists())
            {
                fullFileList.append(fi.filePath());
            }
        }
    }
    return fullFileList;
}

void GeneralWorkflowFunction::exportFiles(QWidget* parent, const QString& defaultDir, const QStringList& fileList,
                                          const QStringList& filePathes, DicomTaskManager* manager,
                                          IStateManager* value, IColorMapManager* colorMapManager,
                                          DeleteFilesController* deleteController, bool isCurrentMultiChoice,
                                          bool deleteAfterSendExam, bool sendSR)
{
    DicomSeverInfoDialog dlg(value, colorMapManager, m_DiskDevice, parent);

    QObject::connect(&dlg, SIGNAL(deleteCurrentSelections()), parent, SLOT(onDeleteCurrentSelections()));

    dlg.setDefaultDirName(defaultDir); //可能传入的值为空
    dlg.setExprotFileNames(fileList);
    dlg.setExportFilePaths(filePathes); //存放图片的文件夹名
    dlg.setDicomTaskManager(manager);
    dlg.setDeleteFilesController(deleteController); //非archive中操作指针为空
    dlg.setIsCurrentMultiChoice(isCurrentMultiChoice);
    dlg.setDeleteAfterSendExam(deleteAfterSendExam);
    dlg.setIsSendSR(sendSR);
    dlg.initWidegt();
    dlg.adjustSize();
    dlg.exec();
}

void GeneralWorkflowFunction::hasUsableDevice(QWidget* parent, bool* pflag, bool* sflag, bool* uflag, bool* nflag,
                                              bool* bflag, bool* srflag)
{
    *pflag = false;
    *sflag = false;
    *uflag = false;
    *nflag = false;
    *bflag = false;
    if (srflag != NULL)
    {
        *srflag = false;
    }

    if (AppSetting::isDicom())
    {
        //        if(SystemInfo::instance().netInfo()->linked())
        //        {
        //            if(DicomRegister::instance().checkIfDicomPrintUsable())
        //            {
        //                *pflag = true;
        //            }
        //            if(DicomRegister::instance().checkIfDicomStorageUsable())
        //            {
        //                *sflag = true;
        //            }
        if (DicomChison::checkIfDefaultServerExist(DicomChison::storage))
        {
            *sflag = true;
        }
        if (DicomChison::checkIfDefaultServerExist(DicomChison::print))
        {
            *pflag = true;
        }
        if (srflag != NULL && DicomChison::checkIfDefaultServerExist(DicomChison::sr))
        {
            *srflag = true;
        }
        //        }
        //        else
        //        {
        //            MessageBoxFrame::warning(parent, QString(), tr("Network not connected!\nPlease contact network"));
        //        }
    }
    if (m_DiskDevice->diskInfo()->uDiskPaths().count() > 0)
    {
        *uflag = true;
    }

    if (SmbNetFsController::instance().hasResourceEntry())
    {
        *nflag = true;
    }
    else
    {
        *nflag = false;
    }

    log()->info() << QString("hasBluetoothLocalDevice:%1").arg(BluetoothInfo::hasBluetoothLocalDevice());
    if (BluetoothInfo::hasBluetoothLocalDevice())
    {
        log()->info() << QString("BluetoothInfo-> enable:%1, hasUsableBluetoothDevice:%2")
                             .arg(BluetoothInfo::instance()->bluetoothEnable())
                             .arg(BluetoothInfo::instance()->hasUsableBluetoothDevice());

        if (BluetoothInfo::instance()->bluetoothEnable() && BluetoothInfo::instance()->hasUsableBluetoothDevice())
        {
            *bflag = true;
        }
    }
}

void GeneralWorkflowFunction::setDiskDevice(IDiskDevice* diskDevice)
{
    m_DiskDevice = diskDevice;
}
