build-windows-atom-job:
  stage: build
  tags:
    - Atom
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  script:
    - ./configure_windows_x86.bat -a x64 -m Atom
  cache:
    key: build-Atom-cache
    paths:
      - build/Atom
    policy: push

release-atom-job:
  stage: release
  tags:
    - Atom
  rules:
    - if: $model == "Atom"
  before_script:
    - bash.exe ./ci/shell/getknown_hosts_atom.sh
    - bash.exe ./ci/shell/pullrelatedrep.sh -m Atom -r $ResourceBranch -x windows -a $AppcommonBranch -t $CI_COMMIT_BRANCH -v $PreVersion
  script:
    - bash.exe ./ci/shell/runrelease.sh -m Atom -v $MainVersion -a $VersionAdd -x windows
    - ./ci/shell/exit_ConsoleWindowHost.bat


