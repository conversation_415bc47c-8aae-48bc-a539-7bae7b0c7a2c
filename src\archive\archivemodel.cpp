#include "archivemodel.h"
#include "archivemanager.h"
#include "util.h"
#include "patientworkflow.h"
#include "generalworkflowfunction.h"

/**
 * @brief ArchiveModel archive开始运行，将含有数据库的磁盘加到磁盘列表中
 *        默认显示当天检查的病人，而且以study模式显示；左侧backup、delete
 *        等按钮置灰，最下面的一行按钮只有cancel和easyview是可用的；最下
 *        面的按钮控制archive与easyview、newpatient的相互切换,进入easyview
 *        需要将选中的检查图片存放路径列表传给easyview，进入newpatient界面
 *        需要检查的studyoid
 * @param parent
 */
ArchiveModel::ArchiveModel(IStateManager* stateManager, QWidget* parent)
    : QObject(parent)
    , m_ArchiveManagerDialog(NULL)
    , m_GeneralWorkflowFunction(NULL)
{
    m_ArchiveManagerDialog = new ArchiveManagerDialog(stateManager, parent);
    connect(m_ArchiveManagerDialog, SIGNAL(closed()), this, SIGNAL(closed()));
    connect(m_ArchiveManagerDialog, SIGNAL(onlyJump()), this, SIGNAL(onlyJump()));
    connect(m_ArchiveManagerDialog, SIGNAL(autoCallBack(int)), this, SIGNAL(autoCallBack(int)));
    connect(m_ArchiveManagerDialog, SIGNAL(entryPatientState()), this, SIGNAL(entryPatientState()));
    connect(m_ArchiveManagerDialog, SIGNAL(entryBrowseState()), this, SIGNAL(entryBrowseState()));
    connect(m_ArchiveManagerDialog, SIGNAL(sendDirPathList(QStringList)), this, SIGNAL(sendDirPathList(QStringList)));
    connect(m_ArchiveManagerDialog, SIGNAL(sendStudyOid(QString)), this, SIGNAL(sendStudyOid(QString)));
    connect(m_ArchiveManagerDialog, SIGNAL(currentDiskPathChanged(QString)), this,
            SIGNAL(currentDiskPathChanged(QString)));
    connect(m_ArchiveManagerDialog, SIGNAL(entryContinueExamState()), this, SIGNAL(entryContinueExamState()));
    connect(m_ArchiveManagerDialog, SIGNAL(entryEditExamState()), this, SIGNAL(entryEditExamState()));
}

ArchiveModel::~ArchiveModel()
{
    //    Util::SafeDeletePtr(m_ArchiveManagerDialog);
}

void ArchiveModel::exec()
{
    m_ArchiveManagerDialog->show();
    m_ArchiveManagerDialog->activateWindow();
    m_ArchiveManagerDialog->setButtonEnabled(false, ArchiveManagerDialog::button_new);
    m_ArchiveManagerDialog->setButtonEnabled(false, ArchiveManagerDialog::button_continue);
}

ArchiveManagerDialog* ArchiveModel::archiveManagerDialog() const
{
    return m_ArchiveManagerDialog;
}

void ArchiveModel::setPatientWorkflow(PatientWorkflow* patientWorkflow)
{
    m_ArchiveManagerDialog->setPatientWorkflow(patientWorkflow);
}

void ArchiveModel::setGeneralWorkflowFunction(GeneralWorkflowFunction* generalWorkflowFunction)
{
    m_GeneralWorkflowFunction = generalWorkflowFunction;
    m_ArchiveManagerDialog->setGeneralWorkflowFunction(generalWorkflowFunction);
}

void ArchiveModel::setDicomTaskManager(DicomTaskManager* dicomTaskManager)
{
    m_ArchiveManagerDialog->setDicomTaskManager(dicomTaskManager);
}

void ArchiveModel::setToolsFacade(IToolsFacade* value)
{
    m_ArchiveManagerDialog->setToolsFacade(value);
}

void ArchiveModel::setColorMapManager(IColorMapManager* value)
{
    m_ArchiveManagerDialog->setColorMapManager(value);
}

void ArchiveModel::setDiskDevice(IDiskDevice* diskDevice)
{
    m_ArchiveManagerDialog->setDiskDevice(diskDevice);
}
