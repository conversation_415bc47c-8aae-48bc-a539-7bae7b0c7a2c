#ifndef TREEITEM_H
#define TREEITEM_H
#include "archive_global.h"

#include <QList>
#include <QVariant>
#include <QVector>

/**
 * @brief 以parent含有多个children的形式，使archive界面中具有patient模式
 */
class ARCHIVESHARED_EXPORT TreeItem
{
public:
    TreeItem(const QList<QVariant>& data, TreeItem* parent = 0);
    ~TreeItem();
    void appendChild(TreeItem* child);
    TreeItem* child(int row);
    int childCount() const;
    int columnCount() const;
    QVariant data(int column) const;
    int row() const;
    TreeItem* parent();

private:
    QList<TreeItem*> childItems;
    QList<QVariant> itemData;
    TreeItem* parentItem;
};

#endif // TREEITEM_H
