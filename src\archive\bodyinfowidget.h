#ifndef BODYINFOWIDGET_H
#define BODYINFOWIDGET_H
#include "archive_global.h"
#include "basewidget.h"
#include "patientinfoinputwidget.h"
#include "sonoimtinfowidget.h"

class Study;
class QSettings;
namespace Ui
{
class BodyInfoWidget;
}

/**
 * @brief 新建病人的界面，负责病人基本信息的部分（身高、体重等）
 * */
class ARCHIVESHARED_EXPORT BodyInfoWidget : public BaseWidget
{
    Q_OBJECT
public:
    explicit BodyInfoWidget(QWidget* parent = 0);
    ~BodyInfoWidget();
    /**
     * @brief setWidgetVisible 控制一些控件的显隐性
     * @param visible
     */
    void setWidgetVisible(bool visible);
    /**
     * @brief setStudy 设置需要添加基本信息的study
     *
     * @param study
     */
    void setStudy(Study* study);
    /**
     * @brief flawlessStudy 根据输入框中的信息，给study赋值
     *
     * @param study
     */
    void flawlessStudy(Study* study);
    /**
     * @brief clearStudyInfo 清空检查信息
     */
    void clearStudyInfo();
    void setCurrentWidgetEnabled(bool enabled);
    void initSet();
    void setAnimalSpeciesIndex(int index);
    void setIMTVisible(bool visible);
    void setIMTWidgetVisible(bool visible);

public slots:
    void setInfo(const QMap<QString, QStringList>& info);
    void onPatientInfoUnitChanged(const QString& key, const int unitIndex);

signals:
    void baseInfo(const QMap<QString, QStringList>& info);

protected:
    const QMap<QString, QStringList> infoList();
    void calBsa();
    void retranslateUi();

private slots:
    //    void on_lineEditHeight_editingFinished();
    //    void on_lineEditWeight_editingFinished();
    void on_lineEditBsa_editingFinished();
    void on_lineEditHr_editingFinished();
    //    void on_lineEditHeight_textEdited(const QString &arg1);
    //    void on_lineEditWeight_textEdited(const QString &arg1);
    void on_lineEditBsa_textEdited(const QString& arg1);
    void on_lineEditHr_textEdited(const QString& arg1);
    void patientInfo_textEdited();
    void patientInfo_stateChanged(int state);

private:
    Ui::BodyInfoWidget* ui;
    PatientInfoInputModel m_PatientHeightModel;
    PatientInfoInputModel m_PatientWeightModel;
    int m_AnimalSpeciesIndex;
};

#endif // BODYINFOWIDGET_H
