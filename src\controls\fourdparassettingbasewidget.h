#ifndef FOURDPARASSETTINGBASEWIDGET_H
#define FOURDPARASSETTINGBASEWIDGET_H

#include "controls_global.h"
#include "ibasewidget.h"
#include "fourdparasinfo.h"

namespace Ui
{
class FourDParasSettingBaseWidget;
}

class FourDParasSettingBaseWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit FourDParasSettingBaseWidget(QWidget* parent = 0);
    ~FourDParasSettingBaseWidget();

protected:
    void addWidget(QWidget* widget);
    virtual void initalize();
    virtual void currenIndexChanged(const QString& arg1);
    virtual bool contains(const QString& name);
    virtual void save(const QString& name) = 0;
    virtual void add(const QString& name) = 0;
    virtual void del(const QString& name) = 0;
    virtual bool rename(const QString& newName, const QString& oldName) = 0;
    virtual QStringList names() = 0;
    template <class T> bool isExistNameT(const QString& name);
    template <class T> void addOneParaT(const QString& name, const T& para);
    template <class T> void saveOneParaT();
    template <class T> void deleteOneParaT(const QString& name);
    template <class T> void renameT(const QString& oldName, const QString& newName);
    template <class T> QStringList namesT();
    template <class T> T oneParasT(const QString& name) const;

    virtual void retranslateUi();
private slots:
    void on_comboBox_currentIndexChanged(const QString& arg1);

    void on_addButton_clicked();

    void on_renameButton_clicked();

    void on_deleteButton_clicked();

    void on_saveButton_clicked();

protected:
    Ui::FourDParasSettingBaseWidget* ui;
    FourDParasInfo::ParaType m_ParaType;
    QString m_SettingName;

private:
    QString m_Warning;
};

#endif // FOURDPARASSETTINGBASEWIDGET_H
