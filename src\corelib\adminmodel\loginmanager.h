#ifndef LOGINMANAGER_H
#define LOGINMANAGER_H

#include "adminmodel_global.h"
#include "ifingerprintoperator.h"
#include <QObject>
#include <QSharedPointer>

class AdminConfigModel;
class ADMINMODELSHARED_EXPORT LoginManager : public QObject
{
    Q_OBJECT
public:
    explicit LoginManager(QObject* parent = nullptr);
    ~LoginManager();

    void initialize();
    void stop();
    int login(const QString& username, const QString& pwd);
    void emergency() const;
    const QStringList getAllUserNames() const;
    int getUserAuthorityId(const QString& userName) const;
    int isCorrectPwd(const QString& username, const QString& pwd);
    bool isLogin() const;
    const QStringList getAllUserNamesAndClearData() const;
    void setPreUserId();
    void logout();

signals:
    void finish(int result);
    void send();

private:
    AdminConfigModel* m_model;
};

#endif // LOGINMANAGER_H
