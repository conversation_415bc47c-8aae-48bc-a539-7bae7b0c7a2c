#
# The module defines the following variables:
# IIMAGE_FOUND - True if IIMAGE found.
# IIMAGE_INCLUDE_DIRS - where to find nzmrd.hpp, etc.
# IIMAGE_LIBRARIES - List of libraries when using IIMAGE.
#


thirdparty_prefix_path(iimage)

find_path ( IIMAGE_INCLUDE_DIR
            NAMES
                nzmrdv2.h
            HINTS
                ${IIMAGE_ROOT}/include
            )
set(IIMAGE_INCLUDE_DIRS ${IIMAGE_INCLUDE_DIR})
message("Found IIMAGE headers: ${IIMAGE_INCLUDE_DIRS}")

find_library ( IIMAGE_LIBRARY
            NAMES 
                nzmrdv2
            HINTS
                ${IIMAGE_LIBPATH}
             )
set(IIMAGE_LIBRARIES ${IIMAGE_LIBRARY})
message("Found IIMAGE libs: ${IIMAGE_LIBRARIES}")
