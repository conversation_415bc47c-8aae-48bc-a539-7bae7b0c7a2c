#
# The module defines the following variables:
# ZEUS_FOUND - True if zeus found.
# ZEUS_INCLUDE_DIRS - where to find zeusinfostruct.h, etc.
# ZEUS_LIBRARIES - List of libraries when using chisonultrasoundimageprocess.
#

thirdparty_prefix_path(glut)

find_path ( GLUT_INCLUDE_DIR
            NAMES
                glew.h
            HINTS
                ${GLUT_ROOT}/include
    )
set(GLUT_INCLUDE_DIRS ${GLUT_INCLUDE_DIR})
message("Found glut headers: ${GLUT_INCLUDE_DIRS}")


if(BUILD_SYS STREQUAL "windows")
    find_library ( GLEW_LIBRARY
                NAMES
                    glew32
                HINTS
                    ${GLUT_ROOT}/lib
                 )
    find_library ( GLUT_LIBRARY
                 NAMES
                     freeglut
                 HINTS
                     ${GLUT_ROOT}/lib
                  )
else()
    find_library ( GLEW_LIBRARY
                NAMES
                    GLEW
                HINTS
                    ${GLUT_ROOT}/lib
                 )
    find_library ( GLUT_LIBRARY
                NAMES
                    glut
                HINTS
                    ${GLUT_ROOT}/lib
                 )
endif()


set(GLUT_LIBRARIES ${GLUT_LIBRARY} ${GLEW_LIBRARY})
message("Found glut libs: ${GLUT_LIBRARIES}")
