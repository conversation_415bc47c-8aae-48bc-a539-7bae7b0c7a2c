#include "fingerworker.h"
#include <QThread>
#include <QDebug>
#include "logger.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, FingerWorker)
FingerWorker::FingerWorker(QObject* parent)
    : QObject(parent)
{
#ifdef USE_OPENFINGERPRINT
    connect(&m_Process, SIGNAL(readyRead()), this, SLOT(onReadProcess()));
    connect(&m_Process, SIGNAL(finished(int, QProcess::ExitStatus)), this,
            SLOT(onProcessFinished(int, QProcess::ExitStatus)));
#endif
}

FingerWorker::~FingerWorker()
{
}

void FingerWorker::setCmd(const QString& cmd)
{
    m_Cmd = cmd;
}

void FingerWorker::setArgs(const QStringList& list)
{
    m_ListArgs.clear();
    foreach (QString arg, list)
    {
        m_ListArgs.append(arg);
        log()->info() << PRETTY_FUNCTION << " arg = " << arg;
    }
}

void FingerWorker::start()
{
    log()->info() << PRETTY_FUNCTION << " m_Cmd = " << m_Cmd;
#ifdef USE_OPENFINGERPRINT
    m_Process.start(m_Cmd, m_ListArgs);
#endif
}

void FingerWorker::stop()
{
#ifdef USE_OPENFINGERPRINT
    if (QProcess::NotRunning != m_Process.state())
    {
        m_Process.kill();
        m_Process.waitForFinished();
    }
#endif
}

void FingerWorker::onReadProcess()
{
#ifdef USE_OPENFINGERPRINT
    QByteArray data = m_Process.readAll();
    emit readProcess(data);
#endif
}

#ifdef USE_OPENFINGERPRINT
void FingerWorker::onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus)
{
    Q_UNUSED(exitStatus)
    QByteArray data = m_Process.readAll();
    emit processFinished(exitCode, data);
}
#endif
