#ifndef FINGERWORKER_H
#define FINGERWORKER_H

#include "adminmodel_global.h"
#include <QObject>
#ifdef USE_OPENFINGERPRINT
#include <QProcess>
#endif

class ADMINMODELSHARED_EXPORT FingerWorker : public QObject
{
    Q_OBJECT
public:
    explicit FingerWorker(QObject* parent = 0);
    virtual ~FingerWorker();
    void setCmd(const QString& cmd);
    void setArgs(const QStringList& list);
    void start();
    void stop();

signals:
    void readProcess(QByteArray& data);
    void processFinished(int exitCode, QByteArray& data);

protected slots:
    void onReadProcess();
#ifdef USE_OPENFINGERPRINT
    void onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus);
#endif
private:
#ifdef USE_OPENFINGERPRINT
    QProcess m_Process;
#endif
    QString m_Cmd;
    QStringList m_ListArgs;
};

#endif // FINGERWORKER_H
