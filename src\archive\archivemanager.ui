<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ArchiveManager</class>
 <widget class="QWidget" name="ArchiveManager">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>984</width>
    <height>662</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Archive</string>
  </property>
  <layout class="QGridLayout" name="gridLayout" rowstretch="0" columnstretch="0">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QFrame" name="frame_2">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="0" column="0">
       <widget class="ArchiveTopWidget" name="widgetTop" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="TreeViewWidget" name="treeView" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="ArchiveButtonWidget" name="widgetButton" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
      <item row="2" column="0" colspan="2">
       <widget class="BorderedQFrame" name="frame">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="ImageSkimManager" name="widgetImage" native="true"/>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ImageSkimManager</class>
   <extends>QWidget</extends>
   <header>imageskimmanager.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>BorderedQFrame</class>
   <extends>QFrame</extends>
   <header>borderedqframe.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>ArchiveTopWidget</class>
   <extends>QWidget</extends>
   <header>archivetopwidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>ArchiveButtonWidget</class>
   <extends>QWidget</extends>
   <header>archivebuttonwidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>TreeViewWidget</class>
   <extends>QWidget</extends>
   <header>treeviewwidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
