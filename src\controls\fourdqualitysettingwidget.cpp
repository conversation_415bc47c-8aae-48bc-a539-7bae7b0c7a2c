#include "fourdqualitysettingwidget.h"
#include "fourdparascontainer.h"

FourDQualitySettingWidget::FourDQualitySettingWidget(QWidget* parent)
    : FourDParasSettingBaseWidget(parent)
    , m_Child(new FourDQualityWidget(this))
{
    addWidget(m_Child);
    m_SettingName = QString("FourDImageQuality");
    m_ParaType = FourDParasInfo::FourDQuality;
}

void FourDQualitySettingWidget::initalize()
{
    m_Child->initalize();
    FourDParasSettingBaseWidget::initalize();
}

void FourDQualitySettingWidget::setSonoParameters(SonoParameters* sonoParameters)
{
    m_Child->setSonoParameters(sonoParameters);
}

void FourDQualitySettingWidget::currenIndexChanged(const QString& arg1)
{
    m_Child->setFourDQualityPara(oneParasT<FourDParasInfo::FourDQualityPara>(arg1));
}

bool FourDQualitySettingWidget::contains(const QString& name)
{
    return isExistNameT<FourDParasInfo::FourDQualityPara>(name);
}

void FourDQualitySettingWidget::add(const QString& name)
{
    addOneParaT<FourDParasInfo::FourDQualityPara>(name, m_Child->fourDQualityPara());
}

void FourDQualitySettingWidget::save(const QString& name)
{
    add(name);
    FourDParasManager::instance().saveFourDQualityParas();
    m_Child->updateParameters();
}

void FourDQualitySettingWidget::del(const QString& name)
{
}

bool FourDQualitySettingWidget::rename(const QString& oldName, const QString& newName)
{
    return true;
}

QStringList FourDQualitySettingWidget::names()
{
    return namesT<FourDParasInfo::FourDQualityPara>();
}
