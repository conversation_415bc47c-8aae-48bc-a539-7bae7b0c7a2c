add_definitions(-DAUTOTEST_LIBRARY)

include_depends(utilitymodel presetmodel probeselectionview keyboard usf.interface.peripheral.disk.ui)

add_library_qt(autotest
    autotest.cpp
    autotesttaskgenerator.cpp
    burnintest.cpp
    htmlexporter.cpp
    itestcommand.cpp
    itestmachine.cpp
    testcontainer.cpp
    testmachine.cpp
    testswitchprobecommand.cpp
    testtimecommand.cpp
    testcommandnames.cpp
#    tcpserver.cpp
#    appdeal.cpp
#    filesreceived.cpp
    eventserialization.cpp
    qinputeventrecorder.cpp
    stateevent.cpp
    tgcdataevent.cpp
    messageevent.cpp
    )

target_link_libraries(autotest utilitymodel presetmodel probeselectionview usf.interface.peripheral.disk.ui)

if(${USE_KEYBOARD})
    target_include_directories(autotest PRIVATE keyboard ${CKEYBOARD_INCLUDE_DIRS})
    target_link_libraries(autotest keyboard ${CKEYBOARD_LIBRARIES})
endif(${USE_KEYBOARD})

if(${TLD})
    target_include_directories(autotest PRIVATE ${TLD_INCLUDE_DIRS})
    target_link_libraries(autotest ${TLD_LIBRARIES})
endif(${TLD})
