#!/bin/bash

function analysis {
    echo "usbId: $1" >> $filename
    echo "attach ok:" `grep -E "onUsbProbeAttached.*$1 attach ok" temp.txt | wc -l` >> $filename
    echo "attach error:" `grep -E "onUsbProbeAttached.*$1 attach error" temp.txt | wc -l` >> $filename
    echo "open success:" `grep -E "open usb probe success, usbId:  $1" temp.txt | wc -l` >> $filename
    echo "open error:" `grep -E "error opening device, usbId: $1" temp.txt | wc -l` >> $filename
}

filename=probe.txt
dir="probe_analysis"
if [ ! -d $dir ]; then
    mkdir probe_analysis
else
    rm probe_analysis/*
fi

cd $dir
logs=`ls ../res/log/default.log*`
for log in $logs
do
    echo $log
    grep -E -a 'usbPortid.*delete ok|usbPortid.*attach|open usb probe|error opening device' $log >> temp.txt
    grep -E -a 'usbPortid.*delete ok|onUsbProbeAttached.*attach' temp.txt | grep -E -a -B 1 'onUsbProbeAttached.*error' > probeerror.txt
    # grep -E -a 'usbPortid.*delete ok|onUsbProbeAttached.*attach' temp.txt >> temp1.txt
    # grep -E -a -B 1 'onUsbProbeAttached.*error' temp1.txt > probeerror.txt
    sed -i "s/void ProbeIdentifyDataObserver::onUsbProbeAttached(IoDeviceUSB\*)  //g;s/void ProbeIdentifyDataObserver::onUsbProbeDetach(int)  //g;s/qint32 IoDeviceUSB::open(libusb_device\*)//g" probeerror.txt
done
analysis 35
analysis 36
analysis 31
analysis 34
analysis 11
analysis 18