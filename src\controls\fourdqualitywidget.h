#ifndef FOURDQUALITYWIDGET_H
#define FOURDQUALITYWIDGET_H

#include "controls_global.h"
#include "fourdparasetbasewidget.h"
#include "fourdparasinfo.h"
#include <QHash>

namespace Ui
{
class FourDQualityWidget;
}

class CONTROLSSHARED_EXPORT FourDQualityWidget : public FourDParaSetBaseWidget
{
    Q_OBJECT
public:
    explicit FourDQualityWidget(QWidget* parent = 0);
    ~FourDQualityWidget();
    virtual void initalize();
    void setFourDQualityPara(const FourDParasInfo::FourDQualityPara& para);
    FourDParasInfo::FourDQualityPara fourDQualityPara();

protected:
    void retranslateUi();

private:
    Ui::FourDQualityWidget* ui;
    FourDParasInfo::FourDQualityPara m_FourDQualityPara;
};

#endif // FOURDQUALITYWIDGET_H
