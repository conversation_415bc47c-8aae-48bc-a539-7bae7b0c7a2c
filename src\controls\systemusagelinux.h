#ifndef SYSTEMUSAGELINUX_H
#define SYSTEMUSAGELINUX_H
//#if defined(SYS_APPLE) || defined(SYS_UNIX)

#include <string>
#include "controls_global.h"
#include "isystemusage.h"

class CONTROLSSHARED_EXPORT SystemUsageLinux : public ISystemUsage
{
public:
    SystemUsageLinux();
    ~SystemUsageLinux();

    virtual int getCpuUsage();
    virtual int getCpuTemperature();
    virtual unsigned long getCpuThreadCount();
    virtual unsigned long getCpuCoresCount();
    virtual void getMemoryUsage(unsigned int& nMemTotal, unsigned int& nMemFree);
    virtual int getCurVolumeVal();
    virtual bool setCurVolumeVal(int nVolumeVal);
    virtual bool playVideo(const QString& fileName);
};

//#endif
#endif // SYSTEMUSAGELINUX_H
