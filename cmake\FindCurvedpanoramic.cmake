#
# The module defines the following variables:
# CURVE<PERSON><PERSON>ORAMIC_FOUND - True if CURVEDPANORAMIC found.
# CURVE<PERSON><PERSON><PERSON>AMIC_INCLUDE_DIRS - where to find Panoramic.h, etc.
# CURVE<PERSON><PERSON>ORAMIC_LIBRARIES - List of libraries when using Panoramic.
#

thirdparty_prefix_path(curvedpanoramic)

find_path( CURVEDPANOR<PERSON>IC_INCLUDE_DIR
    NAMES
    Panoramic.h
    HINTS
    ${CURVEDPANORAMIC_ROOT}/include
    )
set(CURVEDPANORAMIC_INCLUDE_DIRS ${CURVEDPAN<PERSON>AM<PERSON>_INCLUDE_DIR})
message("Found curvedpanoramic headers: ${C<PERSON><PERSON><PERSON><PERSON>ORAM<PERSON>_INCLUDE_DIRS}")

find_library ( CURVEDPANORAMIC_LIBRARY
    NAMES
    Stitch
    HINTS
    ${CURVEDPANORAMIC_ROOT}/lib
    )

set(CURVEDP<PERSON><PERSON>AM<PERSON>_LIBRARIES ${CURVEDPANORAMIC_LIBRARY})
message("Found curvedpanoramic libs: ${CUR<PERSON>DPANORAM<PERSON>_LIBRARIES}")


