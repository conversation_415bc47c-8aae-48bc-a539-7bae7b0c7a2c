#ifndef SONOMSKALG_H
#define SONOMSKALG_H

#include "algorithm_global.h"
#include <QLibrary>
#include <QMutex>
#ifdef USE_SONOMSK
#include "msk_api.hpp"
#else
enum mskPlaneType
{
    BBSA,
    BBLA
};
#endif
#include "opencv2/core.hpp"
#include "virtualalg.h"

class ALGORITHMSHARED_EXPORT SonoMskAlg : public VirtualAlg
{
    friend class AlgInstance;

private:
    SonoMskAlg();

public:
    inline bool isError(void)
    {
        return m_IsError;
    }

    typedef void (*CreateViewontext)(const char* seg_modelpath, mskPlaneType inType, bool opencl);
    typedef void (*RemoveViewontext)();
    typedef int (*MskSeg)(std::tuple<std::vector<std::string>, std::vector<std::vector<cv::Point>>>& contours,
                          unsigned char* imgin, int width, int height);

    void createViewontext(const char* seg_modelpath, mskPlaneType inType, bool opencl = true);
    void removeViewontext();
    void mskSeg(std::tuple<std::vector<std::string>, std::vector<std::vector<cv::Point>>>& contours,
                unsigned char* imgin, int width, int height);

public:
    CreateViewontext m_CreateContext;
    RemoveViewontext m_RemoveContext;
    MskSeg m_MskSeg;
    QMutex m_Mutex;
};
#endif // SONOMSKALG_H
