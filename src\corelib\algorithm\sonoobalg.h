#ifndef SONOOBALG_H
#define SONOOBALG_H

#include "algorithm_global.h"
#ifdef USE_SONOOB
#include "OB.h"
#else
#define MAXBPDACNUM 2
#define MAXFHLNUM 1

struct OBJ
{
    int x1;
    int y1;
    int x2;
    int y2;
    int nameid; //不需要
    float conf; //置信度，可以理解为识别分数
};

enum FETUS_MEA_CODE
{
    BPD = 1,
    OFD = 2,
    HC = 3
};

typedef struct BPD_STRUCT
{

    // HC
    int cx4; //椭圆中点坐标
    int cy4; //椭圆中点y坐标
    int width4;
    int height4;
    float angle4; //角度（不需要）

    // BPD OFD都是返回两个坐标点
    int x1;
    int y1;
    int x2;
    int y2;
} BPDS;

typedef struct AC_STRUCT
{
    int cx; //椭圆中点坐标
    int cy; //椭圆中点y坐标
    int width;
    int height;
    float angle; //角度（不需要）
} ACS;

typedef struct FHL_STRUCT
{
    int x1;
    int y1;
    int x2;
    int y2;
    float length; //不需要

} FLS;
#endif
#include "virtualalg.h"
#include <QLibrary>

class ALGORITHMSHARED_EXPORT SonoOBAlg : public VirtualAlg
{
    friend class AlgInstance;

public:
    inline bool isError(void)
    {
        return m_IsError;
    }
    typedef int (*BpdInit)(const char* model_path_detect, const char* model_path_neijie, const char* model_path_waijie,
                           float conf_thres, float thres);
    typedef int (*BpdMeasure)(uint8_t* imgdata, int imgw, int imgh, int OP, BPDS* bpdsp);
    typedef void (*BpdRelease)();
    typedef int (*StomachInit)(const char* model_path_detect, const char* model_path, float conf_thres, float thres);
    typedef int (*StomachMeasure)(uint8_t* imgdata, int imgw, int imgh, ACS* acp);
    typedef void (*StomachRelease)();
    typedef int (*FhlInit)(const char* seg_model_path, const char* detect_model_path, float seg_thres,
                           float conf_thres);
    typedef int (*FhlMeasure)(uint8_t* imgdata, int imgw, int imgh, FLS* flp);
    typedef void (*FhlRelease)();
    typedef int (*InitStandardBpdac)(const char* det_model_path1, const char* det_model_path2, float conf_thres);
    typedef int (*StandardBpdacDetectobject)(uint8_t* imgdata, int width, int height, OBJ* objsp);
    typedef void (*StandardBpdacRelease)();
    typedef int (*InitStandardFhl)(const char* det_model_path, float conf_thres);
    typedef int (*StandardFhlDetectobject)(uint8_t* imgdata, int width, int height, OBJ* objsp);
    typedef void (*StandardFhlRelease)();

public:
    int bpdInit(const char* model_path_detect, const char* model_path_neijie, const char* model_path_waijie,
                float conf_thres, float thres);
    int bpdMeasure(uint8_t* imgdata, int imgw, int imgh, int OP, BPDS* bpdsp);
    void bpdRelease();
    int stomachInit(const char* model_path_detect, const char* model_path, float conf_thres, float thres);
    int stomachMeasure(uint8_t* imgdata, int imgw, int imgh, ACS* acp);
    void stomachRelease();
    int fhlInit(const char* seg_model_path, const char* detect_model_path, float seg_thres, float conf_thres);
    int fhlMeasure(uint8_t* imgdata, int imgw, int imgh, FLS* flp);
    void fFhlRelease();
    int initStandardBpdac(const char* det_model_path1, const char* det_model_path2, float conf_thres);
    int standardBpdacDetectobject(uint8_t* imgdata, int width, int height, OBJ* objsp);
    void standardBpdacRelease();
    int initStandardFhl(const char* det_model_path, float conf_thres);
    int standardFhlDetectobject(uint8_t* imgdata, int width, int height, OBJ* objsp);
    void standardFhlRelease();

private:
    SonoOBAlg();
};
#endif // SONOOBALG_H
