#ifndef SYSTEMTBSTATUSWIDGET_H
#define SYSTEMTBSTATUSWIDGET_H

#include "controls_global.h"
#include "basewidget.h"

namespace Ui
{
class SystemTBStatusWidget;
}

class SystemHintModel;

class CONTROLSSHARED_EXPORT SystemTBStatusWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit SystemTBStatusWidget(QWidget* parent = nullptr);
    ~SystemTBStatusWidget();

    void setModel(SystemHintModel* model);

private slots:
    void slot_onTBStatusChanged();

protected:
    void retranslateUi();

private:
    Ui::SystemTBStatusWidget* ui;
    SystemHintModel* m_Model;
};

#endif // SYSTEMTBSTATUSWIDGET_H
