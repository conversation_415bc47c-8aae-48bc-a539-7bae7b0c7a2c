#include "abstractfourdlicenseprocessor.h"
#include "sonoparameters.h"
#include "appsetting.h"
#include "licenseinitializer.h"
#include "parameter.h"
#include "bfpnames.h"

AbstractFourDLicenseProcessor::AbstractFourDLicenseProcessor(QObject* parent)
    : IFourDLicenseProcessor(parent)
    , m_SonoParameters(NULL)
    , m_FourDLicenseState(NotVerified)
    , m_FourDRenderMax(0)
    , m_FourDOpened(false)
    , m_RemainDays(0)
{
    connect(&LicenseInitializer::instance(), SIGNAL(fourDStatusChanged(bool)), this, SLOT(onFourDStatusChanged(bool)));
    connect(&LicenseInitializer::instance(), SIGNAL(virtualHDStatusChanged(bool)), this,
            SLOT(onVirtualHDStatusChanged(bool)));
}

AbstractFourDLicenseProcessor::~AbstractFourDLicenseProcessor()
{
}

bool AbstractFourDLicenseProcessor::isFourDOpened() const
{
    return m_FourDOpened;
}

int AbstractFourDLicenseProcessor::remainDays() const
{
    return m_RemainDays;
}

void AbstractFourDLicenseProcessor::processFourDLicenseState(bool showInfo)
{
    Q_UNUSED(showInfo);
}

void AbstractFourDLicenseProcessor::setSonoParameters(SonoParameters* sonoParameters)
{
    m_SonoParameters = sonoParameters;
    m_FourDRenderMax = m_SonoParameters->parameter(BFPNames::FourDRenderStr)->max();
}

void AbstractFourDLicenseProcessor::onVirtualHDStatusChanged(bool isOpen)
{
    if (isOpen)
    {
        if (!m_FourDOpened)
        {
            AppSetting::setFunctionEnabled(LicenseItemKey::KeyVirtualHD, false);
            LicenseInitializer::instance().saveToLicenseFile(LicenseItemKey::KeyVirtualHD, false);
        }
        else
        {
            m_SonoParameters->parameter(BFPNames::FourDRenderStr)->setMax(m_FourDRenderMax);
        }
    }
    else
    {
        m_SonoParameters->parameter(BFPNames::FourDRenderStr)->setMax(m_FourDRenderMax - 1);
    }
}

void AbstractFourDLicenseProcessor::onSystemDateChanged()
{
}
