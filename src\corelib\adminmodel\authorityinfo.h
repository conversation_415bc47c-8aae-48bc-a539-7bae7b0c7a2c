#ifndef AUTHORITY_H
#define AUTHORITY_H
#include <QString>
#include "adminmodel_global.h"

class ADMINMODELSHARED_EXPORT AuthorityInfo
{
public:
    enum AuthorityId
    {
        AuthNUll = 0,
        Admin = 1,
        Administrator,
        User,
        Service
    };
    AuthorityInfo(AuthorityId id, QString name);
    QString name() const;
    AuthorityId Id() const;
    void setId(const AuthorityId& Id);

private:
    AuthorityId m_Id;
    QString m_name;
};

#endif // AUTHORITY_H
