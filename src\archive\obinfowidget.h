#ifndef OBINFOWIDGET_H
#define OBINFOWIDGET_H
#include "archive_global.h"
#include "basewidget.h"
#include "patientinfoinputwidget.h"
#include "screenorientationmodel.h"

class Study;
class QDate;

namespace Ui
{
class ObInfoWidget;
}

/**
 * @brief 新建病人的界面，有产科的病人基本信息
 */
class ARCHIVESHARED_EXPORT ObInfoWidget : public BaseWidget
{
    Q_OBJECT
    Q_PROPERTY(bool isHor READ isHor)

public:
    explicit ObInfoWidget(QWidget* parent = 0);
    ~ObInfoWidget();
    /**
     * @brief setStudy 设置一个需要添加信息的study
     * @param study
     */
    void setStudy(const Study* study);
    /**
     * @brief flawlessStudy 根据界面编辑框的内容，完善study信息
     *
     * @param study
     */
    void flawlessStudy(Study* study);
    /**
     * @brief clearStudyInfo 清空study的信息
     */
    void clearStudyInfo();
    /**
     * @brief setCurrentWidgetEnabled 设置控件的状态
     *
     * @param enabled
     */
    void setCurrentWidgetEnabled(bool enabled);
    /**
     * @brief updateGA 更新胎儿的胎龄, Bug:16869
     */
    void updateGA();
    void setAnimalSpeciesIndex(int index);
    bool isHor();

public slots:
    void setInfo(const QMap<QString, QStringList>& info);
    void onPatientInfoUnitChanged(const QString& key, const int unitIndex);

signals:
    void baseInfo(const QMap<QString, QStringList>& info);

protected:
    const QMap<QString, QStringList> infoList();
    void calBsa();
    void retranslateUi();
    void orientationChanged(Qt::ScreenOrientation orientation);
    void hideEvent(QHideEvent* e);
    void initSet();

private slots:
    void onLineEditLmpDateChanged(const QDate& date);
    void onLineEditGaChanged(const QString& wd);
    void onLineEditGaFinished();
    void onLineEditEddDateChanged(const QDate& date);
    void onLineEditOvulDateChaned(const QDate& date);
    void onLineEditEstabDateChanged(const QDate& date);
    //    void on_lineEditHeight_editingFinished();
    //    void on_lineEditWeight_editingFinished();
    void on_lineEditBsa_editingFinished();
    void on_lineEditHr_editingFinished();
    //    void on_lineEditHeight_textEdited(const QString &arg1);
    //    void on_lineEditWeight_textEdited(const QString &arg1);
    void on_lineEditBsa_textEdited(const QString& arg1);
    void on_lineEditHr_textEdited(const QString& arg1);
    void patientInfo_textEdited();

private:
    Ui::ObInfoWidget* ui;
    bool m_isLoadStudy;
    PatientInfoInputModel m_PatientHeightModel;
    PatientInfoInputModel m_PatientWeightModel;
    int m_AnimalSpeciesIndex;
    ScreenOrientationModel* m_Model;
};

#endif // OBINFOWIDGET_H
