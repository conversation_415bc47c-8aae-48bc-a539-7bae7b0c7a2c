#include "sonothyroidalg.h"
#include "resource.h"

SonoThyroidAlg::SonoThyroidAlg()
    : VirtualAlg()
{
    m_CreateContext = NULL;
    m_RemoveContext = NULL;
    m_ThyroidCls = NULL;
    m_ThyroidDetection = NULL;
    m_Lib.setFileName("thyroidClass");
}

void SonoThyroidAlg::create_Viewontext(const char* detect_modelpath, const char* cls_modelpath,
                                       const char* clcache_path)
{
    m_BeginInit = true;
    if (m_CreateContext == NULL)
    {
        m_CreateContext = (CreateViewContext)m_Lib.resolve("createViewontext");
    }
    if (m_CreateContext == NULL)
    {
        m_IsError = true;
        return;
    }
    m_CreateContext(detect_modelpath, cls_modelpath, clcache_path);
    m_BeginInit = false;
    m_Initialized = true;
}

void SonoThyroidAlg::removeViewontext()
{
    if (m_RemoveContext == NULL)
    {
        m_RemoveContext = (RemoveViewContext)m_Lib.resolve("removeViewontext");
    }
    if (m_RemoveContext == NULL)
    {
        m_IsError = true;
        return;
    }

    m_Initialized = false;
    m_RemoveContext();
}

void SonoThyroidAlg::thyroidCls(unsigned char* imgin, int width, int height, boxOut& roi, thyroidOut& out)
{
    if (m_ThyroidCls != NULL)
    {
        m_ThyroidCls = (ThyroidCls)m_Lib.resolve("thyroidCls");
    }
    if (m_ThyroidCls == NULL || !m_Initialized)
    {
        m_IsError = true;
        return;
    }

    m_ThyroidCls(imgin, width, height, roi, out);
}

void SonoThyroidAlg::thyroidDetection(unsigned char* imgin, int width, int height, boxOut& boxOut)
{
    if (m_ThyroidDetection == NULL)
    {
        m_ThyroidDetection = (ThyroidDetection)m_Lib.resolve("thyroid_detection");
    }
    if (m_ThyroidDetection == NULL || !m_Initialized)
    {
        m_IsError = true;
        return;
    }

    m_ThyroidDetection(imgin, width, height, boxOut);
}
