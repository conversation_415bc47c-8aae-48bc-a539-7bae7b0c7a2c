/*
 * =====================================================================================
 *         Author:  <PERSON> (<EMAIL>)
 * =====================================================================================
 */

#include "autotest.h"
#include "resource.h"
#include "stateeventnames.h"
#include "itestmachine.h"
#include "diskselectionwidget.h"
#include "util.h"
#include <QDir>

#define EVENT_START_INTERVAL_TIME 2000
#define EVENT_INTERVAL_TIME 5000

AutoTest::AutoTest(QObject* parent)
    : QObject(parent)
    , m_currentTaskIndex(0)
    , m_testTotalTime(-1)
    , m_isHandleSpecialEventThatNeedsWait(false)
    , m_isInHandleEventList(false)
    , m_machine(NULL)
    , m_currentTestTime(-1)
    , m_isIniValid(false)
    , m_iniIndex(-1)
    , m_isStarted(false)
{
    m_eventTimer.setSingleShot(true);

    connectProcessTask();
}

bool AutoTest::containsSpecialEvent(const QString& event)
{
    return m_specialEvent.contains(event);
}

void AutoTest::setScriptName(const QString& value)
{
    m_currentScriptName = value;
    readIni();
}

void AutoTest::setTestMachine(ITestMachine* machine)
{
    m_machine = machine;
}

int AutoTest::currentTaskIndex() const
{
    return m_currentTaskIndex;
}

void AutoTest::setCurrentTaskIndex(int value)
{
    m_currentTaskIndex = value;
}

int AutoTest::testTotalTime() const
{
    return m_testTotalTime;
}

void AutoTest::setTestTotalTime(int testTotalTime)
{
    m_testTotalTime = testTotalTime;
}

const QHash<int, TinyEvent>& AutoTest::eventScripts() const
{
    return m_eventScripts;
}

int AutoTest::currentTestTime() const
{
    return m_currentTestTime;
}

void AutoTest::insertEventScripts(int key, TinyEvent value)
{
    m_eventScripts[key] = value;
}

const AutoTestTaskGenerator& AutoTest::taskGenerator() const
{
    return m_taskGenerator;
}

void AutoTest::checktaskLists()
{
    Q_ASSERT_X(m_taskGenerator.taskList().count() != 0, "BurnInTest::checkEventScripts()", "taskList cannot be empty!");
}

void AutoTest::checkIniLegal()
{
    checkMemberValue();
    checktaskLists();
}

void AutoTest::organizeEventTables()
{
    m_taskGenerator.organize(this);
}

void AutoTest::organizeEventScripts(QSettings& settings)
{
    m_eventScripts.clear();

    int size = settings.beginReadArray("EventScripts");

    for (int i = 0; i < size; ++i)
    {
        settings.setArrayIndex(i);

        TinyEvent event;
        event.eventList = settings.value("EventName").toStringList();
        event.timeInterval = settings.value("TimeInterval").toInt();
        insertEventScripts(i + 1, event);
    }

    settings.endArray();
}

const QDateTime& AutoTest::startTime() const
{
    return m_startTime;
}

void AutoTest::setStartTime(const QDateTime& value)
{
    m_startTime = value;

    if (!m_isStarted)
    {
        m_originStartTime = value;
        m_isStarted = true;
    }
}

// 进行测试时间的验证
bool AutoTest::checkIfBeyondTestTime()
{
    int secs = m_startTime.secsTo(QDateTime::currentDateTime());

    m_currentTestTime = m_originStartTime.secsTo(QDateTime::currentDateTime());

    if (secs > m_testTotalTime)
    {
        return true;
    }
    else
    {
        return false;
    }
}

const QStringList& AutoTest::currentHandeEventList() const
{
    return m_currentHandeEventList;
}

void AutoTest::setCurrentHandeEventList(const QStringList& currentHandeEventList)
{
    m_currentHandeEventList = currentHandeEventList;
}

bool AutoTest::isInHandleEventList() const
{
    return m_isInHandleEventList;
}

void AutoTest::setIsInHandleEventList(bool isInHandleEventList)
{
    m_isInHandleEventList = isInHandleEventList;
}

void AutoTest::makeCurrentHandleEventList(const QStringList& eventList)
{
    QStringList resList = eventList;
    resList.removeFirst();
    setCurrentHandeEventList(resList);
}

void AutoTest::handleEvent(const QString& event)
{
    if (AutoTest::containsSpecialEvent(event))
    {
        m_machine->handleSpecialEvent(event);
    }
    else
    {
        m_machine->handleNormalEvent(event);
    }
}

bool AutoTest::isHandleSpecialEventThatNeedsWait() const
{
    return m_isHandleSpecialEventThatNeedsWait;
}

void AutoTest::setIsHandleSpecialEventThatNeedsWait(bool isHandleSpecialEventThatNeedWait)
{
    m_isHandleSpecialEventThatNeedsWait = isHandleSpecialEventThatNeedWait;
}

void AutoTest::resetFlags()
{
    setCurrentTaskIndex(0);
    setIsInHandleEventList(false);
    setIsHandleSpecialEventThatNeedsWait(false);
    m_currentTestTime = -1;
    m_isStarted = false;
    m_iniIndex = -1;

    m_machine->resetFlags();
}

QTimer& AutoTest::eventTimer()
{
    return m_eventTimer;
}

void AutoTest::startTest(int msec)
{
    Q_ASSERT(m_machine != NULL);

    setStartTime(QDateTime::currentDateTime());

    if (msec == 0)
    {
        eventTimer().start(EVENT_START_INTERVAL_TIME);
    }
    else
    {
        eventTimer().start(msec);
    }
}

void AutoTest::stopTest()
{
    m_eventTimer.stop();

    finishHandler();

    // 每次stop别忘了进行相关reset
    resetFlags();
}

void AutoTest::handleEventList(const QStringList& eventList)
{
    if (eventList.isEmpty())
    {
        return;
    }

    if (eventList.count() == 1)
    {
        handleEvent(eventList.at(0));
        setIsInHandleEventList(false);
    }
    else
    {
        handleEvent(eventList.at(0));

        makeCurrentHandleEventList(eventList);

        eventTimer().start(EVENT_INTERVAL_TIME);
        setIsInHandleEventList(true);
    }
}

const QHash<QString, int>& AutoTest::circleEvents() const
{
    return m_circleEvents;
}

void AutoTest::setSpecialEventList(const QStringList& eventList)
{
    m_specialEvent = eventList;
}

bool AutoTest::isIniValid() const
{
    return m_isIniValid;
}

void AutoTest::updateIniFiles()
{
    setTestScriptDirName(Resource::externalAutoTestIniDir());
}

void AutoTest::setTestScriptDirName(const QString& dirName)
{
    getIniFiles(dirName);

    prepareFirstIni();
}

void AutoTest::prepareFirstIni()
{
    if (m_iniFiles.count() > 0)
    {
        m_iniIndex++;

        if (m_iniFiles.count() > m_iniIndex)
        {
            QString firstIniName = m_iniFiles.at(m_iniIndex).absoluteFilePath();

            setScriptName(firstIniName);
        }
    }
    else
    {
        m_isIniValid = false;
    }
}

void AutoTest::getIniFiles(const QString& dirName)
{
    QString externalPath = DiskSelectionDialog::getUDiskFilePath(dirName, false);
    QFileInfoList tempFileInfoList;

    if (!externalPath.isEmpty())
    {
        QDir dir(externalPath);
        tempFileInfoList = dir.entryInfoList(QStringList() << "*.ini");

        if (tempFileInfoList.count() > 0)
        {
            checkLegalAndMoveToRes(tempFileInfoList);
        }
    }

    QDir dir(Resource::internalAutoTestIniDir());
    m_iniFiles = dir.entryInfoList(QStringList() << "*.ini");
}

void AutoTest::checkLegalAndMoveToRes(const QFileInfoList& infoList)
{
    QFileInfoList validFileList;

    foreach (QFileInfo info, infoList)
    {
        QSettings setting(info.absoluteFilePath(), QSettings::IniFormat);

        int circleSize = setting.beginReadArray("CircleScripts");
        setting.endArray();
        int eventSize = setting.beginReadArray("EventScripts");
        setting.endArray();

        if (setting.value("TestTotalTime", 0).toInt() != 0 && circleSize != 0 || eventSize != 0)
        {
            validFileList << info;
        }
    }

    if (validFileList.count() > 0)
    {
        QDir dir(Resource::internalAutoTestIniDir());

        QFileInfoList removeFiles = dir.entryInfoList(QStringList() << "*.ini");

        foreach (QFileInfo rmInfo, removeFiles)
        {
            dir.remove(rmInfo.absoluteFilePath());
        }

        if (!dir.exists())
        {
            Util::Mkdir(Resource::internalAutoTestIniDir());
        }

        foreach (QFileInfo fileInfo, validFileList)
        {
            QFile::copy(fileInfo.absoluteFilePath(),
                        Resource::internalAutoTestIniDir() + Resource::pathSeparator + fileInfo.fileName());
        }
    }
}

void AutoTest::organizeCircleScripts(QSettings& settings)
{
    m_circleEvents.clear();

    int size = settings.beginReadArray("CircleScripts");

    for (int i = 0; i < size; ++i)
    {
        settings.setArrayIndex(i);

        QStringList eventNameList = settings.value("EventName").toStringList();
        QString eventName = eventNameList.join(",");

        m_circleEvents[eventName] = settings.value("EventCircleTime").toInt();
    }

    settings.endArray();
}

void AutoTest::nextTask()
{
    Task task = taskGenerator().taskList().at(currentTaskIndex());
    eventTimer().start(task.timeInterval * 1000);

    int taskIndex = currentTaskIndex();
    taskIndex++;
    taskIndex %= taskGenerator().taskList().count();
    setCurrentTaskIndex(taskIndex);
}

void AutoTest::tryToNextTask()
{
    if (!isInHandleEventList() && !isHandleSpecialEventThatNeedsWait())
    {
        nextTask();
    }
    else
    {
        eventTimer().start(EVENT_INTERVAL_TIME);
    }
}

void AutoTest::handleTask()
{
    Task task = taskGenerator().taskList().at(currentTaskIndex());

    if (!isInHandleEventList())
    {
        handleEventList(task.eventList);
    }
    else
    {
        handleEventList(currentHandeEventList());
    }
}

void AutoTest::finishHandler()
{
    m_machine->handleFinishEvent();
}

void AutoTest::disconnectProcessTask()
{
    disconnect(&m_eventTimer, SIGNAL(timeout()), this, SLOT(processTask()));
}

void AutoTest::connectProcessTask()
{
    connect(&m_eventTimer, SIGNAL(timeout()), this, SLOT(processTask()));
}

void AutoTest::processTask()
{
    if (!checkIfBeyondTestTime())
    {
        handleTask();

        if (!isInHandleEventList() && !isHandleSpecialEventThatNeedsWait())
        {
            nextTask();
        }
    }
    else
    {
        finishCurrentTest();
    }
}

void AutoTest::finishCurrentTest()
{
    if (m_iniIndex == m_iniFiles.count() - 1)
    {
        stopTest();
    }
    else
    {
        gotoNextTest();
    }
}

void AutoTest::gotoNextTest()
{
    m_eventTimer.stop();
    clearItems();

    m_iniIndex++;
    if (m_iniFiles.count() > m_iniIndex)
    {
        setScriptName(m_iniFiles.at(m_iniIndex).absoluteFilePath());

        startTest(5000);
    }
}

void AutoTest::clearItems()
{
    m_eventScripts.clear();
    m_circleEvents.clear();
    m_currentTaskIndex = 0;
    m_isInHandleEventList = false;
    m_isHandleSpecialEventThatNeedsWait = false;
}

void AutoTest::checkMemberValue()
{
    Q_ASSERT(m_testTotalTime > 0);
}

// 这边可以入手进行comboBox的筛选已经CheckList
void AutoTest::readIni()
{
    QSettings settings(m_currentScriptName, QSettings::IniFormat);

    setTestTotalTime(settings.value("TestTotalTime", 0).toInt());

    organizeCircleScripts(settings);
    organizeEventScripts(settings);

    if (m_circleEvents.count() == 0 && m_eventScripts.count() == 0 || m_testTotalTime == 0)
    {
        m_isIniValid = false;
    }
    else
    {
        organizeEventTables();
        checkIniLegal();

        m_isIniValid = true;
    }
}
