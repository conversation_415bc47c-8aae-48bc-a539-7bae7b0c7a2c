#ifndef SYSTEMHINTELLIPSEWIDGET_H
#define SYSTEMHINTELLIPSEWIDGET_H

#include "basewidget.h"
#include "regiongenerator.h"

class SystemHintModel;

namespace Ui
{
class SystemHintEllipseWidget;
}

class SystemHintEllipseWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit SystemHintEllipseWidget(QWidget* parent = 0);
    ~SystemHintEllipseWidget();

    void setModel(SystemHintModel* value);

private slots:
    void onWorkStatusChanged();
    void onTBStatusChanged();

protected:
    void retranslateUi();
    void paintEvent(QPaintEvent* ev);

private:
    void setPaintParameters();

private:
    Ui::SystemHintEllipseWidget* ui;
    SystemHintModel* m_Model;
    RegionGenerator m_RegionGenerator;
    QPainterPath m_PainterPath;
    bool m_Generated;
};

#endif // SYSTEMHINTELLIPSEWIDGET_H
