#!/bin/bash

# Variables
USER="root"  # Replace with the actual user
IP=$1  # Replace with the actual IP address

# Check if the remote machine is online
if ! ping -c 1 "$IP" &> /dev/null; then
    echo "Error: The remote machine at $IP is not online. Deployment cannot proceed."
    exit 1
fi
TARGET_MACHINE="$USER@$IP"
REMOTE_DIR="/usr/tigerapp"  # Replace with the actual remote directory

# Function to create directories on the target machine
create_directories() 
{
    ssh "$TARGET_MACHINE" "mkdir -p $1 && echo 'Created directory: $1'"
}

# Function to copy files to the target machine
copy_files() 
{
    echo "Copying files from $1 to $TARGET_MACHINE:$2/"
    scp -r $1 $TARGET_MACHINE:$2/
}

# Add the target machine's key to known_hosts
ssh-keyscan -H $IP >> ~/.ssh/known_hosts
ssh-copy-id $TARGET_MACHINE

Create necessary directories
create_directories "$REMOTE_DIR"
create_directories "$REMOTE_DIR/res/"
create_directories "$REMOTE_DIR/res/modelfiles/"
create_directories "$REMOTE_DIR/res/modelfiles/apple/"

ssh "$TARGET_MACHINE" "killall tiger_main xultrasound && echo 'Killed processes: tiger_main and xultrasound'"

ssh "$TARGET_MACHINE" "rm -rf $REMOTE_DIR/res/log && echo 'Deleted log directory at $REMOTE_DIR/res/log'"
# Copy files
copy_files "build/Apple/bin/*" "$REMOTE_DIR"
for item in ~/code/resource-xunit/res/*; do
    if [[ ! "$item" =~ "modelfiles" ]]; then
        echo "Copying item: $item"
        copy_files $item $REMOTE_DIR/res
    fi
done
copy_files ~/code/resource-xunit/res/modelfiles/apple/ $REMOTE_DIR/res/modelfiles/
ssh "$TARGET_MACHINE" "reboot && echo 'Remote machine is rebooting...'"

echo "Deployment successful on SonoAir:$IP"

