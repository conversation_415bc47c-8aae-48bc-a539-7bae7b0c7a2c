#ifndef FILETREEVIEW_H
#define FILETREEVIEW_H
#include "archive_global.h"

//#include <QWidget>
#include "basewidget.h"
#include <QFileInfoList>

namespace Ui
{
class FileTreeView;
}

/**
 * @brief 提供treeview界面
 */
class QAbstractItemModel;
class QModelIndex;
class QFileInfo;
class ARCHIVESHARED_EXPORT FileTreeView : public BaseWidget
{
    Q_OBJECT

public:
    explicit FileTreeView(QWidget* parent = 0);
    ~FileTreeView();
    /**
     * @brief setModel 添加model
     *
     * @param model
     */
    void setModel(QAbstractItemModel* model);
    /**
     * @brief setRootIndex 设置根目录
     *
     * @param index
     */
    void setRootIndex(const QModelIndex& index);
    /**
     * @brief setAnimated 控制节点是否 可以扩展
     *
     * @param enable
     */
    void setAnimated(bool enable);
    /**
     * @brief setIndentation 当前的目录
     *
     * @param i
     */
    void setIndentation(int i);
    /**
     * @brief setSortingEnabled 可否排序
     *
     * @param enable
     */
    void setSortingEnabled(bool enable);
    void setRootIsDecorated(bool show);
    void setItemsExpandable(bool enable);
    QFileInfo currentFileInfo();
signals:
    void fileInfoList(const QFileInfoList& infoList);
private slots:
    void on_pushButtonBack_clicked();
    void on_pushButtonMakeDir_clicked();
    void on_pushButtonDelete_clicked();

protected:
    void retranslateUi();

private:
    Ui::FileTreeView* ui;
};

#endif // FILETREEVIEW_H
