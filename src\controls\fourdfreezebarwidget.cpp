#include "fourdfreezebarwidget.h"
#include "ui_freezebarwidget.h"
#include "ibuffermanager.h"
#include "freezebar.h"
#include "ilinebuffermanager.h"

FourDFreezeBarWidget::FourDFreezeBarWidget(QWidget* parent)
    : FreezeBarWidget(parent)
{
    ui->freezeBar->setVisible(true);
    ui->freezeBar->setFreezeLabelVisible(true);
}

FourDFreezeBarWidget::~FourDFreezeBarWidget()
{
}

void FourDFreezeBarWidget::setAllCurrentIndex(int frameCount, int bufferIndex)
{
    ui->freezeBar->setStartIndex(m_BufferManager->startIndex());
    ui->freezeBar->setCurrentIndex(m_BufferManager->currentIndex());
    ui->freezeBar->setEndIndex(m_BufferManager->endIndex());
    ui->freezeBar->setMax(frameCount - 1);
}

void FourDFreezeBarWidget::setFreezeBarVisible(bool value, int bufferIndex, int layoutIndex)
{
    Q_UNUSED(layoutIndex)
    ui->freezeBar->setFreezeLabelVisible(value);
    ui->freezeBar->setVisible(value);
    setAllCurrentIndex(m_BufferManager->frameCount(), bufferIndex);
    emit freezeBarShow(value);
}

void FourDFreezeBarWidget::onBufferStartIndexChanged(int bufferIndex, int layoutIndex)
{
    ui->freezeBar->setStartIndex(m_BufferManager->startIndex(layoutIndex));
}

void FourDFreezeBarWidget::onBufferCurrentIndexChanged(int bufferIndex, int layoutIndex, int frameCount)
{
    Q_UNUSED(layoutIndex)
    setAllCurrentIndex(frameCount, bufferIndex);
}

void FourDFreezeBarWidget::onBufferEndIndexChanged(int bufferIndex, int layoutIndex)
{
    ui->freezeBar->setEndIndex(m_BufferManager->endIndex(layoutIndex));
}
