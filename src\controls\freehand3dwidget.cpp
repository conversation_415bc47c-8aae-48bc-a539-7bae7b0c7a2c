#include "freehand3dwidget.h"
#include "ui_freehand3dwidget.h"
#include "progressbarview.h"
#include "freehand3dmodel.h"
#include "util.h"
#include "imagetile.h"
#include "imagewidget.h"
#include "sonoparameters.h"
#include "parameter.h"
#include "bfpnames.h"
#include "modeluiconfig.h"
#include "VolumeViewer.h"
#include "freehand3dapi.h"
#include <QThread>
#include "variantutil.h"

namespace Ultra4D
{
class UltraViewerGLMRD2;
typedef UltraViewerGLMRD2 UltraViewer;
} // namespace Ultra4D

FreeHand3DWidget::FreeHand3DWidget(ImageWidget* imagewidget, ILineBufferManager* buffermanager, QWidget* parent)
    : IBaseWidget(parent)
    , ui(new Ui::FreeHand3DWidget)
    , m_ImageWidget(imagewidget)
    , m_ImageTile(imagewidget->imageTile())
    , m_SonoParameters(m_ImageTile->sonoParameters())
    , m_Model(new FreeHand3DModel(m_ImageTile, buffermanager))
    , m_ProgressBar(new ProgressBarView(this))
    , m_SendThread(new QThread())
    , m_CurrentAxis(XAxis)
    , m_ZooomValue(1.0f)
    , m_IsInit(false)
{
    ui->setupUi(this);
    m_Model->moveToThread(m_SendThread);
    m_SendThread->start();
    connect(this, SIGNAL(sendVolumeData()), m_Model, SLOT(process3DData()));
    connect(m_Model, SIGNAL(updateProgressBarValue(qint64)), m_ProgressBar, SLOT(updateProgressBarValue(qint64)));
    connect(m_Model, SIGNAL(updateProgressBarText(const QString&)), m_ProgressBar,
            SLOT(setProgressBarText(const QString&)));
    connect(m_Model, SIGNAL(volumeDataSended()), this, SLOT(onVolumeDataSended()));

    m_ProgressBar->hideProgressBar();
    setWindowFlags(Qt::WindowStaysOnTopHint);
}

FreeHand3DWidget::~FreeHand3DWidget()
{
    delete ui;
    DELETE_PTR(m_Model);
    DELETE_PTR(m_SendThread);
    DELETE_PTR(m_ProgressBar);
    QHash<AxisType, LabelWithIconData*>::const_iterator iter = m_LabelHash.constBegin();
    for (; iter != m_LabelHash.constEnd(); iter++)
    {
        delete iter.value();
    }
}

void FreeHand3DWidget::sendImageData()
{
    if (!m_IsInit)
    {
        init();
        m_IsInit = true;
    }
    m_ProgressBar->setProgressBarTextVisible(false);
    m_ProgressBar->setBusyIndicative(false);
    m_ProgressBar->setCancelButtonVisible(false);
    m_ProgressBar->setText(tr("Initialising. Please wait."));
    m_ProgressBar->showProgressBar();
    m_ProgressBar->updateProgressBarValue(0);
    m_ZooomValue = 1.0f;
    emit sendVolumeData();
}

void FreeHand3DWidget::init()
{
    m_LabelHash[XAxis] =
        new LabelWithIconData(ui->labelX, ModelUiConfig::instance().value(ModelUiConfig::HD3DXIcon).toString(),
                              ModelUiConfig::instance().value(ModelUiConfig::HD3DXIcon1).toString());

    m_LabelHash[YAxis] =
        new LabelWithIconData(ui->labelY, ModelUiConfig::instance().value(ModelUiConfig::HD3DYIcon).toString(),
                              ModelUiConfig::instance().value(ModelUiConfig::HD3DYIcon1).toString());

    m_LabelHash[ZAxis] =
        new LabelWithIconData(ui->labelZ, ModelUiConfig::instance().value(ModelUiConfig::HD3DZIcon).toString(),
                              ModelUiConfig::instance().value(ModelUiConfig::HD3DZIcon1).toString());
    onAxisChanged(0);

    FreeHand3DAPI::instance().create(ui->freehand3DRenderWidget);
    QObjectList childList = this->children();
    foreach (QObject* child, childList)
    {
        QLabel* label = qobject_cast<QLabel*>(child);
        if (label != NULL)
        {
            label->setAttribute(Qt::WA_NoSystemBackground);
        }
    }
}

void FreeHand3DWidget::highlightCurrentAxis(FreeHand3DWidget::AxisType axis)
{
    switch (axis)
    {
    case XAxis:
        m_LabelHash[XAxis]->highlightLablel();
        m_LabelHash[YAxis]->lowlightLabel();
        m_LabelHash[ZAxis]->lowlightLabel();
        break;
    case YAxis:
        m_LabelHash[XAxis]->lowlightLabel();
        m_LabelHash[YAxis]->highlightLablel();
        m_LabelHash[ZAxis]->lowlightLabel();
        break;
    case ZAxis:
        m_LabelHash[XAxis]->lowlightLabel();
        m_LabelHash[YAxis]->lowlightLabel();
        m_LabelHash[ZAxis]->highlightLablel();
        break;
    default:
        break;
    }
}

void FreeHand3DWidget::flashAxisLabel(int time)
{
    m_LabelHash[m_CurrentAxis]->lowlightLabel();
    QTimer::singleShot(time, this, SLOT(onFlashAxisLabelEnd()));
}

void FreeHand3DWidget::retranslateUi()
{
}

void FreeHand3DWidget::showEvent(QShowEvent* e)
{
    QWidget::showEvent(e);
}

void FreeHand3DWidget::onAxisChanged(const QVariant& val)
{
    m_CurrentAxis = (AxisType)val.toInt();
    Q_ASSERT(m_CurrentAxis < 3);
    highlightCurrentAxis(m_CurrentAxis);
}

void FreeHand3DWidget::onRotate(bool increase)
{
    flashAxisLabel();
    int angle = increase ? 9 : -9;
    switch (m_CurrentAxis)
    {
    case XAxis:
        FreeHand3DAPI::instance().rotate(angle, Ultra4D::VolumeViewer::X_AXIS);
        break;
    case YAxis:
        FreeHand3DAPI::instance().rotate(-angle, Ultra4D::VolumeViewer::Y_AXIS);
        break;
    case ZAxis:
        FreeHand3DAPI::instance().rotate(-angle, Ultra4D::VolumeViewer::Z_AXIS);
        break;
    default:
        break;
    }
}

void FreeHand3DWidget::onFreeHand3DZoom(bool increase)
{
    float step = increase ? 0.02 : -0.02;
    if (m_ZooomValue + step <= 2.0f && m_ZooomValue + step >= 0.1f)
    {
        m_ZooomValue += step;
    }
    FreeHand3DAPI::instance().zoom(m_ZooomValue);
}

void FreeHand3DWidget::onFreeHand3DReset()
{
    m_ZooomValue = 1.0f;
    FreeHand3DAPI::instance().reset();
}

void FreeHand3DWidget::onBeforecurSonoParametersChanged()
{
    disconnect(m_SonoParameters->parameter(BFPNames::FreeHand3DVolmnSpacingStr), SIGNAL(valueChanging(QVariant)),
               m_Model, SLOT(onVolumeSpacingChanged(QVariant)));
    disconnect(m_SonoParameters->parameter(BFPNames::FreeHand3DRotateAxisStr), SIGNAL(valueChanging(QVariant)), this,
               SLOT(onAxisChanged(QVariant)));
}

void FreeHand3DWidget::onCurSonoParametersChanged()
{
    m_SonoParameters = m_ImageTile->sonoParameters();
    connect(m_SonoParameters->parameter(BFPNames::FreeHand3DVolmnSpacingStr), SIGNAL(valueChanging(QVariant)), m_Model,
            SLOT(onVolumeSpacingChanged(QVariant)));
    connect(m_SonoParameters->parameter(BFPNames::FreeHand3DRotateAxisStr), SIGNAL(valueChanging(QVariant)), this,
            SLOT(onAxisChanged(QVariant)));
}

void FreeHand3DWidget::onVolumeDataSended()
{
    m_ProgressBar->hideProgressBar();
    emit show3DImage();
    m_ZooomValue = 1.0f;
    FreeHand3DAPI::instance().makeFreeHand3D();
}

void FreeHand3DWidget::onFlashAxisLabelEnd()
{
    m_LabelHash[m_CurrentAxis]->highlightLablel();
}
