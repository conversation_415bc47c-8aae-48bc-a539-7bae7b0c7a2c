#include "userpresetdatahandler.h"
#include "abstractdbconnection.h"
#include "dbtranscation.h"
#include "idatabasemanager.h"
#include "idbaccess.h"
#include "ordermap/ordermap.h"
#include "resource.h"
#include "syncer.h"
#include <QFile>
#include <QFileInfo>
#include <stdlib.h>

// Authority table
#define TB_AUTHORITY "Authority"
#define CN_AUTHORITYID "AuthorityId"
#define CN_AUTHORITYNAME "AuthorityName"
// user table
#define TB_USERS "Users"
#define CN_USERID "UserId"
#define CN_USERNAME "UserName"
#define CN_PWD "Password"
#define CN_IS_CHANGED "IsChanged"
// Fingerprint
#define TB_FINGERPRINT "Fingerprint"
#define CN_FINGPRINT_INDEX "FingerIndex"
#define CN_FINGERPRINTID "FingerprintId"
#define CN_FINGERPRINTNAME "FingerprintName"
#define CN_FINGERPRINT_STATUS "Status"
// face
#define TB_FACE "Face"
#define CN_FACEID "FaceId"
#define CN_FACENAME "FaceName"

class UserPresetDataHandlerPrivate
{

public:
    UserPresetDataHandlerPrivate(const QString& dbFileName, IDataBaseManager* databaseManager)
        : m_DBAccess(databaseManager->createDBAccess(dbFileName))
    {
    }

    IDBAccess* m_DBAccess;
};

UserPresetDataHandler::UserPresetDataHandler()
    : d(NULL)
{
    QFileInfo fileinfo(Resource::usersDBFileName());

    if (!fileinfo.exists() || fileinfo.size() == 0)
    {
        QFile::remove(Resource::usersDBFileName());

        if (QFile::copy(QString(":/files/%1").arg(fileinfo.fileName()), Resource::usersDBFileName()))
        {
            // AppLogger::info("create database %1!", QFile::exists(fileinfo.filePath()));

            QFile file(fileinfo.filePath());
            file.setPermissions(file.permissions() | QFile::WriteOwner);

            UTIL_DATA_SYNCER
        }
        else
        {
            return;
        }
    }
}

UserPresetDataHandler* UserPresetDataHandler::instance()
{
    static UserPresetDataHandler handler;
    return &handler;
}

UserPresetDataHandler::~UserPresetDataHandler()
{
    if (d != NULL) // May a delete cost is heavy than a simple comparsion!
    {
        delete d;
    }
}

void UserPresetDataHandler::initialize(IDataBaseManager* databaseManager)
{
    d = new UserPresetDataHandlerPrivate(Resource::usersDBFileName(), databaseManager);
}

bool UserPresetDataHandler::getAllAuthorityInfo(QList<AuthorityInfo>& authorities) const
{
    if (d == NULL)
    {
        return false;
    }
    DBTranscation t(**(d->m_DBAccess->database()));
    authorities.clear();
    bool ret = d->m_DBAccess->select(TB_AUTHORITY, QStringList() << CN_AUTHORITYID << CN_AUTHORITYNAME);

    while (ret && d->m_DBAccess->next())
    {
        AuthorityInfo::AuthorityId id = (AuthorityInfo::AuthorityId)(d->m_DBAccess->value(0).toInt());
        authorities.append(AuthorityInfo(id, d->m_DBAccess->value(1).toString()));
    }

    return ret;
}

bool UserPresetDataHandler::getAllUserInfo(QList<UserInfo>& users) const
{
    if (d == NULL)
    {
        return false;
    }

    DBTranscation t(**(d->m_DBAccess->database()));
    users.clear();
    bool ret = d->m_DBAccess->select(TB_USERS, QStringList() << CN_USERID << CN_USERNAME << CN_PWD << CN_AUTHORITYID
                                                             << CN_IS_CHANGED);

    while (ret && d->m_DBAccess->next())
    {
        users.append(UserInfo(d->m_DBAccess->value(0).toString(), d->m_DBAccess->value(1).toString(),
                              d->m_DBAccess->value(2).toString(), d->m_DBAccess->value(3).toInt(),
                              d->m_DBAccess->value(4).toBool()));
    }
    return ret;
}

bool UserPresetDataHandler::addUser(const UserInfo& user)
{
    return addUsers(QList<UserInfo>() << user);
}

bool UserPresetDataHandler::addUsers(const QList<UserInfo>& users)
{
    if (d == NULL)
    {
        return false;
    }
    DBTranscation t(**(d->m_DBAccess->database()));
    VariantOrderMap data;
    bool ret = true;
    foreach (const UserInfo& user, users)
    {
        data[CN_USERID] = user.userId();
        data[CN_USERNAME] = user.userName();
        data[CN_PWD] = user.password();
        data[CN_AUTHORITYID] = user.authorityId();
        data[CN_IS_CHANGED] = user.isChanged();
        ret = d->m_DBAccess->insert(TB_USERS, data);
        if (!ret)
        {
            break;
        }
    }
    return ret;
}

bool UserPresetDataHandler::removeUser(const QString& username)
{
    return removeUsers(QStringList() << username);
}

bool UserPresetDataHandler::removeUsers(const QStringList& usernames)
{
    if (d == NULL)
    {
        return false;
    }
    DBTranscation t(**(d->m_DBAccess->database()));
    VariantOrderMap data;
    bool ret = true;
    foreach (const QString& name, usernames)
    {
        data[CN_USERNAME] = name;
        ret = d->m_DBAccess->deletes(TB_USERS, data);
        if (!ret)
        {
            break;
        }
    }
    return ret;
}

bool UserPresetDataHandler::updateUser(const UserInfo& user)
{
    if (d == NULL)
    {
        return false;
    }
    DBTranscation t(**(d->m_DBAccess->database()));
    VariantOrderMap data;
    data[CN_USERID] = user.userId();
    data[CN_PWD] = user.password();
    data[CN_AUTHORITYID] = user.authorityId();
    data[CN_IS_CHANGED] = user.isChanged();

    VariantOrderMap conds;
    conds[CN_USERNAME] = user.userName();
    return d->m_DBAccess->update(TB_USERS, data, conds);
}

bool UserPresetDataHandler::addFingerprint(const FingerprintInfo& value)
{
    return addFingerprints(QList<FingerprintInfo>() << value);
}

bool UserPresetDataHandler::addFingerprints(const QList<FingerprintInfo>& value)
{
    if (d == NULL)
    {
        return false;
    }

    DBTranscation t(**(d->m_DBAccess->database()));
    VariantOrderMap data;
    bool ret = true;
    foreach (const FingerprintInfo& finger, value)
    {
        data[CN_FINGERPRINTID] = finger.fingerprintId();
        data[CN_FINGERPRINTNAME] = finger.fingerprintName();
        data[CN_USERID] = finger.userId();
        data[CN_FINGERPRINT_STATUS] = finger.status();
        ret = d->m_DBAccess->insert(TB_FINGERPRINT, data);
        if (!ret)
        {
            break;
        }
    }
    return ret;
}

bool UserPresetDataHandler::removeFingerprint(int id)
{
    return removeFingerprints(QList<int>() << id);
}

bool UserPresetDataHandler::removeFingerprints(const QList<int>& value)
{
    if (d == NULL)
    {
        return false;
    }
    DBTranscation t(**(d->m_DBAccess->database()));
    VariantOrderMap data;
    bool ret = true;
    foreach (const int id, value)
    {
        data[CN_FINGPRINT_INDEX] = id;
        ret = d->m_DBAccess->deletes(TB_FINGERPRINT, data);
        if (!ret)
        {
            break;
        }
    }
    return ret;
}

bool UserPresetDataHandler::updateFingerprint(const FingerprintInfo& value)
{
    if (d == NULL)
    {
        return false;
    }
    DBTranscation t(**(d->m_DBAccess->database()));
    VariantOrderMap data;
    data[CN_FINGERPRINTID] = value.fingerprintId();
    data[CN_FINGERPRINTNAME] = value.fingerprintName();
    data[CN_USERID] = value.userId();
    data[CN_FINGERPRINT_STATUS] = value.status();

    VariantOrderMap conds;
    conds[CN_FINGPRINT_INDEX] = value.fingerIndex();
    return d->m_DBAccess->update(TB_FINGERPRINT, data, conds);
}

bool UserPresetDataHandler::getAllFingerprintInfo(QList<FingerprintInfo>& value)
{
    if (d == NULL)
    {
        return false;
    }
    DBTranscation t(**(d->m_DBAccess->database()));
    value.clear();
    bool ret = d->m_DBAccess->select(TB_FINGERPRINT, QStringList()
                                                         << CN_FINGPRINT_INDEX << CN_FINGERPRINTID << CN_FINGERPRINTNAME
                                                         << CN_USERID << CN_FINGERPRINT_STATUS);

    while (ret && d->m_DBAccess->next())
    {
        value.append(FingerprintInfo(d->m_DBAccess->value(0).toInt(), d->m_DBAccess->value(1).toInt(),
                                     d->m_DBAccess->value(2).toString(), d->m_DBAccess->value(3).toString(),
                                     d->m_DBAccess->value(4).toBool()));
    }
    return ret;
}

bool UserPresetDataHandler::removeUserById(const QString& userId)
{
    if (d == NULL)
    {
        return false;
    }
    DBTranscation t(**(d->m_DBAccess->database()));
    VariantOrderMap data;
    bool ret = true;
    data[CN_USERID] = userId;
    ret = d->m_DBAccess->deletes(TB_USERS, data);
    return ret;
}
