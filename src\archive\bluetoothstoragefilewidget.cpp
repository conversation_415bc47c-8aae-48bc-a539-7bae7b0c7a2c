#include "bluetoothstoragefilewidget.h"
#include "ui_bluetoothstoragefilewidget.h"
#include "bluetoothfiletransfer.h"
#include "messageboxframe.h"
#include "generalworkflowfunction.h"
#include "util.h"
#include "resource.h"
#include "bluetoothinfo.h"
#include <QDir>
#include <QBluetoothTransferManager>
#include <QBluetoothTransferRequest>
#include <QBluetoothTransferReply>

BluetoothStorageFileWidget::BluetoothStorageFileWidget(IColorMapManager* colorMapManager, QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::BluetoothStorageFilleWidget)
    , m_copy(new ImageProcessedCopy(colorMapManager))
    , m_isGDPR(false)
{
    ui->setupUi(this);
    ui->checkBoxVideo->setChecked(true);
    this->adjustSize();
}

BluetoothStorageFileWidget::~BluetoothStorageFileWidget()
{
    if (ui)
    {
        delete ui;
        ui = nullptr;
    }
    if (m_copy)
    {
        delete m_copy;
        m_copy = nullptr;
    }
}

void BluetoothStorageFileWidget::initBluetoothNames()
{
    QStringList pairedDeviceNames = BluetoothInfo::instance()->getPairedDeviceNames();
    ui->comboBoxBluetoothDevices->addItems(pairedDeviceNames);
    ui->comboBoxBluetoothDevices->setCurrentIndex(BluetoothInfo::instance()->currentDefaultIndex());
}

void BluetoothStorageFileWidget::setExportFileListPaths(const QStringList& filePaths)
{
    m_fileNameList = filePaths;
}

void BluetoothStorageFileWidget::setDicomTaskManager(DicomTaskManager* dicomTaskManager)
{
    m_DicomTaskManager = dicomTaskManager;
    m_copy->setTaskManager(m_DicomTaskManager);
}

bool BluetoothStorageFileWidget::isFileExists(const QString& fileName, const QString& path)
{
    QDir dir(path);
    QFileInfo info(dir.filePath(fileName));
    return info.exists();
}

void BluetoothStorageFileWidget::setCopyOptions()
{
    m_copy->setCinetransTo(ui->checkBoxVideo->isChecked() ? ui->comboBox_2->currentText() : QString());

    QStringList suffixFilters = QStringList() << Resource::calcSuffix;
    suffixFilters << Resource::iniSuffix << "bin"
                  << "txt";
    suffixFilters << Resource::singleFourdImgSuffix << Resource::multiFourdImgSuffix;
    suffixFilters << Resource::imgSuffix;
    suffixFilters << Resource::measSuffix << Resource::reportSuffix;
    if (!ui->checkBoxVideo->isChecked())
        suffixFilters << Resource::cineSuffix;
    setFileFilter(suffixFilters);
    readyForFile();

    QFileInfo info(m_fileNameList[0]);
    QString studyOid;
    if (info.isDir())
    {
        studyOid = info.fileName();
    }
    else
    {
        studyOid = info.dir().dirName();
    }
    Patient* usedPatient = GeneralWorkflowFunction::getPatient(studyOid);
    if (usedPatient != NULL)
    {
        CheckInfoFilter c(*usedPatient);

        for (int i = 0; i < m_tmpFilePathes.count(); i++)
        {
            BluetoothFileTransfer* task =
                new BluetoothFileTransfer(m_tmpFilePathes[i], FileFormatEnum::format_yes, &c, m_bluetoothAddr);
            m_DicomTaskManager->add(task);
        }
    }
}

void BluetoothStorageFileWidget::readyForFile()
{
    QDir dir(Resource::bluetoothTempDir);
    if (!dir.exists())
    {
        Util::Mkdir(Resource::bluetoothTempDir);
    }

    for (int i = 0; i < m_tmpFilePathes.count(); i++)
    {
        QFileInfo srcInfo(m_tmpFilePathes[i]);
        m_tmpFilePath = m_copy->destFileName(srcInfo.fileName());
        if (m_isGDPR)
        {
            m_tmpFilePath = "GDPR_" + m_tmpFilePath;
        }
        m_tmpFilePath = dir.absolutePath() + "/" + m_tmpFilePath;
        m_copy->qCopyFile(m_tmpFilePathes[i], m_tmpFilePath);
        m_tmpFilePathes[i] = m_tmpFilePath;
    }
}

QFileInfoList BluetoothStorageFileWidget::getAllInfoList(const QDir& dir)
{
    QFileInfoList infoList = dir.entryInfoList(QDir::Files | QDir::NoDotAndDotDot | QDir::Hidden);

    return infoList;
}

void BluetoothStorageFileWidget::setFileFilter(QStringList suffixFilters)
{
    m_bluetoothAddr = BluetoothInfo::instance()->getAddr(m_bluetoothDeviceName);

    for (int i = 0; i < m_fileNameList.count(); i++)
    {
        QFileInfo srcInfo(m_fileNameList[i]);
        if (srcInfo.isFile())
        {
            if (suffixFilters.contains(srcInfo.suffix()))
            {
                continue;
            }
            else
            {
                m_tmpFilePathes << m_fileNameList[i];
            }
        }

        if (srcInfo.isDir())
        {
            QDir srcDir(m_fileNameList[i]);
            foreach (QFileInfo fileInfo, getAllInfoList(srcDir))
            {
                if (suffixFilters.contains(fileInfo.suffix()))
                {
                    continue;
                }
                else
                {
                    m_tmpFilePathes << fileInfo.absoluteFilePath();
                }
            }
        }
    }
}

void BluetoothStorageFileWidget::retranslateUi()
{
    ui->retranslateUi(this);
}

void BluetoothStorageFileWidget::on_pushButtonClose_clicked()
{
    emit closed();
}

void BluetoothStorageFileWidget::on_pushButtonExport_clicked()
{
    setCopyOptions();
    emit closed();
    MessageBoxFrame::tipInformation(tr("Bluetooth File Transfer Task has been added to the Task Queue!"));
}

void BluetoothStorageFileWidget::on_comboBoxBluetoothDevices_currentIndexChanged(const QString& bluetoothdevicename)
{
    m_bluetoothDeviceName = bluetoothdevicename;
}

void BluetoothStorageFileWidget::on_radioButtonPNG_toggled(bool checked)
{
    if (checked)
    {
        m_copy->setImageType(ImageProcessedCopy::PNG);
    }
}

void BluetoothStorageFileWidget::on_radioButtonJPG_toggled(bool checked)
{
    if (checked)
    {
        m_copy->setImageType(ImageProcessedCopy::JPG);
    }
}

void BluetoothStorageFileWidget::on_radioButtonBMP_toggled(bool checked)
{
    if (checked)
    {
        m_copy->setImageType(ImageProcessedCopy::BMP);
    }
}

void BluetoothStorageFileWidget::on_checkBoxGDPR_toggled(bool checked)
{
    m_copy->setIsGDPR(checked);
    m_isGDPR = checked;
}
