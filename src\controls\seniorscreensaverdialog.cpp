#include "seniorscreensaverdialog.h"
#include "ui_seniorscreensaverdialog.h"
#include "resource.h"
#include "setting.h"
#include <QDir>
#include <QScreen>
#include <QMouseEvent>
#include <QDebug>

SeniorScreenSaverDialog::SeniorScreenSaverDialog(QWidget* parent)
    : ScreenSaverDialog(parent)
    , ui(new Ui::SeniorScreenSaverDialog)
    , m_CursorMovedTimer(new QTimer(this))
{
    ui->setupUi(this);

    m_ScreenSize = QGuiApplication::screenAt(QCursor::pos())->size();

    // 因为此处选用的是进程的方案，当ffplay进程启动、关闭时，会有明显的窗口切换被用户察觉到，故此处对监听ffplay进程状态，
    // 根据ffplay进程的打开关闭，实时控制当前对话框，营造一个在切换时显示黑色背景。
    connect(ui->widget, &SeniorMediaWidget::ffplayIsRuning, this, &SeniorScreenSaverDialog::onfplayIsRuning);
    connect(ui->widget, &SeniorMediaWidget::ffplayIsEnded, this, &SeniorScreenSaverDialog::onffplayIsEnded);
    connect(ui->widget, &SeniorMediaWidget::ffplayExited, this, &SeniorScreenSaverDialog::screenSaverClicked);

    // 因为此处选用的是进程的方案，当ffplay窗口弹出时，无法监听鼠标位移，所以需要后台实时检测光标
    // 屏保单击退出，由ffplay程序自己完成
    m_CursorMovedTimer->setInterval(300);
    connect(m_CursorMovedTimer, &QTimer::timeout, this, &SeniorScreenSaverDialog::onCursorMoveTimeout);
    m_CursorMovedTimer->start();
}

void SeniorScreenSaverDialog::doExec(int pictureIndex)
{
    // 1、获取播放列表
    QDir dir(Resource::customScreenSaverDir);
    Q_ASSERT(dir.exists());

    QFileInfoList infoList = dir.entryInfoList(Resource::customScreenSaverFilters);
    Q_ASSERT(!infoList.isEmpty());

    QStringList paths;
    foreach (QFileInfo info, infoList)
    {
        paths << info.filePath();
    }

    if (Setting::instance().defaults().screenSaverPreviewed()) // 预览特定文件
    {
        paths.clear();
        Q_ASSERT(infoList.count() > pictureIndex);
        paths << infoList.at(pictureIndex).filePath();
        Setting::instance().defaults().setScreenSaverPreviewed(false);
    }

    show();

    // 2、设置声音状态
    ui->widget->setMute(Setting::instance().defaults().screenSaverIsMute());

    // 3、更新播放器列表并开始播放
    ui->widget->setPaths(paths);
    ui->widget->start(Setting::instance().defaults().screenSaverImageShowTime() * 1000);

    m_CursorPos = QCursor::pos();
}

void SeniorScreenSaverDialog::doQuit()
{
    ui->widget->stop();
    close();
}

SeniorScreenSaverDialog::~SeniorScreenSaverDialog()
{
    delete ui;
}

void SeniorScreenSaverDialog::onCursorMoveTimeout()
{
    QPoint pos = QCursor::pos() - m_CursorPos;
    if (pos.manhattanLength() > 3)
    {
        QMouseEvent event(QEvent::MouseMove, QCursor::pos(), Qt::LeftButton, Qt::LeftButton, Qt::NoModifier);
        QApplication::sendEvent(qApp, &event);
    }
}

void SeniorScreenSaverDialog::onfplayIsRuning()
{
    hide();
}

void SeniorScreenSaverDialog::onffplayIsEnded()
{
    show();
}
