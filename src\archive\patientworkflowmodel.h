#ifndef PATIENTWORKFLOWMODEL_H
#define PATIENTWORKFLOWMODEL_H
#include "archive_global.h"

#include <QObject>
class ArchiveModel;
class GlanceImagesModel;
class PatientWorkflow;
class PatientEditModel;
class GeneralWorkflowFunction;
class DicomTaskManager;
#ifdef USE_4D
class FourDCallBackModel;
#endif

class IToolsFacade;
class IStateManager;
class IDiskDevice;
class IColorMapManager;

class ARCHIVESHARED_EXPORT PatientWorkflowModel : public QObject
{
    Q_OBJECT
public:
    explicit PatientWorkflowModel(IStateManager* stateManager, QWidget* parent = 0);
    ~PatientWorkflowModel();
    void showArchiveDialog() const;
    void showPatientDialog() const;
    void showGlanceImages();
    void close();
    void setPatientWorkflow(PatientWorkflow* patientWorkflow);
    void setDicomTaskManager(DicomTaskManager* dicomTaskManager);
    void setToolsFacade(IToolsFacade* value);
    void setColorMapManager(IColorMapManager* colorMapManager);
    void setDiskDevice(IDiskDevice* diskDevice);
    PatientEditModel* patientEditModel() const;
    GlanceImagesModel* glanceImagesModel() const;
    ArchiveModel* archiveModel() const;
#ifdef USE_4D
    FourDCallBackModel* fourdCallBackModel() const;
#endif
    void setIsInEditExamState(bool value);
    bool isInEditExamState() const;
signals:
    void archiveDialogClosed();
    void patientDialogClosed(int result);
    void reviewDialogClosed();
    void onlyJump();
    /**
     * @brief entryArchiveState 由其他状态进入archive状态会发射该信号
     *
     * @return
     */
    void entryArchiveState();
    void entryPatientState();
    void entryBrowseState();
    void entryContinueExamState();
    void entryEditExamState();
    void autoCallBack(int index);

    void ntpButClicked(const QString& category, const QString& butName, const QString& value);
    void tpButClicked(const QString& category, const QString& butName, const QString& value);

#ifdef USE_4D
    void fourdCallBack(const QString&);
#endif
public slots:
    void onCreateNewExam();
    void onTPButClicked(const QString& category, const QString& butName, const QString& value);
private slots:
    void onEntryEditExamState();

private:
    ArchiveModel* m_Archive;
    PatientEditModel* m_Patient;
    GlanceImagesModel* m_Glance;
#ifdef USE_4D
    FourDCallBackModel* m_FourDCallBack;
#endif
    PatientWorkflow* m_PatientWorkflow;
    GeneralWorkflowFunction* m_GeneralWorkflowFunction;
    bool m_InEditExamState;
};

#endif // PATIENTWORKFLOWMODEL_H
