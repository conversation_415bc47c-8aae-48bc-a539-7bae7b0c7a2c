#ifndef IMAGEUNPROCESSEDCOPY_H
#define IMAGEUNPROCESSEDCOPY_H
#include "archive_global.h"

#include <QObject>
#include "basecopy.h"

class ARCHIVESHARED_EXPORT ImageUnprocessedCopy : public BaseCopy
{
    Q_OBJECT
public:
    explicit ImageUnprocessedCopy(QObject* parent = 0);
    virtual ~ImageUnprocessedCopy();
    virtual bool qCopyFile(const QString& src, const QString& dest);

signals:

public slots:
};

#endif // IMAGEUNPROCESSEDCOPY_H
