build-p9-job:
  stage: build
  needs: [pre-build-job]
  tags:
    - P9
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  script:
    - ./configure_unix_x86_64bit.sh -m P9
  cache:
    key: build-P9-cache
    paths:
      - build/P9
    policy: push

release-p9-job:
  stage: release
  tags:
    - P9
  rules:
    - if: $model == "P9"
  before_script:
    - ./ci/shell/getknown_hosts.sh
    - ./ci/shell/pullrelatedrep.sh -m P9 -r $ResourceBranch
  script:
    - ./ci/shell/runrelease.sh -m P9 -v $MainVersion -a $VersionAdd


