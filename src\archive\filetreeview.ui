<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FileTreeView</class>
 <widget class="QWidget" name="FileTreeView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>281</width>
    <height>429</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="horizontalSpacing">
    <number>6</number>
   </property>
   <property name="verticalSpacing">
    <number>0</number>
   </property>
   <property name="margin">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QPushButton" name="pushButtonBack">
     <property name="text">
      <string>back</string>
     </property>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="QPushButton" name="pushButtonMakeDir">
     <property name="text">
      <string>new dir</string>
     </property>
    </widget>
   </item>
   <item row="0" column="2">
    <widget class="QPushButton" name="pushButtonDelete">
     <property name="text">
      <string>delete</string>
     </property>
    </widget>
   </item>
   <item row="1" column="0" colspan="3">
    <widget class="FiliationTreeView" name="treeView"/>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>FiliationTreeView</class>
   <extends>QTreeView</extends>
   <header location="global">filiationtreeview.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
