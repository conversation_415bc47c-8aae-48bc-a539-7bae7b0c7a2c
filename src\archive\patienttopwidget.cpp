#include "patienttopwidget.h"
#include "ui_patienttopwidget.h"
#include "model/patient.h"
#include "resource.h"
#include "model/study.h"
#include "basedateedit.h"
#include "patientmanagement.h"
#include "patientsage.h"
#include "stringqueue.h"
#include "dataaccesslayerhelper.h"
#include "appsetting.h"
#include <QRegExpValidator>
#include "messageboxframe.h"
#include "dicomregister.h"
#include "PersonName.h"
#include "util.h"
#include "deleteitemdelegate.h"
#include "doublevalidator.h"
#include "formula.h"
#include "queryretrievewidget.h"
#include "uiutil.h"
#include <QScreen>

PatientTopWidget::PatientTopWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::PatientTopWidget)
    , m_Model(ScreenOrientationModel::getInstance())
    , m_WorkListSearchDialog(nullptr)
    , m_StateManager(NULL)
{
    ui->setupUi(this);

    QRegExp rx("^[0-9a-zA-Z_-]+$");
    QValidator* validator = new QRegExpValidator(rx, this);
    ui->lineEditID->setValidator(validator);
    ui->lineEditID->setMaxLength(20);

    QRegExp rxName("[^/:*?<>|\"\\\\^=]{1,64}$"); //根据windows文件夹命名方式禁用以下字符/:*?<>|"\^=.
    QValidator* validatorName = new QRegExpValidator(rxName, this);
    ui->lineEditName->setValidator(validatorName);
    ui->lineEditLastName->setValidator(validatorName);
    ui->lineEditMiddleName->setValidator(validatorName);

    if (AppSetting::isAnimal())
    {
        translateSpecies();

        //        DeleteItemDelegate *itemDelegate = new DeleteItemDelegate(this);
        //        ui->comboBoxDoctor->setItemDelegate(itemDelegate);
        //        connect(itemDelegate, SIGNAL(deleteItem(int)), this, SLOT(deleteItem(int)));
        //        ui->comboBoxDoctor->setShowDeleteAll(true);
        //        connect(ui->comboBoxDoctor, SIGNAL(deleteAllItem()), this, SLOT(deleteAllItem()));

        //        ui->comboBoxDoctor->setValidator(validatorName);
        //        ui->comboBoxDoctor->clear();
        //        ui->comboBoxDoctor->addItems(StringQueues::instance().refPhysician().items());
        ui->label_MiddleName->setVisible(false);
        ui->lineEditMiddleName->setVisible(false);
        ui->label_LastName->setText(tr("Host"));
        ui->label->setText(tr("Name"));
        if (AppSetting::isAnimalAndChinese())
        {
            ui->label_2->setText(tr("Animal ID"));
            ui->label->setText(tr("Animal Name"));
        }
        connect(ui->comboBoxSpecies, SIGNAL(currentIndexChanged(int)), this,
                SLOT(on_comboBoxSpecies_currentIndexChanged(int index)));
    }
    else
    {
        ui->labelSpecies->setVisible(false);
        ui->comboBoxSpecies->setVisible(false);
    }
    ui->labelDoctor->setVisible(false);
    ui->comboBoxDoctor->setVisible(false);

    initSet();
    translateSex();
    setAge();
    connect(ui->lineEditDateTime, SIGNAL(dateChanged(QDate)), this, SLOT(on_lineEditDateTime_dateChanged(QDate)));

    Util::disablePushButtonDefault(this);
}

PatientTopWidget::~PatientTopWidget()
{
    delete ui;
}

void PatientTopWidget::setPatient(Patient* patient)
{
    if (patient != NULL)
    {
        ui->lineEditID->setText(patient->PatientId());
        ui->lineEditName->setText(patient->firstName());
        ui->lineEditLastName->setText(patient->lastName());
        // For consistency when switching from human to VET
        // If the text of lineEditMiddleName is set only in human mode, the middle name will be lost in above case.
        ui->lineEditMiddleName->setText(patient->middleName());
        ui->comboBoxSex->setCurrentIndex(Resource::sexIndex(patient->PatientsSex()));
        ui->lineEditDateTime->setDate(patient->PatientsBirthDate().date());

        if (AppSetting::isAnimal())
        {
            ui->comboBoxSpecies->setCurrentIndex(Resource::speciesIndex(patient->Species()));

            //            if(patient->firstStudy() != NULL)
            //            {
            //                NameComponent name;
            //                name.setInternalPersonName(patient->firstStudy()->ReferringPhysiciansName());
            //                ui->comboBoxDoctor->setEditText(name.formattedName());
            //                setStudy(patient->firstStudy());
            //            }
        }

        PatientsAge::instance().setBirthDate(patient->PatientsBirthDate());
        setAge();
    }
}

void PatientTopWidget::setStateManager(IStateManager* value)
{
    m_StateManager = value;
}

void PatientTopWidget::flawlessPatient(Patient* patient)
{
    if (patient != NULL)
    {
        patient->setPatientsSex(Resource::getSex(ui->comboBoxSex->currentIndex()));
        patient->setPatientsBirthDate(ui->lineEditDateTime->getDate().startOfDay());
        patient->setPatientsBirthDateRaw(ui->lineEditDateTime->getDate().toString("yyyyMMdd"));
        patient->setPatientsAge(ui->lineEditAgeY->text().toInt());

        if (AppSetting::isAnimal())
        {
            patient->setSpecies(Resource::getSpecies(ui->comboBoxSpecies->currentIndex()));

            //            if(patient->firstStudy() != NULL)
            //            {
            //                patient->firstStudy()->setReferringPhysiciansName(ui->comboBoxDoctor->currentText().trimmed());
            //                flawlessStudy(patient->firstStudy());
            //            }

            //            if(StringQueues::instance().refPhysician().add(ui->comboBoxDoctor->currentText().trimmed()))
            //            {
            //                ui->comboBoxDoctor->clear();
            //                ui->comboBoxDoctor->addItems(StringQueues::instance().refPhysician().items());
            //            }
        }

        NameComponent nameCheck;
        QString truncatedName;

        truncatedName =
            nameCheck.getTruncatedName(ui->lineEditLastName->text().trimmed(), ui->lineEditName->text().trimmed(),
                                       ui->lineEditMiddleName->text().trimmed(), QString(), QString());
        patient->setPatientsName(truncatedName);
    }
}

void PatientTopWidget::flawlessStudy(Study* study)
{
    if (study != NULL)
    {
        //        study->setBSA(ui->lineEditBSA->text().toFloat());
        //        study->setWeight(ui->lineEditWeight->text().toFloat());
        //        study->setHR(ui->lineEditHR->text().toInt());
    }
}

void PatientTopWidget::setStudy(const Study* study)
{
    if (study != NULL)
    {
        //        ui->lineEditBSA->setText(QString::number(study->BSA(), 'f', 2));
        //        ui->lineEditWeight->setText(QString::number(study->Weight(), 'f', 2));
        //        ui->lineEditHR->setText(QString::number(study->HR()));
    }
}

QString PatientTopWidget::patientId() const
{
    return ui->lineEditID->text();
}

void PatientTopWidget::setPatientId(const QString& id)
{
    ui->lineEditID->setText(id);
}

void PatientTopWidget::createId()
{
    ui->lineEditID->setText(PatientManagement::newPatientId());
}

void PatientTopWidget::clearPatientInfo()
{
    ui->lineEditName->setText("");
    ui->lineEditLastName->setText("");
    ui->lineEditMiddleName->setText("");
    ui->comboBoxSex->setCurrentIndex(Resource::trSexes().count() - 1); // 修改打开新病人信息时性别为空
                                                                       //    ui->comboBoxDoctor->setEditText("");
    ui->lineEditDateTime->setDate(QDate());
    PatientsAge::instance().setBirthDate(ui->lineEditDateTime->getDate().startOfDay());
    setAge();
}

void PatientTopWidget::clearStudyInfo()
{
    //    if(AppSetting::isAnimal())
    //    {
    //        ui->comboBoxDoctor->clearEditText();
    //    }
}

void PatientTopWidget::setCurrentWidgetEnabled(bool enabled)
{
    ui->lineEditDateTime->setEnabled(enabled);
    ui->comboBoxSex->setEnabled(enabled);
    ui->lineEditID->setEnabled(enabled);
    ui->lineEditAgeM->setEnabled(enabled);
    ui->comboBoxSpecies->setEnabled(enabled);
    ui->lineEditAgeY->setEnabled(enabled);
    ui->lineEditName->setEnabled(enabled);
    ui->lineEditLastName->setEnabled(enabled);
    ui->lineEditMiddleName->setEnabled(enabled);
    //    ui->comboBoxDoctor->setEnabled(enabled);
    ui->pushButtonArchive->setEnabled(enabled);
    ui->pushButtonWorkList->setEnabled(enabled);
    ui->pushButtonQR->setEnabled(enabled);
}

bool PatientTopWidget::isNameLengthValid() const
{
    bool bRet = false;
    NameComponent nameCheck;
    int err = NameComponent::ERR_OK;

    err = nameCheck.setSplitNamesAlphabetic(ui->lineEditLastName->text().trimmed(), ui->lineEditName->text().trimmed(),
                                            ui->lineEditMiddleName->text().trimmed(), QString(), QString());

    if (NameComponent::ERR_OK == err)
    {
        bRet = true;
    }

    return bRet;
}

void PatientTopWidget::showWorklist()
{
    on_pushButtonWorkList_clicked();
}

QString PatientTopWidget::mppsDcmFileName() const
{
    return m_MPPSDcmFileName;
}

void PatientTopWidget::clearMPPSDcmFileName()
{
    m_MPPSDcmFileName.clear();
}

bool PatientTopWidget::isHor()
{
    return m_Model->isHor();
}

void PatientTopWidget::translateSex()
{
    QStringList tredSexes;
    foreach (QString sex, Resource::trSexes())
    {
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
        tredSexes.append(QApplication::translate("Sex", sex.toUtf8().data(), 0, QApplication::UnicodeUTF8));
#else
        tredSexes.append(QApplication::translate("Sex", sex.toUtf8().data()));
#endif
    }
    ui->comboBoxSex->clear();
    ui->comboBoxSex->insertItems(0, tredSexes);
}

void PatientTopWidget::translateSpecies()
{
    QStringList tredtrSpecies;
    foreach (QString spec, Resource::trSpecies())
    {
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
        tredtrSpecies.append(QApplication::translate("Sex", spec.toUtf8().data(), 0, QApplication::UnicodeUTF8));
#else
        tredtrSpecies.append(QApplication::translate("Sex", spec.toUtf8().data()));
#endif
    }
    ui->comboBoxSpecies->clear();
    ui->comboBoxSpecies->insertItems(0, tredtrSpecies);
}

void PatientTopWidget::initSet()
{
    ui->lineEditID->setText(PatientManagement::newPatientId());
    PatientsAge::instance().setBirthDate(ui->lineEditDateTime->getDate().startOfDay());
    ui->lineEditAgeY->setValidator(new QIntValidator(0, 150, this));
    ui->lineEditAgeM->setValidator(new QIntValidator(1, 11, this));
    QDate minDate = QDate::currentDate().addYears(-150).addMonths(-11);
    minDate.setDate(minDate.year(), minDate.month(), 1);
    ui->lineEditDateTime->setMinimumDate(minDate); //设置输入出生日期的最小值;
}

void PatientTopWidget::retranslateUi()
{
    ui->retranslateUi(this);
    if (AppSetting::isAnimal())
    {
        ui->label_LastName->setText(tr("Host"));
        ui->label->setText(tr("Name"));
        if (AppSetting::isAnimalAndChinese())
        {
            ui->label_2->setText(tr("Animal ID"));
            ui->label->setText(tr("Animal Name"));
        }
    }
    translateSex();
    translateSpecies();
}

void PatientTopWidget::orientationChanged(Qt::ScreenOrientation orientation)
{
    QString className(this->metaObject()->className());
    OrientationConfigItem config;
    m_Model->getInfosByClassName(orientation, className, config);
    UiUtil::ScreenHVChangedUpdateUI(this, ui->gridLayout_3, config);
}

void PatientTopWidget::calBsa(int index)
{
    //    ui->lineEditBSA->setText(QString::number(Formula::calAnimalBsa(index
    //                            ,ui->lineEditWeight->text().toFloat()),'f', 2));
}

void PatientTopWidget::setAge()
{
    ui->lineEditAgeY->setText(QString::number(PatientsAge::instance().ageYear()));
    ui->lineEditAgeM->setText(QString::number(PatientsAge::instance().ageMonth()));
}

void PatientTopWidget::setBirthDate()
{
    ui->lineEditDateTime->setDate(PatientsAge::instance().birthDate().date());
}

void PatientTopWidget::on_pushButtonArchive_clicked()
{
    emit clickedArchive();
}

void PatientTopWidget::on_lineEditID_textEdited(const QString& arg1)
{
    clearPatientInfo();
    if (DataAccessLayerHelper::isPatientExists(arg1))
    {
        Patient* patient = DataAccessLayerHelper::getPatient(arg1, false);
        setPatient(patient);
        delete patient;
    }

    if (PatientManagement::newPatientId() != arg1)
    {
        emit isNewPatientId(false);
    }
    else
    {
        emit isNewPatientId(true);
    }
}

void PatientTopWidget::on_lineEditAgeY_editingFinished()
{
    if (ui->lineEditAgeY->text().toInt() == PatientsAge::instance().ageYear())
    {
        return;
    }
    PatientsAge::instance().setAgeYear(ui->lineEditAgeY->text());
    setBirthDate();
}

void PatientTopWidget::on_lineEditAgeM_editingFinished()
{
    if (ui->lineEditAgeM->text().toInt() == PatientsAge::instance().ageMonth())
    {
        return;
    }
    PatientsAge::instance().setAgeMonth(ui->lineEditAgeM->text());
    setBirthDate();
}

void PatientTopWidget::on_lineEditID_editingFinished()
{
}

void PatientTopWidget::on_lineEditDateTime_dateChanged(const QDate& date)
{
    if (date == PatientsAge::instance().birthDate().date())
    {
        return;
    }
    PatientsAge::instance().setBirthDate(date.startOfDay());
    setAge();
}

void PatientTopWidget::on_pushButtonWorkList_clicked()
{
    if (DicomRegister::instance().checkIsRegistered())
    {
        if (m_WorkListSearchDialog == nullptr)
        {
            m_WorkListSearchDialog = new WorkListSearchDialog(m_StateManager, this);
            connect(m_WorkListSearchDialog, SIGNAL(getDicomResponse(DcmResponseInfo)), this,
                    SLOT(onResponse(DcmResponseInfo)));
        }
        m_WorkListSearchDialog->exec();
    }
}

void PatientTopWidget::onResponse(const DcmResponseInfo& tag)
{
    ui->lineEditID->setText(QString(tag.PatientID).trimmed());
    ui->lineEditID->setEnabled(false);
    NameComponent name;
    name.setInternalPersonName(QString(tag.PatientsName).trimmed());
    ui->lineEditLastName->setText(name.lastNameAlphabetic());
    ui->lineEditName->setText(name.firstNameAlphabetic());
    ui->lineEditMiddleName->setText(name.middleNameAlphabetic());
    ui->comboBoxSex->setCurrentIndex(Resource::sexIndex(QString(tag.PatientsSex).trimmed()));
    QDate date(QDate::fromString(QString(tag.PatientsBirthDate).trimmed(), "yyyyMMdd"));
    ui->lineEditDateTime->setDate(date);
    //    if(AppSetting::isAnimal())
    //    {
    //        ui->comboBoxDoctor->setEditText(QString(tag.PhysiciansName).trimmed());
    //    }
    //    else
    {
        name.setInternalPersonName(QString(tag.PhysiciansName).trimmed());
        emit physiciansNameSetted(name.formattedName());
        emit accessionSetted(QString(tag.AccessionNumber).trimmed());
    }

    PatientsAge::instance().setBirthDate(date.startOfDay());
    setAge();
    if (!tag.StudyInstanceID.isEmpty())
    {
        m_MPPSDcmFileName = Resource::dicomRspTempDir + tag.StudyInstanceID + Resource::dcmExt;
    }

    Patient* patient = nullptr;
    Study* study = nullptr;
    if (DataAccessLayerHelper::isPatientExists(tag.PatientID))
    {
        patient = DataAccessLayerHelper::getPatient(tag.PatientID);
    }
    else
    {
        patient = new Patient();
    }

    if (patient != nullptr)
    {
        QDateTime birthDate(QDate::fromString(tag.PatientsBirthDate, "yyyyMMdd").startOfDay());
        patient->setPatientId(QString(tag.PatientID).trimmed());
        patient->setPatientsName(tag.PatientsName);
        patient->setPatientsSex(QString(tag.PatientsSex).trimmed());
        patient->setPatientsBirthDate(birthDate);
        patient->setPatientsBirthDateRaw(tag.PatientsBirthDate);
        patient->setPatientsName(tag.PatientsName);
        PatientsAge patientsAge;
        patientsAge.setBirthDate(birthDate);
        patient->setPatientsAge(patientsAge.ageYear());
    }
    else
    {
        return;
    }

    QString studyOid = QString(tag.StudyInstanceID).replace(".", "-").trimmed();
    if (DataAccessLayerHelper::isStudyExists(studyOid))
    {
        study = DataAccessLayerHelper::getStudy(studyOid);
        study->setPatient(patient);
    }
    else
    {
        study = new Study(patient);
    }
    if (study != NULL)
    {
        study->setStudyOid(studyOid);
        study->setStudyInstanceUid(tag.StudyInstanceID);
        study->setReferringPhysiciansName(tag.PhysiciansName);
        study->setAccessionNumber(tag.AccessionNumber);
        study->setStudyDescription(tag.StudyDescription);
        study->setHeight(QString(tag.PatientSize).toFloat());
        study->setWeight(QString(tag.PatientWeight).toFloat());
        study->setStudyState(1);
        patient->Studies().append(study);
    }
    else
    {
        delete patient;
        return;
    }
    emit updateStudyInfo(patient);
    delete patient;
}

void PatientTopWidget::hideEvent(QHideEvent* e)
{
    ui->lineEditDateTime->hideChildren();
    BaseWidget::hideEvent(e);
}
void PatientTopWidget::deleteItem(const int& index)
{
    //    if(index < ui->comboBoxDoctor->count()
    //        && StringQueues::instance().refPhysician().remove(ui->comboBoxDoctor->itemText(index).trimmed()))
    //    {
    //        ui->comboBoxDoctor->removeItem(index);
    //    }
}

void PatientTopWidget::deleteAllItem()
{
    StringQueues::instance().refPhysician().clear();
}

void PatientTopWidget::on_comboBoxSpecies_currentIndexChanged(int index)
{
    emit animalSpeciesIndexChanged(index);
}

void PatientTopWidget::on_lineEditWeight_editingFinished()
{
}

void PatientTopWidget::on_lineEditWeight_textEdited(const QString& arg1)
{
}

void PatientTopWidget::on_pushButtonQR_clicked()
{
    if (DicomRegister::instance().checkIsRegistered())
    {
        QueryRetrieveDialog dlg(m_StateManager, this);
        dlg.clearWidget();
        dlg.exec();
    }
}

void PatientTopWidget::on_lineEditAgeY_textEdited(const QString& arg1)
{
    PatientsAge::instance().setAgeYear(ui->lineEditAgeY->text());
    setBirthDate();
}

void PatientTopWidget::on_lineEditAgeM_textEdited(const QString& arg1)
{
    if (arg1.toInt() > 11)
    {
        int addyear = arg1.toInt() / 12;
        int month = arg1.toInt() % 12;
        ui->lineEditAgeM->setText(QString::number(month));
        ui->lineEditAgeY->setText(QString::number(ui->lineEditAgeY->text().toInt() + addyear));
        PatientsAge::instance().setAgeYear(ui->lineEditAgeY->text());
    }
    PatientsAge::instance().setAgeMonth(ui->lineEditAgeM->text());
    setBirthDate();
}
