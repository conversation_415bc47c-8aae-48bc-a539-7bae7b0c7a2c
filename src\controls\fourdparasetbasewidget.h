#ifndef FOURDPARASETBASEWIDGET_H
#define FOURDPARASETBASEWIDGET_H
#include "controls_global.h"
#include "basewidget.h"
#include "fourdparasinfo.h"

class SonoParameters;
class CONTROLSSHARED_EXPORT FourDParaSetBaseWidget : public BaseWidget
{
    Q_OBJECT
public:
    FourDParaSetBaseWidget(QWidget* parent = 0);
    virtual ~FourDParaSetBaseWidget();
    virtual void initalize() = 0;
    void setSonoParameters(SonoParameters* sonoParameters);
    void setParametersIndex(int index);
    void setParametersMax(int max);
    void updateParameters();

protected:
    virtual void onSetSonoParameters();

protected:
    SonoParameters* m_sonoParameters;
    FourDParasInfo::ParaType m_ParaType;
    QString m_RelateParasName;
};

#endif // FOURDPARASETBASEWIDGET_H
