#include "fpsinfowidget.h"
#include "ui_fpsinfowidget.h"
#include "appsetting.h"
#include <QPainter>
#include "lineimagedatapalmreceiver.h"
#include <QTimer>
#include <QMouseEvent>

#define TOTALNAME "Package Images"

FpsInfoWidget::FpsInfoWidget(QWidget* parent)
    : BaseWidget(parent)
    , ui(new Ui::FpsInfoWidget)
    , m_Column(8)
    , m_ShowFpsInfo(false)
{
    ui->setupUi(this);
    setWindowFlags(windowFlags() | Qt::Tool | Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint);

    connect(&AppSettingEventObject::instance(), SIGNAL(presetModeChanged(bool)), this, SLOT(onPresetModeChanged(bool)));

    move(0, 0);

    QLabel* label = new QLabel;
    ui->gridLayout->addWidget(label, m_Values.count() / m_Column, m_Values.count() % m_Column);
    m_Values.insert(TOTALNAME, label);

    startShow();
}

FpsInfoWidget::~FpsInfoWidget()
{
    delete ui;
}

void FpsInfoWidget::onFpsChanged(const QString& name, const float fps)
{
    if (!m_ShowFpsInfo)
    {
        return;
    }
    QLabel* label = NULL;
    if (m_Values.contains(name))
    {
        label = m_Values.value(name);
    }
    else
    {
        QLabel* label = new QLabel;
        ui->gridLayout->addWidget(label, m_Values.count() / m_Column, m_Values.count() % m_Column);
        m_Values.insert(name, label);
    }
    if (label != NULL)
    {
        label->setText(QString("%1:  %2").arg(name).arg(QString::number(fps, 'f', 1)));
    }
}

void FpsInfoWidget::paintEvent(QPaintEvent* event)
{
    BaseWidget::paintEvent(event);
    QPainter p(this);
    p.setPen(Qt::NoPen);
    p.setBrush(Qt::black);
    p.drawRect(rect());
}

void FpsInfoWidget::setColumn(int column)
{
    m_Column = column;
}

void FpsInfoWidget::mousePressEvent(QMouseEvent* event)
{
    if (event->buttons() & Qt::LeftButton)
    {
        m_PressedPt = event->globalPos() - this->geometry().topLeft();
    }
}

void FpsInfoWidget::mouseMoveEvent(QMouseEvent* event)
{
    if (event->buttons() & Qt::LeftButton)
    {
        move(event->globalPos() - m_PressedPt);
    }
}

void FpsInfoWidget::onPresetModeChanged(bool preset)
{
    if (preset)
    {
        if (!m_ShowFpsInfo)
        {
            show();
        }
        m_ShowFpsInfo = true;
    }
    else
    {
        if (m_ShowFpsInfo)
        {
            hide();
        }
        m_ShowFpsInfo = false;
    }
}

void FpsInfoWidget::startShow()
{
    QTimer::singleShot(1000, this, SLOT(doShow()));
}

void FpsInfoWidget::doShow()
{
    if (m_ShowFpsInfo)
    {
        QLabel* label = NULL;
        label = m_Values.value(TOTALNAME);
        if (label != NULL)
        {
#ifdef USE_PREPROCESS
            label->setText(QString("%1: %2  BL: %3  BP: %4  CL: %5  CP: %6  WL: %7  WP: %8 ")
                               .arg(TOTALNAME, QString::number(ImageDataReceiverBase::m_LineTypeStatistic.value(-1)),
                                    QString::number(ImageDataReceiverBase::m_BLines),
                                    QString::number(ImageDataReceiverBase::m_BPoints),
                                    QString::number(ImageDataReceiverBase::m_CLines),
                                    QString::number(ImageDataReceiverBase::m_CPoints),
                                    QString::number(ImageDataReceiverBase::m_WaveLineNum),
                                    QString::number(ImageDataReceiverBase::m_WavePoints)));
            ImageDataReceiverBase::m_LineTypeStatistic[-1] = 0;
#endif
        }
    }
    startShow();
}

void FpsInfoWidget::retranslateUi()
{
}
