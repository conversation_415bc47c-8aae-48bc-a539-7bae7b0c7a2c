#include "inputunitgroupwidget.h"
#include "ui_inputunitgroupwidget.h"
#include "measurementdef.h"
#include "doublevalidator.h"
#include "measurementtext.h"
#include "math.h"
#include <QLineEdit>

InputUnitGroupWidget::InputUnitGroupWidget(QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::InputUnitGroupWidget)
{
    ui->setupUi(this);
}

InputUnitGroupWidget::~InputUnitGroupWidget()
{
    delete ui;
}

void InputUnitGroupWidget::setEditEnabled(const bool enabled)
{
    ui->lineEditValue->setEnabled(enabled);
}

void InputUnitGroupWidget::setEditText(const QString& textValue)
{
    ui->lineEditValue->setText(textValue);
}

QString InputUnitGroupWidget::curEditText()
{
    return ui->lineEditValue->text();
}

double InputUnitGroupWidget::curValue()
{
    return ui->lineEditValue->text().toDouble();
}

void InputUnitGroupWidget::setUnitName(QString unitName)
{
    ui->labelUnit->setText(unitName);
}

void InputUnitGroupWidget::setValue(QVariant value, const double maxLimit)
{
    ui->lineEditValue->blockSignals(true);
    switch (value.type())
    {
    case QVariant::Int:
        ui->lineEditValue->setValidator(new QIntValidator(0, floor(maxLimit), this));
        ui->lineEditValue->setText(QString::number(floor(value.toInt())));
        break;
    case QVariant::Double:
        ui->lineEditValue->setValidator(new DoubleValidator(0, maxLimit, 2, this));
        ui->lineEditValue->setText(QString::number(value.toFloat(), 'f', 2));
        break;
    default:
        break;
    }
    ui->lineEditValue->blockSignals(false);
}

void InputUnitGroupWidget::setUnitMinWidth(const int width)
{
    if (width > 0)
    {
        ui->labelUnit->setMinimumWidth(width);
    }
}

void InputUnitGroupWidget::on_lineEditValue_editingFinished()
{
    emit textEdited();
}

void InputUnitGroupWidget::on_lineEditValue_textEdited(const QString& arg1)
{
    Q_UNUSED(arg1)
    int pos = ui->lineEditValue->cursorPosition();
    emit textEdited();
    ui->lineEditValue->setCursorPosition(pos);
}
