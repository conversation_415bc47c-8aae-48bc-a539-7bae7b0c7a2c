#ifndef PROGRESSBARVIEW_H
#define PROGRESSBARVIEW_H
#include "archive_global.h"

#include <QObject>

class QWidget;
class QPoint;
class QSize;
class ProgressBarFrame;
class IStateManager;
class ARCHIVESHARED_EXPORT ProgressBarView : public QObject
{
    Q_OBJECT
public:
    explicit ProgressBarView(IStateManager* value, QWidget* parent = 0);
    ~ProgressBarView();
    void setText(const QString& value);
    void setBusyIndicative(bool value);
    void setCancelButtonVisible(bool value);
    void move(const QPoint& point);
    QSize size() const;
    QWidget* widget() const;

protected:
    ProgressBarFrame* progressBar(QWidget* widget = 0);
signals:
    void cancelButtonClicked();
public slots:
    void showProgressBar();
    void execProgressBar();
    void hideProgressBar();
    void setProgressBarText(const QString& text);
    void setProgressBarTaskInfoForText(const QString& text);
    void setProgressBarTextVisible(bool bValue);
    void updateProgressBarValue(qint64 value);
    void updateProgressBar(qint64 value);

private:
    ProgressBarFrame* m_ProgressBar;
    IStateManager* m_StateManager;
};

#endif // PROGRESSBARVIEW_H
