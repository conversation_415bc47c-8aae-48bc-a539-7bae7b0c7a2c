#ifndef DATABASEVIEWMODEL_H
#define DATABASEVIEWMODEL_H
#include "archive_global.h"
#include <QSqlQueryModel>
#include <QIcon>
#include <QFileInfoList>
//#include "studydbbridge.h"
#include "querycriteria.h"
#include "PersonName.h"

/**
 * @brief 读取数据库信息的基类，被patient模式和study模式的类继承
 */
class ARCHIVESHARED_EXPORT DataBaseViewModel : public QSqlQueryModel
{
    Q_OBJECT
public:
    DataBaseViewModel(QObject* parent = 0);
    ~DataBaseViewModel();
    /**
     * @brief data 病人检查信息
     * @param index
     * @param role
     * @return
     */
    virtual QVariant data(const QModelIndex& index, int role) const;
    /**
     * @brief sort 排序
     *
     * @param column
     * @param order
     */
    virtual void sort(int column, Qt::SortOrder order);
    /**
     * @brief sortString 排序的后缀为patient或者study，因为patient和study存放在不同的
     *      表中
     * @param key
     * @param order
     * @param constStr
     */
    void sortString(const QString& key, const QString& order, QString constStr = QString("Patient."));
    void setHeaderDatas();
    /**
     * @brief baseQuery 初始化
     */
    void baseQuery();
    /**
     * @brief searchSQl 只可以搜索patientId、patientsName中的一个,  studyTime的时间段
     *  其他的搜索不支持
     * @param criteria
     * @return
     */
    void searchSql(const QueryCriteria& criteria);
    /**
     * @brief updateQuery 刷新
     */
    void updateQuery();
    /**
     * @brief fileInfo 根据选择的item，提供QFileInfo
     *
     * @param index
     *
     * @return
     */
    virtual QFileInfo fileInfo(const QModelIndex& index);
    /**
     * @brief filePath 直接返回路径名
     * @param index
     * @return
     */
    virtual QString filePath(const QModelIndex& index);
    /**
     * @brief deletePatient 删除一个patient
     *
     * @param fileInfoList
     */
    virtual void deletePatient(const QFileInfoList& fileInfoList);
    /**
     * @brief deleteStudy 删除一个study
     * @param fileInfoList
     */
    virtual void deleteStudy(const QFileInfoList& fileInfoList);
    virtual QString patientId(const QModelIndex& index);
    virtual QString studyOid(const QModelIndex& index);
    static QString sqlDateFormat();
    static QString sqlString();
    void setDisk(const QString& dirPath);
    QString getDisk() const;

protected:
    QSqlQuery getQuery();
    QString dateTimeToStr(const QDateTime& dateTime);

private:
    QString setupNameQuery(const QueryCriteria& criteria);
    QString setupNameQueryCN(const QueryCriteria& criteria);
    QString setupNameQueryEN(const QueryCriteria& criteria);
    void appendNameQueryHead(QString& nameQuery);
    void appendNameQueryAND(QString& nameQuery);
    void appendNameQueryTail(QString& nameQuery);

signals:
    void sortChanged();
    void update();
    void selectionCleared();

protected:
    QIcon m_FolderIcon;
    QString m_Disk;
    QString m_QueryString;
    QString m_QueryStringHead;
    QString m_QueryStringBase;
};

#endif // DATABASEVIEWMODEL_H
