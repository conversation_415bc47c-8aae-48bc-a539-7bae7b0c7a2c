#include "sonocarotidguidealg.h"

SonoCarotidGuideAlg::SonoCarotidGuideAlg()
    : VirtualAlg()
{
    m_Lib.setFileName("SonoCarotidGuide64");
    m_CreateContext = NULL;
    m_RemoveContext = NULL;
    m_Process = NULL;
}

int SonoCarotidGuideAlg::CreateSonoCarotidGuideHandle(SonoCarotidGuideHandle* pSonoCarotidGuideHandle,
                                                      InitInfo* pInitInfo)
{
    m_BeginInit = true;
    if (m_CreateContext == NULL)
    {
        m_CreateContext = (createSonoCarotidGuideHandle)m_Lib.resolve("CreateSonoCarotidGuideHandle");
    }
    if (m_CreateContext == NULL)
    {
        m_Initialized = false;
        m_IsError = true;
        return ERROR_CODE_UNKNOWN;
    }
    m_CreateContext(pSonoCarotidGuideHandle, pInitInfo);
    m_Initialized = true;
    m_BeginInit = false;
    return ERROR_CODE_OK;
}

int SonoCarotidGuideAlg::ProcessSonoCarotidGuide(SonoCarotidGuideHandle hSonoCarotidGuideHandle,
                                                 CAROTID_VIEW_TYPE nCarotidViewType, int nImageWidth, int nImageHeight,
                                                 int nImageDataSize, unsigned char* pImageData,
                                                 ImageRoiRect* pImageRoiRect, CarotidGuideResult* pCarotidGuideResult)
{
    if (m_Process == NULL)
    {
        m_Process = (processSonoCarotidGuide)m_Lib.resolve("ProcessSonoCarotidGuide");
    }
    if (m_Process == NULL || !m_Initialized)
    {
        m_IsError = true;
        return ERROR_CODE_UNKNOWN;
    }
    if (nImageWidth != m_StInitInfo.nImageWidth || nImageHeight != m_StInitInfo.nImageHeight)
    {
        return ERROR_CODE_UNKNOWN;
    }
    m_Process(hSonoCarotidGuideHandle, nCarotidViewType, nImageWidth, nImageHeight, nImageDataSize, pImageData,
              pImageRoiRect, pCarotidGuideResult);
    return ERROR_CODE_OK;
}

int SonoCarotidGuideAlg::ReleaseSonoCarotidGuideHandle(SonoCarotidGuideHandle* pSonoCarotidGuideHandle)
{
    if (m_RemoveContext == NULL)
    {
        m_RemoveContext = (releaseSonoCarotidGuideHandle)m_Lib.resolve("ReleaseSonoCarotidGuideHandle");
    }
    if (m_RemoveContext == NULL || !m_Initialized)
    {
        m_IsError = true;
        return ERROR_CODE_UNKNOWN;
    }
    m_Initialized = false;
    m_RemoveContext(pSonoCarotidGuideHandle);
    return ERROR_CODE_OK;
}
