<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>BluetoothStorageFilleWidget</class>
 <widget class="QWidget" name="BluetoothStorageFilleWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>547</width>
    <height>405</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout" columnstretch="2,2,0,3,0,0">
   <item row="4" column="1" colspan="2">
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>168</width>
       <height>256</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="0" column="0">
    <widget class="QLabel" name="label">
     <property name="text">
      <string>Bluetooth Device</string>
     </property>
    </widget>
   </item>
   <item row="0" column="1" colspan="5">
    <widget class="BaseComboBox" name="comboBoxBluetoothDevices"/>
   </item>
   <item row="1" column="3">
    <widget class="QCheckBox" name="checkBoxVideo">
     <property name="text">
      <string>Cine transform to:</string>
     </property>
    </widget>
   </item>
   <item row="1" column="1">
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QRadioButton" name="radioButtonBMP">
       <property name="text">
        <string>BMP</string>
       </property>
       <property name="checked">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QRadioButton" name="radioButtonPNG">
       <property name="text">
        <string>PNG</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QRadioButton" name="radioButtonJPG">
       <property name="text">
        <string>JPG</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="5" column="0" colspan="6">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QPushButton" name="pushButtonExport">
       <property name="maximumSize">
        <size>
         <width>120</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="text">
        <string>Export</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButtonClose">
       <property name="maximumSize">
        <size>
         <width>120</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="text">
        <string>Close</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="4">
    <widget class="BaseComboBox" name="comboBox_2">
     <item>
      <property name="text">
       <string>AVI</string>
      </property>
     </item>
     <item>
      <property name="text">
       <string>MP4</string>
      </property>
     </item>
    </widget>
   </item>
   <item row="2" column="1">
    <widget class="QCheckBox" name="checkBoxGDPR">
     <property name="text">
      <string>De-Identification</string>
     </property>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QLabel" name="label_2">
     <property name="text">
      <string>Image Type</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BaseComboBox</class>
   <extends>QComboBox</extends>
   <header>basecombobox.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
