#include "sonoavalg.h"

SonoAVAlg::SonoAVAlg()
    : VirtualAlg()
{
    m_Lib.setFileName("vasdet");
    m_CarotidInit = false;
    m_ArmInit = false;
    m_AxillaryInit = false;
    m_VesselInit = false;
}

void SonoAVAlg::vasdetModelPathSet(char* path)
{
    if (m_VasdetModelPathSet == NULL)
    {
        m_VasdetModelPathSet = (VasdetModelPathSet)m_Lib.resolve("vasdet_model_path_set");
    }
    if (m_VasdetModelPathSet == NULL)
    {
        m_IsError = true;
        return;
    }
    m_VasdetModelPathSet(path);
}

bool SonoAVAlg::compareRectangle(int x1, int y1, int width1, int height1, int x2, int y2, int width2, int height2,
                                 float compare_iou_thre)
{
    if (m_CompareRectangle == NULL)
    {
        m_CompareRectangle = (CompareRectangle)m_Lib.resolve("compareRectangle");
    }
    if (m_CompareRectangle == NULL)
    {
        m_IsError = true;
        return false;
    }
    return m_CompareRectangle(x1, y1, width1, height1, x2, y2, width2, height2, compare_iou_thre);
}

int SonoAVAlg::carotidVesselDetInit(float conf, float params[])
{
    if (m_CarotidVesselDetInit == NULL)
    {
        m_CarotidVesselDetInit = (CarotidVesselDetInit)m_Lib.resolve("carotid_vessel_det_init");
    }
    if (m_CarotidVesselDetInit == NULL)
    {
        m_IsError = true;
        return false;
    }
    int res = m_CarotidVesselDetInit(conf, params);
    m_CarotidInit = true;
    return res;
}

int SonoAVAlg::carotidVesselDet(uint8_t* imgdata, int width, int height, int roix, int roiy, int roiwidth,
                                int roiheight, AV::OBJ* objsp)
{
    if (m_CarotidVesselDet == NULL)
    {
        m_CarotidVesselDet = (CarotidVesselDet)m_Lib.resolve("carotid_vessel_det");
    }
    if (m_CarotidVesselDet == NULL || !m_CarotidInit)
    {
        return 0;
    }
    return m_CarotidVesselDet(imgdata, width, height, roix, roiy, roiwidth, roiheight, objsp);
}

void SonoAVAlg::carotidVesselDetRelease()
{
    if (m_CarotidVesselDetRelease == NULL)
    {
        m_CarotidVesselDetRelease = (CarotidVesselDetRelease)m_Lib.resolve("carotid_vessel_det_release");
    }
    m_CarotidInit = false;
    m_CarotidVesselDetRelease();
}

int SonoAVAlg::armVesselDetInit(float conf, float params[])
{
    if (m_ArmVesselDetInit == NULL)
    {
        m_ArmVesselDetInit = (ArmVesselDetInit)m_Lib.resolve("arm_vessel_det_init");
    }
    if (m_ArmVesselDetInit == NULL)
    {
        return 1;
    }
    int res = m_ArmVesselDetInit(conf, params);
    m_ArmInit = true;
    return res;
}

int SonoAVAlg::armVesselDet(uint8_t* imgdata, int width, int height, int roix, int roiy, int roiwidth, int roiheight,
                            AV::OBJ* objsp)
{
    if (m_ArmVesselDet == NULL)
    {
        m_ArmVesselDet = (ArmVesselDet)m_Lib.resolve("arm_vessel_det");
    }
    if (m_ArmVesselDet == NULL || !m_ArmInit)
    {
        return 0;
    }
    return m_ArmVesselDet(imgdata, width, height, roix, roiy, roiwidth, roiheight, objsp);
}

void SonoAVAlg::armVesselDetRelease()
{
    if (m_ArmVesselDetRelease == NULL)
    {
        m_ArmVesselDetRelease = (ArmVesselDetRelease)m_Lib.resolve("arm_vessel_det_release");
    }
    m_ArmInit = false;
    m_ArmVesselDetRelease();
}

int SonoAVAlg::axillaryVesselDetInit(float conf, float params[])
{
    if (m_AxillaryVesselDetInit == NULL)
    {
        m_AxillaryVesselDetInit = (AxillaryVesselDetInit)m_Lib.resolve("axillary_vessel_det_init");
    }
    if (m_AxillaryVesselDetInit == NULL)
    {
        return 1;
    }
    int res = m_AxillaryVesselDetInit(conf, params);
    m_AxillaryInit = true;
    return res;
}

int SonoAVAlg::axillaryVesselDet(uint8_t* imgdata, int width, int height, int roix, int roiy, int roiwidth,
                                 int roiheight, AV::OBJ* objsp)
{
    if (m_AxillaryVesselDet == NULL)
    {
        m_AxillaryVesselDet = (AxillaryVesselDet)m_Lib.resolve("axillary_vessel_det");
    }
    if (m_AxillaryVesselDet == NULL || !m_AxillaryInit)
    {
        return 0;
    }
    return m_AxillaryVesselDet(imgdata, width, height, roix, roiy, roiwidth, roiheight, objsp);
}

void SonoAVAlg::axillaryVesselDetRelease()
{
    if (m_AxillaryVesselDetRelease == NULL)
    {
        m_AxillaryVesselDetRelease = (AxillaryVesselDetRelease)m_Lib.resolve("axillary_vessel_det_release");
    }
    m_AxillaryInit = false;
    m_AxillaryVesselDetRelease();
}

int SonoAVAlg::vesselPwClsInit(float conf, float params[])
{
    if (m_VesselPwClsInit == NULL)
    {
        m_VesselPwClsInit = (VesselPwClsInit)m_Lib.resolve("vessel_pw_cls_init");
    }
    if (m_VesselPwClsInit == NULL)
    {
        return 1;
    }
    int res = m_VesselPwClsInit(conf, params);
    m_VesselInit = true;
    return res;
}

int SonoAVAlg::vesselPwCls(uint8_t* imgdata, int width, int height, float* probability)
{
    if (m_VesselPwCls == NULL)
    {
        m_VesselPwCls = (VesselPwCls)m_Lib.resolve("vessel_pw_cls");
    }
    if (m_VesselPwCls == NULL || !m_VesselInit)
    {
        return -1;
    }
    return m_VesselPwCls(imgdata, width, height, probability);
}

void SonoAVAlg::vesselPwClsRelease()
{
    if (m_VesselPwClsRelease == NULL)
    {
        m_VesselPwClsRelease = (VesselPwClsRelease)m_Lib.resolve("vessel_pw_cls_release");
    }
    m_VesselInit = false;
    m_VesselPwClsRelease();
}

bool SonoAVAlg::carotidInit() const
{
    return m_CarotidInit;
}

bool SonoAVAlg::ArmInit() const
{
    return m_ArmInit;
}

bool SonoAVAlg::AxillaryInit() const
{
    return m_AxillaryInit;
}

bool SonoAVAlg::VesselInit() const
{
    return m_VesselInit;
}
