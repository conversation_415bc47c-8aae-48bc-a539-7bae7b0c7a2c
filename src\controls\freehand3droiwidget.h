#ifndef FREEHAND3DROIWIDGET_H
#define FREEHAND3DROIWIDGET_H

#include "presetmodel_global.h"
#include "basewidget.h"

class ImageWidget;
class SonoParameters;

class PRESETMODELSHARED_EXPORT FreeHand3DRoiWidget : public BaseWidget
{
    Q_OBJECT

public:
    explicit FreeHand3DRoiWidget(QWidget* parent = 0);
    ~FreeHand3DRoiWidget();

protected:
    void retranslateUi();
    void paintEvent(QPaintEvent* event);
public slots:
    void onFreeHand3DRoiOnChanged(const QVariant& value);
    void onBeforecurSonoParametersChanged();
    void onCurSonoParametersChanged();
    void onFreezeChanged(const QVariant& value);

private slots:
    void onRightButtonReleased();
    void leftButtonPressed(const QPoint& pos);
    void movedAction(const QPoint& offset, const QPoint& pos);

private:
    void changeMouseAction(bool changeRoi = true);

private:
    ImageWidget* m_ImageWidget;
    SonoParameters* m_SonoParameters;
    bool m_ShowFreeHand3DRoiWidget;
    bool m_UpdateFreeHand3DRoi;
    bool m_SolidLine;
    QSize m_ImageSize;
    QSize m_RoiMinSize;
    QSize m_RoiSize;
    QPoint m_RoiPos;
    int m_OldMoveBitSet;
};

#endif // FREEHAND3DROIWIDGET_H
