#ifndef DATABACKUPWIDGET_H
#define DATABACKUPWIDGET_H
#include "archive_global.h"

#include <QWidget>
#include "basewidget.h"
#include "baseinputabledialogframe.h"

/**
 * @brief 备份文件时的提示界面
 */
class DataBackupWidget;
class IDiskDevice;
class ARCHIVESHARED_EXPORT DataBackupDialog : public BaseInputAbleDialogFrame
{
    Q_OBJECT
public:
    explicit DataBackupDialog(IDiskDevice* diskDevice, QWidget* parent = 0);
    DataBackupWidget* childDialog();
    bool isUDiskMounted() const;

protected:
signals:
    void clickOK(const QString& discName);

private:
    DataBackupWidget* m_Child;
};

namespace Ui
{
class DataBackupWidget;
}

class ARCHIVESHARED_EXPORT DataBackupWidget : public BaseWidget // modifed from QWidget to BaseWidget by jyq
{
    Q_OBJECT
public:
    explicit DataBackupWidget(IDiskDevice* diskDevice, QWidget* parent = 0);
    ~DataBackupWidget();
    bool isUDiskMounted() const;

protected:
    void retranslateUi();
signals:
    void clickOK(const QString& discName);
    void exit();
private slots:
    void on_pushButtonOK_clicked();
    void on_pushButtonCancel_clicked();

private:
    Ui::DataBackupWidget* ui;
};

#endif // DATABACKUPWIDGET_H
