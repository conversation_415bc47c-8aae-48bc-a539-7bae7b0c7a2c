#include "dicomseverinfowidget.h"
#include "ui_dicomseverinfowidget.h"
#include "messageboxframe.h"
#include "generalworkflowfunction.h"
#include <QSettings>
#include "globaldef.h"
#include "dicomprinttool.h"
#include "dicomstoragetool.h"
#include "dicomtaskmanager.h"
#include "exportfilewidget.h"
#include <QVariant>
#include <QStringList>
#include <QDir>
#include <QFileInfo>
#include "dicommodel.h"
#include "dicomserverwidget.h"
#include "networkstoragefilewidget.h"
#include "resource.h"
#include "bluetoothstoragefilewidget.h"

DicomSeverInfoDialog::DicomSeverInfoDialog(IStateManager* value, IColorMapManager* colorMapManager,
                                           IDiskDevice* diskDevice, QWidget* parent)
    : BaseInputAbleDialogFrame(parent)
    , m_Child(NULL)
{
    setIsShowOk(false);
    m_Child = new DicomSeverInfoWidget(value, colorMapManager, diskDevice, this);
    connect(m_<PERSON>, SIGNAL(accept()), this, SLOT(accept()));
    connect(m_Child, SIGNAL(deleteCurrentSelections()), this, SIGNAL(deleteCurrentSelections()));
    this->setContent(m_Child);
    this->adjustSize();
}

void DicomSeverInfoDialog::setDefaultDirName(const QString& name)
{
    m_Child->setDefaultDirName(name);
}

void DicomSeverInfoDialog::setExportFilePaths(const QStringList& list)
{
    m_Child->setExportFilePaths(list);
}

void DicomSeverInfoDialog::setIsCurrentMultiChoice(bool value)
{
    m_Child->setIsCurrentMultiChoice(value);
}

void DicomSeverInfoDialog::setExprotFileNames(const QStringList& list)
{
    m_Child->setExprotFileNames(list);
}

void DicomSeverInfoDialog::setDicomTaskManager(DicomTaskManager* manager)
{
    m_Child->setDicomTaskManager(manager);
}

void DicomSeverInfoDialog::setDeleteFilesController(DeleteFilesController* controller)
{
    m_Child->setDeleteFilesController(controller);
}

void DicomSeverInfoDialog::setDeleteAfterSendExam(bool flag)
{
    m_Child->setDeleteAfterSendExam(flag);
}

void DicomSeverInfoDialog::setIsSendSR(bool value)
{
    m_Child->setIsSendSR(value);
}

// void DicomSeverInfoDialog::setPatient(Patient *p)
//{
//    m_Child->setPatient(p);
//}

void DicomSeverInfoDialog::initWidegt()
{
    m_Child->initWidegt();
}

void DicomSeverInfoDialog::reject()
{
    if (!m_Child->isWorking())
    {
        QDialog::reject();
    }
    else
    {
        MessageBoxFrame::tipWarning(this, QString(), tr("Background task is running, please wait for a moment."));
    }
}

DicomSeverInfoWidget::DicomSeverInfoWidget(IStateManager* value, IColorMapManager* colorMapManager,
                                           IDiskDevice* diskDevice, QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::DicomSeverInfoWidget)
    , m_FileNames(QStringList())
    , m_DicomTaskManager(NULL)
    , m_DeleteFilesController(NULL)
    , m_DicomModel(NULL)
    , m_NetworkStorageWidget(NULL)
    , m_ExportWidget(NULL)
    , m_isCurrentMultiChoice(false)
    , m_deleteAfterSendExam(true)
    , m_IsSendSR(false)
    , m_StateManager(value)
    , m_ColorMapManager(colorMapManager)
    , m_DiskDevice(diskDevice)
{
    ui->setupUi(this);
}

void DicomSeverInfoWidget::setDefaultDirName(const QString& name)
{
    m_DefaultDirName = name;
}

void DicomSeverInfoWidget::setExportFilePaths(const QStringList& list)
{
    m_ExportFilePathes = list;
}

void DicomSeverInfoWidget::setExprotFileNames(const QStringList& list)
{
    m_FileNames = list;
}

void DicomSeverInfoWidget::setDicomTaskManager(DicomTaskManager* manager)
{
    m_DicomTaskManager = manager;
}

void DicomSeverInfoWidget::setDeleteFilesController(DeleteFilesController* controller)
{
    m_DeleteFilesController = controller;
}

void DicomSeverInfoWidget::setIsCurrentMultiChoice(bool value)
{
    m_isCurrentMultiChoice = value;
}

void DicomSeverInfoWidget::setDeleteAfterSendExam(bool flag)
{
    m_deleteAfterSendExam = flag;
}

void DicomSeverInfoWidget::setIsSendSR(bool value)
{
    m_IsSendSR = value;
}

// void DicomSeverInfoWidget::setPatient(Patient *p)
//{
//    m_Patient = p;
//}

DicomSeverInfoWidget::~DicomSeverInfoWidget()
{
    if (m_DicomModel != NULL)
    {
        delete m_DicomModel;
    }
    delete ui;
}

void DicomSeverInfoWidget::resizeEvent(QResizeEvent* e)
{
    QWidget::resizeEvent(e);
}

void DicomSeverInfoWidget::initWidegt()
{
    ui->listWidget->clear();
    bool pflag = false, sflag = false, uflag = false, nflag = false, srflag = false, bflag = false;
    bool containRetrieveExam = false;
    foreach (QString path, m_ExportFilePathes)
    {
        if (QFileInfo(path).baseName().startsWith(Resource::dicomRetrievePrefix))
        {
            containRetrieveExam = true;
            break;
        }
    }
    GeneralWorkflowFunction::hasUsableDevice(this, &pflag, &sflag, &uflag, &nflag, &bflag, &srflag);
    if (!m_IsSendSR)
    {
        srflag = false;
    }
    if (pflag || sflag || srflag)
    {
        if (m_DicomModel != NULL)
        {
            delete m_DicomModel;
            m_DicomModel = NULL;
        }
        m_DicomModel = new DicomModel(m_DicomTaskManager, m_FileNames, m_ExportFilePathes);
    }
    if (uflag)
    {
        static const QString str(QT_TR_NOOP("UDisk")); // added by  jyq to translate
        m_ExportWidget = new ExportFileWidget(m_StateManager, m_ColorMapManager, m_DiskDevice, this);
        connect(m_ExportWidget, SIGNAL(closed()), this, SIGNAL(accept()));
        connect(m_ExportWidget, SIGNAL(deleteCurrentSelections()), this, SIGNAL(deleteCurrentSelections()));
        m_ExportWidget->setDefaultDirName(m_DefaultDirName);
        m_ExportWidget->setExportFilePaths(m_ExportFilePathes);
        m_ExportWidget->setDeleteAfterSendExam(m_deleteAfterSendExam);
        m_ExportWidget->setIsCurrentMultiChoice(m_isCurrentMultiChoice);
        m_ExportWidget->setDicomTaskManager(m_DicomTaskManager);
        m_ExportWidget->setDeleteFilesController(m_DeleteFilesController);
        m_ExportWidget->showDcmSRCheckBox(m_IsSendSR);
        m_ExportWidget->setContainRetrieveExam(containRetrieveExam);
        ui->listWidget->addItem(tr(str.toUtf8()));
        ui->stackedWidget->addWidget(m_ExportWidget);
    }
    if (pflag)
    {
        static const QString str(QT_TR_NOOP("DicomPrint")); // added by  jyq to translate
                                                            //        DicomPrintWidget *w = new DicomPrintWidget(this);
        DicomServerWidget* print = new DicomServerWidget(DicomChison::print, this);
        connect(print, SIGNAL(closed()), this, SIGNAL(accept()));
        print->setModel(m_DicomModel);
        print->setDeleteFilesController(m_DeleteFilesController);
        ui->listWidget->addItem(tr(str.toUtf8()));
        ui->stackedWidget->addWidget(print);
    }
    if (sflag)
    {
        static const QString str(QT_TR_NOOP("DicomStorage")); // added by  jyq to translate
        //        DicomStorageWidget *w = new DicomStorageWidget(this);
        DicomServerWidget* storage = new DicomServerWidget(DicomChison::storage, this);
        connect(storage, SIGNAL(closed()), this, SIGNAL(accept()));
        storage->setModel(m_DicomModel);
        storage->setDeleteFilesController(m_DeleteFilesController);
        ui->listWidget->addItem(tr(str.toUtf8()));
        ui->stackedWidget->addWidget(storage);
    }
    if (srflag)
    {
        static const QString str(QT_TR_NOOP("DicomSR")); // added by  jyq to translate
                                                         //        DicomStorageWidget *w = new DicomStorageWidget(this);
        DicomServerWidget* sr = new DicomServerWidget(DicomChison::sr, this);
        connect(sr, SIGNAL(closed()), this, SIGNAL(accept()));
        sr->setModel(m_DicomModel);
        sr->setDeleteFilesController(m_DeleteFilesController);
        sr->modifyWarningInfo(tr("There are no measurement results for DICOM SR!"));
        ui->listWidget->addItem(tr(str.toUtf8()));
        ui->stackedWidget->addWidget(sr);
    }
    if (nflag)
    {
        static const QString str(QT_TR_NOOP("Network Storage"));
        m_NetworkStorageWidget = new NetworkStorageFileWidget(m_StateManager, m_ColorMapManager, this);
        connect(m_NetworkStorageWidget, SIGNAL(closed()), this, SIGNAL(accept()));
        connect(m_NetworkStorageWidget, SIGNAL(deleteCurrentSelections()), this, SIGNAL(deleteCurrentSelections()));
        m_NetworkStorageWidget->setDefaultDirName(m_DefaultDirName);
        m_NetworkStorageWidget->setExportFilePaths(m_ExportFilePathes);
        m_NetworkStorageWidget->setDeleteAfterSendExam(m_deleteAfterSendExam);
        m_NetworkStorageWidget->setIsCurrentMultiChoice(m_isCurrentMultiChoice);
        m_NetworkStorageWidget->setDicomTaskManager(m_DicomTaskManager);
        m_NetworkStorageWidget->setDeleteFilesController(m_DeleteFilesController);
        m_NetworkStorageWidget->showDcmSRCheckBox(m_IsSendSR);
        if (NULL != m_ExportWidget)
        {
            m_ExportWidget->setContainRetrieveExam(containRetrieveExam);
        }
        ui->listWidget->addItem(tr(str.toUtf8()));
        ui->stackedWidget->addWidget(m_NetworkStorageWidget);
        m_NetworkStorageWidget->initServiceNames();
    }
    if (bflag)
    {
        static const QString str(QT_TR_NOOP("Bluetooth Storage"));
        m_BluetoothStorageWidget = new BluetoothStorageFileWidget(m_ColorMapManager, this);
        connect(m_BluetoothStorageWidget, SIGNAL(closed()), this, SIGNAL(accept()));
        m_BluetoothStorageWidget->setExportFileListPaths(m_ExportFilePathes);
        m_BluetoothStorageWidget->setDicomTaskManager(m_DicomTaskManager);
        ui->listWidget->addItem(tr(str.toUtf8()));
        ui->stackedWidget->addWidget(m_BluetoothStorageWidget);
        m_BluetoothStorageWidget->initBluetoothNames();
    }
    if (uflag && nflag && NULL != m_ExportWidget && NULL != m_NetworkStorageWidget)
    {
        connect(m_ExportWidget, SIGNAL(exportTypeChanged(QString)), m_NetworkStorageWidget,
                SLOT(onExportTypeChanged(QString)));
        connect(m_NetworkStorageWidget, SIGNAL(exportTypeChanged(QString)), m_ExportWidget,
                SLOT(onExportTypeChanged(QString)));
    }

    ui->listWidget->setCurrentRow(0);
    ui->stackedWidget->setCurrentIndex(0);
}

bool DicomSeverInfoWidget::isWorking()
{
    if (m_NetworkStorageWidget != NULL)
    {
        return m_NetworkStorageWidget->isWorking();
    }
    return false;
}

void DicomSeverInfoWidget::on_listWidget_currentRowChanged(int currentRow)
{
    ui->stackedWidget->setCurrentIndex(currentRow);
    ui->stackedWidget->widget(currentRow)->activateWindow();
}
