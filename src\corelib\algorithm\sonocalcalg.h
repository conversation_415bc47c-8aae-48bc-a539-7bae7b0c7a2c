#ifndef SONOCALCALG_H
#define SONOCALCALG_H

#include "algorithm_global.h"
#include "virtualalg.h"
#include <QLibrary>
#include <QList>
#include <QSettings>
#include <QString>
#include <QVariant>
#include <array>

class ALGORITHMSHARED_EXPORT AutoCystisAlg : public VirtualAlg
{
    friend class AlgInstance;

public:
    ~AutoCystisAlg();

    typedef struct Point
    {
        int x;
        int y;
    } Point;

    typedef struct PointF
    {
        float x;
        float y;
    } PointF;

    typedef struct Ellipse
    {
        float x;
        float y;
        float width;
        float height;
        float rotationAngle;
    } Ellipse;

    inline bool isError(void)
    {
        return m_IsError;
    }
    int SetParameters(float r0 = 10, float alpha0 = 0.6, float simga0 = 1.5, float gI_threshold0 = 0.3,
                      float gI_balloon_v0 = 1., float num_iters0 = 100);
    std::vector<Point> getContour(unsigned char* imgData, int imgWidth, int imgHeight, int posX, int posY);
    std::pair<int, int> getAreaAndLength(std::vector<PointF>& points);
    int getLOngAndShortDiameter(std::vector<PointF>& points, Ellipse& gl);

protected:
    void* call_Instance(void);
    void call_Dispose(void* instance);
    int call_SetParameters(void* instance, float r0, float alpha0, float simga0, float gI_threshold0,
                           float gI_balloon_v0, float num_iters0);
    int call_GetContourNum(void* instance, unsigned char* imgData, int imgWidth, int imgHeight, int posX, int posY);
    int call_GetEveryContourPointsNum(void* instance, int* everyContourPntNum);
    int call_GetEveryContourPoints(void* instance, size_t count, int* everyContourPntNum, Point** m_ContourPnts);
    int call_GetPointsArea(PointF* pnts, size_t count);
    int call_GetPointsLength(PointF* pnts, size_t count);
    int call_GetPointsEllipseFitting(PointF* pnts, size_t count, Ellipse& gl);

private:
    AutoCystisAlg();

protected:
    void* m_Instance;
};
#endif // SONOCALCALG_H
