# os: windows，apple，android，unix
if(WIN32)
    set(BUILD_SYS "windows")
    add_definitions(-DSYS_WINDOWS)
elseif(APPLE)
    set(BUILD_SYS "apple")
    add_definitions(-DSYS_APPLE)
elseif(ANDROID)
    set(BUILD_SYS "android")
    add_definitions(-DSYS_ANDROID)
elseif(UNIX)
    set(BUILD_SYS "unix")
    add_definitions(-DSYS_UNIX)
endif()

# cpu: x86, ios, aarch
if(BUILD_TAR STREQUAL "x86")
    add_definitions(-DTAR_X86)
elseif(BUILD_TAR STREQUAL "ios")
    add_definitions(-DTAR_IOS)
elseif(BUILD_TAR STREQUAL "aarch")
    add_definitions(-DTAR_AARCH)
elseif(BUILD_TAR STREQUAL "arm")
    add_definitions(-DTAR_ARM)
endif()

# arch: 64bit，32bit
if(CMAKE_SIZEOF_VOID_P EQUAL 8)
    set(BUILD_ARC "64bit")
    add_definitions(-DARC_64BIT)
else()
    set(BUILD_ARC "32bit")
    add_definitions(-DARC_64BIT)
endif()

set(BUILD_SYS_TAR_ARC_NAME ${BUILD_SYS}_${BUILD_TAR}_${BUILD_ARC})
set(THIRDPARTYLIB_PATH ${PROJECT_SOURCE_DIR}/src/thirdparty/${BUILD_SYS_TAR_ARC_NAME})

# find function may not include third-party library paths, add the path
set(CMAKE_FIND_ROOT_PATH ${CMAKE_FIND_ROOT_PATH} ${THIRDPARTYLIB_PATH})

# 临时代码，未来会拿掉
if(APPLE)
    #apple 平台为ios平台,ios的qt平台是qt5,不支持qwebframe qwebengine 等,需要mock
    set(QT5MOCKAPI_ROOT ${THIRDPARTYLIB_PATH}/qt5mockapi/)
    find_package(qt5mockapi REQUIRED)
endif()

if(MSVC)
    add_definitions(-DPRETTY_FUNCTION=__FUNCTION__)

    set(THIRDPARTYLIB_NAME "bin")
    set(LIB_REFIX "")
    if(${CMAKE_BUILD_TYPE} STREQUAL "Debug")
        set(LIB_POSTFIX "d")
    endif()
    set(LIB_STATIC_EXT ".lib")
else()
    add_definitions(-DPRETTY_FUNCTION=__PRETTY_FUNCTION__)

    set(THIRDPARTYLIB_NAME "lib")
    set(LIB_REFIX "lib")
    set(LIB_POSTFIX "")
    set(LIB_STATIC_EXT ".a")
endif()

if(MSVC)
    # MultiThreaded
    # Compile with -MT or equivalent flag(s) to use a multi-threaded statically-linked runtime library.

    # MultiThreadedDLL
    # Compile with -MD or equivalent flag(s) to use a multi-threaded dynamically-linked runtime library.

    # MultiThreadedDebug
    # Compile with -MTd or equivalent flag(s) to use a multi-threaded statically-linked runtime library.

    # MultiThreadedDebugDLL
    # Compile with -MDd or equivalent flag(s) to use a multi-threaded dynamically-linked runtime library.
#    set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>")

    add_compile_options("$<$<C_COMPILER_ID:MSVC>:/utf-8>")
    add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/utf-8>")
    
    # This option is supported in Visual Studio 2017 and later.
    string(REGEX MATCH "^19[1-9][0-9]" ISVS2017LATER "${MSVC_VERSION}")
    if(ISVS2017LATER)
        add_compile_options("$<$<C_COMPILER_ID:MSVC>:/diagnostics:caret>")
        add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/diagnostics:caret>")
    endif(ISVS2017LATER)

    set(CMAKE_CXX_FLAGS  "${CMAKE_CXX_FLAGS} /W3 /WX /wd4996 /wd4661 /wd4172")

    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /fsanitize=address /Zi")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /Zi")
    set(CMAKE_EXE_LINKER_FLAGS_RELEASE "${CMAKE_EXE_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF")
    set(CMAKE_SHARED_LINKER_FLAGS_RELEASE "${CMAKE_SHARED_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF")
else()
    option(USE_GCOV "use gcov to code coverage" OFF)
    if(USE_GCOV)
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fprofile-arcs -ftest-coverage")
    endif()
    # message(STATUS "CMAKE_CXX_FLAGS is set to: ${CMAKE_CXX_FLAGS}")

    set(nowarning_flags_cpp "")
    if(${CMAKE_CXX_COMPILER_ID} STREQUAL "Clang") 
        set(warning_flags "${warning_flags} -Qunused-arguments ")
        set(nowarning_flags_cpp "${nowarning_flags_cpp} -Wno-inconsistent-missing-override -Wno-unused-value -Wno-switch -Wno-constant-conversion")
    endif()

    # set(warning_flags "-Wno-unused -Wreorder -Wextra  -Wno-unused-parameter -Wno-sign-compare -Wwrite-strings -Wignored-qualifiers")
    # 屏蔽-Wno-deprecated-copy
    set(nowarning_flags "-Wno-unused-result -Wno-unused")
    # if(NOT APPLE)
    #     # 删除stringop-overflow
    #     #set(warning_flags "${nowarning_flags} -Werror -Wextra -Werror=return-type")
    #     # 删除-Werror
    #     set(warning_flags "${nowarning_flags} -Wextra -Werror=return-type")
    # endif(NOT APPLE)

    #qmake 使用fPIC, x86_64需要fPIC
    # -fPIC             Generate position-independent code if possible (large mode).
    # -fpermissive      Downgrade some diagnostics about nonconformant code from errors to warnings. 
    #                   Thus, using -fpermissive will allow some nonconforming code to compile.
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} ${warning_flags} -fPIC")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${warning_flags} ${nowarning_flags_cpp} -fPIC")

    if(USE_ASAN)
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsanitize=address,undefined -fno-omit-frame-pointer -g")
        set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -fsanitize=address,undefined")
    endif()
    if(USE_TSAN)
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsanitize=thread,undefined -fno-omit-frame-pointer -g")
        set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -fsanitize=thread,undefined")
    endif()

    find_program(CCACHE_FOUND ccache)
    if(CCACHE_FOUND)
        set_property(GLOBAL PROPERTY RULE_LAUNCH_COMPILE ccache)
        set_property(GLOBAL PROPERTY RULE_LAUNCH_LINK ccache)
    endif(CCACHE_FOUND)
endif(MSVC)

# set_property(GLOBAL PROPERTY RULE_LAUNCH_COMPILE distcc)
# set_property(GLOBAL PROPERTY RULE_LAUNCH_LINK distcc)
