#ifndef FILIATIONTREEVIEWMODEL_H
#define FILIATIONTREEVIEWMODEL_H
#include "archive_global.h"

#include <QObject>
#include <QFileInfoList>

/**
 * @brief 为FiliationTreeView界面类提供提示信息
 */
class QDir;
class ARCHIVESHARED_EXPORT FiliationTreeViewModel : public QObject
{
    Q_OBJECT
public:
    explicit FiliationTreeViewModel(QObject* parent = 0);
    /**
     * @brief newFolderName 新建文件夹时自行提供文件名
     *
     * @param dir
     * @param str
     *
     * @return
     */
    static QString newFolderName(const QDir& dir, const QString& str);
    /**
     * @brief deFoldername 新建文件夹名
     *
     * @return
     */
    static QString deFoldername();
    /**
     * @brief mkDir 新建文件
     *
     * @param infoList
     * @param widget
     */
    void mkDir(const QFileInfoList& infoList, QWidget* widget);
    /**
     * @brief createDir 开始新建文件夹
     *
     * @param dir
     * @param dirName
     * @param widget
     *
     * @return
     */
    static bool createDir(const QDir& dir, const QString& dirName, QWidget* widget = 0);

signals:

public slots:
private:
};

#endif // FILIATIONTREEVIEWMODEL_H
