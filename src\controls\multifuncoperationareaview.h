#ifndef MULTIFUNCOPERATIONAREAVIEW_H
#define MULTIFUNCOPERATIONAREAVIEW_H

#include "controls_global.h"
#include "basewidget.h"
#include <QHash>

class QLabel;
// class MultiFuncOperationAreaModel;

class SystemHintModel;
namespace Ui
{
class MultiFuncOperationAreaView;
}
/**
 * @brief 多功能操作区界面
 */
class CONTROLSSHARED_EXPORT MultiFuncOperationAreaView : public BaseWidget
{
    Q_OBJECT

public:
    explicit MultiFuncOperationAreaView(QWidget* parent = 0);
    ~MultiFuncOperationAreaView();

    void setModel(SystemHintModel* model);

    void setTrackballButtonStatus(const QString& status);
    void setLeftButtonStatus(const QString& status);
    void setRightButtonStatus(const QString& status);

private slots:
    void onRawTBStatusChanged(bool isMeasuring);
    void onLeftButtonStatusChanged();
    void onRightButtonStatusChanged();

private:
    void retranslateUi();
    void init();
    void setupcontainerWidget();

private:
    Ui::MultiFuncOperationAreaView* ui;
    SystemHintModel* m_Model;
    QHash<QString, QString> m_TrackballIconHash;
    QHash<QString, QString> m_LeftButtonIconHash;
    QHash<QString, QString> m_RightButtonIconHash;
};

#endif // MULTIFUNCOPERATIONAREAVIEW_H
